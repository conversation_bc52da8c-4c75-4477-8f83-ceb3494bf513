trigger:
  batch: true
  branches:
    include:
      - master
      - develop
  paths:
    exclude:
      - README.md
      - /docs/
      - /blob/
pr:
  branches:
    include:
      - master
      - develop
  paths:
    exclude:
      - README.md
      - /docs/
      - /blob/
resources:
  repositories:
    - repository: devops-tools
      name: Agora/devops-tools
      type: git
      ref: develop
    - repository: spareparts-demo-data
      name: Agora/spareparts-demo-data
      type: git
      ref: master

parameters:
  - name: publish
    displayName: Publish packages and artifacts
    type: boolean
    default: false
variables:
  isMaster: $[eq(variables['Build.SourceBranch'], 'refs/heads/master')]
  isDevelop: $[eq(variables['Build.SourceBranch'], 'refs/heads/develop')]
  branchName: $[replace(variables['Build.SourceBranch'], 'refs/heads/', '')]
  
stages:
  - stage: Build_apis
    displayName: Build agora spare-parts
    pool:
      name: V30119700VMS004-LVL4
    jobs:
      - template: .pipelines/templates/build-and-publish-artifacts.yaml
        parameters:
          publish: ${{parameters.publish}}

  - ${{ if or(parameters.publish, eq(variables['Build.SourceBranch'], 'refs/heads/master'), eq(variables['Build.SourceBranch'], 'refs/heads/develop')) }}:
    - stage: build_charts
      dependsOn: Build_apis
      displayName: Build charts
      pool:
        name: V30119700VMS004-LVL4
      jobs:
        - template: publish-helm-chart/publish-helm-chart.yaml@devops-tools
          parameters:
            chart_folder: charts/spareparts-api
            chart_name: spareparts-api
            pre_steps:
              - checkout : self
                fetchTags: true
                fetchDepth: 0
              - script: pip install -U --user commitizen
                displayName: Install commitizen
              - script: |
                  export PATH="$HOME/.local/bin:$PATH"
                  cz -nr 3 bump --files-only --devrelease $(date +%Y%m%d)$(Build.BuildId)
                displayName: Increase automatically the dev version
                condition: and(succeeded(), ne(variables['Build.SourceBranch'], 'refs/heads/master'))
            post_steps:
              - script: echo "##vso[build.addbuildtag]published-api-chart"
                displayName: Add published-api-chart tag
        - template: publish-helm-chart/publish-helm-chart.yaml@devops-tools
          parameters:
            chart_folder: charts/spareparts-mdms
            chart_name: spareparts-mdms
            pre_steps:
              - checkout : self
                fetchTags: true
                fetchDepth: 0
              - script: pip install -U --user commitizen
                displayName: Install commitizen
              - script: |
                  export PATH="$HOME/.local/bin:$PATH"
                  cz -nr 3 bump --files-only --devrelease $(date +%Y%m%d)$(Build.BuildId)
                displayName: Increase automatically the dev version
                condition: and(succeeded(), ne(variables['Build.SourceBranch'], 'refs/heads/master'))
            post_steps:
              - script: echo "##vso[build.addbuildtag]published-mdms-chart"
                displayName: Add published-mdms-chart tag

  - ${{ if or(parameters.publish, eq(variables['Build.SourceBranch'], 'refs/heads/develop')) }}:
    - stage: Deploy_to_SANDBOX
      dependsOn: Build_apis
      displayName: Deploy to SANDBOX
      pool:
        name: V30119700VMS004-LVL4
      jobs:
        - job: Deployment
          steps:
            - checkout: none

            - task: AzureCLI@2
              displayName: Allow installing az extensions without prompt
              inputs:
                azureSubscription: 'VISIATIVSANDBOX301666-COMMERCE'
                scriptType: 'bash'
                scriptLocation: 'inlineScript'
                inlineScript: 'az config set extension.use_dynamic_install=yes_without_prompt'

            - task: AzureCLI@2
              displayName: Deploy new api image to spareparts-api-dev
              inputs:
                azureSubscription: 'VISIATIVSANDBOX301666-COMMERCE'
                scriptType: 'bash'
                scriptLocation: 'inlineScript'
                inlineScript: 'az containerapp update -n spareparts-api-ea -g SmartPart_Future -i smartpart.azurecr.io/spareparts-api:$(Build.BuildNumber)'

            - task: AzureCLI@2
              displayName: Deploy new mdms image to spareparts-mdms-dev
              inputs:
                azureSubscription: 'VISIATIVSANDBOX301666-COMMERCE'
                scriptType: 'bash'
                scriptLocation: 'inlineScript'
                inlineScript: 'az containerapp update -n spareparts-mdms-ea -g SmartPart_Future -i smartpart.azurecr.io/spareparts-mdms:$(Build.BuildNumber)'

    - template: .pipelines/templates/deploy-to-vvd.yaml
      parameters:
        env: dev
        releasesToDeploy :
          - spareparts-api
          - spareparts-mdms
        dependsOn: build_charts

  - ${{ if or(parameters.publish, eq(variables['Build.SourceBranch'], 'refs/heads/master')) }}:
    - template: .pipelines/templates/deploy-to-vvd.yaml
      parameters:
        env: int
        releasesToDeploy :
          - spareparts-api
          - spareparts-mdms
        dependsOn: build_charts

    - template: .pipelines/templates/deploy-to-vvd.yaml
      parameters:
        env: prod
        releasesToDeploy :
          - spareparts-api
          - spareparts-mdms
        dependsOn: deploy_vvd_int