
# Introduction

- This project contains the source code for the Spareparts microservices. It is developed with [ASPNET core 8](https://docs.microsoft.com/en-us/aspnet/core/?view=aspnetcore-8.0) and based on the [ABP framework](https://docs.abp.io/en/abp/latest).

# Getting Started

Requirements

-   Visual studio 2022
-   Abp CLI (see abp cli installation)
-   [Docker](https://docs.docker.com/get-docker/)
-   [Azurite](https://github.com/Azure/Azurite)
-   [Python](https://www.python.org/downloads/)
-   [Commitizen tools](https://commitizen-tools.github.io/commitizen/)

Abp cli installation :

```cmd
> dotnet tool install -g Volo.Abp.Cli
```

> See the [CLI documentation](https://docs.abp.io/en/abp/latest/CLI) for all available options.

Azurite installation :

```cmd
> npm i -global azurite
```

After installing Azurite, launch it :

```cmd
> azurite-blob
```

If you want to vizualise Azurite containers, use :
[Azure Storage Explorer](https://azure.microsoft.com/en-us/features/storage-explorer/)

# Commitizen

## Transition from commitizen/cz-cli to Commitizen (Python)

### Background

Our project has transitioned from using [commitizen/cz-cli](https://github.com/commitizen/cz-cli), a Node.js-based tool, to [Commitizen](https://commitizen-tools.github.io/), a Python-based tool. This change affects our commit message formatting and version management processes.

### Reasons for the Switch

The new Commitizen tool offers several advanced features that improve our development workflow:

1. Version bumping: Automatically increment version numbers based on commit types.
2. Changelog generation: Automatically create and update changelogs.
3. Managing dev releases: Better support for pre-release versions.
4. Customizable commit rules: Adapt the commit message format to our project needs.
5. Integration with other tools: Works well with various CI/CD pipelines.

### Important Changes

1. Python Requirement: The project now requires Python for version bumping and other Commitizen operations. This adds a new dependency to our development environment.

2. No longer npm-based: The project is no longer commitizen-friendly with the npm package. This means we're moving away from Node.js-based tooling for commit management.

3. Configuration File: We now use a `.cz.toml` file to manage project versions and Commitizen settings. This file should be committed to the repository.

4. Expanded Version Bumping: Version bumping can now update services and charts in addition to the main project version. This ensures consistency across our project components.

5. Commit Message Format: It remains the same, we use always [Conventional Commits](https://www.conventionalcommits.org/en/v1.0.0/)

### Developer Actions Required

To align with the new setup, developers should:

1. Uninstall the old cz-cli:
   ```
   npm uninstall -g commitizen cz-conventional-changelog
   ```

2. Install the new Python-based Commitizen tool:
   ```
   pip install --user -U Commitizen
   ```

3. Familiarize yourself with the new commit message format defined in the `.cz.toml` file.

4. When bumping versions, use the new Commitizen commands `cz bump`.

### Using the New Commitizen

1. To create a commit:
   ```
   cz commit
   ```

2. To bump the version:
   ```
   cz bump
   ```

3. To generate a changelog:
   ```
   cz changelog
   ```

### Configuration

The `.cz.toml` file in the project root controls Commitizen's behavior. Key configurations include:

- Version format
- Commit message rules
- Changelog settings
- Bump settings

Refer to this file for project-specific commit and version management rules.

### Notes

- Ensure you have Python 3.8+ installed on your system before installing the new Commitizen tool.
- If you encounter any issues with the new setup, please refer to the [Commitizen documentation](https://commitizen-tools.github.io/commitizen/).

### Migration Period

We understand this change may require some adjustment. During the initial migration period:

- Be patient with team members as they adapt to the new system.
- Report any issues or confusion to the project leads promptly.
# Architecture

Global architecture
![Architecture](blob/imgs/1-architecture.png)

Modules architecture
![Layers](blob/imgs/2-module-architecture.png)

CICD - The big picture
![CICD](blob/imgs/3-ci-cd-pipeline.png)

Master data management service : DCP Command (Create / Update / Delete)
![DCP-COMMAND](blob/imgs/5-data-change-proposal-request-pipeline.png)
 *DCP: **Data change proposal**
# Install dev cert

You can install dev certs by executing the following commands

> dotnet dev-certs https -ep $env:USERPROFILE\\.aspnet\https\Visiativ.SpareParts.HttpApi.Host.pfx -p crypticpassword

> dotnet dev-certs https --trust


## Project Docs

1. [API Conventions](./docs/API_CONVENTIONS.md)
   - Introduction
   - General Guidelines
   - Resource Naming
   - Parameter Naming
   - Response Naming
   - Error Handling

2. [CQRS with MediatR](./docs/CQRS.md)
   - Introduction to CQRS
   - Prerequisites
   - MediatR Framework
   - Implementing CQRS with MediatR
   - Conclusion

3. [Data Access Policy](./docs/Data_Access_Policy.md)
   - Overview
   - Key Interfaces
   - Implementation in Query Handlers
   - Query Definition
   - Access Control Services
   - SQL Views
   - Updating the ResourceVisibility View

4. [Developer Guide](./docs/DEVELOPER_GUIDE.md)
   - Naming Conventions in C#
   - C# Best Practices
   - Domain-Driven Design (DDD) Principles
   - Data Transfer Objects (DTOs) in the Application Layer
   - Cross-Cutting Concerns in ABP.io Framework

5. [Integration Tests](./docs/INTEGRATION_TESTS.md)
   - Overview
   - Prerequisites
   - Configuration
   - Integration Test Setup
     - Add Dependencies
     - DatabaseFixture
     - ABP Module Configuration
     - Test Collection Fixture
     - Integration Tests

6. [User Story Definition](./docs/USER_STORY_DEFINITION.md) - WIP
   - User Stories
   - Definition of Ready (DoR)
     - Key Components of a Ready User Story
     - RACI Matrix for User Story Readiness
   - Definition of Done (DoD)
     - Key Validation Points

7. [Branching Strategy](./docs/BRANCHING_STRATEGY.md)
   - Overview
   - Branches
     - Git Configuration
     - Main Branch (master)
     - Develop Branch
   - Feature Workflow
     - Feature Naming
     - Pull Request Process
     - Merge Process
   - Bug Workflow
     - Bug Naming
     - Bug Pull Request Process
     - Bug Merge Process
   - Hotfix Workflow
     - Hotfix Naming
     - Hotfix Process
   - Commit Messages
     - Conventional Commit Messages
     - Message Structure
   - Approval Process

# Build docker image
/!\ TODO - Maybe will be changed to aspire

# Unit & Integration tests
Integration tests are runnig using [TestContainers](https://dotnet.testcontainers.org/).

# Database Migrations
## Introduction

The init container project `DbMigrator` defines the entry point for all module migrations.

## DbMigrator project

The DbMigrator is a console application which is properly configured and supports multi-tenant scenarios where each tenant has its own database, including migration and seeding.

It is necessary to run the DbMigrator before deploying a new version of the solution to ensure that the database schema is updated and necessary initial data is seeded. Once complete, the actual application can be deployed.

Having a separate console application offers several benefits:

- It ensures that the application runs on a ready database before updating.
- The application starts faster compared to seeding the initial data internally.
- The application can function properly in a clustered environment (as a sidecar), as seeding data on application startup would cause conflicts.
