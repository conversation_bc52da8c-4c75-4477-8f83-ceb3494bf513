# Introduction

This repository provides the structure, rules, and processes for managing and deploying demo data used for provisioning new tenants.

## Overview
The repository contains the following resources:

- **CSV Files:**
  - Components CSV file
  - BOM lines CSV file
- **Resource Folders:**
  - Drawings
  - Images
  - Documents

## Project Structure
```
root/
├── components.csv             # Components data
├── bomlines.csv               # BOM lines data
├── drawings/                  # Drawings folder
│   ├── component_code/        # Subfolder for component drawings
│   │   ├── drawing1.pdf       # Drawing file
│   │   ├── drawing1.csv       # Drawing index file
│   └── ...
├── images/                    # Images folder
│   ├── component_code.jpeg    # Component image
│   └── ...
├── documents/                 # Documents folder
│   ├── component_code/        # Subfolder for component documents
│   │   ├── document1.pdf      # Document file
│   └── ...
└── README.md                  # Repository documentation
```

## General Rules
1. Images are stored in the `images` folder and named after the component code.
2. Drawings are stored in the `drawings` folder and are organized by subfolders named after the component code.
3. Documents are stored in the `documents` folder and are organized by subfolders named after the component code.
4. **Supported formats:**
   - Drawings: PDF (SVG files are not supported in this version).
   - Images: Supported MIME types are the same as those allowed by the API.
   - Documents: Supported MIME types are the same as those allowed by the API.
5. Each component can only have one thumbnail or image. Multiple images are not supported.

## CSV Files

### Components CSV File
- **Format:**
  ```
  code, label_fr, description_fr, label_en, description_en, label_de, description_de, type
  ```
- **Rules:**
  - The `code` field must be unique.
  - The `type` field accepts only the following values:
    - `product`
    - `assembly`
    - `part`

### BOM Lines CSV File
- **Format:**
  ```
  parent_assembly_code, parent_child_code, quantity
  ```

### Drawing Indexes CSV File
- **Format:**
  ```
  child_component_code, index
  ```
- **File Naming Convention:**
  - The index file must have the same name as the corresponding drawing file and reside in the same folder.
    - Example: `drawing1.pdf`, `drawing1.csv`

## File Naming Conventions

### Images
- Naming format: `component_code.ext`
  - Supported extensions: `.jpg`, `.jpeg`, `.png`
- Example: `123456.jpeg`

## Watermark Requirement
All images and drawings must include the "demo data" watermark. It is not the responsibility of the demo provisioning service to ensure the watermark is applied. This requirement must be fulfilled before adding the files to the repository.

## Data Import Rules
1. Import follows the same business rules already present in the domain layer.
2. If the data is incorrect, the import will be rejected, and the following actions occur:
   - A log message explains the data inconsistency.
   - (Note: A pipeline test ensures data correctness before import.)
3. Components with missing or incorrect images will be assigned a default component image.
4. Documents or drawings not adhering to naming conventions will not be imported or linked to components.

## Deployment Process

### Deployment Steps
1. Demo data is stored and managed in this repository.
2. When publishing artifacts:
   - The data is copied from the repository.
   - A test ensures data validity.
   - Valid data is copied to the `LocalPublishApiFolder`.

### Owner Responsibilities
- The product owner has full read and write access to this repository and is responsible for updating its contents.

### Caution
- Changes to demo data do not affect already provisioned tenants. Only new tenants will receive updated demo data.

### Manual Deployment
If the demo data changes, a manual deployment must be triggered on the `spareparts-api` develop branch.

---

This README ensures a clear understanding of the structure, naming conventions, and processes associated with the demo data repository.

