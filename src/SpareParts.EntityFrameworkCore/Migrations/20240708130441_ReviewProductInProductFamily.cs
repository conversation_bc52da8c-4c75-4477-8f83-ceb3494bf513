using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SpareParts.Migrations
{
    /// <inheritdoc />
    public partial class ReviewProductInProductFamily : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_CoreProductInProductFamily",
                table: "CoreProductInProductFamily");

            migrationBuilder.DropIndex(
                name: "IX_CoreComponentTranslations_Language_ComponentId_TenantId_DeletionTime",
                table: "CoreComponentTranslations");

            migrationBuilder.DropIndex(
                name: "IX_CoreComponents_Code_TenantId_DeletionTime",
                table: "CoreComponents");

            migrationBuilder.AddColumn<Guid>(
                name: "Id",
                table: "CoreProductInProductFamily",
                type: "uniqueidentifier",
                nullable: false,
                defaultValueSql: "NEWID()");

            migrationBuilder.AddPrimaryKey(
                name: "PK_CoreProductInProductFamily",
                table: "CoreProductInProductFamily",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "IX_CoreProductInProductFamily_ProductFamilyId_ProductId_TenantId_DeletionTime",
                table: "CoreProductInProductFamily",
                columns: new[] { "ProductFamilyId", "ProductId", "TenantId", "DeletionTime" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CoreComponentTranslations_Language_ComponentId_TenantId_DeletionTime",
                table: "CoreComponentTranslations",
                columns: new[] { "Language", "ComponentId", "TenantId", "DeletionTime" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CoreComponents_Code_TenantId_DeletionTime",
                table: "CoreComponents",
                columns: new[] { "Code", "TenantId", "DeletionTime" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_CoreProductInProductFamily",
                table: "CoreProductInProductFamily");

            migrationBuilder.DropIndex(
                name: "IX_CoreProductInProductFamily_ProductFamilyId_ProductId_TenantId_DeletionTime",
                table: "CoreProductInProductFamily");

            migrationBuilder.DropIndex(
                name: "IX_CoreComponentTranslations_Language_ComponentId_TenantId_DeletionTime",
                table: "CoreComponentTranslations");

            migrationBuilder.DropIndex(
                name: "IX_CoreComponents_Code_TenantId_DeletionTime",
                table: "CoreComponents");

            migrationBuilder.DropColumn(
                name: "Id",
                table: "CoreProductInProductFamily");

            migrationBuilder.AddPrimaryKey(
                name: "PK_CoreProductInProductFamily",
                table: "CoreProductInProductFamily",
                columns: new[] { "ProductFamilyId", "ProductId" });

            migrationBuilder.CreateIndex(
                name: "IX_CoreComponentTranslations_Language_ComponentId_TenantId_DeletionTime",
                table: "CoreComponentTranslations",
                columns: new[] { "Language", "ComponentId", "TenantId", "DeletionTime" },
                unique: true,
                filter: "[TenantId] IS NOT NULL AND [DeletionTime] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_CoreComponents_Code_TenantId_DeletionTime",
                table: "CoreComponents",
                columns: new[] { "Code", "TenantId", "DeletionTime" },
                unique: true,
                filter: "[TenantId] IS NOT NULL AND [DeletionTime] IS NOT NULL");
        }
    }
}
