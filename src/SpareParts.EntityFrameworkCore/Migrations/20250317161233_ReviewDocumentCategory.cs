using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SpareParts.Migrations
{
    /// <inheritdoc />
    public partial class ReviewDocumentCategory : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CoreDocumentInDocumentCategory_CoreDocumentCategories_CategoriesId",
                table: "CoreDocumentInDocumentCategory");

            migrationBuilder.DropPrimaryKey(
                name: "PK_CoreDocumentInDocumentCategory",
                table: "CoreDocumentInDocumentCategory");

            migrationBuilder.DropIndex(
                name: "IX_CoreDocumentInDocumentCategory_DocumentId",
                table: "CoreDocumentInDocumentCategory");

            migrationBuilder.RenameColumn(
                name: "CategoriesId",
                table: "CoreDocumentInDocumentCategory",
                newName: "DocumentCategoryId");

            migrationBuilder.AddColumn<string>(
                name: "Color",
                table: "CoreDocumentCategories",
                type: "nvarchar(7)",
                maxLength: 7,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddPrimaryKey(
                name: "PK_CoreDocumentInDocumentCategory",
                table: "CoreDocumentInDocumentCategory",
                columns: new[] { "DocumentId", "DocumentCategoryId" });

            migrationBuilder.CreateIndex(
                name: "IX_CoreDocumentInDocumentCategory_DocumentCategoryId",
                table: "CoreDocumentInDocumentCategory",
                column: "DocumentCategoryId");

            migrationBuilder.AddForeignKey(
                name: "FK_CoreDocumentInDocumentCategory_CoreDocumentCategories_DocumentCategoryId",
                table: "CoreDocumentInDocumentCategory",
                column: "DocumentCategoryId",
                principalTable: "CoreDocumentCategories",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CoreDocumentInDocumentCategory_CoreDocumentCategories_DocumentCategoryId",
                table: "CoreDocumentInDocumentCategory");

            migrationBuilder.DropPrimaryKey(
                name: "PK_CoreDocumentInDocumentCategory",
                table: "CoreDocumentInDocumentCategory");

            migrationBuilder.DropIndex(
                name: "IX_CoreDocumentInDocumentCategory_DocumentCategoryId",
                table: "CoreDocumentInDocumentCategory");

            migrationBuilder.DropColumn(
                name: "Color",
                table: "CoreDocumentCategories");

            migrationBuilder.RenameColumn(
                name: "DocumentCategoryId",
                table: "CoreDocumentInDocumentCategory",
                newName: "CategoriesId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_CoreDocumentInDocumentCategory",
                table: "CoreDocumentInDocumentCategory",
                columns: new[] { "CategoriesId", "DocumentId" });

            migrationBuilder.CreateIndex(
                name: "IX_CoreDocumentInDocumentCategory_DocumentId",
                table: "CoreDocumentInDocumentCategory",
                column: "DocumentId");

            migrationBuilder.AddForeignKey(
                name: "FK_CoreDocumentInDocumentCategory_CoreDocumentCategories_CategoriesId",
                table: "CoreDocumentInDocumentCategory",
                column: "CategoriesId",
                principalTable: "CoreDocumentCategories",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
