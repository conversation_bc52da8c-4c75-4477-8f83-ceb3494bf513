using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SpareParts.Migrations
{
    /// <inheritdoc />
    public partial class AddComponentOnlineTranslations : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "CoreComponentOnlineTranslations",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ComponentId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Language = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    Label = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    DeletionTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeleterId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CoreComponentOnlineTranslations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CoreComponentOnlineTranslations_CoreComponents_ComponentId",
                        column: x => x.ComponentId,
                        principalTable: "CoreComponents",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_CoreComponentOnlineTranslations_ComponentId",
                table: "CoreComponentOnlineTranslations",
                column: "ComponentId");

            migrationBuilder.CreateIndex(
                name: "IX_CoreComponentOnlineTranslations_Label",
                table: "CoreComponentOnlineTranslations",
                column: "Label");

            migrationBuilder.CreateIndex(
                name: "IX_CoreComponentOnlineTranslations_Language_ComponentId_TenantId_DeletionTime",
                table: "CoreComponentOnlineTranslations",
                columns: new[] { "Language", "ComponentId", "TenantId", "DeletionTime" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CoreComponentOnlineTranslations");
        }
    }
}
