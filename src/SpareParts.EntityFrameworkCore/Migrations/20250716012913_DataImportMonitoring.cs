using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SpareParts.Migrations
{
    /// <inheritdoc />
    public partial class DataImportMonitoring : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "CoreMasterProposals",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ExternalImportId = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    ExternalProposalId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Origin = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Code = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ComponentProposalsCount = table.Column<int>(type: "int", nullable: false),
                    BomProposalsCount = table.Column<int>(type: "int", nullable: true),
                    ComponentImageProposalsCount = table.Column<int>(type: "int", nullable: true),
                    DrawingProposalsCount = table.Column<int>(type: "int", nullable: true),
                    Action = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Status = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Error = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Warnings = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CoreMasterProposals", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CoreBomProposals",
                columns: table => new
                {
                    MasterProposalId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ExternalProposalId = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    ParentAssemblyCode = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Lines = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Status = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Error = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Warnings = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CoreBomProposals", x => new { x.MasterProposalId, x.ExternalProposalId });
                    table.ForeignKey(
                        name: "FK_CoreBomProposals_CoreMasterProposals_MasterProposalId",
                        column: x => x.MasterProposalId,
                        principalTable: "CoreMasterProposals",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CoreComponentImageProposals",
                columns: table => new
                {
                    MasterProposalId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ExternalProposalId = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    ComponentCode = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ImagePath = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Status = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Error = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Warnings = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CoreComponentImageProposals", x => new { x.MasterProposalId, x.ExternalProposalId });
                    table.ForeignKey(
                        name: "FK_CoreComponentImageProposals_CoreMasterProposals_MasterProposalId",
                        column: x => x.MasterProposalId,
                        principalTable: "CoreMasterProposals",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CoreComponentProposals",
                columns: table => new
                {
                    MasterProposalId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ExternalProposalId = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    Code = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Translations = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Type = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Status = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Error = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Warnings = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CoreComponentProposals", x => new { x.MasterProposalId, x.ExternalProposalId });
                    table.ForeignKey(
                        name: "FK_CoreComponentProposals_CoreMasterProposals_MasterProposalId",
                        column: x => x.MasterProposalId,
                        principalTable: "CoreMasterProposals",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CoreDrawingProposals",
                columns: table => new
                {
                    MasterProposalId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ExternalProposalId = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    AssemblyCode = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ComponentCodesByIndex = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Path = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Status = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Error = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Warnings = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CoreDrawingProposals", x => new { x.MasterProposalId, x.ExternalProposalId });
                    table.ForeignKey(
                        name: "FK_CoreDrawingProposals_CoreMasterProposals_MasterProposalId",
                        column: x => x.MasterProposalId,
                        principalTable: "CoreMasterProposals",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_CoreMasterProposals_ExternalImportId_TenantId",
                table: "CoreMasterProposals",
                columns: new[] { "ExternalImportId", "TenantId" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CoreBomProposals");

            migrationBuilder.DropTable(
                name: "CoreComponentImageProposals");

            migrationBuilder.DropTable(
                name: "CoreComponentProposals");

            migrationBuilder.DropTable(
                name: "CoreDrawingProposals");

            migrationBuilder.DropTable(
                name: "CoreMasterProposals");
        }
    }
}
