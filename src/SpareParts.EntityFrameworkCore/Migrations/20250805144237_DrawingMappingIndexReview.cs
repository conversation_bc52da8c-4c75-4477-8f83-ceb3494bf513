using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SpareParts.Migrations
{
    /// <inheritdoc />
    public partial class DrawingMappingIndexReview : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_CoreDrawingMappings_DrawingId",
                table: "CoreDrawingMappings",
                column: "DrawingId");


            migrationBuilder.Sql("DROP INDEX IF EXISTS [IX_CoreDrawingMappings_DrawingId_ComponentId] ON [CoreDrawingMappings]");
            migrationBuilder.Sql("DROP INDEX IF EXISTS [IX_CoreDrawingMappings_DrawingId_Index] ON [CoreDrawingMappings]");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_CoreDrawingMappings_DrawingId",
                table: "CoreDrawingMappings");
        }
    }
}
