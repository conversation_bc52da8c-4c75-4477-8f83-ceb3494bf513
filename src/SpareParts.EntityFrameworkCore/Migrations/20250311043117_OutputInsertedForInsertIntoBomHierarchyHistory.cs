using Microsoft.EntityFrameworkCore.Migrations;
using SpareParts.Core;
using static SpareParts.Common.SqlScriptProvider;

#nullable disable

namespace SpareParts.Migrations
{
    /// <inheritdoc />
    public partial class OutputInsertedForInsertIntoBomHierarchyHistory : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("DROP PROCEDURE InsertIntoBomHierarchyHistory;");
            migrationBuilder.Sql(GetSqlQuery(ScriptName.InsertIntoBomHierarchyHistoryProcedure.ToString(), 3));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("DROP PROCEDURE InsertIntoBomHierarchyHistory;");
            migrationBuilder.Sql(GetSqlQuery(ScriptName.InsertIntoBomHierarchyHistoryProcedure.ToString(), 2));
        }
    }
}
