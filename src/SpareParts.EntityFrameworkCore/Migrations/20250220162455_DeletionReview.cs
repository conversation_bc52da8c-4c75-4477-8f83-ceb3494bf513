using System;
using Microsoft.EntityFrameworkCore.Migrations;
using static SpareParts.Common.SqlScriptProvider;
using ScriptName = SpareParts.Core.ScriptName;

#nullable disable

namespace SpareParts.Migrations
{
    /// <inheritdoc />
    public partial class DeletionReview : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CoreEquipments_AdmCompanies_CompanyId",
                table: "CoreEquipments");

            migrationBuilder.AddForeignKey(
                name: "FK_CoreEquipments_AdmCompanies_CompanyId",
                table: "CoreEquipments",
                column: "CompanyId",
                principalTable: "AdmCompanies",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.Sql("DELETE FROM AdmInvitations WHERE TenantId IS NULL OR IsDeleted = 1");

            migrationBuilder.Sql("DELETE FROM AdmCompanies WHERE TenantId IS NULL OR IsDeleted = 1");

            migrationBuilder.Sql("DELETE FROM CommonPublicResources WHERE TenantId IS NULL");

            migrationBuilder.Sql("DELETE FROM CoreBomLines WHERE TenantId IS NULL");

            migrationBuilder.Sql("DELETE FROM CoreAssemblies WHERE TenantId IS NULL");

            migrationBuilder.Sql("DELETE FROM CoreComponents WHERE TenantId IS NULL OR IsDeleted = 1");

            migrationBuilder.Sql("DELETE FROM CoreComponentDocuments WHERE TenantId IS NULL");

            migrationBuilder.Sql("DELETE FROM CoreComponentOnlineTranslations WHERE TenantId IS NULL OR IsDeleted = 1");

            migrationBuilder.Sql("DELETE FROM CoreComponentTranslations WHERE TenantId IS NULL OR IsDeleted = 1");

            migrationBuilder.Sql("DELETE FROM CoreParts WHERE TenantId IS NULL");

            migrationBuilder.Sql("DELETE FROM CoreProducts WHERE TenantId IS NULL");

            migrationBuilder.Sql("DELETE FROM CoreEquipments WHERE TenantId IS NULL");

            migrationBuilder.Sql("DELETE FROM CoreProductFamilies WHERE TenantId IS NULL");

            migrationBuilder.Sql("DELETE FROM CoreProductFamilyTranslations WHERE TenantId IS NULL OR IsDeleted = 1");

            migrationBuilder.Sql("DELETE FROM CoreProductInProductFamily WHERE TenantId IS NULL OR IsDeleted = 1");

            migrationBuilder.Sql("DELETE FROM CoreComponentDrawingIndexes WHERE TenantId IS NULL");

            migrationBuilder.Sql("DELETE FROM CoreDocuments WHERE TenantId IS NULL");

            migrationBuilder.Sql("DELETE FROM CoreDocumentCategoryTranslations WHERE TenantId IS NULL OR IsDeleted = 1 OR DocumentCategoryId IS NULL");

            migrationBuilder.Sql("DELETE FROM CoreDocumentTranslations WHERE TenantId IS NULL OR IsDeleted = 1 OR DocumentId IS NULL");

            migrationBuilder.Sql("DELETE FROM CoreDrawings WHERE TenantId IS NULL");

            migrationBuilder.Sql("DELETE FROM CoreResources WHERE TenantId IS NULL");

            migrationBuilder.DropForeignKey(
                name: "FK_CoreBomLines_CoreComponents_ParentComponentId",
                table: "CoreBomLines");

            migrationBuilder.DropForeignKey(
                name: "FK_CoreDocumentCategoryTranslations_CoreDocumentCategories_DocumentCategoryId",
                table: "CoreDocumentCategoryTranslations");

            migrationBuilder.DropForeignKey(
                name: "FK_CoreDocumentTranslations_CoreDocuments_DocumentId",
                table: "CoreDocumentTranslations");

            migrationBuilder.DropForeignKey(
                name: "FK_CoreDrawings_CoreAssemblies_ComponentId",
                table: "CoreDrawings");

            migrationBuilder.DropPrimaryKey(
                name: "PK_CoreProductInProductFamily",
                table: "CoreProductInProductFamily");

            migrationBuilder.DropIndex(
                name: "IX_CoreProductInProductFamily_ProductFamilyId_ProductId_TenantId_DeletionTime",
                table: "CoreProductInProductFamily");

            migrationBuilder.DropPrimaryKey(
                name: "PK_CoreProductFamilyTranslations",
                table: "CoreProductFamilyTranslations");

            migrationBuilder.DropIndex(
                name: "IX_CoreProductFamilyTranslations_Language",
                table: "CoreProductFamilyTranslations");

            migrationBuilder.DropPrimaryKey(
                name: "PK_CoreDocumentTranslations",
                table: "CoreDocumentTranslations");

            migrationBuilder.DropIndex(
                name: "IX_CoreDocumentTranslations_Language",
                table: "CoreDocumentTranslations");

            migrationBuilder.DropPrimaryKey(
                name: "PK_CoreDocumentCategoryTranslations",
                table: "CoreDocumentCategoryTranslations");

            migrationBuilder.DropIndex(
                name: "IX_CoreDocumentCategoryTranslations_Language",
                table: "CoreDocumentCategoryTranslations");

            migrationBuilder.DropIndex(
                name: "IX_CoreComponentTranslations_Language_ComponentId_TenantId_DeletionTime",
                table: "CoreComponentTranslations");

            migrationBuilder.DropIndex(
                name: "IX_CoreComponents_Code_TenantId_DeletionTime",
                table: "CoreComponents");

            migrationBuilder.DropPrimaryKey(
                name: "PK_CoreComponentOnlineTranslations",
                table: "CoreComponentOnlineTranslations");

            migrationBuilder.DropIndex(
                name: "IX_CoreComponentOnlineTranslations_Language_ComponentId_TenantId_DeletionTime",
                table: "CoreComponentOnlineTranslations");

            migrationBuilder.DropIndex(
                name: "IX_AdmInvitations_Code_TenantId_DeletionTime",
                table: "AdmInvitations");

            migrationBuilder.DropIndex(
                name: "IX_AdmCompanies_Code_TenantId_DeletionTime",
                table: "AdmCompanies");

            migrationBuilder.DropColumn(
                name: "Id",
                table: "CoreProductInProductFamily");

            migrationBuilder.DropColumn(
                name: "Id",
                table: "CoreProductFamilyTranslations");

            migrationBuilder.DropColumn(
                name: "Id",
                table: "CoreDocumentTranslations");

            migrationBuilder.DropColumn(
                name: "Id",
                table: "CoreDocumentCategoryTranslations");

            migrationBuilder.DropPrimaryKey(
                name: "PK_CoreComponentTranslations",
                table: "CoreComponentTranslations");

            migrationBuilder.DropColumn(
                name: "Id",
                table: "CoreComponentTranslations");

            migrationBuilder.DropColumn(
                name: "Id",
                table: "CoreComponentOnlineTranslations");

            migrationBuilder.RenameColumn(
                name: "ComponentId",
                table: "CoreDrawings",
                newName: "AssemblyId");

            migrationBuilder.RenameIndex(
                name: "IX_CoreDrawings_ComponentId",
                table: "CoreDrawings",
                newName: "IX_CoreDrawings_AssemblyId");

            migrationBuilder.RenameColumn(
                name: "ParentComponentId",
                table: "CoreBomLines",
                newName: "ParentAssemblyId");

            migrationBuilder.RenameIndex(
                name: "IX_CoreBomLines_ParentComponentId",
                table: "CoreBomLines",
                newName: "IX_CoreBomLines_ParentAssemblyId");

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreResources",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreProducts",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreProductInProductFamily",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreProductFamilyTranslations",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreProductFamilies",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreParts",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreEquipments",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreDrawings",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreDocumentTranslations",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "DocumentId",
                table: "CoreDocumentTranslations",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreDocuments",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreDocumentCategoryTranslations",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "DocumentCategoryId",
                table: "CoreDocumentCategoryTranslations",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreDocumentCategories",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreComponentTranslations",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreComponents",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreComponentOnlineTranslations",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreComponentDrawingIndexes",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreComponentDocuments",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreBomLines",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreAssemblies",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CommonPublicResources",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "AdmInvitations",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "AdmCompanies",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AddPrimaryKey(
                name: "PK_CoreProductInProductFamily",
                table: "CoreProductInProductFamily",
                columns: new[] { "ProductFamilyId", "ProductId" });

            migrationBuilder.AddPrimaryKey(
                name: "PK_CoreProductFamilyTranslations",
                table: "CoreProductFamilyTranslations",
                columns: new[] { "Language", "ProductFamilyId" });

            migrationBuilder.AddPrimaryKey(
                name: "PK_CoreDocumentTranslations",
                table: "CoreDocumentTranslations",
                columns: new[] { "Language", "DocumentId" });

            migrationBuilder.AddPrimaryKey(
                name: "PK_CoreDocumentCategoryTranslations",
                table: "CoreDocumentCategoryTranslations",
                columns: new[] { "Language", "DocumentCategoryId" });

            migrationBuilder.AddPrimaryKey(
                name: "PK_CoreComponentTranslations",
                table: "CoreComponentTranslations",
                columns: new[] { "Language", "ComponentId" });

            migrationBuilder.AddPrimaryKey(
                name: "PK_CoreComponentOnlineTranslations",
                table: "CoreComponentOnlineTranslations",
                columns: new[] { "Language", "ComponentId" });

            migrationBuilder.CreateIndex(
                name: "IX_CoreComponents_Code_TenantId",
                table: "CoreComponents",
                columns: new[] { "Code", "TenantId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AdmInvitations_Code_TenantId",
                table: "AdmInvitations",
                columns: new[] { "Code", "TenantId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AdmCompanies_Code_TenantId",
                table: "AdmCompanies",
                columns: new[] { "Code", "TenantId" },
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_CoreBomLines_CoreComponents_ParentAssemblyId",
                table: "CoreBomLines",
                column: "ParentAssemblyId",
                principalTable: "CoreComponents",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CoreDocumentCategoryTranslations_CoreDocumentCategories_DocumentCategoryId",
                table: "CoreDocumentCategoryTranslations",
                column: "DocumentCategoryId",
                principalTable: "CoreDocumentCategories",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_CoreDocumentTranslations_CoreDocuments_DocumentId",
                table: "CoreDocumentTranslations",
                column: "DocumentId",
                principalTable: "CoreDocuments",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_CoreDrawings_CoreAssemblies_AssemblyId",
                table: "CoreDrawings",
                column: "AssemblyId",
                principalTable: "CoreAssemblies",
                principalColumn: "Id");

            migrationBuilder.Sql(GetSqlQuery(ScriptName.BeforeDeleteFromAssembliesTrigger.ToString(), 2));

            migrationBuilder.Sql(GetSqlQuery(ScriptName.BomPathsSqlView.ToString(), 2));

            migrationBuilder.Sql("DROP PROCEDURE InsertIntoBomHierarchyHistory;");
            migrationBuilder.Sql(GetSqlQuery(ScriptName.InsertIntoBomHierarchyHistoryProcedure.ToString(), 2));

            migrationBuilder.Sql(GetSqlQuery(ScriptName.ProductFlattenHierarchySqlView.ToString(), 6));

            migrationBuilder.Sql(GetSqlQuery(ScriptName.ResourceVisibilitySqlView.ToString(), 2));

            migrationBuilder.Sql(
                "UPDATE CoreDrawings SET IsDeleted = 1 WHERE AssemblyId IN (SELECT Id FROM CoreAssemblies WHERE IsDeleted = 1)");
            migrationBuilder.Sql(
                "UPDATE CoreBomLines SET IsDeleted = 1 WHERE ParentAssemblyId IN (SELECT Id FROM CoreAssemblies WHERE IsDeleted = 1)");
            migrationBuilder.Sql(
                "UPDATE CoreDocumentCategoryTranslations SET IsDeleted = 1 WHERE DocumentCategoryId IN (SELECT Id FROM CoreDocumentCategories WHERE IsDeleted = 1)");
            migrationBuilder.Sql(
                "UPDATE CoreDocumentTranslations SET IsDeleted = 1 WHERE DocumentId IN (SELECT Id FROM CoreDocuments WHERE IsDeleted = 1)");
            migrationBuilder.Sql(
                "UPDATE CoreComponentDocuments SET IsDeleted = 1 WHERE DocumentId IN (SELECT Id FROM CoreDocuments WHERE IsDeleted = 1)");
            migrationBuilder.Sql(
                "UPDATE CoreResources SET IsDeleted = 1 WHERE Id IN (SELECT ResourceId FROM CoreDocuments WHERE IsDeleted = 1)");
            migrationBuilder.Sql(
                "UPDATE CoreComponentDrawingIndexes SET IsDeleted = 1 WHERE DrawingId IN (SELECT Id FROM CoreDrawings WHERE IsDeleted = 1)");
            migrationBuilder.Sql(
                "UPDATE CoreResources SET IsDeleted = 1 WHERE Id IN (SELECT ImageId FROM CoreDrawings WHERE IsDeleted = 1)");
            migrationBuilder.Sql(
                "UPDATE CoreResources SET IsDeleted = 1 WHERE Id IN (SELECT SourceFileId FROM CoreDrawings WHERE IsDeleted = 1)");
            migrationBuilder.Sql(
                "UPDATE CoreEquipments SET IsDeleted = 1 WHERE ProductId IN (SELECT Id FROM CoreProducts WHERE IsDeleted = 1)");
            migrationBuilder.Sql(
                "UPDATE CoreProductInProductFamily SET IsDeleted = 1 WHERE ProductId IN (SELECT Id FROM CoreProducts WHERE IsDeleted = 1)");
            migrationBuilder.Sql(
                "UPDATE CoreProductInProductFamily SET IsDeleted = 1 WHERE ProductFamilyId IN (SELECT Id FROM CoreProductFamilies WHERE IsDeleted = 1)");
            migrationBuilder.Sql(
                "UPDATE CoreProductFamilyTranslations SET IsDeleted = 1 WHERE ProductFamilyId IN (SELECT Id FROM CoreProductFamilies WHERE IsDeleted = 1)");

            migrationBuilder.Sql("UPDATE CoreDocumentTranslations SET Description = NULL WHERE Description = ''");

            migrationBuilder.Sql("UPDATE CoreProductFamilyTranslations SET Description = NULL WHERE Description = ''");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CoreBomLines_CoreComponents_ParentAssemblyId",
                table: "CoreBomLines");

            migrationBuilder.DropForeignKey(
                name: "FK_CoreDocumentCategoryTranslations_CoreDocumentCategories_DocumentCategoryId",
                table: "CoreDocumentCategoryTranslations");

            migrationBuilder.DropForeignKey(
                name: "FK_CoreDocumentTranslations_CoreDocuments_DocumentId",
                table: "CoreDocumentTranslations");

            migrationBuilder.DropForeignKey(
                name: "FK_CoreDrawings_CoreAssemblies_AssemblyId",
                table: "CoreDrawings");

            migrationBuilder.DropForeignKey(
                name: "FK_CoreEquipments_AdmCompanies_CompanyId",
                table: "CoreEquipments");

            migrationBuilder.DropPrimaryKey(
                name: "PK_CoreProductInProductFamily",
                table: "CoreProductInProductFamily");

            migrationBuilder.DropPrimaryKey(
                name: "PK_CoreProductFamilyTranslations",
                table: "CoreProductFamilyTranslations");

            migrationBuilder.DropPrimaryKey(
                name: "PK_CoreDocumentTranslations",
                table: "CoreDocumentTranslations");

            migrationBuilder.DropPrimaryKey(
                name: "PK_CoreDocumentCategoryTranslations",
                table: "CoreDocumentCategoryTranslations");

            migrationBuilder.DropPrimaryKey(
                name: "PK_CoreComponentTranslations",
                table: "CoreComponentTranslations");

            migrationBuilder.DropIndex(
                name: "IX_CoreComponents_Code_TenantId",
                table: "CoreComponents");

            migrationBuilder.DropPrimaryKey(
                name: "PK_CoreComponentOnlineTranslations",
                table: "CoreComponentOnlineTranslations");

            migrationBuilder.DropIndex(
                name: "IX_AdmInvitations_Code_TenantId",
                table: "AdmInvitations");

            migrationBuilder.DropIndex(
                name: "IX_AdmCompanies_Code_TenantId",
                table: "AdmCompanies");

            migrationBuilder.RenameColumn(
                name: "AssemblyId",
                table: "CoreDrawings",
                newName: "ComponentId");

            migrationBuilder.RenameIndex(
                name: "IX_CoreDrawings_AssemblyId",
                table: "CoreDrawings",
                newName: "IX_CoreDrawings_ComponentId");

            migrationBuilder.RenameColumn(
                name: "ParentAssemblyId",
                table: "CoreBomLines",
                newName: "ParentComponentId");

            migrationBuilder.RenameIndex(
                name: "IX_CoreBomLines_ParentAssemblyId",
                table: "CoreBomLines",
                newName: "IX_CoreBomLines_ParentComponentId");

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreResources",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreProducts",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreProductInProductFamily",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AddColumn<Guid>(
                name: "Id",
                table: "CoreProductInProductFamily",
                type: "uniqueidentifier",
                nullable: false,
                defaultValueSql: "NEWID()");

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreProductFamilyTranslations",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AddColumn<Guid>(
                name: "Id",
                table: "CoreProductFamilyTranslations",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreProductFamilies",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreParts",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreEquipments",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreDrawings",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreDocumentTranslations",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AlterColumn<Guid>(
                name: "DocumentId",
                table: "CoreDocumentTranslations",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AddColumn<Guid>(
                name: "Id",
                table: "CoreDocumentTranslations",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreDocuments",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreDocumentCategoryTranslations",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AlterColumn<Guid>(
                name: "DocumentCategoryId",
                table: "CoreDocumentCategoryTranslations",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AddColumn<Guid>(
                name: "Id",
                table: "CoreDocumentCategoryTranslations",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreDocumentCategories",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreComponentTranslations",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AddColumn<Guid>(
                name: "Id",
                table: "CoreComponentTranslations",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreComponents",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreComponentOnlineTranslations",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AddColumn<Guid>(
                name: "Id",
                table: "CoreComponentOnlineTranslations",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreComponentDrawingIndexes",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreComponentDocuments",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreBomLines",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CoreAssemblies",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "CommonPublicResources",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "AdmInvitations",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "AdmCompanies",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AddPrimaryKey(
                name: "PK_CoreProductInProductFamily",
                table: "CoreProductInProductFamily",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_CoreProductFamilyTranslations",
                table: "CoreProductFamilyTranslations",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_CoreDocumentTranslations",
                table: "CoreDocumentTranslations",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_CoreDocumentCategoryTranslations",
                table: "CoreDocumentCategoryTranslations",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_CoreComponentTranslations",
                table: "CoreComponentTranslations",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_CoreComponentOnlineTranslations",
                table: "CoreComponentOnlineTranslations",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "IX_CoreProductInProductFamily_ProductFamilyId_ProductId_TenantId_DeletionTime",
                table: "CoreProductInProductFamily",
                columns: new[] { "ProductFamilyId", "ProductId", "TenantId", "DeletionTime" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CoreProductFamilyTranslations_Language",
                table: "CoreProductFamilyTranslations",
                column: "Language");

            migrationBuilder.CreateIndex(
                name: "IX_CoreDocumentTranslations_Language",
                table: "CoreDocumentTranslations",
                column: "Language");

            migrationBuilder.CreateIndex(
                name: "IX_CoreDocumentCategoryTranslations_Language",
                table: "CoreDocumentCategoryTranslations",
                column: "Language");

            migrationBuilder.CreateIndex(
                name: "IX_CoreComponentTranslations_Language_ComponentId_TenantId_DeletionTime",
                table: "CoreComponentTranslations",
                columns: new[] { "Language", "ComponentId", "TenantId", "DeletionTime" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CoreComponents_Code_TenantId_DeletionTime",
                table: "CoreComponents",
                columns: new[] { "Code", "TenantId", "DeletionTime" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CoreComponentOnlineTranslations_Language_ComponentId_TenantId_DeletionTime",
                table: "CoreComponentOnlineTranslations",
                columns: new[] { "Language", "ComponentId", "TenantId", "DeletionTime" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AdmInvitations_Code_TenantId_DeletionTime",
                table: "AdmInvitations",
                columns: new[] { "Code", "TenantId", "DeletionTime" },
                unique: true,
                filter: "[TenantId] IS NOT NULL AND [DeletionTime] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_AdmCompanies_Code_TenantId_DeletionTime",
                table: "AdmCompanies",
                columns: new[] { "Code", "TenantId", "DeletionTime" },
                unique: true,
                filter: "[TenantId] IS NOT NULL AND [DeletionTime] IS NOT NULL");

            migrationBuilder.AddForeignKey(
                name: "FK_CoreBomLines_CoreComponents_ParentComponentId",
                table: "CoreBomLines",
                column: "ParentComponentId",
                principalTable: "CoreComponents",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CoreDocumentCategoryTranslations_CoreDocumentCategories_DocumentCategoryId",
                table: "CoreDocumentCategoryTranslations",
                column: "DocumentCategoryId",
                principalTable: "CoreDocumentCategories",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CoreDocumentTranslations_CoreDocuments_DocumentId",
                table: "CoreDocumentTranslations",
                column: "DocumentId",
                principalTable: "CoreDocuments",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CoreDrawings_CoreAssemblies_ComponentId",
                table: "CoreDrawings",
                column: "ComponentId",
                principalTable: "CoreAssemblies",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CoreEquipments_AdmCompanies_CompanyId",
                table: "CoreEquipments",
                column: "CompanyId",
                principalTable: "AdmCompanies",
                principalColumn: "Id");

            migrationBuilder.Sql(GetSqlQuery(ScriptName.BeforeDeleteFromAssembliesTrigger.ToString(), 1));

            migrationBuilder.Sql(GetSqlQuery(ScriptName.BomPathsSqlView.ToString(), 1));

            migrationBuilder.Sql("DROP PROCEDURE InsertIntoBomHierarchyHistory;");
            migrationBuilder.Sql(GetSqlQuery(ScriptName.InsertIntoBomHierarchyHistoryProcedure.ToString(), 1));

            migrationBuilder.Sql(GetSqlQuery(ScriptName.ProductFlattenHierarchySqlView.ToString(), 5));

            migrationBuilder.Sql(GetSqlQuery(ScriptName.ResourceVisibilitySqlView.ToString(), 1));
        }
    }
}
