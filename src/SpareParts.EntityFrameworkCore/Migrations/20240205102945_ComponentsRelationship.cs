using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SpareParts.Migrations
{
    /// <inheritdoc />
    public partial class ComponentsRelationship : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CoreComponents_CoreResources_ImageId",
                table: "CoreComponents");

            migrationBuilder.DropIndex(
                name: "IX_CoreComponents_Code_Version_TenantId",
                table: "CoreComponents");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "CoreProductFamilies");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "CoreProductFamilies");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "CoreProductFamilies");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "CoreComponents");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "CoreComponents");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "CoreComponents");

            migrationBuilder.DropColumn(
                name: "Version",
                table: "CoreComponents");

            migrationBuilder.RenameColumn(
                name: "CanBeBomItem",
                table: "CoreProducts",
                newName: "IsVisible");

            migrationBuilder.AddColumn<string>(
                name: "ConcurrencyStamp",
                table: "CoreProducts",
                type: "nvarchar(40)",
                maxLength: 40,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTime>(
                name: "CreationTime",
                table: "CoreProducts",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<Guid>(
                name: "CreatorId",
                table: "CoreProducts",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "LastModificationTime",
                table: "CoreProducts",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "LastModifierId",
                table: "CoreProducts",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "TenantId",
                table: "CoreProducts",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ConcurrencyStamp",
                table: "CoreParts",
                type: "nvarchar(40)",
                maxLength: 40,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTime>(
                name: "CreationTime",
                table: "CoreParts",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<Guid>(
                name: "CreatorId",
                table: "CoreParts",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "LastModificationTime",
                table: "CoreParts",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "LastModifierId",
                table: "CoreParts",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "TenantId",
                table: "CoreParts",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ConcurrencyStamp",
                table: "CoreAssemblies",
                type: "nvarchar(40)",
                maxLength: 40,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTime>(
                name: "CreationTime",
                table: "CoreAssemblies",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<Guid>(
                name: "CreatorId",
                table: "CoreAssemblies",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "LastModificationTime",
                table: "CoreAssemblies",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "LastModifierId",
                table: "CoreAssemblies",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "TenantId",
                table: "CoreAssemblies",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_CoreComponents_Code_TenantId",
                table: "CoreComponents",
                columns: new[] { "Code", "TenantId" },
                unique: true,
                filter: "[TenantId] IS NOT NULL");

            migrationBuilder.AddForeignKey(
                name: "FK_CoreComponents_CoreResources_ImageId",
                table: "CoreComponents",
                column: "ImageId",
                principalTable: "CoreResources",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CoreComponents_CoreResources_ImageId",
                table: "CoreComponents");

            migrationBuilder.DropIndex(
                name: "IX_CoreComponents_Code_TenantId",
                table: "CoreComponents");

            migrationBuilder.DropColumn(
                name: "ConcurrencyStamp",
                table: "CoreProducts");

            migrationBuilder.DropColumn(
                name: "CreationTime",
                table: "CoreProducts");

            migrationBuilder.DropColumn(
                name: "CreatorId",
                table: "CoreProducts");

            migrationBuilder.DropColumn(
                name: "LastModificationTime",
                table: "CoreProducts");

            migrationBuilder.DropColumn(
                name: "LastModifierId",
                table: "CoreProducts");

            migrationBuilder.DropColumn(
                name: "TenantId",
                table: "CoreProducts");

            migrationBuilder.DropColumn(
                name: "ConcurrencyStamp",
                table: "CoreParts");

            migrationBuilder.DropColumn(
                name: "CreationTime",
                table: "CoreParts");

            migrationBuilder.DropColumn(
                name: "CreatorId",
                table: "CoreParts");

            migrationBuilder.DropColumn(
                name: "LastModificationTime",
                table: "CoreParts");

            migrationBuilder.DropColumn(
                name: "LastModifierId",
                table: "CoreParts");

            migrationBuilder.DropColumn(
                name: "TenantId",
                table: "CoreParts");

            migrationBuilder.DropColumn(
                name: "ConcurrencyStamp",
                table: "CoreAssemblies");

            migrationBuilder.DropColumn(
                name: "CreationTime",
                table: "CoreAssemblies");

            migrationBuilder.DropColumn(
                name: "CreatorId",
                table: "CoreAssemblies");

            migrationBuilder.DropColumn(
                name: "LastModificationTime",
                table: "CoreAssemblies");

            migrationBuilder.DropColumn(
                name: "LastModifierId",
                table: "CoreAssemblies");

            migrationBuilder.DropColumn(
                name: "TenantId",
                table: "CoreAssemblies");

            migrationBuilder.RenameColumn(
                name: "IsVisible",
                table: "CoreProducts",
                newName: "CanBeBomItem");

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "CoreProductFamilies",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "CoreProductFamilies",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "CoreProductFamilies",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "CoreComponents",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "CoreComponents",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "CoreComponents",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "Version",
                table: "CoreComponents",
                type: "nvarchar(450)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateIndex(
                name: "IX_CoreComponents_Code_Version_TenantId",
                table: "CoreComponents",
                columns: new[] { "Code", "Version", "TenantId" },
                unique: true,
                filter: "[TenantId] IS NOT NULL");

            migrationBuilder.AddForeignKey(
                name: "FK_CoreComponents_CoreResources_ImageId",
                table: "CoreComponents",
                column: "ImageId",
                principalTable: "CoreResources",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
