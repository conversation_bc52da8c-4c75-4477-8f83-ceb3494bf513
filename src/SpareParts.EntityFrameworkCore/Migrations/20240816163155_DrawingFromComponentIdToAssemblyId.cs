using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SpareParts.Migrations
{
    /// <inheritdoc />
    public partial class DrawingFromComponentIdToAssemblyId : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CoreDrawings_CoreComponents_ComponentId",
                table: "CoreDrawings");

            migrationBuilder.AddForeignKey(
                name: "FK_CoreDrawings_CoreAssemblies_ComponentId",
                table: "CoreDrawings",
                column: "ComponentId",
                principalTable: "CoreAssemblies",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CoreDrawings_CoreAssemblies_ComponentId",
                table: "CoreDrawings");

            migrationBuilder.AddForeignKey(
                name: "FK_CoreDrawings_CoreComponents_ComponentId",
                table: "CoreDrawings",
                column: "ComponentId",
                principalTable: "CoreComponents",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
