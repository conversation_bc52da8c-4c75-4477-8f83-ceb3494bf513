using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SpareParts.Migrations
{
    /// <inheritdoc />
    public partial class Add_Default_Company_Flag : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsDefault",
                table: "AdmCompanies",
                type: "bit",
                nullable: false,
                defaultValue: false);

            string query = @"UPDATE AdmCompanies
SET IsDefault = 1
WHERE Id in (SELECT c.Id FROM AdmCompanies as c Inner join AbpTenants as t On c.TenantId=t.Id AND c.name=t.name)";
            migrationBuilder.Sql(query);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsEnabled",
                table: "AbpTenants",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }
    }
}
