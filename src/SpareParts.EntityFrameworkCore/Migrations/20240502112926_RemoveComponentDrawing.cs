using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SpareParts.Migrations
{
    /// <inheritdoc />
    public partial class RemoveComponentDrawing : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CoreComponentDrawingIndexes_CoreComponents_ComponentId",
                table: "CoreComponentDrawingIndexes");

            migrationBuilder.DropForeignKey(
                name: "FK_CoreComponentDrawingIndexes_CoreDrawings_DrawingId",
                table: "CoreComponentDrawingIndexes");

            migrationBuilder.DropTable(
                name: "CoreComponentDrawings");

            migrationBuilder.RenameColumn(
                name: "ComponentId",
                table: "CoreComponentDrawingIndexes",
                newName: "ChildComponentId");

            migrationBuilder.AddColumn<Guid>(
                name: "ComponentId",
                table: "CoreDrawings",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddColumn<int>(
                name: "Rank",
                table: "CoreDrawings",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateIndex(
                name: "IX_CoreDrawings_ComponentId",
                table: "CoreDrawings",
                column: "ComponentId");

            migrationBuilder.AddForeignKey(
                name: "FK_CoreComponentDrawingIndexes_CoreComponents_ChildComponentId",
                table: "CoreComponentDrawingIndexes",
                column: "ChildComponentId",
                principalTable: "CoreComponents",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_CoreComponentDrawingIndexes_CoreDrawings_DrawingId",
                table: "CoreComponentDrawingIndexes",
                column: "DrawingId",
                principalTable: "CoreDrawings",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CoreDrawings_CoreComponents_ComponentId",
                table: "CoreDrawings",
                column: "ComponentId",
                principalTable: "CoreComponents",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CoreComponentDrawingIndexes_CoreComponents_ChildComponentId",
                table: "CoreComponentDrawingIndexes");

            migrationBuilder.DropForeignKey(
                name: "FK_CoreComponentDrawingIndexes_CoreDrawings_DrawingId",
                table: "CoreComponentDrawingIndexes");

            migrationBuilder.DropForeignKey(
                name: "FK_CoreDrawings_CoreComponents_ComponentId",
                table: "CoreDrawings");

            migrationBuilder.DropIndex(
                name: "IX_CoreDrawings_ComponentId",
                table: "CoreDrawings");

            migrationBuilder.DropColumn(
                name: "ComponentId",
                table: "CoreDrawings");

            migrationBuilder.DropColumn(
                name: "Rank",
                table: "CoreDrawings");

            migrationBuilder.RenameColumn(
                name: "ChildComponentId",
                table: "CoreComponentDrawingIndexes",
                newName: "ComponentId");

            migrationBuilder.CreateTable(
                name: "CoreComponentDrawings",
                columns: table => new
                {
                    ComponentId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DrawingId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Rank = table.Column<int>(type: "int", nullable: false),
                    TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CoreComponentDrawings", x => new { x.ComponentId, x.DrawingId });
                    table.ForeignKey(
                        name: "FK_CoreComponentDrawings_CoreComponents_ComponentId",
                        column: x => x.ComponentId,
                        principalTable: "CoreComponents",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CoreComponentDrawings_CoreDrawings_DrawingId",
                        column: x => x.DrawingId,
                        principalTable: "CoreDrawings",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_CoreComponentDrawings_DrawingId",
                table: "CoreComponentDrawings",
                column: "DrawingId");

            migrationBuilder.AddForeignKey(
                name: "FK_CoreComponentDrawingIndexes_CoreComponents_ComponentId",
                table: "CoreComponentDrawingIndexes",
                column: "ComponentId",
                principalTable: "CoreComponents",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_CoreComponentDrawingIndexes_CoreDrawings_DrawingId",
                table: "CoreComponentDrawingIndexes",
                column: "DrawingId",
                principalTable: "CoreDrawings",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
