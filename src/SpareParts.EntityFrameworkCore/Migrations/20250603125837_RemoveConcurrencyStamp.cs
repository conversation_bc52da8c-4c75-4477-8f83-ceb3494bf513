using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SpareParts.Migrations
{
    /// <inheritdoc />
    public partial class RemoveConcurrencyStamp : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ConcurrencyStamp",
                table: "CoreProducts");

            migrationBuilder.DropColumn(
                name: "ConcurrencyStamp",
                table: "CoreProductFamilies");

            migrationBuilder.DropColumn(
                name: "ConcurrencyStamp",
                table: "CoreEquipments");

            migrationBuilder.DropColumn(
                name: "ConcurrencyStamp",
                table: "CoreDocuments");

            migrationBuilder.DropColumn(
                name: "ConcurrencyStamp",
                table: "CoreComponents");

            migrationBuilder.DropColumn(
                name: "ConcurrencyStamp",
                table: "AdmInvitations");

            migrationBuilder.DropColumn(
                name: "ConcurrencyStamp",
                table: "AdmCompanies");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ConcurrencyStamp",
                table: "CoreProducts",
                type: "nvarchar(40)",
                maxLength: 40,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ConcurrencyStamp",
                table: "CoreProductFamilies",
                type: "nvarchar(40)",
                maxLength: 40,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ConcurrencyStamp",
                table: "CoreEquipments",
                type: "nvarchar(40)",
                maxLength: 40,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ConcurrencyStamp",
                table: "CoreDocuments",
                type: "nvarchar(40)",
                maxLength: 40,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ConcurrencyStamp",
                table: "CoreComponents",
                type: "nvarchar(40)",
                maxLength: 40,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ConcurrencyStamp",
                table: "AdmInvitations",
                type: "nvarchar(40)",
                maxLength: 40,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ConcurrencyStamp",
                table: "AdmCompanies",
                type: "nvarchar(40)",
                maxLength: 40,
                nullable: false,
                defaultValue: "");
        }
    }
}
