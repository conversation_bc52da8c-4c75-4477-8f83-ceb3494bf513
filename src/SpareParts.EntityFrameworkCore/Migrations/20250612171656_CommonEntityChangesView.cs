using Microsoft.EntityFrameworkCore.Migrations;
using SpareParts.Common;

#nullable disable

namespace SpareParts.Migrations
{
    /// <inheritdoc />
    public partial class CommonEntityChangesView : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(SqlScriptProvider.GetSqlQuery(ScriptName.EntityChangesSqlView.ToString(), 1));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("DROP VIEW CommonEntityChanges;");
        }
    }
}
