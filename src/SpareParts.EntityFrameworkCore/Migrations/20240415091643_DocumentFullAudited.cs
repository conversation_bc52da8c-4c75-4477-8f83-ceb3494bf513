using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SpareParts.Migrations
{
    /// <inheritdoc />
    public partial class DocumentFullAudited : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ConcurrencyStamp",
                table: "CoreDocuments",
                type: "nvarchar(40)",
                maxLength: 40,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTime>(
                name: "CreationTime",
                table: "CoreDocuments",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<Guid>(
                name: "CreatorId",
                table: "CoreDocuments",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "CoreDocuments",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "CoreDocuments",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "CoreDocuments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<DateTime>(
                name: "LastModificationTime",
                table: "CoreDocuments",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "LastModifierId",
                table: "CoreDocuments",
                type: "uniqueidentifier",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ConcurrencyStamp",
                table: "CoreDocuments");

            migrationBuilder.DropColumn(
                name: "CreationTime",
                table: "CoreDocuments");

            migrationBuilder.DropColumn(
                name: "CreatorId",
                table: "CoreDocuments");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "CoreDocuments");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "CoreDocuments");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "CoreDocuments");

            migrationBuilder.DropColumn(
                name: "LastModificationTime",
                table: "CoreDocuments");

            migrationBuilder.DropColumn(
                name: "LastModifierId",
                table: "CoreDocuments");
        }
    }
}
