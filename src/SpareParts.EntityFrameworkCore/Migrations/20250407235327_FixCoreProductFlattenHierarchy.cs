using Microsoft.EntityFrameworkCore.Migrations;
using SpareParts.Core;
using static SpareParts.Common.SqlScriptProvider;

#nullable disable

namespace SpareParts.Migrations
{
    /// <inheritdoc />
    public partial class FixCoreProductFlattenHierarchy : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(GetSqlQuery(ScriptName.ProductFlattenHierarchySqlView.ToString(), 7));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(GetSqlQuery(ScriptName.ProductFlattenHierarchySqlView.ToString(), 6));
        }
    }
}