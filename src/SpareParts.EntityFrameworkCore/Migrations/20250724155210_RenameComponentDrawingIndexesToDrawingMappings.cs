using Microsoft.EntityFrameworkCore.Migrations;
using System;

#nullable disable

namespace SpareParts.Migrations
{
    /// <inheritdoc />
    public partial class RenameComponentDrawingIndexesToDrawingMappings : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_CoreComponentDrawingIndexes",
                table: "CoreComponentDrawingIndexes");

            migrationBuilder.RenameTable(name: "CoreComponentDrawingIndexes", newName: "CoreDrawingMappings");

            migrationBuilder.AddColumn<Guid>(
                name: "Id",
                table: "CoreDrawingMappings",
                type: "uniqueidentifier",
                nullable: false,
                defaultValueSql: "NEWID()");

            migrationBuilder.AddPrimaryKey(
                name: "PK_CoreDrawingMappings",
                table: "CoreDrawingMappings",
                column: "Id");

        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_CoreDrawingMappings",
                table: "CoreDrawingMappings");

            migrationBuilder.DropColumn(
                name: "Id",
                table: "CoreDrawingMappings");

            migrationBuilder.RenameTable(name: "CoreDrawingMappings", newName: "CoreComponentDrawingIndexes");

            migrationBuilder.AddPrimaryKey(
                name: "PK_CoreComponentDrawingIndexes",
                table: "CoreComponentDrawingIndexes",
                columns: ["DrawingId", "Index"]);
        }
    }
}
