using Microsoft.EntityFrameworkCore.Migrations;
using SpareParts.Common;
using SpareParts.Core;
using ScriptName = SpareParts.Core.ScriptName;


#nullable disable

namespace SpareParts.Migrations
{
    /// <inheritdoc />
    public partial class AddCodeToProductFlattenHierarchy : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(SqlScriptProvider.GetSqlQuery(ScriptName.ProductFlattenHierarchySqlView.ToString(), 3));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(SqlScriptProvider.GetSqlQuery(ScriptName.ProductFlattenHierarchySqlView.ToString(), 2));
        }
    }
}
