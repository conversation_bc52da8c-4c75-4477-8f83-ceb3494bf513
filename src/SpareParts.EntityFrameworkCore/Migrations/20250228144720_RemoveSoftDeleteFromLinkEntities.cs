using System;
using Microsoft.EntityFrameworkCore.Migrations;
using SpareParts.Core;
using static SpareParts.Common.SqlScriptProvider;

#nullable disable

namespace SpareParts.Migrations
{
    /// <inheritdoc />
    public partial class RemoveSoftDeleteFromLinkEntities : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("DELETE FROM CoreComponentDocuments WHERE IsDeleted = 1");
            migrationBuilder.Sql("DELETE FROM CoreProductInProductFamily WHERE IsDeleted = 1");
            migrationBuilder.Sql("DELETE FROM CoreComponentDrawingIndexes WHERE IsDeleted = 1");
            migrationBuilder.Sql(GetSqlQuery(ScriptName.ProductFamilyFlattenHierarchySqlView.ToString(), 4));
            migrationBuilder.DropIndex(
                name: "IX_CommonHelpRequests_Code_TenantId",
                table: "CommonHelpRequests");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "CoreProductInProductFamily");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "CoreProductInProductFamily");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "CoreProductInProductFamily");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "CoreComponentDrawingIndexes");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "CoreComponentDrawingIndexes");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "CoreComponentDrawingIndexes");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "CoreComponentDocuments");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "CoreComponentDocuments");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "CoreComponentDocuments");

            migrationBuilder.CreateIndex(
                name: "IX_CommonHelpRequests_Code_TenantId",
                table: "CommonHelpRequests",
                columns: new[] { "Code", "TenantId" },
                unique: true,
                filter: "[TenantId] IS NOT NULL");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_CommonHelpRequests_Code_TenantId",
                table: "CommonHelpRequests");

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "CoreProductInProductFamily",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "CoreProductInProductFamily",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "CoreProductInProductFamily",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "CoreComponentDrawingIndexes",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "CoreComponentDrawingIndexes",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "CoreComponentDrawingIndexes",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "CoreComponentDocuments",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "CoreComponentDocuments",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "CoreComponentDocuments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.CreateIndex(
                name: "IX_CommonHelpRequests_Code_TenantId",
                table: "CommonHelpRequests",
                columns: new[] { "Code", "TenantId" },
                unique: true);

            migrationBuilder.Sql(GetSqlQuery(ScriptName.ProductFamilyFlattenHierarchySqlView.ToString(), 3));
        }
    }
}
