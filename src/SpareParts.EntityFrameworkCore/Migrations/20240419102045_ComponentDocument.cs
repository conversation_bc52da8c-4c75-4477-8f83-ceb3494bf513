using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SpareParts.Migrations
{
    /// <inheritdoc />
    public partial class ComponentDocument : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CoreComponentDocuments_CoreComponents_ComponentId",
                table: "CoreComponentDocuments");

            migrationBuilder.DropForeignKey(
                name: "FK_CoreComponentDocuments_CoreDocuments_DocumentId",
                table: "CoreComponentDocuments");

            migrationBuilder.AddColumn<Guid>(
                name: "TenantId",
                table: "CoreComponentDocuments",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_CoreComponentDocuments_CoreComponents_ComponentId",
                table: "CoreComponentDocuments",
                column: "ComponentId",
                principalTable: "CoreComponents",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CoreComponentDocuments_CoreDocuments_DocumentId",
                table: "CoreComponentDocuments",
                column: "DocumentId",
                principalTable: "CoreDocuments",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CoreComponentDocuments_CoreComponents_ComponentId",
                table: "CoreComponentDocuments");

            migrationBuilder.DropForeignKey(
                name: "FK_CoreComponentDocuments_CoreDocuments_DocumentId",
                table: "CoreComponentDocuments");

            migrationBuilder.DropColumn(
                name: "TenantId",
                table: "CoreComponentDocuments");

            migrationBuilder.AddForeignKey(
                name: "FK_CoreComponentDocuments_CoreComponents_ComponentId",
                table: "CoreComponentDocuments",
                column: "ComponentId",
                principalTable: "CoreComponents",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_CoreComponentDocuments_CoreDocuments_DocumentId",
                table: "CoreComponentDocuments",
                column: "DocumentId",
                principalTable: "CoreDocuments",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
