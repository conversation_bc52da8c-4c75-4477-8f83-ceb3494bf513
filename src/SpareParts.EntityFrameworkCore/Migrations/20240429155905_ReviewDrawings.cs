using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SpareParts.Migrations
{
    /// <inheritdoc />
    public partial class ReviewDrawings : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CoreDrawingIndexes");

            migrationBuilder.CreateTable(
                name: "CoreComponentDrawingIndexes",
                columns: table => new
                {
                    ComponentId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DrawingId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Index = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CoreComponentDrawingIndexes", x => new { x.ComponentId, x.DrawingId, x.Index });
                    table.ForeignKey(
                        name: "FK_CoreComponentDrawingIndexes_CoreComponents_ComponentId",
                        column: x => x.ComponentId,
                        principalTable: "CoreComponents",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CoreComponentDrawingIndexes_CoreDrawings_DrawingId",
                        column: x => x.DrawingId,
                        principalTable: "CoreDrawings",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_CoreComponentDrawingIndexes_DrawingId",
                table: "CoreComponentDrawingIndexes",
                column: "DrawingId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CoreComponentDrawingIndexes");

            migrationBuilder.CreateTable(
                name: "CoreDrawingIndexes",
                columns: table => new
                {
                    ComponentId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DrawingId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Index = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CoreDrawingIndexes", x => new { x.ComponentId, x.DrawingId });
                    table.ForeignKey(
                        name: "FK_CoreDrawingIndexes_CoreComponents_ComponentId",
                        column: x => x.ComponentId,
                        principalTable: "CoreComponents",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CoreDrawingIndexes_CoreDrawings_DrawingId",
                        column: x => x.DrawingId,
                        principalTable: "CoreDrawings",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_CoreDrawingIndexes_DrawingId",
                table: "CoreDrawingIndexes",
                column: "DrawingId");
        }
    }
}
