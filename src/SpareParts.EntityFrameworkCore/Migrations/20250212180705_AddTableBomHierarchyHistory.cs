using System;
using Microsoft.EntityFrameworkCore.Migrations;
using SpareParts.Common;
using ScriptName = SpareParts.Core.ScriptName;

#nullable disable

namespace SpareParts.Migrations
{
    /// <inheritdoc />
    public partial class AddTableBomHierarchyHistory : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "CoreBomHierarchyHistory",
                columns: table => new
                {
                    DateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    TenantName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ProductId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ProductCode = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ProductIsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    MaxLevel = table.Column<int>(type: "int", nullable: false),
                    LinesNumber = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                });
            migrationBuilder.Sql(SqlScriptProvider.GetSqlQuery(ScriptName.InsertIntoBomHierarchyHistoryProcedure.ToString(), 1));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("DROP PROCEDURE InsertIntoBomHierarchyHistory;");
            migrationBuilder.DropTable(
                name: "CoreBomHierarchyHistory");
        }
    }
}
