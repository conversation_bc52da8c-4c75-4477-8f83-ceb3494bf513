using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SpareParts.Migrations
{
    /// <inheritdoc />
    public partial class FixUniqueIndexes : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_CoreComponentTranslations_Language",
                table: "CoreComponentTranslations");

            migrationBuilder.DropIndex(
                name: "IX_CoreComponents_Code_TenantId",
                table: "CoreComponents");

            migrationBuilder.DropIndex(
                name: "IX_AdmInvitations_Code",
                table: "AdmInvitations");

            migrationBuilder.DropIndex(
                name: "IX_AdmCompanies_Code_TenantId",
                table: "AdmCompanies");

            migrationBuilder.CreateIndex(
                name: "IX_CoreComponentTranslations_Language_ComponentId_TenantId_DeletionTime",
                table: "CoreComponentTranslations",
                columns: new[] { "Language", "ComponentId", "TenantId", "DeletionTime" },
                unique: true,
                filter: "[TenantId] IS NOT NULL AND [DeletionTime] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_CoreComponents_Code_TenantId_DeletionTime",
                table: "CoreComponents",
                columns: new[] { "Code", "TenantId", "DeletionTime" },
                unique: true,
                filter: "[TenantId] IS NOT NULL AND [DeletionTime] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_AdmInvitations_Code_TenantId_DeletionTime",
                table: "AdmInvitations",
                columns: new[] { "Code", "TenantId", "DeletionTime" },
                unique: true,
                filter: "[TenantId] IS NOT NULL AND [DeletionTime] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_AdmCompanies_Code_TenantId_DeletionTime",
                table: "AdmCompanies",
                columns: new[] { "Code", "TenantId", "DeletionTime" },
                unique: true,
                filter: "[TenantId] IS NOT NULL AND [DeletionTime] IS NOT NULL");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_CoreComponentTranslations_Language_ComponentId_TenantId_DeletionTime",
                table: "CoreComponentTranslations");

            migrationBuilder.DropIndex(
                name: "IX_CoreComponents_Code_TenantId_DeletionTime",
                table: "CoreComponents");

            migrationBuilder.DropIndex(
                name: "IX_AdmInvitations_Code_TenantId_DeletionTime",
                table: "AdmInvitations");

            migrationBuilder.DropIndex(
                name: "IX_AdmCompanies_Code_TenantId_DeletionTime",
                table: "AdmCompanies");

            migrationBuilder.CreateIndex(
                name: "IX_CoreComponentTranslations_Language",
                table: "CoreComponentTranslations",
                column: "Language");

            migrationBuilder.CreateIndex(
                name: "IX_CoreComponents_Code_TenantId",
                table: "CoreComponents",
                columns: new[] { "Code", "TenantId" },
                unique: true,
                filter: "[TenantId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_AdmInvitations_Code",
                table: "AdmInvitations",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AdmCompanies_Code_TenantId",
                table: "AdmCompanies",
                columns: new[] { "Code", "TenantId" },
                unique: true,
                filter: "[TenantId] IS NOT NULL");
        }
    }
}
