using Microsoft.EntityFrameworkCore.Migrations;
using System;
using SpareParts.Common;
using static SpareParts.Common.SqlScriptProvider;
using ScriptName = SpareParts.Core.ScriptName;

#nullable disable

namespace SpareParts.Migrations
{
    /// <inheritdoc />
    public partial class RemoveSoftDelete : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CoreProductFamilies_CoreResources_ImageId",
                table: "CoreProductFamilies");

            migrationBuilder.DropForeignKey(
                name: "FK_CoreProductFamilyTranslations_CoreProductFamilies_ProductFamilyId",
                table: "CoreProductFamilyTranslations");

            migrationBuilder.DropForeignKey(
                name: "FK_CoreProductInProductFamily_CoreProductFamilies_ProductFamilyId",
                table: "CoreProductInProductFamily");

            migrationBuilder.Sql(SqlScriptProvider.GetSqlQuery(ScriptName.BeforeDeleteFromProductFamiliesTrigger.ToString(), 1));

            migrationBuilder.Sql("DELETE FROM CoreProducts WHERE IsDeleted = 1");

            migrationBuilder.Sql("DELETE FROM CoreProductFamilyTranslations WHERE IsDeleted = 1");

            migrationBuilder.Sql("DELETE FROM CoreProductFamilies WHERE IsDeleted = 1");

            migrationBuilder.Sql("DELETE FROM CoreParts WHERE IsDeleted = 1");

            migrationBuilder.Sql("DELETE FROM CoreEquipments WHERE IsDeleted = 1");

            migrationBuilder.Sql("DELETE FROM CoreDrawings WHERE IsDeleted = 1");

            migrationBuilder.Sql("DELETE FROM CoreDocumentTranslations WHERE IsDeleted = 1");

            migrationBuilder.Sql("DELETE FROM CoreDocuments WHERE IsDeleted = 1");

            migrationBuilder.Sql("DELETE FROM CoreDocumentCategoryTranslations WHERE IsDeleted = 1");

            migrationBuilder.Sql("DELETE FROM CoreDocumentCategories WHERE IsDeleted = 1");

            migrationBuilder.Sql("DELETE FROM CoreComponentTranslations WHERE IsDeleted = 1");

            migrationBuilder.Sql("DELETE FROM CoreComponents WHERE IsDeleted = 1");

            migrationBuilder.Sql("DELETE FROM CoreBomLines WHERE IsDeleted = 1");

            migrationBuilder.Sql("DELETE FROM CoreBomHierarchyHistory WHERE ProductIsDeleted = 1");

            migrationBuilder.Sql("DELETE FROM CoreBomDocuments WHERE IsDeleted = 1");

            migrationBuilder.Sql("DELETE FROM CoreAssemblies WHERE IsDeleted = 1");

            migrationBuilder.Sql("DELETE FROM CommonPublicResources WHERE IsDeleted = 1");

            migrationBuilder.Sql("DELETE FROM CoreResources WHERE IsDeleted = 1");

            migrationBuilder.Sql("DELETE FROM AdmInvitations WHERE IsDeleted = 1");

            migrationBuilder.Sql("DELETE FROM AdmCompanies WHERE IsDeleted = 1");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "CoreResources");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "CoreResources");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "CoreResources");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "CoreProducts");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "CoreProducts");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "CoreProducts");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "CoreProductFamilyTranslations");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "CoreProductFamilyTranslations");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "CoreProductFamilyTranslations");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "CoreProductFamilies");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "CoreProductFamilies");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "CoreProductFamilies");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "CoreParts");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "CoreParts");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "CoreParts");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "CoreEquipments");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "CoreEquipments");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "CoreEquipments");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "CoreDrawings");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "CoreDrawings");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "CoreDrawings");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "CoreDocumentTranslations");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "CoreDocumentTranslations");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "CoreDocumentTranslations");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "CoreDocuments");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "CoreDocuments");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "CoreDocuments");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "CoreDocumentCategoryTranslations");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "CoreDocumentCategoryTranslations");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "CoreDocumentCategoryTranslations");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "CoreDocumentCategories");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "CoreDocumentCategories");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "CoreDocumentCategories");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "CoreComponentTranslations");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "CoreComponentTranslations");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "CoreComponentTranslations");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "CoreComponents");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "CoreComponents");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "CoreComponents");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "CoreBomLines");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "CoreBomLines");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "CoreBomLines");

            migrationBuilder.DropColumn(
                name: "ProductIsDeleted",
                table: "CoreBomHierarchyHistory");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "CoreBomDocuments");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "CoreBomDocuments");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "CoreBomDocuments");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "CoreAssemblies");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "CoreAssemblies");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "CoreAssemblies");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "CommonPublicResources");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "CommonPublicResources");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "CommonPublicResources");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "AdmInvitations");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "AdmInvitations");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "AdmInvitations");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "AdmCompanies");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "AdmCompanies");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "AdmCompanies");

            migrationBuilder.AddForeignKey(
                name: "FK_CoreProductFamilies_CoreResources_ImageId",
                table: "CoreProductFamilies",
                column: "ImageId",
                principalTable: "CoreResources",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CoreProductFamilyTranslations_CoreProductFamilies_ProductFamilyId",
                table: "CoreProductFamilyTranslations",
                column: "ProductFamilyId",
                principalTable: "CoreProductFamilies",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CoreProductInProductFamily_CoreProductFamilies_ProductFamilyId",
                table: "CoreProductInProductFamily",
                column: "ProductFamilyId",
                principalTable: "CoreProductFamilies",
                principalColumn: "Id");

            migrationBuilder.Sql(SqlScriptProvider.GetSqlQuery(ScriptName.BomPathsSqlView.ToString(), 4));

            migrationBuilder.Sql(SqlScriptProvider.GetSqlQuery(ScriptName.ProductFamilyFlattenHierarchySqlView.ToString(), 5));

            migrationBuilder.Sql(SqlScriptProvider.GetSqlQuery(ScriptName.ProductFlattenHierarchySqlView.ToString(), 9));

            migrationBuilder.Sql("DROP PROCEDURE InsertIntoBomHierarchyHistory;");
            migrationBuilder.Sql(GetSqlQuery(ScriptName.InsertIntoBomHierarchyHistoryProcedure.ToString(), 4));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CoreProductFamilies_CoreResources_ImageId",
                table: "CoreProductFamilies");

            migrationBuilder.DropForeignKey(
                name: "FK_CoreProductFamilyTranslations_CoreProductFamilies_ProductFamilyId",
                table: "CoreProductFamilyTranslations");

            migrationBuilder.DropForeignKey(
                name: "FK_CoreProductInProductFamily_CoreProductFamilies_ProductFamilyId",
                table: "CoreProductInProductFamily");

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "CoreResources",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "CoreResources",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "CoreResources",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "CoreProducts",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "CoreProducts",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "CoreProducts",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "CoreProductFamilyTranslations",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "CoreProductFamilyTranslations",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "CoreProductFamilyTranslations",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "CoreProductFamilies",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "CoreProductFamilies",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "CoreProductFamilies",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "CoreParts",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "CoreParts",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "CoreParts",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "CoreEquipments",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "CoreEquipments",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "CoreEquipments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "CoreDrawings",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "CoreDrawings",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "CoreDrawings",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "CoreDocumentTranslations",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "CoreDocumentTranslations",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "CoreDocumentTranslations",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "CoreDocuments",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "CoreDocuments",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "CoreDocuments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "CoreDocumentCategoryTranslations",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "CoreDocumentCategoryTranslations",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "CoreDocumentCategoryTranslations",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "CoreDocumentCategories",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "CoreDocumentCategories",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "CoreDocumentCategories",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "CoreComponentTranslations",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "CoreComponentTranslations",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "CoreComponentTranslations",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "CoreComponents",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "CoreComponents",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "CoreComponents",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "CoreBomLines",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "CoreBomLines",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "CoreBomLines",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "ProductIsDeleted",
                table: "CoreBomHierarchyHistory",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "CoreBomDocuments",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "CoreBomDocuments",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "CoreBomDocuments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "CoreAssemblies",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "CoreAssemblies",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "CoreAssemblies",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "CommonPublicResources",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "CommonPublicResources",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "CommonPublicResources",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "AdmInvitations",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "AdmInvitations",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "AdmInvitations",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "AdmCompanies",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "AdmCompanies",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "AdmCompanies",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddForeignKey(
                name: "FK_CoreProductFamilies_CoreResources_ImageId",
                table: "CoreProductFamilies",
                column: "ImageId",
                principalTable: "CoreResources",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_CoreProductFamilyTranslations_CoreProductFamilies_ProductFamilyId",
                table: "CoreProductFamilyTranslations",
                column: "ProductFamilyId",
                principalTable: "CoreProductFamilies",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_CoreProductInProductFamily_CoreProductFamilies_ProductFamilyId",
                table: "CoreProductInProductFamily",
                column: "ProductFamilyId",
                principalTable: "CoreProductFamilies",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
