using Microsoft.AspNetCore.DataProtection.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using SpareParts.Administration.EntityFrameworkCore;
using SpareParts.Core.EntityFrameworkCore;
using Volo.Abp.AuditLogging.EntityFrameworkCore;
using Volo.Abp.BackgroundJobs.EntityFrameworkCore;
using Volo.Abp.Data;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.FeatureManagement.EntityFrameworkCore;
using Volo.Abp.PermissionManagement.EntityFrameworkCore;
using Volo.Abp.SettingManagement.EntityFrameworkCore;

namespace SpareParts.EntityFrameworkCore;

[ConnectionStringName("Default")]
public class SparePartsDbContext(DbContextOptions<SparePartsDbContext> options) :
    AbpDbContext<SparePartsDbContext>(options),
    IDataProtectionKeyContext
{
    /* Add DbSet properties for your Aggregate Roots / Entities here. */

    public DbSet<DataProtectionKey> DataProtectionKeys { get; set; } = null!;


    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.ConfigurePermissionManagement();
        modelBuilder.ConfigureSettingManagement();
        modelBuilder.ConfigureBackgroundJobs();
        modelBuilder.ConfigureAuditLogging();
        modelBuilder.ConfigureFeatureManagement();
        modelBuilder.ConfigureCore();
        modelBuilder.ConfigureAdministration();
    }
}