<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>SpareParts</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\SpareParts.Application.Contracts\SpareParts.Application.Contracts.csproj" />
  </ItemGroup>

  <ItemGroup>
	  <ProjectReference Include="..\..\modules\SpareParts.Core\src\SpareParts.Core.HttpApi\SpareParts.Core.HttpApi.csproj" />
	  <ProjectReference Include="..\..\modules\SpareParts.Administration\src\SpareParts.Administration.HttpApi\SpareParts.Administration.HttpApi.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Update="Fody">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
    </PackageReference>
  </ItemGroup>

</Project>
