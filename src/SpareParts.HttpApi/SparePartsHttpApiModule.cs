using Localization.Resources.AbpUi;
using SpareParts.Administration;
using SpareParts.Core;
using SpareParts.Localization;
using Volo.Abp.Localization;
using Volo.Abp.Modularity;

namespace SpareParts;

[DependsOn(
    typeof(SparePartsApplicationContractsModule),
    typeof(CoreHttpApiModule),
    typeof(AdministrationHttpApiModule)
        )]
public class SparePartsHttpApiModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        ConfigureLocalization();
    }

    private void ConfigureLocalization()
    {
        Configure<AbpLocalizationOptions>(options =>
        {
            options.Resources
                .Get<SparePartsResource>()
                .AddBaseTypes(
                    typeof(AbpUiResource)
                );
        });
    }
}