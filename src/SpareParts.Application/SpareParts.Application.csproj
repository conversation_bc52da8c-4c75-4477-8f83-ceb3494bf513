<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>SpareParts</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\modules\SpareParts.Core\src\SpareParts.Core.Application\SpareParts.Core.Application.csproj" />
    <ProjectReference Include="..\..\modules\SpareParts.Administration\src\SpareParts.Administration.Application\SpareParts.Administration.Application.csproj" />
    <ProjectReference Include="..\SpareParts.Domain\SpareParts.Domain.csproj" />
    <ProjectReference Include="..\SpareParts.Application.Contracts\SpareParts.Application.Contracts.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Update="Fody">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
    </PackageReference>
  </ItemGroup>

</Project>
