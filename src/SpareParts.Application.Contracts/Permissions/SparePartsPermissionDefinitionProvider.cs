using SpareParts.Localization;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Localization;

namespace SpareParts.Permissions;

public class SparePartsPermissionDefinitionProvider : PermissionDefinitionProvider
{
    public override void Define(IPermissionDefinitionContext context)
    {
        context.AddGroup(SparePartsPermissions.GroupName);
        //Define your own permissions here. Example:
        //myGroup.AddPermission(SparePartsPermissions.MyPermission1, L("Permission:MyPermission1"));
    }

    private static LocalizableString L(string name)
    {
        return LocalizableString.Create<SparePartsResource>(name);
    }
}
