<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>SpareParts</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\SpareParts.Domain.Shared\SpareParts.Domain.Shared.csproj" />
    <ProjectReference Include="..\..\modules\SpareParts.Core\src\SpareParts.Core.Application.Contracts\SpareParts.Core.Application.Contracts.csproj" />
    <ProjectReference Include="..\..\modules\SpareParts.Administration\src\SpareParts.Administration.Application.Contracts\SpareParts.Administration.Application.Contracts.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Update="Fody">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
    </PackageReference>
  </ItemGroup>

</Project>
