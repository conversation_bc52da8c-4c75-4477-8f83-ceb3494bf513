using SpareParts.Administration;
using SpareParts.Core;
using Volo.Abp.Modularity;
using Volo.Abp.ObjectExtending;

namespace SpareParts;

[DependsOn(
    typeof(SparePartsDomainSharedModule),
    typeof(AbpObjectExtendingModule),
    typeof(CoreApplicationContractsModule),
    typeof(AdministrationApplicationContractsModule)

        )]
public class SparePartsApplicationContractsModule : AbpModule
{
    public override void PreConfigureServices(ServiceConfigurationContext context)
    {
        SparePartsDtoExtensions.Configure();
    }
}
