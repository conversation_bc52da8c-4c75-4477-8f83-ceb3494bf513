<Project>
	<PropertyGroup>
		<LangVersion>latest</LangVersion>
		<Version>0.1.0</Version>
		<NoWarn>$(NoWarn);CS1591</NoWarn>
		<AbpProjectType>module</AbpProjectType>
		<RunSettingsFilePath>$(MSBuildThisFileDirectory)\CodeCoverage.runsettings</RunSettingsFilePath>
		<IsTestProject Condition="$(MSBuildProjectFullPath.Contains('test')) and ($(MSBuildProjectName.EndsWith('.Tests')) or $(MSBuildProjectName.EndsWith('.TestBase')))">true</IsTestProject>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="ConfigureAwait.Fody" PrivateAssets="All" />
		<PackageReference Include="Fody">
			<PrivateAssets>All</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
		</PackageReference>
		<PackageReference Condition="'$(IsTestProject)' == 'true'" Include="coverlet.collector">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
		</PackageReference>
	</ItemGroup>


	<Target Name="NoWarnOnRazorViewImportedTypeConflicts" BeforeTargets="RazorCoreCompile">
		<PropertyGroup>
			<NoWarn>$(NoWarn);0436</NoWarn>
		</PropertyGroup>
	</Target>

</Project>