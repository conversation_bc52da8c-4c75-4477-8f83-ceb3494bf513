using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.Documents.Dtos;
using SpareParts.Core.Documents.Queries;
using SpareParts.Core.Permissions;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Core.Stats;

public partial class StatsController
{
    [HttpGet]
    [Route("component-documents")]
    [Authorize(CorePermissions.Components.Stats)]
    [SwaggerOperation(Summary = "Get stats for component documents",
        Description = "Get statistics for component documents",
        OperationId = "Get_Component_Documents_Stats",
        Tags = ["Stats"])]
    public async Task<ComponentDocumentsStatsDto> GetStatsForComponentDocuments()
    {
        _currentCompany.ThrowIfExternal();
        return await QuerySender.Send(new GetComponentDocumentsStatsQuery());
    }
}