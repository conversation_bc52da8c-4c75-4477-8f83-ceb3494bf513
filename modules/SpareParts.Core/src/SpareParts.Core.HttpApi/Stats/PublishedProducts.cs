using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.Components.Dtos.Products;
using SpareParts.Core.Components.Queries.Products;
using SpareParts.Core.Permissions;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Core.Stats;
public partial class StatsController
{
    [HttpGet]
    [Route("published-products")]
    [Authorize(CorePermissions.Products.Stats)]
    [SwaggerOperation(Summary = "Get stats for published products",
        Description = "Get statistics for published products",
        OperationId = "Get_Published_Products_Stats",
        Tags = ["Stats"])]
    public async Task<PublishedProductsStatsDto> GetStatsForPublishedProducts()
    {
        _currentCompany.ThrowIfExternal();
        return await QuerySender.Send(new GetPublishedProductsStatsQuery());
    }
}
