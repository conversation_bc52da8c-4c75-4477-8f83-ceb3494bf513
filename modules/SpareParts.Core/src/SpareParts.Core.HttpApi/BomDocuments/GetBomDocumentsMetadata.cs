using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.BomDocuments.Dtos;
using SpareParts.Core.BomDocuments.Queries;
using SpareParts.Core.BomDocuments.QueryFilters;
using Swashbuckle.AspNetCore.Annotations;
using Volo.Abp.Application.Dtos;

namespace SpareParts.Core.BomDocuments;
public partial class BomDocumentsController
{
    [HttpGet]
    [SwaggerOperation(Summary = "Get metadata from BOM documents",
        Description = "Get metadata from BOM documents",
        OperationId = "Get_Bom_Documents_Metadata",
        Tags = ["BomDocuments"])]
    public async Task<PagedResultDto<BomDocumentDto>> GetBomDocumentsMetadata([FromQuery] BomDocumentPaginationFilter filter)
    {
        return await QuerySender.Send(new GetBomDocumentsMetadataQuery(filter));
    }
}
