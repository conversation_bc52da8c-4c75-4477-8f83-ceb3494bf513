using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.BomDocuments.Commands;
using SpareParts.Core.BomDocuments.Dtos;
using SpareParts.Core.BomDocuments.Dtos.Inputs;
using SpareParts.Core.Permissions;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Core.BomDocuments;
public partial class BomDocumentsController
{
    [HttpPost]
    [Authorize(CorePermissions.BomDocuments.Create)]
    [SwaggerOperation(Summary = "Create a new bom document",
        Description = "Create a new bom document",
        OperationId = "Create_BomDocument",
        Tags = ["BomDocuments"])]
    [ProducesResponseType(typeof(BomDocumentDto), StatusCodes.Status202Accepted)]
    public async Task<CreatedResult> Create([FromBody] CreateBomDocumentDto createBomDocumentDto)
    {
        CreateBomDocumentCommand createBomDocumentCommand = ObjectMapper.Map<CreateBomDocumentDto, CreateBomDocumentCommand>(createBomDocumentDto);
        BomDocumentDto bomDocumentDto = await CommandSender.Send(createBomDocumentCommand);
        return Created(string.Empty, bomDocumentDto);
    }
}