using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.Drawings.Commands;
using SpareParts.Core.Drawings.Dtos;
using SpareParts.Core.Drawings.Dtos.Inputs;
using SpareParts.Core.Permissions;
using Swashbuckle.AspNetCore.Annotations;
using System;
using System.Threading.Tasks;

namespace SpareParts.Core.Drawings;

public partial class DrawingsController
{
    [HttpPatch]
    [Authorize(CorePermissions.Drawings.Edit)]
    [Route("{id:guid}/visibility")]
    [SwaggerOperation(Summary = "Change drawing visibility",
        Description = "Change the the visibility for an existing drawing identified by its unique identifier",
        OperationId = "Change_DrawingVisibility",
        Tags = ["Drawings"])]
    public async Task<DrawingDto> ChangeDrawingVisibility(Guid id, [FromBody] ChangeDrawingVisibilityDto changeDrawingVisibilityDto)
    {
        return await CommandSender.Send(new ChangeDrawingVisibilityCommand(id, changeDrawingVisibilityDto.IsVisible));
    }
}