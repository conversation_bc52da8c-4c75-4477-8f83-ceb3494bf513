using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.Drawings.Commands;
using SpareParts.Core.Drawings.Dtos;
using SpareParts.Core.Drawings.Dtos.Inputs;
using SpareParts.Core.Enums;
using SpareParts.Core.Permissions;
using Swashbuckle.AspNetCore.Annotations;
using System;
using System.Threading.Tasks;

namespace SpareParts.Core.Drawings;

public partial class DrawingsController
{
    [HttpPut]
    [Authorize(CorePermissions.Drawings.Edit)]
    [Route("{id:guid}/mapping/{mappingId:guid}")]
    [SwaggerOperation(Summary = "Update the mapping information for a given drawing",
        Description = "Update the mapping component or index for an existing drawing identified by its unique identifier",
        OperationId = "Update_DrawingMapping",
        Tags = ["Drawings"])]
    public async Task<DrawingMappingDto> UpdateDrawingMapping(Guid id, Guid mappingId, [FromBody] CreateUpdateDrawingMappingDto createUpdateDrawingMappingDto)
    {
        return await CommandSender.Send(new UpdateDrawingMappingCommand(id, mappingId, CallerOrigin.Api, createUpdateDrawingMappingDto));
    }
}