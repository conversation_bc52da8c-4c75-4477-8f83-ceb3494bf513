using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.Documents.Commands;
using SpareParts.Core.Documents.Dtos;
using SpareParts.Core.Documents.Dtos.Inputs;
using SpareParts.Core.Permissions;
using Swashbuckle.AspNetCore.Annotations;
using Volo.Abp;

namespace SpareParts.Core.Documents;

public partial class DocumentsController
{
    [HttpPost]
    [Authorize(CorePermissions.Documents.Create)]
    [SwaggerOperation(Summary = "Create a new document",
        Description = "Create a new document",
        OperationId = "Create_Document",
        Tags = ["Documents"])]
    [ProducesResponseType(typeof(DocumentDto), StatusCodes.Status201Created)]
    public async Task<CreatedResult> CreateDocument([FromBody] CreateDocumentDto createDocumentDto)
    {
        switch (createDocumentDto.Resource.Type)
        {
            case Enums.DocumentType.Resource:
                if (!Guid.TryParse(createDocumentDto.Resource.Value, out Guid idResource))
                {
                    throw new BusinessException("The resource id is incorrect");
                }

                CreateFileDocumentCommand createFileDocumentCommand = new(
                    new DocumentResourceDto<Guid>
                    {
                        Type = createDocumentDto.Resource.Type,
                        Value = idResource
                    },
                    createDocumentDto.Translations, createDocumentDto.Languages ?? [], createDocumentDto.IsPublic ?? true);
                DocumentDto fileDocumentDto = await CommandSender.Send(createFileDocumentCommand);
                return Created(string.Empty, fileDocumentDto);

            case Enums.DocumentType.Link:
                CreateLinkDocumentCommand createLinkDocumentCommand = new(
                    new DocumentResourceDto<string>
                    {
                        Type = createDocumentDto.Resource.Type,
                        Value = createDocumentDto.Resource.Value
                    },
                    createDocumentDto.Translations, createDocumentDto.Languages ?? [], createDocumentDto.IsPublic ?? true);
                DocumentDto linkDocumentDto = await CommandSender.Send(createLinkDocumentCommand);
                return Created(string.Empty, linkDocumentDto);
            default:
                throw new BusinessException("The resource type is incorrect");
        }
    }
}