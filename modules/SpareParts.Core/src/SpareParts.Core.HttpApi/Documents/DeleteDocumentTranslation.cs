using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.Documents.Commands;
using SpareParts.Core.Permissions;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Core.Documents;

public partial class DocumentsController
{
    [HttpDelete]
    [Authorize(CorePermissions.Documents.DeleteTranslation)]
    [Route("{id:guid}/translations/{language}")]
    [SwaggerOperation(Summary = "Delete a component document translation",
        Description = "Delete an existing component document translation identified by its unique code language",
        OperationId = "Delete_Component_Document_Translation",
        Tags = ["Documents"])]
    public async Task DeleteDocumentTranslation(Guid id, string language)
    {
        await CommandSender.Send(new DeleteDocumentTranslationCommand(id, language));
    }
}