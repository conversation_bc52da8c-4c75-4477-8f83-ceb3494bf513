using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.Documents.Commands;
using SpareParts.Core.Permissions;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Core.Documents;

public partial class DocumentsController
{
    [HttpPost]
    [Authorize(CorePermissions.Documents.Edit)]
    [Route("{id:guid}/categories/{categoryId:guid}")]
    [SwaggerOperation(Summary = "Attach category to document",
        Description = "Attach an existing category to a document, both identified by their unique identifiers",
        OperationId = "Attach_Category",
        Tags = ["Documents"])]
    public async Task AttachCategory(Guid id, Guid categoryId)
    {
        await CommandSender.Send(new AttachCategoryToDocumentCommand(id, categoryId));
    }
}