using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.Components.QueryFilters;
using SpareParts.Core.Documents.Dtos;
using SpareParts.Core.Documents.Queries;
using Swashbuckle.AspNetCore.Annotations;
using Volo.Abp.Application.Dtos;

namespace SpareParts.Core.Documents;

public partial class DocumentsController
{
    [HttpGet]
    [SwaggerOperation(Summary = "Get documents",
        Description = "Retrieves the documents.",
        OperationId = "Get_Documents",
        Tags = ["Documents"])]
    public async Task<PagedResultDto<DocumentDto>> GetAll([FromQuery] DocumentPaginationFilter filter)
    {
        return await QuerySender.Send(new GetDocumentsQuery(filter));
    }
}