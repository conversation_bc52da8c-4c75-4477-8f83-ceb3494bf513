using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.Permissions;
using Volo.Abp.BackgroundJobs;

namespace SpareParts.Core.DemoData;

public partial class DemoDataController
{
    private IBackgroundJobManager BackgroundJobManager => LazyServiceProvider.LazyGetRequiredService<IBackgroundJobManager>();

    [HttpPost]
    [Route("import")]
    [Authorize(CorePermissions.DemoData.Import)]
    public async Task<string> Import()
    {
        return await BackgroundJobManager.EnqueueAsync(new ImportCoreDemoDataJobArgs(CurrentTenant.Id!.Value,
            CurrentTenant.Name!), BackgroundJobPriority.High);
    }
}