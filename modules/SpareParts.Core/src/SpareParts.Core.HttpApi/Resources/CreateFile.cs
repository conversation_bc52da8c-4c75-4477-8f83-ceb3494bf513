using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Common;
using SpareParts.Core.Permissions;
using SpareParts.Core.Resources.Commands;
using Swashbuckle.AspNetCore.Annotations;
using Volo.Abp.Content;

namespace SpareParts.Core.Resources;
public partial class ResourcesController
{
    [HttpPost]
    [Authorize(CorePermissions.Resources.Create)]
    [Route("files")]
    [SwaggerOperation(Summary = "Upload a new file",
        Description = @"This API endpoint allows users to upload a new file.
<br/>The request should be of type multipart/form-data, and the form data should include a field named 'file' containing the file to be uploaded.",
        OperationId = "Create_File_Resource",
        Tags = ["Resources"])]
    [ProducesResponseType(typeof(Guid), StatusCodes.Status201Created)]
    [RequestSizeLimit(CommonConsts.SizeLimits.FileSizeLimitInBytes + CommonConsts.SizeLimits.OverheadInBytes)]
    public async Task<CreatedResult> CreateFile([FromForm] FileInputDto fileInputDto)
    {
        await using Stream content = fileInputDto.File.OpenReadStream();
        CreateFileResourceCommand createFileResourceCommand = new(new RemoteStreamContent(content, fileInputDto.File.FileName));
        Guid fileResourceId = await CommandSender.Send(createFileResourceCommand);
        return Created(string.Empty, fileResourceId);
    }
}