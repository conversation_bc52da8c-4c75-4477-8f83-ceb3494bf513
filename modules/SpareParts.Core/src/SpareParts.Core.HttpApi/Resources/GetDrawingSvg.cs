using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.Resources.Queries;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Core.Resources;

public partial class ResourcesController
{
    [HttpGet]
    [HttpHead]
    [Route("drawing-files/{id:guid}/svg")]
    [SwaggerOperation(Summary = "Retrieves a drawing svg by its unique identifier.",
        Description = "Return a blob object (stream) representing the drawing svg.",
        OperationId = "Get_Drawing_Svg",
        Tags = ["Resources"])]
    [SwaggerResponse(StatusCodes.Status200OK, "Return a blob object representing the drawing svg.", typeof(FileStreamResult), "image/svg+xml")]
    public async Task<FileStreamResult> GetDrawingSvg(Guid id)
    {
        GetDrawingSvgQuery getDrawingSvgQuery = new(id);
        return CreateFileStreamResult(await QuerySender.Send(getDrawingSvgQuery));
    }
}