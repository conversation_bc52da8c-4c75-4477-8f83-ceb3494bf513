using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.Resources.Queries;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Core.Resources;

public partial class ResourcesController
{
    [HttpGet]
    [HttpHead]
    [Route("files/{id:guid}")]
    [SwaggerOperation(Summary = "Retrieves a file by its unique identifier.",
        Description = "Return a blob object (stream) representing the file.",
        OperationId = "Get_File_Resource",
        Tags = ["Resources"])]
    [SwaggerResponse(StatusCodes.Status200OK, "Return a blob object representing the file.", typeof(FileStreamResult), "application/octet-stream")]
    public async Task<FileStreamResult> GetFile(Guid id)
    {
        GetFileResourceQuery getFileResourceQuery = new(id);
        return CreateFileStreamResult(await QuerySender.Send(getFileResourceQuery));
    }
}