using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.Equipments.Dtos;
using SpareParts.Core.Equipments.Queries;
using SpareParts.Core.Equipments.QueryFilters;
using Swashbuckle.AspNetCore.Annotations;
using Volo.Abp.Application.Dtos;

namespace SpareParts.Core.Equipments;
public partial class EquipmentsController
{
    [HttpGet]
    [SwaggerOperation(Summary = "Retrieves a paged list of equipments based on the specified filter criteria.",
        Description = "Use this endpoint to get a paginated list of equipments based on the provided filter.",
        OperationId = "Get_Equipments",
        Tags = ["Equipments"])]
    public async Task<PagedResultDto<EquipmentDto>> GetEquipments([FromQuery] EquipmentPaginationFilter filter)
    {
        return await QuerySender.Send(new GetEquipmentsQuery(filter));
    }
}
