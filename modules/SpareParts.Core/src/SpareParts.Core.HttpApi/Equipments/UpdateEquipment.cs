using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.Equipments.Commands;
using SpareParts.Core.Equipments.Dtos;
using SpareParts.Core.Equipments.Dtos.Inputs;
using SpareParts.Core.Permissions;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Core.Equipments;
public partial class EquipmentsController
{
    [HttpPut]
    [Authorize(CorePermissions.Equipments.Edit)]
    [Route("{id:guid}")]
    [SwaggerOperation(Summary = "Update an equipment",
        Description = "Update an existing equipment identified by its unique identifier",
        OperationId = "Update_Equipment",
        Tags = ["Equipments"])]
    public async Task<EquipmentDto> UpdateEquipment(Guid id, [FromBody] UpdateEquipmentDto updateEquipmentDto)
    {
        UpdateEquipmentCommand updateEquipmentCommand = ObjectMapper.Map<UpdateEquipmentDto, UpdateEquipmentCommand>(updateEquipmentDto);
        updateEquipmentCommand.Id = id;
        return await CommandSender.Send(updateEquipmentCommand);
    }
}
