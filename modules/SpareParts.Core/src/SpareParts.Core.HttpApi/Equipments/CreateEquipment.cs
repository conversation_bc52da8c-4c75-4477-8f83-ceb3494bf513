using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.Equipments.Commands;
using SpareParts.Core.Equipments.Dtos;
using SpareParts.Core.Equipments.Dtos.Inputs;
using SpareParts.Core.Permissions;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Core.Equipments;
public partial class EquipmentsController
{
    [HttpPost]
    [Authorize(CorePermissions.Equipments.Create)]
    [SwaggerOperation(Summary = "Create a new equipment",
        Description = "Create a new equipment for a product",
        OperationId = "Create_Equipment",
        Tags = ["Equipments"])]
    [ProducesResponseType(typeof(EquipmentDto), StatusCodes.Status201Created)]
    public async Task<CreatedResult> CreateEquipment([FromBody] CreateEquipmentDto createEquipmentDto)
    {
        CreateEquipmentCommand createEquipmentCommand = ObjectMapper.Map<CreateEquipmentDto, CreateEquipmentCommand>(createEquipmentDto);
        EquipmentDto equipmentDto = await CommandSender.Send(createEquipmentCommand);
        return Created(string.Empty, equipmentDto);
    }
}