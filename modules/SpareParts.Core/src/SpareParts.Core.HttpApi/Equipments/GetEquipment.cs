using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.Equipments.Dtos;
using SpareParts.Core.Equipments.Queries;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Core.Equipments;
public partial class EquipmentsController
{
    [HttpGet]
    [Route("{id:guid}")]
    [SwaggerOperation(Summary = "Retrieve an equipment",
        Description = "Retrieve detailed information about a specific equipment identified by its unique identifier",
        OperationId = "Get_Equipment_By_Id",
        Tags = ["Equipments"])]
    public async Task<EquipmentDto> GetEquipmentById(Guid id)
    {
        return await QuerySender.Send(new GetEquipmentByIdQuery(id));
    }
}
