using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.DocumentCategories.Dtos;
using SpareParts.Core.DocumentCategories.Queries;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Core.DocumentCategories;
public partial class DocumentCategoriesController
{
    [HttpGet]
    [Route("{id:guid}")]
    [SwaggerOperation(Summary = "Retrieve a document category",
        Description = "Retrieve detailed information about a specific document category identified by its unique identifier",
        OperationId = "Get_DocumentCategory_By_Id",
        Tags = ["DocumentCategories"])]
    public async Task<DocumentCategoryDto> GetDocumentCategoryById(Guid id)
    {
        return await QuerySender.Send(new GetDocumentCategoryByIdQuery(id));
    }
}
