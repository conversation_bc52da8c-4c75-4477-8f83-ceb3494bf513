using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.DocumentCategories.Commands;
using SpareParts.Core.DocumentCategories.Dtos;
using SpareParts.Core.DocumentCategories.Dtos.Inputs;
using SpareParts.Core.Permissions;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Core.DocumentCategories;
public partial class DocumentCategoriesController
{
    [HttpPut]
    [Authorize(CorePermissions.DocumentCategories.Edit)]
    [Route("{id:guid}")]
    [SwaggerOperation(Summary = "Update a document category",
        Description = "Update an existing document category identified by its unique identifier",
        OperationId = "Update_DocumentCategory",
        Tags = ["DocumentCategories"])]
    public async Task<DocumentCategoryDto> Update(Guid id, [FromBody] UpdateDocumentCategoryDto updateDocumentCategoryDto)
    {
        UpdateDocumentCategoryCommand updateDocumentCategoryCommand = new(id, updateDocumentCategoryDto.Color, updateDocumentCategoryDto.Translations);
        return await CommandSender.Send(updateDocumentCategoryCommand);
    }
}