using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.DocumentCategories.Dtos;
using SpareParts.Core.DocumentCategories.Queries;
using SpareParts.Core.DocumentCategories.QueryFilters;
using Swashbuckle.AspNetCore.Annotations;
using Volo.Abp.Application.Dtos;

namespace SpareParts.Core.DocumentCategories;
public partial class DocumentCategoriesController
{
    [HttpGet]
    [SwaggerOperation(Summary = "Get document categories",
        Description = "Get document categories",
        OperationId = "Get_DocumentCategories",
        Tags = ["DocumentCategories"])]
    public async Task<PagedResultDto<DocumentCategoryDto>> GetAll([FromQuery] DocumentCategoryPaginationFilter filter)
    {
        return await QuerySender.Send(new GetDocumentCategoriesQuery(filter));
    }
}
