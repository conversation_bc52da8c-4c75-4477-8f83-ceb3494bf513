using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.ProductFamilies.Dtos;
using SpareParts.Core.ProductFamilies.Queries;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Core.ProductFamilies;
public partial class ProductFamiliesController
{
    [HttpGet]
    [Route("Hierarchy/{id:guid}")]
    [SwaggerOperation(Summary = "Retrieve product family hierarchy",
        Description = "Retrieve hierarchy of a specific product family identified by its unique identifier",
        OperationId = "Get_ProductFamilies_Hierarchy",
        Tags = ["ProductFamilies"])]
    [ProducesResponseType(typeof(ProductFamilyHierarchyDto), StatusCodes.Status200OK)]
    public async Task<List<ProductFamilyHierarchyDto>> GetProductFamilyHierarchyById(Guid id)
    {
        return await QuerySender.Send(new GetProductFamilyHierarchyByIdQuery(id));
    }
}
