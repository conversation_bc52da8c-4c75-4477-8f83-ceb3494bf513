using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.Permissions;
using SpareParts.Core.ProductFamilies.Commands;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Core.ProductFamilies;

public partial class ProductFamiliesController
{
    [HttpDelete]
    [Authorize(CorePermissions.ProductFamilies.UnLinkProduct)]
    [Authorize(CorePermissions.Products.Default)]
    [Route("{id:guid}/products/{productId:guid}")]
    [SwaggerOperation(Summary = "Unlink product and product family",
        Description = "Unlink a product and a product family identified by their unique identifiers",
        OperationId = "Unlink_Product_And_ProductFamily",
        Tags = ["ProductFamilies"])]
    public async Task UnlinkProductAndProductFamilyCommand(Guid id, Guid productId)
    {
        UnlinkProductFromProductFamilyCommand unlinkProductFromProductFamilyCommand = new(id, productId);
        await CommandSender.Send(unlinkProductFromProductFamilyCommand);
    }
}