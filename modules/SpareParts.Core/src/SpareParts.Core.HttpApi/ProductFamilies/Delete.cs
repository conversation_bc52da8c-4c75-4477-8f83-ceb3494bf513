using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.Permissions;
using SpareParts.Core.ProductFamilies.Commands;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Core.ProductFamilies;
public partial class ProductFamiliesController
{
    [HttpDelete]
    [Authorize(CorePermissions.ProductFamilies.Delete)]
    [Route("{id:guid}")]
    [SwaggerOperation(Summary = "Delete a product family",
        Description = "Delete an existing product family identified by its unique identifier",
        OperationId = "Delete_ProductFamily",
        Tags = ["ProductFamilies"])]
    public async Task Delete(Guid id)
    {
        DeleteProductFamilyCommand deleteProductFamilyCommand = new(id);
        await CommandSender.Send(deleteProductFamilyCommand);
    }
}
