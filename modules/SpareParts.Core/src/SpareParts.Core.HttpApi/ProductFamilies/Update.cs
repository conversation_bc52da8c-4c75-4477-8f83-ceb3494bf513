using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.Permissions;
using SpareParts.Core.ProductFamilies.Commands;
using SpareParts.Core.ProductFamilies.Dtos;
using SpareParts.Core.ProductFamilies.Dtos.Inputs;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Core.ProductFamilies;
public partial class ProductFamiliesController
{
    [HttpPut]
    [Authorize(CorePermissions.ProductFamilies.Edit)]
    [Route("{id:guid}")]
    [SwaggerOperation(Summary = "Update a product family",
        Description = "Update an existing product family identified by its unique identifier",
        OperationId = "Update_ProductFamily",
        Tags = ["ProductFamilies"])]
    public async Task<ProductFamilyDto> Update(Guid id, [FromBody] UpdateProductFamilyDto updateProductFamilyDto)
    {
        UpdateProductFamilyCommand updateProductFamilyCommand = new(id, updateProductFamilyDto);
        return await CommandSender.Send(updateProductFamilyCommand);
    }
}