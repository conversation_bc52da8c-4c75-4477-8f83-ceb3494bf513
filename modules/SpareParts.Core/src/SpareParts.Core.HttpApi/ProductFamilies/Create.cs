using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.Permissions;
using SpareParts.Core.ProductFamilies.Commands;
using SpareParts.Core.ProductFamilies.Dtos;
using SpareParts.Core.ProductFamilies.Dtos.Inputs;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Core.ProductFamilies;
public partial class ProductFamiliesController
{
    [HttpPost]
    [Authorize(CorePermissions.ProductFamilies.Create)]
    [SwaggerOperation(Summary = "Create a new product family",
        Description = "Create a new product family",
        OperationId = "Create_ProductFamily",
        Tags = ["ProductFamilies"])]
    [ProducesResponseType(typeof(ProductFamilyDto), StatusCodes.Status201Created)]
    public async Task<CreatedResult> Create([FromBody] CreateProductFamilyDto createProductFamilyDto)
    {
        CreateProductFamilyCommand createProductFamilyCommand = ObjectMapper.Map<CreateProductFamilyDto, CreateProductFamilyCommand>(createProductFamilyDto);
        ProductFamilyDto productFamilyDto = await CommandSender.Send(createProductFamilyCommand);
        return Created(string.Empty, productFamilyDto);
    }
}
