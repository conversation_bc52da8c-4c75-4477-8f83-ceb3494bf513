using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.Components.Dtos.Products;
using SpareParts.Core.Permissions;
using SpareParts.Core.ProductFamilies.Queries;
using SpareParts.Core.ProductFamilies.QueryFilters;
using Swashbuckle.AspNetCore.Annotations;
using Volo.Abp.Application.Dtos;

namespace SpareParts.Core.ProductFamilies;
public partial class ProductFamiliesController
{
    [HttpGet]
    [Authorize(CorePermissions.Products.Default)]
    [Route("{id:guid}/products")]
    [SwaggerOperation(Summary = "Retrieve products in a specific product family",
        Description = "Retrieve paginated products associated with a specific product family",
        OperationId = "Get_Products",
        Tags = ["ProductFamilies"])]
    public async Task<PagedResultDto<ProductInProductFamilyDto>> GetProducts(Guid id, [FromQuery] ProductsInProductFamilyPaginationFilter filter)
    {
        return await QuerySender.Send(new GetProductsInProductFamilyQuery(id, filter));
    }
}
