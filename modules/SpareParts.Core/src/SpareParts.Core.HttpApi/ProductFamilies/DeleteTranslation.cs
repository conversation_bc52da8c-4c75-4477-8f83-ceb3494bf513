using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.Permissions;
using SpareParts.Core.ProductFamilies.Commands;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Core.ProductFamilies;

public partial class ProductFamiliesController
{
    [HttpDelete]
    [Authorize(CorePermissions.ProductFamilies.DeleteTranslation)]
    [Route("{id:guid}/translations/{language}")]
    [SwaggerOperation(Summary = "Delete a product family translation",
        Description = "Delete an existing product family translation identified by its unique code language",
        OperationId = "Delete_ProductFamily_Translation",
        Tags = ["ProductFamilies"])]
    public async Task DeleteTranslation(Guid id, string language)
    {
        await CommandSender.Send(new DeleteProductFamilyTranslationCommand(id, language));
    }
}