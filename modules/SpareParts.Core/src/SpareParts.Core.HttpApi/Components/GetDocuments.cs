using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.Components.Queries;
using SpareParts.Core.Components.QueryFilters;
using SpareParts.Core.Documents.Dtos;
using Swashbuckle.AspNetCore.Annotations;
using Volo.Abp.Application.Dtos;

namespace SpareParts.Core.Components;

public partial class ComponentsController
{
    [HttpGet]
    [Route("{id:guid}/documents")]
    [SwaggerOperation(Summary = "Get documents for a component",
        Description = "Retrieves documents for a specific component identified by its unique identifier.",
        OperationId = "Get_ComponentDocuments",
        Tags = ["Components"])]
    [Obsolete]
    public async Task<PagedResultDto<DocumentDto>> GetComponentDocuments(Guid id, [FromQuery] ComponentDocumentPaginationFilter filter)
    {
        return await QuerySender.Send(new GetComponentDocumentsQuery(id, filter));
    }
}