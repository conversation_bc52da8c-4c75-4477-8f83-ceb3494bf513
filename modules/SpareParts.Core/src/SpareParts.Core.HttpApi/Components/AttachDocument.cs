using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.Components.Commands;
using SpareParts.Core.Permissions;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Core.Components;

public partial class ComponentsController
{
    [HttpPost]
    [Authorize(CorePermissions.Components.Edit)]
    [Route("{id:guid}/documents/{documentId:guid}")]
    [SwaggerOperation(Summary = "Attach document to component",
        Description = "Attach an existing document to a component, both identified by their unique identifiers",
        OperationId = "Attach_Document",
        Tags = ["Components"])]
    public async Task AttachDocument(Guid id, Guid documentId)
    {
        await CommandSender.Send(new AttachDocumentToComponentCommand(id, documentId));
    }
}