using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.Components.Commands;
using SpareParts.Core.Documents.Commands;
using SpareParts.Core.Documents.Dtos;
using SpareParts.Core.Documents.Dtos.Inputs;
using SpareParts.Core.Permissions;
using Swashbuckle.AspNetCore.Annotations;
using Volo.Abp;

namespace SpareParts.Core.Components;

public partial class ComponentsController
{
    [HttpPost]
    [Authorize(CorePermissions.Documents.Create)]
    [Authorize(CorePermissions.Components.Edit)]
    [Route("{id:guid}/documents")]
    [SwaggerOperation(Summary = "Create a new document",
        Description = "Create a new component document",
        OperationId = "Create_Document",
        Tags = ["Components"])]
    [ProducesResponseType(typeof(DocumentDto), StatusCodes.Status201Created)]
    [Obsolete]
    public async Task<CreatedResult> CreateDocumentAndAttach(Guid id, [FromBody] CreateDocumentDto createDocumentDto)
    {
        switch (createDocumentDto.Resource.Type)
        {
            case Enums.DocumentType.Resource:
                if (!Guid.TryParse(createDocumentDto.Resource.Value, out Guid idResource))
                {
                    throw new BusinessException("The resource id is incorrect");
                }

                CreateFileDocumentCommand createFileDocumentCommand = new(
                    new DocumentResourceDto<Guid>
                    {
                        Type = createDocumentDto.Resource.Type,
                        Value = idResource
                    },
                    createDocumentDto.Translations, createDocumentDto.Languages ?? [], createDocumentDto.IsPublic ?? true);
                DocumentDto fileDocumentDto = await CommandSender.Send(createFileDocumentCommand);
                await CommandSender.Send(new AttachDocumentToComponentCommand(id, fileDocumentDto.Id));
                return Created(string.Empty, fileDocumentDto);

            case Enums.DocumentType.Link:
                CreateLinkDocumentCommand createLinkDocumentCommand = new(
                    new DocumentResourceDto<string>
                    {
                        Type = createDocumentDto.Resource.Type,
                        Value = createDocumentDto.Resource.Value
                    },
                    createDocumentDto.Translations, createDocumentDto.Languages ?? [], createDocumentDto.IsPublic ?? true);
                DocumentDto linkDocumentDto = await CommandSender.Send(createLinkDocumentCommand);
                await CommandSender.Send(new AttachDocumentToComponentCommand(id, linkDocumentDto.Id));
                return Created(string.Empty, linkDocumentDto);
            default:
                throw new BusinessException("The resource type is incorrect");
        }
    }
}