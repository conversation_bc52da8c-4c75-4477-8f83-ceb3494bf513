using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.Components.Commands;
using SpareParts.Core.Permissions;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Core.Components;
public partial class ComponentsController
{
    [HttpDelete]
    [Authorize(CorePermissions.Components.DeleteTranslation)]
    [Route("{id:guid}/online-translations/{language}")]
    [SwaggerOperation(Summary = "Delete a component online translation",
        Description = "Delete an existing component online translation identified by its unique code language",
        OperationId = "Delete_Component_Online_Translation",
        Tags = ["Components"])]
    public async Task DeleteOnlineTranslation(Guid id, string language)
    {
        await CommandSender.Send(new DeleteComponentOnlineTranslationCommand(id, language));
    }
}
