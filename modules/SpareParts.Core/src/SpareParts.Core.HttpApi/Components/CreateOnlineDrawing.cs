using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using SpareParts.Core.Drawings.Commands;
using SpareParts.Core.Drawings.Dtos;
using SpareParts.Core.Enums;
using Volo.Abp.Content;
using SpareParts.Common;
using SpareParts.Core.Permissions;
using SpareParts.Core.Resources;

namespace SpareParts.Core.Components;
public partial class ComponentsController
{
    [HttpPost]
    [Authorize(CorePermissions.Components.Edit)]
    [Route("{id:guid}/drawing")]
    [SwaggerOperation(Summary = "Create an assembly drawing",
        Description = "Create an online drawing associated with a specific assembly identified by its unique identifier.",
        OperationId = "Create_Drawing",
        Tags = ["Assemblies"])]
    [ProducesResponseType(typeof(DrawingDto), StatusCodes.Status201Created)]
    [RequestSizeLimit(CommonConsts.SizeLimits.FileSizeLimitInBytes + CommonConsts.SizeLimits.OverheadInBytes)]
    public async Task<CreatedResult> CreateOnlineDrawing(Guid id, [FromForm] FileInputDto drawingFile)
    {
        Stream stream = drawingFile.File.OpenReadStream();
        RemoteStreamContent drawingStreamContent = new (stream, drawingFile.File.FileName);

        CreateDrawingCommand command = new(id, drawingStreamContent, new Dictionary<string, Guid>(), DrawingOrigin.Online);

        DrawingDto createdDto = await CommandSender.Send(command);

        return Created(string.Empty, createdDto);
    }
}