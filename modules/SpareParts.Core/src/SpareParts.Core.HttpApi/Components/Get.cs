using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.Components.Dtos;
using SpareParts.Core.Components.Queries;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Core.Components;
public partial class ComponentsController
{
    [HttpGet]
    [Route("{id:guid}")]
    [SwaggerOperation(Summary = "Get a component by id",
        Description = "Retrieves information about a specific component identified by its unique identifier.",
        OperationId = "Get_Component",
        Tags = ["Components"])]
    public async Task<ComponentDto> GetComponentById(Guid id)
    {
        return await QuerySender.Send(new GetComponentByIdQuery(id));
    }
}
