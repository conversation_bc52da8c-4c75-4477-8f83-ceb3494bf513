using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Common;
using SpareParts.Core.Components.Dtos;
using SpareParts.Core.Components.Queries;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Core.Components;
public partial class ComponentsController
{
    [HttpGet]
    [Route("by-ids")]
    [SwaggerOperation(Summary = "Get a list of components by ids",
        Description = "Retrieves information about a list of components based on the provided array of component ids.",
        OperationId = "Get_Components_ByIds",
        Tags = ["Components"])]
    public async Task<List<ComponentDto>> GetComponentsByIds([FromHeader][Required][Length(1, LimitedPaginationFilterBase.MaxPerPage)] HashSet<Guid> ids)
    {
        return await QuerySender.Send(new GetComponentsByIdsQuery(ids));
    }
}
