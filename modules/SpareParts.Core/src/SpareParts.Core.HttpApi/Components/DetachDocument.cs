using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.Components.Commands;
using SpareParts.Core.Permissions;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Core.Components;

public partial class ComponentsController
{
    [HttpDelete]
    [Authorize(CorePermissions.Components.Edit)]
    [Route("{id:guid}/documents/{documentId:guid}")]
    [SwaggerOperation(Summary = "Detach document from component",
        Description = "Detach an existing document from a component, both identified by their unique identifiers",
        OperationId = "Detach_Document",
        Tags = ["Components"])]
    public async Task DetachDocument(Guid id, Guid documentId)
    {
        await CommandSender.Send(new DetachDocumentFromComponentCommand(id, documentId));
    }
}