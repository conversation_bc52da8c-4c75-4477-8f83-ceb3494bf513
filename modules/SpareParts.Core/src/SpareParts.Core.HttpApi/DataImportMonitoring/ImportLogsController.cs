using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using SpareParts.Core.Features;
using SpareParts.Core.Permissions;
using Volo.Abp.GlobalFeatures;

namespace SpareParts.Core.DataImportMonitoring;

[ApiVersion("1.0")]
[Authorize(CorePermissions.DataImportMonitoring.Default)]
[RequiresGlobalFeature(typeof(DataImportMonitoringFeature))]
public partial class ImportLogsController : CoreController
{
}