using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.DataImportMonitoring.Queries;
using Swashbuckle.AspNetCore.Annotations;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;

namespace SpareParts.Core.DataImportMonitoring;

public partial class ImportLogsController
{
    [HttpGet]
    [SwaggerOperation(
        Summary = "Retrieve a paginated list of import logs",
        Description = "Returns a paginated list of import logs based on the provided filter. Each log summarizes an import attempt, including product code, status, and action.",
        OperationId = "ImportLogs_GetAll",
        Tags = ["ImportLogs"])]
    public async Task<PagedResultDto<ImportLogDto>> GetAll([FromQuery] ImportLogsPaginationFilter filter)
    {
        return await QuerySender.Send(new GetImportLogsQuery(filter));
    }
}