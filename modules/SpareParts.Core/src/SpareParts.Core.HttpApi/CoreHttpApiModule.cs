using Localization.Resources.AbpUi;
using Microsoft.Extensions.DependencyInjection;
using SpareParts.Common;
using SpareParts.Core.Features;
using SpareParts.Core.Localization;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.GlobalFeatures;
using Volo.Abp.Localization;
using Volo.Abp.Modularity;
using Volo.Abp.Threading;

namespace SpareParts.Core;

[DependsOn(
    typeof(CoreApplicationContractsModule),
    typeof(AbpAspNetCoreMvcModule),
    typeof(CommonHttpApiModule)
    )]
public class CoreHttpApiModule : AbpModule
{
    private static readonly OneTimeRunner OneTimeRunner = new();

    public override void PreConfigureServices(ServiceConfigurationContext context)
    {
        PreConfigure<IMvcBuilder>(mvcBuilder =>
        {
            mvcBuilder.AddApplicationPartIfNotExists(typeof(CoreHttpApiModule).Assembly);
        });

        OneTimeRunner.Run(() =>
        {
            if (bool.TryParse(context.Services.GetConfiguration()["GlobalFeatures:Core.Equipment"], out bool equipmentEnabled) && equipmentEnabled)
            {
                GlobalFeatureManager.Instance.Modules.CoreFeatures().Equipment.Enable();
            }
            if (bool.TryParse(context.Services.GetConfiguration()["GlobalFeatures:Core.DocumentCenter"], out bool documentCenterEnabled) && documentCenterEnabled)
            {
                GlobalFeatureManager.Instance.Modules.CoreFeatures().DocumentCenter.Enable();
            }
            if (bool.TryParse(context.Services.GetConfiguration()["GlobalFeatures:Core.BomDocument"], out bool bomDocumentEnabled) && bomDocumentEnabled)
            {
                GlobalFeatureManager.Instance.Modules.CoreFeatures().BomDocument.Enable();
            }
            if (bool.TryParse(context.Services.GetConfiguration()["GlobalFeatures:Core.MultiDrawing"], out bool multiDrawingEnabled) && multiDrawingEnabled)
            {
                GlobalFeatureManager.Instance.Modules.CoreFeatures().MultiDrawing.Enable();
            }
            if (bool.TryParse(context.Services.GetConfiguration()["GlobalFeatures:Core.DataImportMonitoring"], out bool dataImportMonitoringEnabled) && dataImportMonitoringEnabled)
            {
                GlobalFeatureManager.Instance.Modules.CoreFeatures().DataImportMonitoring.Enable();
            }
        });
    }

    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        Configure<AbpLocalizationOptions>(options =>
        {
            options.Resources
                .Get<CoreResource>()
                .AddBaseTypes(typeof(AbpUiResource));
        });
    }
}