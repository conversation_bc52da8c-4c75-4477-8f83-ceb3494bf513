using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.Components.Dtos.Products;
using SpareParts.Core.Components.Queries.Products;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Core.Products;
public partial class ProductsController
{
    [HttpGet]
    [Route("{id:guid}")]
    [SwaggerOperation(Summary = "Get a product by Id",
        Description = "Get a product by id",
        OperationId = "Get_Product",
        Tags = ["Products"])]
    public async Task<ProductDto> Get(Guid id)
    {
        return await QuerySender.Send(new GetProductByIdQuery(id));
    }
}
