using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.Components.Dtos.Products;
using SpareParts.Core.Components.Queries.Products;
using SpareParts.Core.Components.QueryFilters.Products;
using Swashbuckle.AspNetCore.Annotations;
using Volo.Abp.Application.Dtos;

namespace SpareParts.Core.Products;
public partial class ProductsController
{
    [HttpGet]
    [SwaggerOperation(Summary = "Retrieves a paged list of products based on the specified filter criteria.",
        Description = @"Use this endpoint to get a paginated list of products based on the provided filter. 
<br/>If the filter text is not null, a search will be performed on product codes or labels.
The search language is determined by the user's browser language. 
<br/>If no data is found in the user's language, a fallback to the default language will be applied.",
        OperationId = "Get_Products",
        Tags = ["Products"])]
    public async Task<PagedResultDto<ProductDto>> GetAll([FromQuery] ProductPaginationFilter filter)
    {
        return await QuerySender.Send(new GetProductsQuery(filter));
    }
}
