using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.Components.Dtos.Products;
using SpareParts.Core.Components.Queries.Products;
using SpareParts.Core.Components.QueryFilters.Products;
using Swashbuckle.AspNetCore.Annotations;
using Volo.Abp.Application.Dtos;

namespace SpareParts.Core.Products;
public partial class ProductsController
{
    [HttpGet]
    [Route("{id:Guid}/search")]
    [SwaggerOperation(Summary = "Search a component within product scoped by Id",
        Description = "Search a component within product scoped by Id",
        OperationId = "Search_Component_By_Product_Id",
        Tags = ["Products"])]
    public async Task<PagedResultDto<ComponentInProductDto>> SearchComponentByProductId(Guid id, [FromQuery] ProductComponentSearchPaginationFilter filter)
    {
        return await QuerySender.Send(new SearchComponentByProductIdQuery(id, filter));
    }
}
