using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.Components.Commands.Products;
using SpareParts.Core.Components.Dtos.Products;
using SpareParts.Core.Components.Dtos.Products.Inputs;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Core.Products;
public partial class ProductsController
{
    [HttpPatch]
    [Route("{id:guid}/public")]
    [SwaggerOperation(Summary = "Update a product public flag",
        Description = "Update the public flag of an existing product identified by its unique identifier",
        OperationId = "Update_Product_Public_Flag",
        Tags = ["Products"])]
    public async Task<ProductPublicFlagDto> UpdatePublicFlag(Guid id, [FromBody] UpdateProductPublicFlagDto updateProductPublicFlagDto)
    {
        UpdateProductPublicFlagCommand updateProductPublicFlagCommand = new(id, updateProductPublicFlagDto.IsPublic);
        return await CommandSender.Send(updateProductPublicFlagCommand);
    }
}
