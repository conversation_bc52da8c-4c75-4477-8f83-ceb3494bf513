using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Core.Components.Dtos.Products;
using SpareParts.Core.Components.Queries.Products;
using SpareParts.Core.Components.QueryFilters.Products;
using SpareParts.Core.Permissions;
using Swashbuckle.AspNetCore.Annotations;
using Volo.Abp.Application.Dtos;

namespace SpareParts.Core.Products;

public partial class ProductsController
{
    [HttpGet]
    [Route("{id:Guid}/search-documents")]
    [Authorize(CorePermissions.Documents.Search)]
    [SwaggerOperation(Summary = "Search documents within product scope by Id",
        Description = "Search documents within product scope by Id",
        OperationId = "Search_Documents_By_Product_Id",
        Tags = ["Products"])]
    public async Task<PagedResultDto<DocumentInProductDto>> SearchDocumentsByProduct(Guid id, [FromQuery] ProductDocumentsSearchPaginationFilter filter)
    {
        return await QuerySender.Send(new SearchDocumentsByProductQuery(id, filter));
    }
}