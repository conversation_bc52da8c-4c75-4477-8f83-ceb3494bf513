using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Threading.Tasks;
using SpareParts.Core.Entities.Boms;
using SpareParts.Core.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace SpareParts.Core;

public class BomHierarchyHistoryRepository(IDbContextProvider<CoreDbContext> dbContextProvider)
    : EfCoreRepository<CoreDbContext, BomHierarchyHistory>(dbContextProvider), IBomHierarchyHistoryRepository
{
    public async Task<List<BomHierarchyHistory>> InsertBomHierarchyDataAsync()
    {
        DbTransaction? transaction = await GetDbTransactionAsync() as DbTransaction;
        DbConnection connection = (DbConnection)await GetDbConnectionAsync();

        await using DbCommand command = connection.CreateCommand();
        command.Transaction = transaction;
        command.CommandText = "InsertIntoBomHierarchyHistory";
        command.CommandType = CommandType.StoredProcedure;

        DbParameter parameter = command.CreateParameter();
        parameter.ParameterName = "@DemoCodePrefix";
        parameter.Value = CoreDomainSharedConsts.DemoCodePrefix;
        command.Parameters.Add(parameter);

        List<BomHierarchyHistory> results = [];

        await using DbDataReader reader = await command.ExecuteReaderAsync();
        while (await reader.ReadAsync())
        {
            results.Add(new BomHierarchyHistory
            {
                DateTime = await reader.GetFieldValueAsync<DateTime>(nameof(BomHierarchyHistory.DateTime)),
                LinesNumber = await reader.GetFieldValueAsync<int>(nameof(BomHierarchyHistory.LinesNumber)),
                MaxLevel = await reader.GetFieldValueAsync<int>(nameof(BomHierarchyHistory.MaxLevel)),
                ProductCode = await reader.GetFieldValueAsync<string>(nameof(BomHierarchyHistory.ProductCode)),
                ProductId = await reader.GetFieldValueAsync<Guid>(nameof(BomHierarchyHistory.ProductId)),
                TenantId = await reader.GetFieldValueAsync<Guid>(nameof(BomHierarchyHistory.TenantId)),
                TenantName = await reader.GetFieldValueAsync<string>(nameof(BomHierarchyHistory.TenantName))
            });
        }
        return results;
    }
}