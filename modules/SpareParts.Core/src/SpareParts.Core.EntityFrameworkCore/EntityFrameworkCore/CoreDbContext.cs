using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using SpareParts.Common;
using SpareParts.Common.Auditing;
using SpareParts.Common.Branding;
using SpareParts.Common.Companies;
using SpareParts.Common.DataFilter;
using SpareParts.Common.EntityFrameworkCore;
using SpareParts.Common.HelpRequests;
using SpareParts.Common.Resources;
using SpareParts.Core.DataVisibility;
using SpareParts.Core.Entities.Boms;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Equipments;
using SpareParts.Core.Entities.ProductFamilies;
using SpareParts.Core.Entities.Resources;
using System;
using System.Diagnostics.CodeAnalysis;
using System.Linq.Expressions;
using SpareParts.Core.DataImportMonitoring;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.Users;

namespace SpareParts.Core.EntityFrameworkCore;

[ExcludeFromCodeCoverage]
[ConnectionStringName(CoreDbProperties.ConnectionStringName)]
[ReplaceDbContext(typeof(ICommonDbContext))]
public class CoreDbContext : AbpDbContext<CoreDbContext>, ICommonDbContext
{
    private ICurrentUser CurrentUser => LazyServiceProvider.LazyGetRequiredService<ICurrentUser>();

    public DbSet<Company> Companies { get; set; } = default!;
    public DbSet<EntityChange> EntityChanges { get; set; } = default!;

    public DbSet<Equipment> Equipments { get; set; } = default!;
    public DbSet<Component> Components { get; set; } = default!;
    public DbSet<Product> Products { get; set; } = default!;
    public DbSet<ProductFlattenHierarchy> ProductFlattenHierarchy { get; set; } = default!;
    public DbSet<BomPath> BomPaths { get; set; } = default!;
    public DbSet<ResourceVisibility> CoreResourceVisibility { get; set; } = default!;
    public DbSet<ProductFamilyFlattenHierarchy> ProductFamilyFlattenHierarchy { get; set; } = default!;
    public DbSet<BomLine> BomLines { get; set; } = default!;
    public DbSet<ProductFamily> ProductFamilies { get; set; } = default!;
    public DbSet<ProductInProductFamily> ProductInProductFamilies { get; set; } = default!;
    public DbSet<ProductFamilyTranslation> ProductFamilyTranslations { get; set; } = default!;
    public DbSet<Resource> Resources { get; set; } = default!;
    public DbSet<ComponentTranslation> ComponentTranslations { get; set; } = default!;
    public DbSet<ComponentOnlineTranslation> ComponentOnlineTranslations { get; set; } = default!;
    public DbSet<Document> Documents { get; set; } = default!;
    public DbSet<DocumentTranslation> DocumentTranslations { get; set; } = default!;
    public DbSet<ComponentDocument> ComponentDocuments { get; set; } = default!;
    public DbSet<Drawing> Drawings { get; set; } = default!;
    public DbSet<DrawingMapping> DrawingMappings { get; set; } = default!;
    public DbSet<DocumentCategory> DocumentCategories { get; set; } = default!;
    public DbSet<DocumentCategoryTranslation> DocumentCategoryTranslations { get; set; } = default!;
    public DbSet<DocumentInDocumentCategory> DocumentInDocumentCategory { get; set; } = default!;
    public DbSet<BomHierarchyHistory> BomHierarchyHistories { get; set; } = default!;
    public DbSet<BomDocument> BomDocuments { get; set; } = default!;
    public DbSet<TenantBranding> Brandings { get; set; } = default!;
    public DbSet<PublicResource> PublicResource { get; set; } = default!;
    public DbSet<HelpRequest> HelpRequests { get; set; } = default!;
    public DbSet<MasterProposal> MasterProposals { get; set; } = default!;
    public DbSet<ComponentProposal> ComponentProposals { get; set; } = default!;
    public DbSet<BomProposal> BomProposals { get; set; } = default!;
    public DbSet<ComponentImageProposal> ComponentImageProposals { get; set; } = default!;
    public DbSet<DrawingProposal> DrawingProposals { get; set; } = default!;

    public CoreDbContext(DbContextOptions<CoreDbContext> options) : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        modelBuilder.ConfigureCore();
    }

    protected override bool ShouldFilterEntity<TEntity>(IMutableEntityType entityType)
    {
        return typeof(IHasPublic).IsAssignableFrom(typeof(TEntity)) ||
                typeof(IHasVisibility).IsAssignableFrom(typeof(TEntity)) ||
                typeof(IHasCompanyId).IsAssignableFrom(typeof(TEntity)) ||
                base.ShouldFilterEntity<TEntity>(entityType);
    }

    protected override Expression<Func<TEntity, bool>>? CreateFilterExpression<TEntity>(ModelBuilder modelBuilder)
    {
        Expression<Func<TEntity, bool>>? expression = base.CreateFilterExpression<TEntity>(modelBuilder);

        if (typeof(IHasPublic).IsAssignableFrom(typeof(TEntity)))
        {
            Expression<Func<TEntity, bool>> isPublicFilter =
                e => !DataFilter.IsEnabled<IHasPublic>() || EF.Property<bool>(e, nameof(IHasPublic.IsPublic));
            expression = expression == null ? isPublicFilter : QueryFilterExpressionHelper.CombineExpressions(expression, isPublicFilter);
        }

        if (typeof(IHasVisibility).IsAssignableFrom(typeof(TEntity)))
        {
            Expression<Func<TEntity, bool>> isVisibleFilter =
                e => !DataFilter.IsEnabled<IHasVisibility>() || EF.Property<bool>(e, nameof(IHasVisibility.IsVisible));
            expression = expression == null ? isVisibleFilter : QueryFilterExpressionHelper.CombineExpressions(expression, isVisibleFilter);
        }

        if (typeof(IHasCompanyId).IsAssignableFrom(typeof(TEntity)))
        {
            Expression<Func<TEntity, bool>> hasCompanyIdFilter =
                e => !DataFilter.IsEnabled<IHasCompanyId>() || ((IHasCompanyId)e).CompanyId == CurrentUser.FindCompanyId();

            expression = expression == null ? hasCompanyIdFilter : QueryFilterExpressionHelper.CombineExpressions(expression, hasCompanyIdFilter);
        }

        if (typeof(IProductFlattenHierarchy).IsAssignableFrom(typeof(TEntity)))
        {
            Expression<Func<TEntity, bool>> hasCompanyIdFilter =
                e => !DataFilter.IsEnabled<IHasCompanyId>() || ((IProductFlattenHierarchy)e).CompanyId == CurrentUser.FindCompanyId() || ((IProductFlattenHierarchy)e).CompanyId == null;

            expression = expression == null ? hasCompanyIdFilter : QueryFilterExpressionHelper.CombineExpressions(expression, hasCompanyIdFilter);
        }

        return expression;
    }
}