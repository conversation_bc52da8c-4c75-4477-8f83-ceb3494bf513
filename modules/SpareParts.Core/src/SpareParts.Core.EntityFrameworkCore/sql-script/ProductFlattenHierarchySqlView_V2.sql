CREATE OR ALTER VIEW CoreProductFlattenHierarchy AS 
                                    WITH Hierarchy AS (
                                    SELECT
                                        product.TenantId AS TenantId,
                                        product.Id AS ProductId,
                                        product.IsPublic as IsPublic,
                                        product.IsVisible as IsVisible,
                                        product.IsDeleted as <PERSON><PERSON><PERSON><PERSON>,
                                        product.Id AS ComponentId,
                                        0 AS Level,
                                        CAST('/' AS nvarchar(max)) AS Path
                                    FROM
                                        CoreProducts product

                                    UNION ALL

                                    SELECT 
                                        hierarchy.TenantId AS TenantId,
                                        hierarchy.ProductId,
                                        hierarchy.IsPublic,
                                        hierarchy.IsVisible,
                                        (hierarchy.IsDeleted | bomline.IsDeleted  | component.IsDeleted) AS IsDeleted,
                                        bomline.ChildComponentId,
                                        hierarchy.Level + 1,
                                        (hierarchy.Path + CAST(bomline.ParentComponentId AS nvarchar(36)) + '/')  AS Path
                                    FROM
                                        Hierarchy AS hierarchy
                                    JOIN
                                        CoreBomLines bomline ON hierarchy.ComponentId = bomline.ParentComponentId AND bomline.ChildComponentId <> bomline.ParentComponentId 
						                                    AND hierarchy.Path not like '%' + CAST(bomline.ChildComponentId AS VARCHAR(36)) + '%'    
                                    JOIN
                                        CoreComponents component ON bomline.ChildComponentId = component.Id
	                                    WHERE hierarchy.LEVEL < 50 
                                    )
                                    SELECT * FROM Hierarchy