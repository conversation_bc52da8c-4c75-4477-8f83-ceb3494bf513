CREATE OR ALTER TRIGGER trg_BeforeDelete_FromComponents
ON [dbo].[CoreComponents]
INSTEAD OF DELETE
AS
BEGIN
    DELETE FROM [dbo].[CoreComponentDrawingIndexes]
    WHERE ComponentId IN (SELECT Id FROM DELETED);

    DELETE FROM [dbo].[CoreDrawings]
    WHERE AssemblyId IN (SELECT Id FROM DELETED);

    DELETE FROM [dbo].[CoreBomLines]
    WHERE ParentAssemblyId IN (SELECT Id FROM DELETED);

    DELETE FROM [dbo].[CoreComponents]
    WHERE Id IN (SELECT Id FROM DELETED);
END;