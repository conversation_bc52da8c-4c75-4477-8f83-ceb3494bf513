CREATE OR ALTER VIEW CoreBomPaths AS
                                        WITH RecursiveBOM AS (
                                            -- Anchor member: start with each Parent-Child relationship from root nodes
                                            SELECT
                                		        b.TenantId,
                                                b.<PERSON>rentComponentId,
                                                b.ChildComponentId AS FinalChildComponentId,
                                                CAST(CONVERT(VARCHAR(MAX), b.ParentComponentId) + '/' + CONVERT(VARCHAR(MAX), b.ChildComponentId) AS VARCHAR(MAX)) AS Path,
                                                b.ParentComponentId AS RootId
                                            FROM CoreBomLines b
                                            INNER JOIN CoreComponents cp ON b.ParentComponentId = cp.Id AND cp.IsDeleted = 0
                                            INNER JOIN CoreComponents cc ON b.ChildComponentId = cc.Id AND cc.IsDeleted = 0
                                            WHERE b.ParentComponentId NOT IN (
                                                SELECT ChildComponentId
                                                FROM CoreBomLines bl
                                                INNER JOIN CoreComponents cc2 ON bl.ChildComponentId = cc2.Id AND cc2.IsDeleted = 0
                                            )
                                            UNION ALL
                                            -- Recursive member: continue building paths by joining child to parent
                                            SELECT
                                		        r.TenantId,
                                                r.ParentComponentId,
                                                b.ChildComponentId AS FinalChildComponentId,
                                                CAST(r.Path + '/' + CONVERT(VARCHAR(MAX), b.ChildComponentId) AS VARCHAR(MAX)) AS Path,
                                                r.RootId
                                            FROM RecursiveBOM r
                                            INNER JOIN CoreBomLines b ON r.FinalChildComponentId = b.ParentComponentId
                                            INNER JOIN CoreComponents cp ON b.ParentComponentId = cp.Id AND cp.IsDeleted = 0
                                            INNER JOIN CoreComponents cc ON b.ChildComponentId = cc.Id AND cc.IsDeleted = 0
                                        )
                                        -- Select only the paths originating from root nodes
                                        SELECT DISTINCT
                                	        TenantId,
                                            FinalChildComponentId,
                                            Path
                                        FROM RecursiveBOM
                                        WHERE ParentComponentId = RootId;