CREATE OR ALTER VIEW CoreBomPaths AS
WITH RecursiveBOM AS (
    -- Anchor member: start with each Parent-Child relationship from root nodes
    SELECT
        b.TenantId,
        b.<PERSON>rentAssemblyId,
        b.ChildComponentId AS FinalChildComponentId,
        CAST(CONVERT(VARCHAR(MAX), b.ParentAssemblyId) + '/' + CONVERT(VARCHAR(MAX), b.ChildComponentId) AS VARCHAR(MAX)) AS Path,
        b.ParentAssemblyId AS RootId
    FROM CoreBomLines b
    INNER JOIN CoreComponents cp ON b.ParentAssemblyId = cp.Id
    INNER JOIN CoreComponents cc ON b.ChildComponentId = cc.Id
    WHERE b.ParentAssemblyId NOT IN (
        SELECT ChildComponentId
        FROM CoreBomLines bl
        INNER JOIN CoreComponents cc2 ON bl.ChildComponentId = cc2.Id
    )
    UNION ALL
    -- Recursive member: continue building paths by joining child to parent
    SELECT
        r.TenantId,
        r.<PERSON>rentAssemblyId,
        b.<PERSON>omponentId AS FinalChildComponentId,
        CAST(r.Path + '/' + CONVERT(VARCHAR(MAX), b.ChildComponentId) AS VARCHAR(MAX)) AS Path,
        r.RootId
    FROM RecursiveBOM r
    INNER JOIN CoreBomLines b ON r.FinalChildComponentId = b.ParentAssemblyId
    INNER JOIN CoreComponents cp ON b.ParentAssemblyId = cp.Id
    INNER JOIN CoreComponents cc ON b.ChildComponentId = cc.Id
)
-- Select only the paths originating from root nodes
SELECT DISTINCT
    TenantId,
    FinalChildComponentId,
    Path
FROM RecursiveBOM
WHERE ParentAssemblyId = RootId;