CREATE PROCEDURE InsertIntoBomHierarchyHistory
    @DemoCodePrefix NVARCHAR(50)
AS
BEGIN
    SET NOCOUNT ON;
    WITH BomHierarchy AS (
        SELECT 
            p.Id AS ProductId,
            b.ChildComponentId,
            1 AS Level
        FROM CoreProducts p
        JOIN CoreComponents c ON p.Id = c.Id AND c.Code NOT LIKE @DemoCodePrefix + '%'
        JOIN CoreBomLines b ON p.Id = b.ParentAssemblyId
        JOIN AbpTenants t ON t.Id = p.TenantId AND t.IsEnabled = 1
        UNION ALL
        SELECT 
            bh.ProductId,
            b.ChildComponentId,
            bh.Level + 1 AS Level
        FROM BomHierarchy bh
        JOIN CoreBomLines b ON bh.ChildComponentId = b.ParentAssemblyId
    )
    INSERT INTO CoreBomHierarchyHistory ([DateTime], TenantId, TenantName, ProductId, ProductCode, MaxLevel, LinesNumber)
    OUTPUT inserted.*
    SELECT
        GETUTCDATE() AS [DateTime],
        p.TenantId,
        t.[Name] AS TenantName,
        bh.ProductId,
        c.Code AS ProductCode,
        Max(bh.Level) AS MaxLevel,
        COUNT(*) AS LinesNumber
    FROM BomHierarchy bh
    JOIN CoreProducts p ON bh.ProductId = p.Id
    JOIN AbpTenants t ON t.Id = p.TenantId
    JOIN CoreComponents c ON c.Id = bh.ProductId AND c.Code NOT LIKE @DemoCodePrefix + '%'
    GROUP BY bh.ProductId, p.TenantId, t.[Name], c.Code
    ORDER BY TenantName ASC, LinesNumber DESC;
END