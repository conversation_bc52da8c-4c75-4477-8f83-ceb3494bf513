CREATE OR ALTER VIEW CoreProductFlattenHierarchy AS 
WITH PreProcessedData AS (
-- Public products (CompanyId is NULL for public products)
SELECT
    product.TenantId,
    product.Id AS ProductId,
    product.IsVisible AS IsVisible,
    CAST(1 AS BIT) AS IsPublic,
	product.LastModificationTime,
    NULL AS CompanyId
FROM CoreProducts product
WHERE product.IsPublic = 1 AND product.IsDeleted = 0

UNION ALL

-- Non-public products that are not linked to any equipment (CompanyId is NULL)
SELECT
    product.TenantId,
    product.Id AS ProductId,
    product.IsVisible AS IsVisible,
    product.IsPublic AS IsPublic,
	product.LastModificationTime,
    equipment.CompanyId AS CompanyId
FROM CoreProducts product
LEFT JOIN CoreEquipments equipment
    ON product.Id = equipment.ProductId
    AND equipment.IsDeleted = 0
WHERE equipment.Id IS NULL
AND product.IsPublic = 0
	AND product.IsDeleted = 0

UNION ALL

-- Non-public products linked to company equipment (CompanyId is from equipment)
SELECT
    product.TenantId,
    product.Id AS ProductId,
    product.IsVisible AS IsVisible,
    CAST(1 AS BIT) AS IsPublic,
	product.LastModificationTime,
    equipment.CompanyId
FROM CoreProducts product
JOIN CoreEquipments equipment 
    ON product.Id = equipment.ProductId
    AND equipment.IsDeleted = 0
	AND product.IsPublic = 0
	AND CompanyId IS NOT NULL
	AND product.IsDeleted = 0
JOIN AdmCompanies company
	ON company.Id = equipment.CompanyId
	AND company.IsDeleted = 0

),
Hierarchy AS (
    SELECT
        preProcessedData.TenantId AS TenantId,
        preProcessedData.ProductId AS ProductId,
        preProcessedData.IsPublic as IsPublic,
        preProcessedData.IsVisible as IsVisible,
        preProcessedData.ProductId AS ComponentId,
        component.Code AS ProductCode,
        component.Code AS ComponentCode,
        preProcessedData.CompanyId AS CompanyId,
        0 AS Level,
		PreProcessedData.LastModificationTime,
        CAST('/' AS nvarchar(max)) AS Path
    FROM
        PreProcessedData preProcessedData
    JOIN
        CoreComponents component ON preProcessedData.ProductId = component.Id AND component.IsDeleted = 0

    UNION ALL

    SELECT 
        hierarchy.TenantId AS TenantId,
        hierarchy.ProductId,
        hierarchy.IsPublic,
        hierarchy.IsVisible,
        bomline.ChildComponentId AS ComponentId,
        hierarchy.ProductCode AS ProductCode,
        component.Code AS ComponentCode,
        hierarchy.CompanyId,
        (hierarchy.Level + 1) AS Level,
		hierarchy.LastModificationTime,
        (hierarchy.Path + CAST(bomline.ParentAssemblyId AS nvarchar(36)) + '/')  AS Path
    FROM
        Hierarchy AS hierarchy
    JOIN
        CoreBomLines bomline ON bomline.IsDeleted = 0 AND hierarchy.ComponentId = bomline.ParentAssemblyId
						    AND hierarchy.Path not like '%' + CAST(bomline.ChildComponentId AS VARCHAR(36)) + '%'
    JOIN
        CoreComponents component ON bomline.ChildComponentId = component.Id AND component.IsDeleted = 0
    )
    SELECT * FROM Hierarchy