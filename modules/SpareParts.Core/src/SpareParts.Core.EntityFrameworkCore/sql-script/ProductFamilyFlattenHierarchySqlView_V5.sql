CREATE OR ALTER VIEW CoreProductFamilyFlattenHierarchy AS 
								WITH 
								Hierarchy AS (
									SELECT
										productFamilies.TenantId,
										productFamilies.Id,
										CAST(NULL AS UNIQUEIDENTIFIER) as ParentId,
										CAST('/' AS nvarchar(max)) AS Path,
										CAST(IsVisible AS bit) AS IsVisible,
										0 AS Level
									FROM
										CoreProductFamilies productFamilies 
									WHERE
										ParentId IS NULL
									UNION ALL
									SELECT
										productFamilies.TenantId,
										productFamilies.Id,
										productFamilies.ParentId as ParentId,
										(hierarchy.Path + CAST(productFamilies.ParentId AS nvarchar(36)) + '/') AS Path,
										CAST((hierarchy.IsVisible & productFamilies.IsVisible) AS bit) AS IsVisible,
										hierarchy.Level + 1
									FROM 
										CoreProductFamilies productFamilies
									JOIN 
										Hierarchy AS hierarchy ON productFamilies.ParentId = hierarchy.Id AND productFamilies.Id <> productFamilies.ParentId 
											AND hierarchy.Path not like '%' + CAST(productFamilies.Id AS VARCHAR(36)) + '%'    
									WHERE 
										hierarchy.LEVEL < 50
								),

								ProductFamilies AS (
									SELECT 
										CoreProductInProductFamily.ProductFamilyId, 
										CoreProductInProductFamily.TenantId,
										CoreProducts.IsVisible,
										CoreProducts.IsPublic,
										CoreProducts.Id As ProductId
									FROM 
										CoreProductInProductFamily 
									LEFT JOIN 
										CoreProducts ON CoreProductInProductFamily.ProductId = CoreProducts.Id
								),

								ChildResult as (
									SELECT 
										Hierarchy.TenantId,
										Hierarchy.Id,
										Hierarchy.ParentId,
										--TODO: When product family visibility is managed, replace ‘ProductFamilies.IsVisible’ with this code
										--CAST((COALESCE(ProductFamilies.IsVisible, Hierarchy.IsVisible) & Hierarchy.IsVisible) AS bit) AS IsVisible,
										ProductFamilies.IsVisible,
										ProductFamilies.IsPublic, 
										ProductFamilies.ProductId, 
										Hierarchy.Path
									FROM 
										Hierarchy
									LEFT JOIN 
										ProductFamilies ON Hierarchy.Id = ProductFamilies.ProductFamilyId
									WHERE 
										Hierarchy.Id = ProductFamilies.ProductFamilyId
								),

								ParentResult as (
									SELECT 
										Hierarchy.TenantId,
										Hierarchy.Id,
										Hierarchy.ParentId,
										CASE WHEN ChildResult.ProductId IS NULL THEN CAST(0 AS bit) 
										ELSE CAST((ChildResult.IsVisible & Hierarchy.IsVisible) AS bit) 
										END AS IsVisible, 
										CASE WHEN ChildResult.ProductId IS NULL THEN CAST(0 AS bit) 
										ELSE ChildResult.IsPublic 
										END AS IsPublic, 
										ChildResult.ProductId,
										Hierarchy.Path 
									FROM 
										Hierarchy
									LEFT JOIN 
										ChildResult ON ChildResult.Path like '%' + CAST(Hierarchy.Id AS VARCHAR(36)) + '%'
								)
								SELECT * from ParentResult
								UNION
								select * from ChildResult