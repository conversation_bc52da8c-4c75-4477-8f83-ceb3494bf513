CREATE OR ALTER VIEW CoreProductFlattenHierarchy AS 
WITH PreProcessedData AS (
	SELECT DISTINCT
		product.TenantId,
		product.Id AS ProductId,
		product.IsVisible AS IsVisible,
		product.IsDeleted AS IsDeleted,
		CAST(
			CASE WHEN product.IsPublic = 1 OR equipment.ProductId IS NOT NULL
			THEN 1 
			ELSE 0 
			END AS BIT
		) AS IsPublic,
		equipment.CompanyId
	FROM
		CoreProducts product
	LEFT JOIN 
		CoreEquipments equipment ON product.Id = equipment.ProductId  AND equipment.IsDeleted = 0
),
Hierarchy AS (
    SELECT
        preProcessedData.TenantId AS TenantId,
        preProcessedData.ProductId AS ProductId,
        preProcessedData.IsPublic as IsPublic,
        preProcessedData.IsVisible as IsVisible,
        preProcessedData.IsDeleted as IsDeleted,
        preProcessedData.ProductId AS ComponentId,
        component.Code AS ProductCode,
        component.Code AS ComponentCode,
        preProcessedData.CompanyId AS CompanyId,
        0 AS Level,
        CAST('/' AS nvarchar(max)) AS Path
    FROM
        PreProcessedData preProcessedData
    JOIN
        CoreComponents component ON preProcessedData.ProductId = component.Id

    UNION ALL

    SELECT 
        hierarchy.TenantId AS TenantId,
        hierarchy.ProductId,
        hierarchy.IsPublic,
        hierarchy.IsVisible,
        (hierarchy.IsDeleted | bomline.IsDeleted  | component.IsDeleted) AS IsDeleted,
        bomline.ChildComponentId AS ComponentId,
        hierarchy.ProductCode AS ProductCode,
        component.Code AS ComponentCode,
        hierarchy.CompanyId,
        (hierarchy.Level + 1) AS Level,
        (hierarchy.Path + CAST(bomline.ParentAssemblyId AS nvarchar(36)) + '/')  AS Path
    FROM
        Hierarchy AS hierarchy
    JOIN
        CoreBomLines bomline ON hierarchy.ComponentId = bomline.ParentAssemblyId AND bomline.ChildComponentId <> bomline.ParentAssemblyId 
						    AND hierarchy.Path not like '%' + CAST(bomline.ChildComponentId AS VARCHAR(36)) + '%'    
    JOIN
        CoreComponents component ON bomline.ChildComponentId = component.Id
    WHERE hierarchy.LEVEL < 50 
    )
    SELECT * FROM Hierarchy