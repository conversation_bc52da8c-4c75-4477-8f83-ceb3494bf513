CREATE OR ALTER VIEW CoreProductFamilyFlattenHierarchy AS 
								WITH 
								Hierarchy AS (
									SELECT
										productFamilies.TenantId,
										productFamilies.Id,
										CAST('/' AS nvarchar(max)) AS Path,
										CAST(IsVisible AS bit) AS IsVisible,
										0 AS Level
									FROM
										CoreProductFamilies productFamilies 
									WHERE
										ParentId IS NULL AND IsDeleted = 0
									UNION ALL
									SELECT
										productFamilies.TenantId,
										productFamilies.Id,
										(hierarchy.Path + CAST(productFamilies.ParentId AS nvarchar(36)) + '/') AS Path,
										CAST((hierarchy.IsVisible & productFamilies.IsVisible) AS bit) AS IsVisible,
										hierarchy.Level + 1
									FROM 
										CoreProductFamilies productFamilies
									JOIN 
										Hierarchy AS hierarchy ON productFamilies.ParentId = hierarchy.Id AND productFamilies.Id <> productFamilies.ParentId 
														AND hierarchy.Path not like '%' + CAST(productFamilies.Id AS VARCHAR(36)) + '%'    
									WHERE 
										hierarchy.LEVEL < 50  AND productFamilies.IsDeleted = 0 
								),

								ProductFamilies AS (
									SELECT 
										CoreProductInProductFamily.ProductFamilyId, 
										CoreProductInProductFamily.TenantId,
										CAST(MAX(CASE WHEN CoreProducts.IsVisible = 1 THEN 1 ELSE 0 END) AS bit) AS IsVisible,
										CAST(MAX(CASE WHEN CoreProducts.IsPublic = 1 THEN 1 ELSE 0 END) AS bit) AS IsPublic
									FROM 
										CoreProductInProductFamily 
									LEFT JOIN 
										CoreProducts ON CoreProductInProductFamily.ProductId = CoreProducts.Id
									WHERE 
										CoreProductInProductFamily.IsDeleted = 0 AND CoreProducts.IsDeleted = 0
									GROUP BY 
										CoreProductInProductFamily.ProductFamilyId, CoreProductInProductFamily.TenantId
								),

								ChildResult as (
									SELECT 
										Hierarchy.TenantId,
										Hierarchy.Id,
										--TODO: When product family visibility is managed, replace �ProductFamilies.IsVisible� with this code
										--CAST((COALESCE(ProductFamilies.IsVisible, Hierarchy.IsVisible) & Hierarchy.IsVisible) AS bit) AS IsVisible,
										ProductFamilies.IsVisible,
										ProductFamilies.IsPublic, 
										Hierarchy.Path
									FROM 
										Hierarchy
									LEFT JOIN 
										ProductFamilies ON Hierarchy.Id = ProductFamilies.ProductFamilyId
									WHERE 
										Hierarchy.Id = ProductFamilies.ProductFamilyId
								),

								ParentResult as (
									SELECT 
										Hierarchy.TenantId,
										Hierarchy.Id,
										CAST((ChildResult.IsVisible & Hierarchy.IsVisible) AS bit) AS IsVisible,
										ChildResult.IsPublic, 
										Hierarchy.Path 
									FROM 
										Hierarchy
									JOIN 
										ChildResult ON ChildResult.Path like '%' + CAST(Hierarchy.Id AS VARCHAR(36)) + '%'
								)

								SELECT * from ParentResult
								UNION
								select * from ChildResult