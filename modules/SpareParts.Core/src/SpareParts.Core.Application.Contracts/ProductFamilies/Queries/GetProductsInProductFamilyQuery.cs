using SpareParts.AbpMediatR.Queries;
using SpareParts.Core.Attributes;
using SpareParts.Core.Components.Dtos.Products;
using SpareParts.Core.ProductFamilies.QueryFilters;
using System;
using Volo.Abp.Application.Dtos;

namespace SpareParts.Core.ProductFamilies.Queries;
public record GetProductsInProductFamilyQuery([ObjectId] Guid ProductFamilyId, ProductsInProductFamilyPaginationFilter Filter) : IQuery<PagedResultDto<ProductInProductFamilyDto>>;
