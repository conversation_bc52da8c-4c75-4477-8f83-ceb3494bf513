using SpareParts.AbpMediatR.Queries;
using SpareParts.Core.Attributes;
using SpareParts.Core.Equipments.Dtos;
using SpareParts.Core.ProductFamilies.QueryFilters;
using System;
using Volo.Abp.Application.Dtos;

namespace SpareParts.Core.ProductFamilies.Queries;
public record GetEquipmentsInProductFamilyQuery([ObjectId] Guid ProductFamilyId, EquipmentsInProductFamilyPaginationFilter Filter) : IQuery<PagedResultDto<EquipmentDto>>;
