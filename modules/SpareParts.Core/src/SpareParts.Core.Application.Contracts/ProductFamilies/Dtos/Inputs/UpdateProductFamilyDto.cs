using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using SpareParts.Common.Dtos;

namespace SpareParts.Core.ProductFamilies.Dtos.Inputs;

public record UpdateProductFamilyDto(
    List<CommonTranslationDto> Translations,
    [Range(1, int.MaxValue)] int Rank,
    bool IsVisible)
{
    public Guid? ImageId { get; set; }
    public Guid? ParentId { get; set; }
}