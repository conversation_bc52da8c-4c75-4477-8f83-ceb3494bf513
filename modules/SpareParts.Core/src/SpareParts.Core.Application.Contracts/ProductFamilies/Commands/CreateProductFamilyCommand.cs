using SpareParts.AbpMediatR.Commands;
using SpareParts.Core.ProductFamilies.Dtos;
using System;
using System.Collections.Generic;
using SpareParts.Common.Dtos;

namespace SpareParts.Core.ProductFamilies.Commands;

public record CreateProductFamilyCommand(List<CommonTranslationDto> Translations, bool IsVisible = false, int Rank = 1) : ICommand<ProductFamilyDto>
{
    public string? Code { get; set; }
    public Guid? ImageId { get; set; }
    public Guid? ParentId { get; set; }
}