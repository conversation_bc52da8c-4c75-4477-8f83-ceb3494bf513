using System;
using System.Collections.Generic;
using SpareParts.Common.Dtos;

namespace SpareParts.Core.Components.Dtos;
public class ComponentDto
{
    public Guid Id { get; set; } = Guid.Empty;
    public string Code { get; set; } = default!;
    public string Type { get; set; } = default!;
    public List<CommonTranslationDto> Translations { get; set; } = default!;
    public List<CommonOnlineTranslationDto> OnlineTranslations { get; set; } = default!;
    public Guid ImageId { get; set; } = Guid.Empty;
    public int ChildrenCount { get; set; } = 0;
}
