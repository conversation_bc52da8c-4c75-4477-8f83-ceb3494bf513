using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;

namespace SpareParts.Core.Components.Dtos;

public class ComponentOnlineTranslationInputDto
{
    [MinLength(2)]
    public string? Label { get; init; }
    public string? Description { get; init; }

    [SetsRequiredMembers]
    public ComponentOnlineTranslationInputDto(string? label, string? description = null)
    {
        Label = label;
        Description = description;
    }

    public ComponentOnlineTranslationInputDto()
    {
    }
}