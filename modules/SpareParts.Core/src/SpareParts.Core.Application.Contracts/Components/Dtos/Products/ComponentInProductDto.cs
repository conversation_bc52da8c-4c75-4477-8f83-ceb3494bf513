using System;
using System.Collections.Generic;

namespace SpareParts.Core.Components.Dtos.Products;

public class ComponentInProductDto
{
    public required Guid ComponentId { get; set; }
    public required List<ComponentInProductContextDto> Contexts { get; set; }
}

public class ComponentInProductContextDto
{
    public required string Path { get; set; }
    public required int Level { get; set; }
}