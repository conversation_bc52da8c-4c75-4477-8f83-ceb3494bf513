using System;
using System.Collections.Generic;
using SpareParts.Common.Dtos;
using Volo.Abp.Application.Dtos;

namespace SpareParts.Core.Components.Dtos.Products;

public class ProductDto : AuditedEntityDto<Guid>
{
    public required string Code { get; set; }
    public required List<CommonTranslationDto> Translations { get; set; }
    public required List<CommonOnlineTranslationDto> OnlineTranslations { get; set; }
    public required Guid ImageId { get; set; }
    public required bool IsVisible { get; set; }
    public required bool IsPublic { get; set; }
    public required bool IsInProductFamily { get; set; }
    public required int ChildCount { get; set; }
}