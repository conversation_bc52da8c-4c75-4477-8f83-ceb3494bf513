using SpareParts.AbpMediatR.Commands;
using SpareParts.Core.Drawings.Dtos;
using SpareParts.Core.Drawings.Dtos.Inputs;
using SpareParts.Core.Enums;
using System;

namespace SpareParts.Core.Drawings.Commands;

public record CreateDrawingMappingCommand(<PERSON><PERSON> DrawingId, Caller<PERSON><PERSON>in CallerOrigin, CreateUpdateDrawingMappingDto CreateUpdateDrawingMappingDto)
    : ICommand<DrawingMappingDto>;