using SpareParts.Core.Localization;
using System.Diagnostics.CodeAnalysis;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Localization;
using Volo.Abp.MultiTenancy;

namespace SpareParts.Core.Permissions;

[ExcludeFromCodeCoverage]
public class CorePermissionDefinitionProvider : PermissionDefinitionProvider
{
    private const string PermissionCreateKey = "Permission:Create";
    private const string PermissionEditKey = "Permission:Edit";
    private const string PermissionSearchKey = "Permission:Search";
    private const string PermissionDeleteKey = "Permission:Delete";
    private const string PermissionDeleteTranslationKey = "Permission:DeleteTranslation";
    private const string PermissionAllKey = "Permission:All";

    public override void Define(IPermissionDefinitionContext context)
    {
        PermissionGroupDefinition permissionGroup = context.AddGroup(CorePermissions.GroupName, L("Permission:Core"));
        permissionGroup.AddPermission(CorePermissions.ShowHidden, L("Permission:ShowHidden"), MultiTenancySides.Tenant);
        AddProductFamiliesPermissions(permissionGroup);
        AddComponentsPermissions(permissionGroup);
        AddResourcesPermissions(permissionGroup);
        AddProductsPermissions(permissionGroup);
        AddDocumentsPermissions(permissionGroup);
        AddEquipmentsPermissions(permissionGroup);
        AddDemoDataPermissions(permissionGroup);
        AddDocumentCategoryPermissions(permissionGroup);
        AddBomDocumentsPermissions(permissionGroup);
        AddDataImportMonitoringPermissions(permissionGroup);
        AddDrawingsPermissions(permissionGroup);
    }

    private static void AddBomDocumentsPermissions(PermissionGroupDefinition permissionGroup)
    {
        PermissionDefinition permissionDefinition = permissionGroup.AddPermission(CorePermissions.BomDocuments.Default, L("Permission:BomDocuments"), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CorePermissions.BomDocuments.Create, L(PermissionCreateKey), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CorePermissions.BomDocuments.Delete, L(PermissionDeleteKey), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CorePermissions.BomDocuments.All, L(PermissionAllKey), MultiTenancySides.Tenant);
    }

    private static void AddDocumentCategoryPermissions(PermissionGroupDefinition permissionGroup)
    {
        PermissionDefinition permissionDefinition = permissionGroup.AddPermission(CorePermissions.DocumentCategories.Default, L("Permission:DocumentCategories"), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CorePermissions.DocumentCategories.Create, L(PermissionCreateKey), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CorePermissions.DocumentCategories.Edit, L(PermissionEditKey), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CorePermissions.DocumentCategories.Delete, L(PermissionDeleteKey), MultiTenancySides.Tenant);
    }

    private static void AddDemoDataPermissions(PermissionGroupDefinition permissionGroup)
    {
        PermissionDefinition permissionDefinition = permissionGroup.AddPermission(CorePermissions.DemoData.Default, L("Permission:DemoData"), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CorePermissions.DemoData.Import, L("Permission:ImportDemoData"), MultiTenancySides.Tenant);
    }

    private static void AddDataImportMonitoringPermissions(PermissionGroupDefinition permissionGroup)
    {
        permissionGroup.AddPermission(CorePermissions.DataImportMonitoring.Default, L("Permission:DataImportMonitoring"), MultiTenancySides.Tenant);
    }

    private static void AddProductFamiliesPermissions(PermissionGroupDefinition permissionGroup)
    {
        PermissionDefinition permissionDefinition = permissionGroup.AddPermission(CorePermissions.ProductFamilies.Default, L("Permission:ProductFamilies"), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CorePermissions.ProductFamilies.Create, L(PermissionCreateKey), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CorePermissions.ProductFamilies.Edit, L(PermissionEditKey), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CorePermissions.ProductFamilies.Delete, L(PermissionDeleteKey), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CorePermissions.ProductFamilies.LinkProduct, L("Permission:LinkProduct"), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CorePermissions.ProductFamilies.UnLinkProduct, L("Permission:UnLinkProduct"), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CorePermissions.ProductFamilies.DeleteTranslation, L("Permission:DeleteTranslation"), MultiTenancySides.Tenant);
    }

    private static void AddComponentsPermissions(PermissionGroupDefinition permissionGroup)
    {
        PermissionDefinition permissionDefinition = permissionGroup.AddPermission(CorePermissions.Components.Default, L("Permission:Components"), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CorePermissions.Components.Edit, L(PermissionEditKey), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CorePermissions.Components.DeleteTranslation, L("Permission:DeleteTranslation"), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CorePermissions.Components.Stats, L("Permission:Stats"), MultiTenancySides.Tenant);
    }

    private static void AddProductsPermissions(PermissionGroupDefinition permissionGroup)
    {
        PermissionDefinition permissionDefinition = permissionGroup.AddPermission(CorePermissions.Products.Default, L("Permission:Products"), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CorePermissions.Products.Edit, L(PermissionEditKey), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CorePermissions.Products.EditVisibility, L("Permission:EditVisibility"), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CorePermissions.Products.Delete, L(PermissionDeleteKey), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CorePermissions.Products.Stats, L("Permission:Stats"), MultiTenancySides.Tenant);
    }

    private static void AddEquipmentsPermissions(PermissionGroupDefinition permissionGroup)
    {
        PermissionDefinition permissionDefinition = permissionGroup.AddPermission(CorePermissions.Equipments.Default, L("Permission:Equipments"), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CorePermissions.Equipments.Create, L(PermissionCreateKey), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CorePermissions.Equipments.Edit, L(PermissionEditKey), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CorePermissions.Equipments.Delete, L(PermissionDeleteKey), MultiTenancySides.Tenant);
    }

    private static void AddResourcesPermissions(PermissionGroupDefinition permissionGroup)
    {
        PermissionDefinition permissionDefinition = permissionGroup.AddPermission(CorePermissions.Resources.Default, L("Permission:Resources"), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CorePermissions.Resources.Create, L(PermissionCreateKey), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CorePermissions.Resources.Edit, L(PermissionEditKey), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CorePermissions.Resources.Delete, L(PermissionDeleteKey), MultiTenancySides.Tenant);
    }

    private static void AddDocumentsPermissions(PermissionGroupDefinition permissionGroup)
    {
        PermissionDefinition permissionDefinition = permissionGroup.AddPermission(CorePermissions.Documents.Default, L("Permission:Documents"), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CorePermissions.Documents.Create, L(PermissionCreateKey), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CorePermissions.Documents.Edit, L(PermissionEditKey), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CorePermissions.Documents.Search, L(PermissionSearchKey), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CorePermissions.Documents.Delete, L(PermissionDeleteKey), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CorePermissions.Documents.DeleteTranslation, L(PermissionDeleteTranslationKey), MultiTenancySides.Tenant);
    }

    private static void AddDrawingsPermissions(PermissionGroupDefinition permissionGroup)
    {
        PermissionDefinition permissionDefinition = permissionGroup.AddPermission(CorePermissions.Drawings.Default, L("Permission:Drawings"), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CorePermissions.Drawings.Edit, L(PermissionEditKey), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CorePermissions.Drawings.Delete, L(PermissionDeleteKey), MultiTenancySides.Tenant);
    }

    private static LocalizableString L(string name)
    {
        return LocalizableString.Create<CoreResource>(name);
    }
}