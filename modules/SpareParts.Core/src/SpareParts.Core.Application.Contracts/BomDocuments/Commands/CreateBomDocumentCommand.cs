using SpareParts.AbpMediatR.Commands;
using SpareParts.Core.BomDocuments.Dtos;
using SpareParts.Core.Enums;
using System;

namespace SpareParts.Core.BomDocuments.Commands;

public record CreateBomDocumentCommand(Guid MainEntityId, BomDocumentContext Context, string Language) : ICommand<BomDocumentDto>
{
    public Guid? AssemblyId { get; set; }

    public void Deconstruct(out Guid mainEntityId, out BomDocumentContext context, out Guid? assemblyId, out string language)
    {
        mainEntityId = MainEntityId;
        context = Context;
        assemblyId = AssemblyId;
        language = Language;
    }
}