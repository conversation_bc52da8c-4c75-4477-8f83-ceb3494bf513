using System;
using System.Collections.Generic;
using SpareParts.Common.Dtos;
using Volo.Abp.Application.Dtos;

namespace SpareParts.Core.Documents.Dtos;

public class ResultUpsertDocumentDto : AuditedEntityDto<Guid>
{
    public required DocumentResourceDto<string> Resource { get; set; }
    public required List<CommonTranslationDto> Translations { get; set; }
    public required string Filename { get; set; }
    public required bool IsPublic { get; set; }
    public required HashSet<string> Languages { get; set; }
}