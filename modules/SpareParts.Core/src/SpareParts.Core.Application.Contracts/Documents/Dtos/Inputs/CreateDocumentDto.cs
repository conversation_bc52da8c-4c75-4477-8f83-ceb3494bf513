using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using SpareParts.Common.Dtos;

namespace SpareParts.Core.Documents.Dtos.Inputs;

public record CreateDocumentDto
{
    [Required]
    public required UpsertDocumentResourceDto Resource { get; init; }

    [Required, MinLength(1)]
    public required List<CommonTranslationDto> Translations { get; init; }
    public bool? IsPublic { get; set; }
    public HashSet<string>? Languages { get; set; }
}