using System;
using System.Collections.Generic;
using SpareParts.AbpMediatR.Commands;
using SpareParts.Common.Dtos;
using SpareParts.Core.Documents.Dtos;

namespace SpareParts.Core.Documents.Commands;

public abstract record UpdateDocumentCommandBase<T> : ICommand<ResultUpsertDocumentDto>
{
    public required Guid DocumentId { get; init; }

    public required DocumentResourceDto<T> Resource { get; init; }

    public required List<CommonTranslationDto> Translations { get; set; }
    public required bool? IsPublic { get; init; }
    public required HashSet<string>? Languages { get; init; }
}