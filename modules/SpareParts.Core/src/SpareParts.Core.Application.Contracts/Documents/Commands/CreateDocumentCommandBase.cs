using System.Collections.Generic;
using SpareParts.AbpMediatR.Commands;
using SpareParts.Common.Dtos;
using SpareParts.Core.Documents.Dtos;

namespace SpareParts.Core.Documents.Commands;

public abstract record CreateDocumentCommandBase<T>(
    DocumentResourceDto<T> Resource,
    List<CommonTranslationDto> Translations,
    HashSet<string> Languages,
    bool IsPublic = true) : ICommand<DocumentDto>;