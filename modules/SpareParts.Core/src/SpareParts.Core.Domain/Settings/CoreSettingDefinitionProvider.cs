using JetBrains.Annotations;
using Volo.Abp.Settings;

namespace SpareParts.Core.Settings;

[UsedImplicitly]
public class CoreSettingDefinitionProvider : SettingDefinitionProvider
{
    public override void Define(ISettingDefinitionContext context)
    {
        context.Add(
            new SettingDefinition(CoreSettings.ComponentDefaultImageId, isVisibleToClients: true)
        );
        context.Add(
            new SettingDefinition(CoreSettings.ProductFamilyDefaultImageId, isVisibleToClients: true)
        );
    }
}