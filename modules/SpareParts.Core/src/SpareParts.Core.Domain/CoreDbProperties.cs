namespace SpareParts.Core;

public static class CoreDbProperties
{
    public static string DbTablePrefix { get; set; } = "Core";

    public static string? DbSchema { get; set; } = null;

    public const string ConnectionStringName = "Core";

    public static class ComponentTableProperties
    {
        //TODO: define fields length
        public static int TypeMaxLength => 15;
    }

    public static class DocumentTableProperties
    {
        public static int TypeMaxLength => 15;
        public static int LinkMaxLength => 2083;
    }

    public static class DrawingMappingTableProperties
    {
        public static int IndexMaxLength => 25;
    }

    public static class DocumentCategoryTableProperties
    {
        public static int ColorLength => 7;
    }

    public static class DrawingTableProperties
    {
        public static int OriginMaxLength => 20;
    }
}