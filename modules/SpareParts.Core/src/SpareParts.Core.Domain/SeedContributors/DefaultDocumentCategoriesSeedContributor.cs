using SpareParts.Core.DomainServices.DocumentCategories;
using SpareParts.Core.Entities.Resources;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.MultiTenancy;

namespace SpareParts.Core.SeedContributors;

public class DefaultDocumentCategoriesSeedContributor : IDataSeedContributor, ITransientDependency
{
    public IAbpLazyServiceProvider LazyServiceProvider { get; set; } = null!;
    private ICurrentTenant CurrentTenant => LazyServiceProvider.LazyGetRequiredService<ICurrentTenant>();
    private IRepository<DocumentCategory, Guid> DocumentCategoryRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<DocumentCategory, Guid>>();
    private DocumentCategoryDomainService DocumentCategoryDomainService => LazyServiceProvider.LazyGetRequiredService<DocumentCategoryDomainService>();

    private const string InstallationColor = "#64BEFF";
    private const string MaintenanceColor = "#FFC702";
    private const string SecurityColor = "#FF763C";
    private const string StandardColor = "#3BA560";

    private const string InstallationLabelFr = "Installation";
    private const string MaintenanceLabelFr = "Maintenance";
    private const string SecurityLabelFr = "Sécurité";
    private const string StandardLabelFr = "Norme";

    private const string InstallationLabelEn = "Installation";
    private const string MaintenanceLabelEn = "Maintenance";
    private const string SecurityLabelEn = "Security";
    private const string StandardLabelEn = "Standard";

    private const string InstallationLabelDe = "Installation";
    private const string MaintenanceLabelDe = "Wartung";
    private const string SecurityLabelDe = "Sicherheit";
    private const string StandardLabelDe = "Norm";

    public async Task SeedAsync(DataSeedContext context)
    {
        if (context.TenantId.HasValue)
        {
            using (CurrentTenant.Change(context.TenantId.Value))
            {
                long count = await DocumentCategoryRepository.GetCountAsync();
                if (count == 0)
                {
                    List<DocumentCategory> documentCategories = [
                        await DocumentCategoryDomainService.CreateAsync(InstallationColor, [
                        new DocumentCategoryTranslation("en", InstallationLabelEn),
                        new DocumentCategoryTranslation("fr", InstallationLabelFr),
                        new DocumentCategoryTranslation("de", InstallationLabelDe)
                    ]),
                    await DocumentCategoryDomainService.CreateAsync(MaintenanceColor, [
                        new DocumentCategoryTranslation("en", MaintenanceLabelEn),
                        new DocumentCategoryTranslation("fr", MaintenanceLabelFr),
                        new DocumentCategoryTranslation("de", MaintenanceLabelDe)
                    ]),
                    await DocumentCategoryDomainService.CreateAsync(SecurityColor, [
                        new DocumentCategoryTranslation("en", SecurityLabelEn),
                        new DocumentCategoryTranslation("fr", SecurityLabelFr),
                        new DocumentCategoryTranslation("de", SecurityLabelDe)
                    ]),
                    await DocumentCategoryDomainService.CreateAsync(StandardColor, [
                        new DocumentCategoryTranslation("en", StandardLabelEn),
                        new DocumentCategoryTranslation("fr", StandardLabelFr),
                        new DocumentCategoryTranslation("de", StandardLabelDe)
                    ])
                        ];
                    await DocumentCategoryRepository.InsertManyAsync(documentCategories);
                }
            }
        }
    }
}