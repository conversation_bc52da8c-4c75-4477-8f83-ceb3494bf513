using FluentValidation;
using Microsoft.Extensions.DependencyInjection;
using SpareParts.Common.Resources;
using SpareParts.Core.Containers;
using SpareParts.Core.Entities.Resources;
using SpareParts.Core.Validators;
using System;
using Volo.Abp.Content;

namespace SpareParts.Core.DomainServices.Resources;

public class FileManager : AbstractResourceManager<FilesContainer, Resource>
{
    protected override AbstractValidator<RemoteStreamContent> ResourceExtensionValidator => LazyServiceProvider.GetRequiredService<FileValidator>();
    protected override Resource CreateEntity(Guid id, string filename, string hash)
    {
        return new Resource(id, filename, hash);
    }
}