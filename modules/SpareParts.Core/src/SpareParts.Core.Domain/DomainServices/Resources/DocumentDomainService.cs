using SpareParts.Core.Entities.Resources;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SpareParts.Common.Translations;
using Volo.Abp.Domain.Services;

namespace SpareParts.Core.DomainServices.Resources;

public class DocumentDomainService : DomainService
{
    private TranslationValidator TranslationValidator => LazyServiceProvider.LazyGetRequiredService<TranslationValidator>();

    public virtual async Task<Document> CreateAsync(Resource resource, List<DocumentTranslation> translations,
        HashSet<string> languages, bool isPublic = true)
    {
        await TranslationValidator.CheckTranslationsForUpsertAsync(translations.Select(t => t.Language).ToList());
        return new Document(GuidGenerator.Create(), resource, translations, languages, isPublic);
    }

    public virtual async Task<Document> CreateAsync(string link, List<DocumentTranslation> translations,
        HashSet<string> languages, bool isPublic = true)
    {
        await TranslationValidator.CheckTranslationsForUpsertAsync(translations.Select(t => t.Language).ToList());
        return new Document(GuidGenerator.Create(), link, translations, languages, isPublic);
    }
}