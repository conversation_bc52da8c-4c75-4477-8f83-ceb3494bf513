using FluentValidation;
using Microsoft.Extensions.DependencyInjection;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Formats.Webp;
using SixLabors.ImageSharp.Processing;
using SpareParts.Common;
using SpareParts.Common.Resources;
using SpareParts.Common.Validators;
using SpareParts.Core.Entities.Resources;
using SpareParts.Core.Enums;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Content;
using SpareParts.Core.Containers;

namespace SpareParts.Core.DomainServices.Resources;

public class ImageManager : AbstractResourceManager<ImagesContainer, Resource>
{
    protected override AbstractValidator<RemoteStreamContent> ResourceExtensionValidator => LazyServiceProvider.GetRequiredService<ImageValidator>();

    private IBackgroundJobManager BackgroundJobManager => LazyServiceProvider.LazyGetRequiredService<IBackgroundJobManager>();

    private readonly Dictionary<ImageSize, int> _widths = new()
    {
        {ImageSize.Xs, 150},
        {ImageSize.S, 400},
        {ImageSize.M, 800}
    };

    protected override Resource CreateEntity(Guid id, string filename, string hash)
    {
        return new Resource(id, filename, hash);
    }

    public override async Task<Resource> CreateAsync(RemoteStreamContent content, CancellationToken cancellationToken = default)
    {
        Resource resource = await base.CreateAsync(content, cancellationToken);
        await BackgroundJobManager.EnqueueAsync(new ThumbnailsGeneratorJobArgs(resource.Id, resource.TenantId!.Value), BackgroundJobPriority.High);
        return resource;
    }

    public override async Task<bool> UpdateAsync(Resource resource, RemoteStreamContent content, CancellationToken cancellationToken = default)
    {
        bool updatedStream = await base.UpdateAsync(resource, content, cancellationToken);
        if (updatedStream)
        {
            await BackgroundJobManager.EnqueueAsync(new ThumbnailsGeneratorJobArgs(resource.Id, resource.TenantId!.Value), BackgroundJobPriority.High);
        }
        return updatedStream;
    }

    public override async Task DeleteBlobAsync(Guid resourceId, CancellationToken cancellationToken = default)
    {
        await base.DeleteBlobAsync(resourceId, cancellationToken);
        await Task.WhenAll(_widths.Keys.Select(size => DeleteBlobAsync(GetBlobName(resourceId, size), cancellationToken)));
    }

    internal async Task ResizeAndSaveImageForAllSizesAsync(Stream stream, Guid resourceId, CancellationToken cancellationToken = default)
    {
        stream.ResetPositionIfSeekable();
        try
        {
            using Image originalImage = await Image.LoadAsync(stream, cancellationToken);
            List<Task> resizeTasks = [];
            resizeTasks.AddRange(_widths.Keys.Select(size =>
                ResizeAndSaveImageAsync(originalImage, resourceId, size, cancellationToken)));
            await Task.WhenAll(resizeTasks);
        }
        catch(Exception ex)
        {
            Logger.LogException(ex);
        }
    }

    private async Task ResizeAndSaveImageAsync(Image originalImage, Guid resourceId, ImageSize size, CancellationToken cancellationToken)
    {
        if (originalImage.Width > _widths[size])
        {
            using Image resizedImage = originalImage.Clone(ctx => ctx.Resize(new ResizeOptions { Size = new Size { Width = _widths[size] }, Mode = ResizeMode.BoxPad }));
            using MemoryStream memoryStream = new();
            await resizedImage.SaveAsync(memoryStream, new WebpEncoder(), cancellationToken);
            memoryStream.ResetPositionIfSeekable();
            await SaveBlobAsync(GetBlobName(resourceId, size), memoryStream, cancellationToken);
        }
    }

    private static string GetBlobName(Guid resourceId, ImageSize size)
    {
        return $"{GetBlobName(resourceId)}_{size}";
    }

    public async Task<Stream> GetBlobAsync(Guid resourceId, ImageSize? size, CancellationToken cancellationToken = default)
    {
        if (size.HasValue)
        {
            Stream? resizedImage = await GetBlobOrNullAsync(GetBlobName(resourceId, size.Value), cancellationToken);
            if (resizedImage != null)
            {
                return resizedImage;
            }
        }

        return await GetBlobAsync(resourceId, cancellationToken);
    }
}