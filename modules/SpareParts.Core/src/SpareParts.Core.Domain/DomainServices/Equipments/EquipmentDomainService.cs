using SpareParts.Common.Companies;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Equipments;
using System;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Domain.ChangeTracking;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;

namespace SpareParts.Core.DomainServices.Equipments;

[DisableEntityChangeTracking]
public class EquipmentDomainService : DomainService
{
    private IRepository<Equipment, Guid> EquipmentRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Equipment, Guid>>();
    private IRepository<Product, Guid> ProductRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Product, Guid>>();
    private IRepository<Company, Guid> CompanyRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Company, Guid>>();

    public virtual async Task<Equipment> CreateAsync(string serialNumber, Guid productId, Guid? companyId = null, CancellationToken cancellationToken = default)
    {
        await CheckSerialNumber(serialNumber, cancellationToken: cancellationToken);
        await CheckProductId(productId, cancellationToken: cancellationToken);
        await CheckCompanyId(companyId, cancellationToken: cancellationToken);

        return new Equipment(GuidGenerator.Create(), serialNumber, productId, companyId);
    }

    public virtual async Task UpdateProductIdAsync(Equipment equipment, Guid productId, CancellationToken cancellationToken = default)
    {
        await CheckProductId(productId, cancellationToken: cancellationToken);
        equipment.ChangeProductId(productId);
    }

    public virtual async Task UpdateCompanyIdAsync(Equipment equipment, Guid? companyId, CancellationToken cancellationToken = default)
    {
        await CheckCompanyId(companyId, cancellationToken: cancellationToken);
        equipment.ChangeCompanyId(companyId);
    }

    private async Task CheckSerialNumber(string serialNumber, CancellationToken cancellationToken = default)
    {
        bool serialNumberExist = await EquipmentRepository.AnyAsync(x => x.SerialNumber == serialNumber, cancellationToken: cancellationToken);
        if (serialNumberExist)
        {
            throw new BusinessException("core:duplicate-serial-number", "serial number already exist");
        }
    }

    private async Task CheckCompanyId(Guid? companyId, CancellationToken cancellationToken = default)
    {
        if (companyId.HasValue)
        {
            Company? company = await CompanyRepository.FindAsync(companyId.Value, cancellationToken: cancellationToken);
            if (company is null)
            {
                throw new BusinessException("core:entity-not-found", "company does not exist");
            }
        }
    }

    private async Task CheckProductId(Guid productId, CancellationToken cancellationToken = default)
    {
        Product? product = await ProductRepository.FindAsync(productId, cancellationToken: cancellationToken);
        if (product is null)
        {
            throw new BusinessException("core:entity-not-found", "product does not exist");
        }
    }
}