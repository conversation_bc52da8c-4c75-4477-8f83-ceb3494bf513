using System;
using System.Threading;
using System.Threading.Tasks;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Enums;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;

namespace SpareParts.Core.DomainServices.Components;

public class ProductDomainService : DomainService
{
    private IRepository<Component, Guid> ComponentRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Component, Guid>>();

    public virtual async Task<Product> CreateAsync(Guid assemblyId, CancellationToken cancellationToken = default)
    {
        Component assembly = await ComponentRepository.GetAsync(assemblyId, false, cancellationToken);
        if (assembly.Type != ComponentType.Assembly)
        {
            throw new EntityNotFoundException($"There is no such an entity. Entity type: Assembly, id: {assemblyId}");
        }
        return new Product(assembly);
    }
}