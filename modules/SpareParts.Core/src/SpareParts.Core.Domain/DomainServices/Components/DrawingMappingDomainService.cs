using SpareParts.Core.Entities.Boms;
using SpareParts.Core.Entities.Components;
using System;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;

namespace SpareParts.Core.DomainServices.Components;

public class DrawingMappingDomainService : DomainService
{
    private IRepository<DrawingMapping> DrawingMappingRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<DrawingMapping>>();
    protected IRepository<Drawing, Guid> DrawingRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Drawing, Guid>>();
    protected IRepository<BomLine> BomLineRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<BomLine>>();

    public async Task<DrawingMapping> CreateAsync(Guid drawingId, string index, Guid componentId, CancellationToken cancellationToken = default)
    {
        Drawing drawing = await DrawingRepository.GetAsync(drawingId, cancellationToken: cancellationToken);

        await ValidateUniqueIndexAndComponentAsync(drawing.Id, index, componentId, cancellationToken);

        await ValidateBomLineExistsAsync(componentId, drawing.AssemblyId, cancellationToken);

        DrawingMapping drawingMapping = new(GuidGenerator.Create(), drawing.Id, index, componentId);
        return drawingMapping;
    }

    public async Task ChangeComponentIdAsync(Guid assemblyId, DrawingMapping drawingMapping, Guid componentId, CancellationToken cancellationToken = default)
    {
        await ValidateUniqueComponentAsync(drawingMapping.DrawingId, componentId, cancellationToken);
        await ValidateBomLineExistsAsync(componentId, assemblyId, cancellationToken);

        drawingMapping.ChangeComponentId(componentId);
    }

    public async Task ChangeIndexAsync(DrawingMapping drawingMapping, string index, CancellationToken cancellationToken = default)
    {
        await ValidateUniqueIndexAsync(drawingMapping.DrawingId, index, cancellationToken);

        drawingMapping.ChangeIndex(index);
    }

    private async Task ValidateBomLineExistsAsync(Guid componentId, Guid assemblyId, CancellationToken cancellationToken)
    {
        bool hasBomLine = await BomLineRepository.AnyAsync(x =>
            x.ParentAssemblyId == assemblyId &&
            x.ChildComponentId == componentId, cancellationToken: cancellationToken);
        if (!hasBomLine)
        {
            throw new BusinessException("400", "This component is not referenced in the BOM");
        }
    }

    private async Task ValidateUniqueIndexAndComponentAsync(Guid drawingId, string index, Guid componentId, CancellationToken cancellationToken)
    {
        bool hasDuplicate = await DrawingMappingRepository.AnyAsync(x =>
            x.DrawingId == drawingId &&
            (x.Index == index || x.ComponentId == componentId), cancellationToken: cancellationToken);
        if (hasDuplicate)
        {
            throw new BusinessException("400", "You cannot have the same index or component several times");
        }
    }

    private async Task ValidateUniqueComponentAsync(Guid drawingId, Guid componentId, CancellationToken cancellationToken)
    {
        bool hasDuplicate = await DrawingMappingRepository.AnyAsync(x =>
            x.DrawingId == drawingId && x.ComponentId == componentId, cancellationToken: cancellationToken);
        if (hasDuplicate)
        {
            throw new BusinessException("400", "You cannot have the same component several times");
        }
    }

    private async Task ValidateUniqueIndexAsync(Guid drawingId, string index, CancellationToken cancellationToken)
    {
        bool hasDuplicate = await DrawingMappingRepository.AnyAsync(x =>
            x.DrawingId == drawingId && x.Index == index, cancellationToken: cancellationToken);
        if (hasDuplicate)
        {
            throw new BusinessException("400", "You cannot have the same index several times");
        }
    }
}