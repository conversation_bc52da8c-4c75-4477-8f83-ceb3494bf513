using SpareParts.Core.Entities.Boms;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;

namespace SpareParts.Core.DomainServices.Components;

public class BomLineDomainService : DomainService
{
    private IReadOnlyRepository<BomLine> BomLineRepository => LazyServiceProvider.LazyGetRequiredService<IReadOnlyRepository<BomLine>>();
    private IReadOnlyRepository<Component, Guid> ComponentRepository => LazyServiceProvider.LazyGetRequiredService<IReadOnlyRepository<Component, Guid>>();
    protected IRepository<BomPath> BomPathRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<BomPath>>();
    protected IRepository<Product, Guid> ProductRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Product, Guid>>();

    public virtual async Task<BomLine> CreateAsync(Guid parentAssemblyId, Guid childComponentId, int rank, int quantity, CancellationToken cancellationToken = default)
    {
        BomLine bomline = new(parentAssemblyId, childComponentId, rank, quantity);

        await ValidateBomLineAsync(parentAssemblyId, childComponentId, cancellationToken);

        return bomline;
    }

    public async Task ValidateBomLineAsync(Guid parentAssemblyId, Guid childComponentId, CancellationToken cancellationToken)
    {
        if (await BomLineRepository.AnyAsync(bl =>
                bl.ChildComponentId.Equals(parentAssemblyId) &&
                bl.ParentAssemblyId.Equals(childComponentId), cancellationToken: cancellationToken))
        {
            string errorMessage = $"Cyclic reference detected in the BOM tree: Component {childComponentId} references itself through the following levels: {childComponentId}/{parentAssemblyId}/{childComponentId}.";
            throw new BusinessException("core:detected-cycle", errorMessage);
        }

        Component? parentComponent = await ComponentRepository.FindAsync(parentAssemblyId, false, cancellationToken);
        if (parentComponent is { Type: ComponentType.Part })
        {
            throw new BusinessException("core:part-as-a-parent", $"part with code {parentComponent.Code} can't be a parent");
        }

        List<BomPath> bomPaths = await BomPathRepository.GetListAsync(bp => bp.FinalChildComponentId.Equals(parentAssemblyId), includeDetails: false, cancellationToken: cancellationToken);
        foreach (BomPath bomPath in bomPaths)
        {
            string newFullPath = bomPath.Path + '/' + childComponentId;
            HashSet<Guid> seen = [];
            if (newFullPath.Split('/').Select(Guid.Parse).All(segment => seen.Add(segment)))
            {
                continue;
            }

            string errorMessage = $"Cyclic reference detected in the BOM tree: Component {childComponentId} references itself through the following levels: {newFullPath}.";
            throw new BusinessException("core:detected-cycle", errorMessage);
        }
    }
}