using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Resources;
using SpareParts.Core.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;

namespace SpareParts.Core.DomainServices.Components;

public class DrawingDomainService : DomainService
{
    private IRepository<Drawing, Guid> DrawingRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Drawing, Guid>>();

    public Drawing Create(Guid assemblyId, double rank, Resource file, Resource image, Dictionary<string, Guid> componentIdsByIndex, DrawingOrigin origin)
    {
        Guid id = GuidGenerator.Create();
        List<DrawingMapping> drawingMappings = (from componentIdByIndex in componentIdsByIndex
                                                select new DrawingMapping(GuidGenerator.Create(), id, componentIdByIndex.Key, componentIdByIndex.Value)).ToList();
        return new Drawing(id, assemblyId, rank, file, image, drawingMappings, origin);
    }

    public static Drawing ChangeToMain(Drawing newDrawing, Drawing oldMainDrawing)
    {
        if (oldMainDrawing.AssemblyId != newDrawing.AssemblyId)
        {
            throw new BusinessException("400", "The old main drawing must belong to the same assembly as the new drawing.");
        }

        if (!oldMainDrawing.IsMain)
        {
            throw new BusinessException("400", "The old drawing is not a main drawing.");
        }

        oldMainDrawing.SetAsMain(false);
        newDrawing.SetAsMain(true);

        return newDrawing;
    }

    public async Task SetAsMainAsync(Drawing drawing, CancellationToken cancellationToken = default)
    {
        await CheckIfAnyMainDrawingExists(drawing.AssemblyId, cancellationToken);
        drawing.SetAsMain(true);
    }

    private async Task CheckIfAnyMainDrawingExists(Guid assemblyId, CancellationToken cancellationToken)
    {
        bool hasMainDrawing = await DrawingRepository.AnyAsync(x => x.AssemblyId == assemblyId && x.IsMain, cancellationToken: cancellationToken);
        if (hasMainDrawing)
        {
            throw new BusinessException("400", "There is already a main drawing for this assembly.");
        }
    }
}