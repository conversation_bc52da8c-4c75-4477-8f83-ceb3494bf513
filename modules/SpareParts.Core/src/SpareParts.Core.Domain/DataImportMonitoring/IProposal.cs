using System.Collections.Generic;

namespace SpareParts.Core.DataImportMonitoring;

public interface IProposal
{
    ProposalStatus Status { get; set; }
    string? Error { get; set; }
    List<string> Warnings { get; set; }

    void SetStatus(ProposalStatus status)
    {
        Status = status;
    }

    void SetError(string error)
    {
        Error = error;
    }

    void AddWarning(string warning)
    {
        Warnings.Add(warning);
    }
}