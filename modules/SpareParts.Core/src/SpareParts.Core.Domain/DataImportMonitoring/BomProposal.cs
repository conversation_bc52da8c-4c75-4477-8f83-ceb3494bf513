using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;

namespace SpareParts.Core.DataImportMonitoring;

[ExcludeFromCodeCoverage]
public class BomProposal : ProposalBase
{
    public BomProposal(string externalProposalId, Guid masterProposalId, string parentAssemblyCode, Dictionary<string, int> lines) : base(externalProposalId, masterProposalId)
    {
        ParentAssemblyCode = parentAssemblyCode;
        Lines = lines;
    }
    
    public string ParentAssemblyCode { get; private set; }
    public Dictionary<string, int> Lines { get; private set; }
}