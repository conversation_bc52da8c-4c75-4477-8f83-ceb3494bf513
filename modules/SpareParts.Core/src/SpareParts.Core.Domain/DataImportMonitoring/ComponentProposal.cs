using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;

namespace SpareParts.Core.DataImportMonitoring;

[ExcludeFromCodeCoverage]
public class ComponentProposal : ProposalBase
{
    public ComponentProposal(string externalProposalId, string code, Dictionary<string, string> translations,
        string type, Guid masterProposalId) : base(externalProposalId, masterProposalId)
    {
        Code = code;
        Translations = translations;
        Type = type;
    }

    public string Code { get; private set; }
    public Dictionary<string, string> Translations { get; private set; }
    public string Type { get; private set; }
}