using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.MultiTenancy;

namespace SpareParts.Core.DataImportMonitoring;

[ExcludeFromCodeCoverage]
public class MasterProposal : AuditedEntity<Guid>, IMultiTenant, IProposal
{
    public Guid? TenantId { get; private set; }
    public string ExternalImportId { get; private set; }
    public string? ExternalProductProposalId { get; private set; }
    public ImportOrigin Origin { get; private set; }
    public string? ProductCode { get; private set; }
    public int ComponentProposalsCount { get; private set; }
    public int? BomProposalsCount { get; private set; }
    public int? ComponentImageProposalsCount { get; private set; }
    public int? DrawingProposalsCount { get; private set; }
    public ImportAction? Action { get; private set; }
    public ProposalStatus Status { get; set; }
    public string? Error { get; set; }
    public List<string> Warnings { get; set; }
    public DateTime? ImportFinishedAt { get; set; }
    public DateTime? ImportStartedAt { get; set; }
    public Guid? ExternalUserId { get; set; }
    public bool ImportHasErrors { get; set; }
    public bool ImportHasWarnings { get; set; }
    public List<string> ImpactedProducts { get; set; }

    public MasterProposal(Guid id, string externalImportId, int componentProposalsCount) : base(id)
    {
        Volo.Abp.Check.NotDefaultOrNull(TenantId, nameof(TenantId));
        Status = ProposalStatus.Pending;
        Origin = ImportOrigin.SolidWorks;
        ComponentProposalsCount = componentProposalsCount;
        ExternalImportId = externalImportId;
        Warnings = [];
        ImpactedProducts = [];
    }

    public void SetProductCode(string code)
    {
        ProductCode = code;
    }

    public void SetExternalProductProposalId(string externalProposalId)
    {
        ExternalProductProposalId = externalProposalId;
    }

    public void SetBomProposalsCount(int bomProposalsCount)
    {
        BomProposalsCount = bomProposalsCount;
    }

    public void SetComponentImageProposalsCount(int componentImageProposalsCount)
    {
        ComponentImageProposalsCount = componentImageProposalsCount;
    }

    public void SetDrawingProposalsCount(int drawingProposalsCount)
    {
        DrawingProposalsCount = drawingProposalsCount;
    }

    public void SetAction(ImportAction action)
    {
        Action = action;
    }
}