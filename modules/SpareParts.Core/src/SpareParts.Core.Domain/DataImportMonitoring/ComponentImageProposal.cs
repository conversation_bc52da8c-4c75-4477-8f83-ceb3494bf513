using System;
using System.Diagnostics.CodeAnalysis;

namespace SpareParts.Core.DataImportMonitoring;

[ExcludeFromCodeCoverage]
public class ComponentImageProposal : ProposalBase
{
    public string ComponentCode { get; private set; }
    public string ImagePath { get; private set; }

    public ComponentImageProposal(string externalProposalId, Guid masterProposalId, string componentCode, string imagePath) : base(externalProposalId, masterProposalId)
    {
        ComponentCode = componentCode;
        ImagePath = imagePath;
    }
}