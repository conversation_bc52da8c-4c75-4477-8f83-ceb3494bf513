using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Threading.Tasks;
using SpareParts.Core.DomainServices.Resources;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.DependencyInjection;
using Volo.Abp.MultiTenancy;

namespace SpareParts.Core;

[ExcludeFromCodeCoverage]
public class ThumbnailsGeneratorJob : AsyncBackgroundJob<ThumbnailsGeneratorJobArgs>, ITransientDependency
{
    public IAbpLazyServiceProvider LazyServiceProvider { get; set; } = default!;
    private ImageManager ImageManager => LazyServiceProvider.LazyGetRequiredService<ImageManager>();
    private CurrentTenant CurrentTenant => LazyServiceProvider.LazyGetRequiredService<CurrentTenant>();
    
    public override async Task ExecuteAsync(ThumbnailsGeneratorJobArgs args)
    {
        using (CurrentTenant.Change(args.TenantId))
        {
            if (await ImageManager.BlobExistsAsync(args.ImageId))
            {
                await using Stream stream = await ImageManager.GetBlobAsync(args.ImageId);
                await ImageManager.ResizeAndSaveImageForAllSizesAsync(stream, args.ImageId);
            }
        }
    }
}