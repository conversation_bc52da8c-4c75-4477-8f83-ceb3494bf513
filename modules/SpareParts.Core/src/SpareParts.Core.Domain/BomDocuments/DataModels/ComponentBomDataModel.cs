using Volo.Abp;

namespace SpareParts.Core.BomDocuments.DataModels;

public class ComponentBomDataModel
{
    public string Code { get; }
    public string Label { get; }
    public int Quantity { get; }

    public ComponentBomDataModel(string code, string label, int quantity)
    {
        Code = Check.NotNullOrWhiteSpace(code, nameof(code));
        Label = Check.NotNullOrWhiteSpace(label, nameof(label));
        Quantity = Check.Positive(quantity, nameof(quantity));
    }
}