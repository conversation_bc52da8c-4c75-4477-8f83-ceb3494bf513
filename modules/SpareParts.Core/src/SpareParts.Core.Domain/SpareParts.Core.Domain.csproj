<Project Sdk="Microsoft.NET.Sdk">

	<Import Project="..\..\..\..\common.props" />

	<PropertyGroup>
		<TargetFrameworks>net9.0</TargetFrameworks>
		<RootNamespace>SpareParts.Core</RootNamespace>
		<Nullable>enable</Nullable>
		<GenerateEmbeddedFilesManifest>true</GenerateEmbeddedFilesManifest>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.Extensions.FileProviders.Embedded" />
		<PackageReference Include="SixLabors.ImageSharp" />
		<PackageReference Include="Volo.Abp.Ddd.Domain" />
		<ProjectReference Include="..\..\..\SpareParts.Common\src\SpareParts.Common.Domain\SpareParts.Common.Domain.csproj" />
		<ProjectReference Include="..\SpareParts.Core.Domain.Shared\SpareParts.Core.Domain.Shared.csproj" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="Images\*.*" />
		<Content Remove="Images\*.*" />
	</ItemGroup>

	<ItemGroup>
	  <None Remove="Fonts\Microsoft-JhengHei.ttf" />
	  <None Remove="Fonts\NotoSans-VariableFont_wdth,wght.ttf" />
	  <None Remove="Fonts\NotoSans.ttf" />
	  <None Remove="Fonts\NotoSansCJKsc-Regular.ttf" />
	  <None Remove="Fonts\NotoSansSC-VariableFont_wght.ttf" />
	  <None Remove="Fonts\NotoSerif-VariableFont_wdth,wght.ttf" />
	  <None Remove="Fonts\Roboto-VariableFont_wdth,wght.ttf" />
	</ItemGroup>

	<ItemGroup>
	  <EmbeddedResource Include="Fonts\NotoSansSC-VariableFont_wght.ttf">
	    <CopyToOutputDirectory>Never</CopyToOutputDirectory>
	  </EmbeddedResource>
	</ItemGroup>

	<ItemGroup>
	  <PackageReference Update="Fody">
	    <PrivateAssets>all</PrivateAssets>
	    <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
	  </PackageReference>
	</ItemGroup>

</Project>
