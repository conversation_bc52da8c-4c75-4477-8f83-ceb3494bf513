using JetBrains.Annotations;
using SpareParts.Common;
using SpareParts.Common.Companies;
using SpareParts.Common.DataFilter;
using SpareParts.Core.Entities.Components;
using System;
using Volo.Abp.Auditing;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.MultiTenancy;

namespace SpareParts.Core.Entities.Equipments;

[Audited]
public class Equipment : AuditedEntity<Guid>, IMultiTenant, IHasCompanyId
{
    private Guid _productId;
    private Guid? _companyId;

    public Product Product { get; private set; } = default!;
    public Company? Company { get; private set; } = default!;

    public Guid? TenantId { get; private set; }

    public string SerialNumber { get; set; } = default!;

    public Guid ProductId
    {
        get => _productId;
        private set => _productId = Check.NotDefault(value, nameof(ProductId));
    }

    public Guid? CompanyId
    {
        get => _companyId;
        private set => _companyId = Check.NotDefaultIfNotNull(value, nameof(CompanyId));
    }

    [UsedImplicitly]
    public Equipment()
    {

    }

    internal Equipment(Guid id, string serialNumber, Guid productId) : base(id)
    {
        Volo.Abp.Check.NotDefaultOrNull(TenantId, nameof(TenantId));
        ProductId = productId;
        SerialNumber = Check.MatchRegex(serialNumber, CoreDomainSharedConsts.EquipmentConsts.SerialNumberPattern, nameof(serialNumber));
    }

    internal Equipment(Guid id, string serialNumber, Guid productId, Guid? companyId) : this(id, serialNumber, productId)
    {
        CompanyId = companyId;
    }

    internal void ChangeProductId(Guid productId)
    {
        ProductId = productId;
    }

    internal void ChangeCompanyId(Guid? companyId)
    {
        CompanyId = companyId;
    }
}