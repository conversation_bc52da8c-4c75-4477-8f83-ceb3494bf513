using JetBrains.Annotations;
using SpareParts.Common;
using System;
using Volo.Abp.Auditing;
using Volo.Abp.Domain.Entities;
using Volo.Abp.MultiTenancy;

namespace SpareParts.Core.Entities.Components;

[Audited]
public sealed class DrawingMapping : Entity<Guid>, IMultiTenant
{
    public Guid ComponentId { get; private set; }
    public Guid DrawingId { get; }
    public string Index { get; private set; } = null!;
    public Guid? TenantId { get; private set; }

    [UsedImplicitly]
    private DrawingMapping()
    {
    }

    internal DrawingMapping(Guid id, Guid drawingId, string index, Guid componentId) : base(id)
    {
        Volo.Abp.Check.NotDefaultOrNull(TenantId, nameof(TenantId));
        DrawingId = Check.NotDefault(drawingId, nameof(drawingId));
        ChangeIndex(index);
        ChangeComponentId(componentId);
    }

    public void ChangeComponentId(Guid componentId)
    {
        ComponentId = Check.NotDefault(componentId, nameof(componentId));
    }

    public void ChangeIndex(string index)
    {
        Index = Volo.Abp.Check.NotNullOrWhiteSpace(index, nameof(index));
    }
}