using JetBrains.Annotations;
using SpareParts.Core.DataVisibility;
using SpareParts.Core.Entities.Resources;
using SpareParts.Core.Enums;
using System;
using System.Collections.Generic;
using Volo.Abp;
using Volo.Abp.Auditing;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.MultiTenancy;

namespace SpareParts.Core.Entities.Components;

[Audited]
public sealed class Drawing : AuditedEntity<Guid>, IMultiTenant, IHasVisibility
{
    private double _rank;

    public Guid? TenantId { get; private set; }
    public Guid AssemblyId { get; private set; }
    public double Rank
    {
        get => _rank;
        set => _rank = Check.Positive(value, nameof(Rank));
    }
    public Resource SourceFile { get; } = null!;
    public Guid SourceFileId { get; }
    public Resource Image { get; } = null!;
    private readonly List<DrawingMapping> _drawingMappings = [];

    public IReadOnlyList<DrawingMapping> DrawingMappings => _drawingMappings;
    public DrawingOrigin Origin { get; private set; }
    public bool IsMain { get; private set; }
    public bool IsVisible { get; set; }

    [UsedImplicitly]
    private Drawing()
    {
    }

    internal Drawing(Guid id, Guid assemblyId, double rank, Resource file, Resource image, List<DrawingMapping> drawingMappings, DrawingOrigin origin) : base(id)
    {
        Check.NotDefaultOrNull(TenantId, nameof(TenantId));
        Common.Check.NotDefault(Id, nameof(Id));
        AssemblyId = Common.Check.NotDefault(assemblyId, nameof(assemblyId));
        Rank = rank;
        SourceFile = Check.NotNull(file, nameof(file));
        SourceFileId = SourceFile.Id;
        Image = Check.NotNull(image, nameof(image));
        _drawingMappings = drawingMappings;
        Origin = origin;
    }

    internal void SetAsMain(bool isMain)
    {
        IsMain = isMain;
    }
}