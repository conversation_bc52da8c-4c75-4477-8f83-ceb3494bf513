using System;
using JetBrains.Annotations;
using Volo.Abp.Auditing;
using Volo.Abp.Domain.Entities;
using Volo.Abp.MultiTenancy;

namespace SpareParts.Core.Entities.Components;

[Audited]
public sealed class ComponentDocument : Entity, IMultiTenant
{
    public Guid ComponentId { get; set; }
    public Guid DocumentId { get; set; }

    [UsedImplicitly]
    private ComponentDocument()
    {
    }

    public ComponentDocument(Guid componentId, Guid documentId)
    {
        Volo.Abp.Check.NotDefaultOrNull(TenantId, nameof(TenantId));
        ComponentId = componentId;
        DocumentId = documentId;
    }

    public override object[] GetKeys()
    {
        return [ComponentId, DocumentId];
    }

    public Guid? TenantId { get; private set; }
}