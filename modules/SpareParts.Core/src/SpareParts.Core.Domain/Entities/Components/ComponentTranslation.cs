using System;
using JetBrains.Annotations;
using SpareParts.Common.Translations;
using Volo.Abp.Auditing;

namespace SpareParts.Core.Entities.Components;

[Audited]
public sealed class ComponentTranslation : CommonTranslation
{
    public Guid ComponentId { get; private set; }

    [UsedImplicitly]
    private ComponentTranslation()
    {
    }

    public ComponentTranslation(string language, string label, string? description = null) : base(language, label, description)
    {
    }

    public override object?[] GetKeys()
    {
        return [ComponentId, Language];
    }
}