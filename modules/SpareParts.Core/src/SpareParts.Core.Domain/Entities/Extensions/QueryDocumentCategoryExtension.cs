using SpareParts.Core.Entities.Resources;
using System;
using System.Linq;
using System.Linq.Expressions;

namespace SpareParts.Core.Entities.Extensions;

public static class QueryDocumentCategoryExtension
{
    public static IQueryable<DocumentCategory> GetDocumentCategorySearchQueryable(this IQueryable<DocumentCategory> documentCategoryQueryable, string keyword, string currentLanguage, string defaultLanguage)
    {
        FilterTranslationExpression<DocumentCategory, DocumentCategoryTranslation> filter = new()
        {
            Keyword = keyword,
            CurrentLanguage = currentLanguage,
            DefaultLanguage = defaultLanguage,
            TranslationsSelector = documentCategory => documentCategory.Translations,
            LanguageSelector = translation => translation.Language,
            LabelSelector = translation => translation.Label
        };

        Expression<Func<DocumentCategory, bool>> filterExpression = QueryTranslationFilterExtension.GetTranslationFilterExpression(filter);
        return documentCategoryQueryable.Where(filterExpression);
    }
}