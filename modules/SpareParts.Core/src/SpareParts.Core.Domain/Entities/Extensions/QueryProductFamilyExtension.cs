using SpareParts.Core.Entities.ProductFamilies;
using System;
using System.Linq;
using System.Linq.Expressions;

namespace SpareParts.Core.Entities.Extensions;

public static class QueryProductFamilyExtension
{
    public static IQueryable<ProductFamily> GetProductFamilySearchQueryable(this IQueryable<ProductFamily> productFamilyQueryable, string keyword, string currentLanguage, string defaultLanguage)
    {
        FilterCodeOrTranslationExpression<ProductFamily, ProductFamilyTranslation> filter = new()
        {
            Keyword = keyword,
            CurrentLanguage = currentLanguage,
            DefaultLanguage = defaultLanguage,
            CodeSelector = productFamily => productFamily.Code,
            TranslationsSelector = productFamily => productFamily.Translations,
            LanguageSelector = translation => translation.Language,
            LabelSelector = translation => translation.Label
        };

        Expression<Func<ProductFamily, bool>> filterExpression = QueryCodeOrTranslationFilterExtension.GetCodeOrTranslationFilterExpression(filter);
        return productFamilyQueryable.Where(filterExpression);
    }
}