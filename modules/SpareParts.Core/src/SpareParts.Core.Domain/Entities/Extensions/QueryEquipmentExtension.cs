using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Equipments;
using System;
using System.Linq;
using System.Linq.Expressions;

namespace SpareParts.Core.Entities.Extensions;

public static class QueryEquipmentExtension
{
    public static IQueryable<Equipment> GetEquipmentSearchQueryable(this IQueryable<Equipment> equipmentQueryable, IQueryable<Product> productQueryable,
        string keyword, string currentLanguage, string defaultLanguage)
    {
        FilterEquipmentProductTranslationExpression<EquipmentProduct, ComponentOnlineTranslation, ComponentTranslation> filter = new()
        {
            Keyword = keyword,
            CurrentLanguage = currentLanguage,
            DefaultLanguage = defaultLanguage,
            SerialNumberSelector = equipmentProduct => equipmentProduct.Equipment.SerialNumber,
            CodeSelector = equipmentProduct => equipmentProduct.Product.Component.Code,
            TranslationsSelector = equipmentProduct => equipmentProduct.Product.Component.Translations,
            LanguageSelector = translation => translation.Language,
            LabelSelector = translation => translation.Label,
            OnlineTranslationsSelector = equipmentProduct => equipmentProduct.Product.Component.OnlineTranslations,
            OnlineLanguageSelector = onlineTranslation => onlineTranslation.Language,
            OnlineLabelSelector = onlineTranslation => onlineTranslation.Label,
        };

        ParameterExpression param = Expression.Parameter(typeof(EquipmentProduct), "x");

        MethodCallExpression serialNumberContains = Expression.Call(
            Expression.Invoke(filter.SerialNumberSelector, param),
            nameof(string.Contains),
            null,
            Expression.Constant(filter.Keyword)
        );

        Expression<Func<EquipmentProduct, bool>> componentFilterExpression = QueryCodeOrTranslationFilterExtension.GetCodeOrTranslationFilterExpression(filter);

        Expression inlinedComponentFilter = componentFilterExpression.Body.ReplaceParameter(componentFilterExpression.Parameters[0], param);

        BinaryExpression combinedExpression = Expression.OrElse(serialNumberContains, inlinedComponentFilter);

        Expression<Func<EquipmentProduct, bool>> equipmentFilter = Expression.Lambda<Func<EquipmentProduct, bool>>(combinedExpression, param);

        equipmentQueryable = equipmentQueryable
            .Join(productQueryable,
                equipment => equipment.ProductId,
                product => product.Id,
                (equipment, product) => new EquipmentProduct { Equipment = equipment, Product = product })
            .WhereIf(!string.IsNullOrWhiteSpace(keyword), equipmentFilter)
            .Select(x => x.Equipment);

        return equipmentQueryable;
    }

    private sealed class EquipmentProduct
    {
        public required Equipment Equipment { get; init; }
        public required Product Product { get; init; }
    }
}