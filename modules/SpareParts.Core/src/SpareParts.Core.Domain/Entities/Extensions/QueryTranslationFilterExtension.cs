using System;
using System.Linq;
using System.Linq.Expressions;

namespace SpareParts.Core.Entities.Extensions;

public class QueryTranslationFilterExtension
{
    protected QueryTranslationFilterExtension()
    {

    }

    public static Expression<Func<T, bool>> GetTranslationFilterExpression<T, TTranslation>(FilterTranslationExpression<T, TTranslation> filter)
    {
        return GetTranslationFilterExpressionBase<T>(
            entityParam => GetLanguageExpression(filter, entityParam)
        );
    }

    public static Expression<Func<T, bool>> GetTranslationFilterExpression<T, TOnlineTranslation, TTranslation>(
            FilterComponentTranslationExpression<T, TOnlineTranslation, TTranslation> filter)
    {
        return GetTranslationFilterExpressionBase<T>(
            entityParam => GetLanguageExpression(filter, entityParam)
        );
    }

    private static Expression<Func<T, bool>> GetTranslationFilterExpressionBase<T>(Func<ParameterExpression, BinaryExpression> getLanguageExpression)
    {
        ParameterExpression entityParam = Expression.Parameter(typeof(T), "entity");

        BinaryExpression languageExpression = getLanguageExpression(entityParam);

        return Expression.Lambda<Func<T, bool>>(languageExpression, entityParam);
    }

    protected static BinaryExpression GetLanguageExpression<T, TTranslation>(
       FilterTranslationExpression<T, TTranslation> filter, ParameterExpression entityParam)
    {
        ParameterExpression translationParam = Expression.Parameter(typeof(TTranslation), "translation");
        InvocationExpression translationsExpression = Expression.Invoke(filter.TranslationsSelector, entityParam);

        MethodCallExpression currentLanguagesExpression = GetCurrentLanguagesExpression(filter, translationsExpression, translationParam);

        BinaryExpression fallbackLanguageExpression = GetFallbackLanguageExpression(filter, translationsExpression, translationParam);

        return Expression.OrElse(currentLanguagesExpression, fallbackLanguageExpression);
    }

    protected static BinaryExpression GetLanguageExpression<T, TOnlineTranslation, TTranslation>(
        FilterComponentTranslationExpression<T, TOnlineTranslation, TTranslation> filter, ParameterExpression entityParam)
    {
        ParameterExpression translationParam = Expression.Parameter(typeof(TTranslation), "translation");
        InvocationExpression translationsExpression = Expression.Invoke(filter.TranslationsSelector, entityParam);

        ParameterExpression onlineTranslationParam = Expression.Parameter(typeof(TOnlineTranslation), "onlineTranslation");
        InvocationExpression onlineTranslationsExpression = Expression.Invoke(filter.OnlineTranslationsSelector, entityParam);

        BinaryExpression currentLanguagesExpression = GetCurrentLanguagesExpression(filter,
            onlineTranslationsExpression, onlineTranslationParam, translationsExpression, translationParam);

        BinaryExpression fallbackLanguageExpression = GetFallbackLanguageExpression(filter, translationsExpression, translationParam);

        return Expression.OrElse(currentLanguagesExpression, fallbackLanguageExpression);
    }

    private static MethodCallExpression GetCurrentLanguagesExpression<T, TTranslation>(
        FilterTranslationExpression<T, TTranslation> filter, InvocationExpression translationsExpression, ParameterExpression translationParam)
    {
        BinaryExpression labelExpression = GetLabelForLanguageExpression(translationParam, filter.LanguageSelector, filter.LabelSelector, filter.CurrentLanguage, filter.Keyword);

        MethodCallExpression translationCondition = Expression.Call(
            typeof(Enumerable), nameof(Enumerable.Any), [typeof(TTranslation)],
            translationsExpression,
            Expression.Lambda<Func<TTranslation, bool>>(labelExpression, translationParam));

        return translationCondition;
    }

    private static BinaryExpression GetCurrentLanguagesExpression<T, TOnlineTranslation, TTranslation>(
        FilterComponentTranslationExpression<T, TOnlineTranslation, TTranslation> filter,
        InvocationExpression onlineTranslationsExpression, ParameterExpression onlineTranslationParam,
        InvocationExpression translationsExpression, ParameterExpression translationParam)
    {
        BinaryExpression onlineLabelExpression = GetLabelForLanguageExpression(onlineTranslationParam, filter.OnlineLanguageSelector, filter.OnlineLabelSelector, filter.CurrentLanguage, filter.Keyword);
        MethodCallExpression onlineCondition = Expression.Call(typeof(Enumerable), nameof(Enumerable.Any), [typeof(TOnlineTranslation)
            ], onlineTranslationsExpression,
            Expression.Lambda<Func<TOnlineTranslation, bool>>(onlineLabelExpression, onlineTranslationParam));

        MethodCallExpression currentLanguagesExpression = GetCurrentLanguagesExpression(filter, translationsExpression, translationParam);

        return Expression.OrElse(onlineCondition, currentLanguagesExpression);
    }

    private static BinaryExpression GetFallbackLanguageExpression<T, TTranslation>(
        FilterTranslationExpression<T, TTranslation> filter,
        InvocationExpression translationsExpression, ParameterExpression translationParam)
    {
        InvocationExpression languageExpression = Expression.Invoke(filter.LanguageSelector, translationParam);
        BinaryExpression notCurrentLanguageExpression = Expression.NotEqual(languageExpression, Expression.Constant(filter.CurrentLanguage));

        MethodCallExpression allExceptCurrentLanguageExpression = Expression.Call(typeof(Enumerable), nameof(Enumerable.All),
            [typeof(TTranslation)], translationsExpression,
            Expression.Lambda<Func<TTranslation, bool>>(notCurrentLanguageExpression, translationParam));

        BinaryExpression labelExpression = GetLabelForLanguageExpression(translationParam, filter.LanguageSelector, filter.LabelSelector, filter.DefaultLanguage, filter.Keyword);

        MethodCallExpression anyDefaultLanguageExpression = Expression.Call(typeof(Enumerable), nameof(Enumerable.Any),
            [typeof(TTranslation)], translationsExpression,
            Expression.Lambda<Func<TTranslation, bool>>(labelExpression, translationParam));

        return Expression.AndAlso(allExceptCurrentLanguageExpression, anyDefaultLanguageExpression);
    }

    private static BinaryExpression GetLabelForLanguageExpression<TTranslation>(ParameterExpression translationParam, Expression<Func<TTranslation, string>> languageSelector,
        Expression<Func<TTranslation, string>> labelSelector, string language, string keyword)
    {
        InvocationExpression languageExpression = Expression.Invoke(languageSelector, translationParam);
        InvocationExpression labelExpression = Expression.Invoke(labelSelector, translationParam);

        MethodCallExpression labelContainsKeyword = Expression.Call(labelExpression, nameof(string.Contains), null, Expression.Constant(keyword));
        return Expression.AndAlso(Expression.Equal(languageExpression, Expression.Constant(language)), labelContainsKeyword);
    }
}