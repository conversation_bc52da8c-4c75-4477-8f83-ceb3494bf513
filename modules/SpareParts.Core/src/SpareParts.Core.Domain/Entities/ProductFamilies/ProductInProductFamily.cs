using JetBrains.Annotations;
using System;
using Volo.Abp.Auditing;
using Volo.Abp.Domain.Entities;
using Volo.Abp.MultiTenancy;

namespace SpareParts.Core.Entities.ProductFamilies;

[Audited]
public sealed class ProductInProductFamily : Entity, IMultiTenant
{
    [UsedImplicitly]
    private ProductInProductFamily()
    {
    }

    public ProductInProductFamily(Guid productId, Guid productFamilyId, int rank)
    {
        Volo.Abp.Check.NotDefaultOrNull(TenantId, nameof(TenantId));
        ProductId = productId;
        ProductFamilyId = productFamilyId;
        Init(rank);
    }

    private void Init(int rank)
    {
        ChangeRank(rank);
    }

    public void ChangeRank(int rank)
    {
        Rank = Volo.Abp.Check.Positive(rank, nameof(rank));
    }

    public Guid ProductId { get; set; }
    public Guid ProductFamilyId { get; set; }
    public int Rank { get; private set; }

    public Guid? TenantId { get; private set; }

    public override object?[] GetKeys()
    {
        return [ProductFamilyId, ProductId];
    }
}