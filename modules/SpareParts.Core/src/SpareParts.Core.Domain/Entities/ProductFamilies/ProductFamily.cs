using JetBrains.Annotations;
using SpareParts.Common;
using SpareParts.Common.Translations;
using SpareParts.Core.Entities.Base;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Resources;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SpareParts.Core.Entities.ProductFamilies;

public class ProductFamily : AuditedCoreEntity, ITranslatedEntity<ProductFamilyTranslation>, IHasImage
{
    public bool IsVisible { get; set; }
    private readonly HashSet<ProductFamilyTranslation> _translations = null!;
    public Guid? ParentId { get; private set; }
    private readonly List<Product> _products = [];
    public IReadOnlyList<Product> Products => _products;
    private readonly List<ProductFamily> _subProductFamilies = [];
    public IReadOnlyList<ProductFamily> SubProductFamilies => _subProductFamilies;
    public int Rank { get; private set; }
    public Resource Image { get; private set; } = null!;
    public Guid ImageId { get; set; }

    [UsedImplicitly]
    private ProductFamily()
    {
    }

    internal ProductFamily(Guid id, string code, IEnumerable<ProductFamilyTranslation> translations, Resource image, Guid? parentId = null)
        : base(id, code)
    {
        _translations = translations.HasAtLeastOne(nameof(translations)).ToHashSet();
        ParentId = parentId;
        Image = image;
    }

    public IReadOnlySet<ProductFamilyTranslation> Translations
    {
        get => _translations;
        private init => _translations = value.ToHashSet();
    }

    public void AddTranslation(ProductFamilyTranslation translation)
    {
        _translations.Add(translation);
    }

    public void ChangeRank(int rank)
    {
        Rank = Volo.Abp.Check.Positive(rank, nameof(rank));
    }

    public void SetAsRoot()
    {
        ParentId = null;
    }

    internal void ChangeParent(Guid parentId)
    {
        ParentId = parentId;
    }
}