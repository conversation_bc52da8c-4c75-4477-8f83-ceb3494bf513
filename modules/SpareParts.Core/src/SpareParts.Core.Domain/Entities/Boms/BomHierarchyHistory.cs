using System;
using Volo.Abp.Domain.Entities;
using Volo.Abp.MultiTenancy;

namespace SpareParts.Core.Entities.Boms;

public class BomHierarchyHistory : IEntity, IMultiTenant
{
    public DateTime DateTime { get; set; }
    public Guid? TenantId { get; set; }
    public string TenantName { get; set; } = null!;
    public Guid ProductId { get; set; }
    public string ProductCode { get; set; } = null!;
    public int MaxLevel { get; set; }
    public int LinesNumber { get; set; }
    public object?[] GetKeys()
    {
        return [DateTime, ProductId];
    }
}