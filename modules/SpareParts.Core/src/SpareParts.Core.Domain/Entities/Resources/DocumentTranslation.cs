using System;
using JetBrains.Annotations;
using SpareParts.Common.Translations;

namespace SpareParts.Core.Entities.Resources;

public class DocumentTranslation : CommonTranslation
{
    public Guid DocumentId { get; private set; }

    [UsedImplicitly]
    private DocumentTranslation()
    {
        
    }

    public DocumentTranslation(string language, string label, string? description = null) : base(language, label, description)
    {
    }

    public override object?[] GetKeys()
    {
        return [DocumentId, Language];
    }
}