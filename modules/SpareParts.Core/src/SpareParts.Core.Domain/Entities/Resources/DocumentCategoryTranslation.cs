using JetBrains.Annotations;
using SpareParts.Common.Translations;
using System;

namespace SpareParts.Core.Entities.Resources;

public sealed class DocumentCategoryTranslation : CommonTranslation
{
    public Guid DocumentCategoryId { get; private set; }

    [UsedImplicitly]
    private DocumentCategoryTranslation()
    {

    }
    public DocumentCategoryTranslation(string language, string label, string? description = null) : base(language, label, description)
    {
    }

    public override object?[] GetKeys()
    {
        return [DocumentCategoryId, Language];
    }
}