using JetBrains.Annotations;
using SpareParts.Common;
using SpareParts.Common.Translations;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using SpareParts.Common.DataFilter;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.MultiTenancy;
using Volo.Abp.Validation;
using Check = Volo.Abp.Check;

namespace SpareParts.Core.Entities.Resources;

public class Document : AuditedEntity<Guid>, IMultiTenant, ITranslatedEntity<DocumentTranslation>, IHasPublic
{
    private readonly HashSet<DocumentTranslation> _translations = null!;
    private List<DocumentCategory> _categories = null!;
    private readonly List<Component> _components = [];

    public Guid? TenantId { get; private set; }
    public DocumentType Type { get; internal set; }

    public Resource? Resource { get; protected set; }
    public string? Link { get; protected set; }

    public HashSet<string> Languages { get; set; } = null!;

    public IReadOnlyList<DocumentCategory> Categories
    {
        get => _categories;
        private set => _categories = value.ToList();
    }

    public IReadOnlySet<DocumentTranslation> Translations
    {
        get => _translations;
        private init => _translations = value.ToHashSet();
    }

    public bool IsPublic { get; set; }

    public void AddTranslation(DocumentTranslation translation)
    {
        _translations.Add(translation);
    }

    public IReadOnlyList<Component> Components => _components;

    [UsedImplicitly]
    private Document()
    {
    }

    private Document(Guid id, List<DocumentTranslation> translations, HashSet<string> languages, bool isPublic = true) : base(id)
    {
        Common.Check.NotDefault(id, nameof(id));
        Check.NotDefaultOrNull(TenantId, nameof(TenantId));
        _translations = translations.HasAtLeastOne(nameof(translations)).ToHashSet();
        IsPublic = isPublic;
        Languages = languages;
    }

    internal Document(Guid id, string link, List<DocumentTranslation> translations, HashSet<string> languages,
        bool isPublic = true) : this(id, translations, languages, isPublic)
    {
        Link = Common.Check.MatchUrl(link, nameof(link));
        Type = DocumentType.Link;
    }

    internal Document(Guid id, Resource resource, List<DocumentTranslation> translations, HashSet<string> languages,
        bool isPublic = true) : this(id, translations, languages, isPublic)
    {
        Resource = Check.NotNull(resource, nameof(resource));
        Type = DocumentType.Resource;
    }

    public void ChangeLink(string link)
    {
        if (Resource != null)
        {
            throw new AbpValidationException("Change type from link to file is not allowed");
        }
        Link = Common.Check.MatchUrl(link, nameof(link));
    }

    public void ChangeFileResource(Resource resource)
    {
        if (!string.IsNullOrEmpty(Link))
        {
            throw new AbpValidationException("Change type from file to link is not allowed");
        }
        Resource = Check.NotNull(resource, nameof(resource));
    }
}