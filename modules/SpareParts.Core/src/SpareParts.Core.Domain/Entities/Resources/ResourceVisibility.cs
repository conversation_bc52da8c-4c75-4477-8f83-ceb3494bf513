using SpareParts.Common.DataFilter;
using SpareParts.Core.DataVisibility;
using System;
using Volo.Abp.Domain.Entities;
using Volo.Abp.MultiTenancy;

namespace SpareParts.Core.Entities.Resources;

public class ResourceVisibility : IEntity, IMultiTenant, IHasVisibility, IHasPublic
{
    public Guid? TenantId { get; set; }
    public Guid ResourceId { get; set; }
    public bool IsVisible { get; set; }
    public bool IsPublic { get; set; }

    public object?[] GetKeys()
    {
        return [ResourceId];
    }
}