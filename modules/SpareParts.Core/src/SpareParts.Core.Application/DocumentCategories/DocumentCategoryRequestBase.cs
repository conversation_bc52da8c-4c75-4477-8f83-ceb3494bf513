using SpareParts.Core.DomainServices.DocumentCategories;
using SpareParts.Core.Entities.Resources;
using System;
using Volo.Abp.Domain.Repositories;

namespace SpareParts.Core.DocumentCategories;
public abstract class DocumentCategoryRequestBase : CoreRequestBase
{
    protected IRepository<DocumentCategory, Guid> DocumentCategoryRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<DocumentCategory, Guid>>();
    protected DocumentCategoryDomainService DocumentCategoryDomainService => LazyServiceProvider.LazyGetRequiredService<DocumentCategoryDomainService>();
}