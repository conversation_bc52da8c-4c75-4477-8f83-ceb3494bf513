using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.DocumentCategories.Commands;
using System.Threading;
using System.Threading.Tasks;

namespace SpareParts.Core.DocumentCategories.CommandHandlers;

public class DeleteDocumentCategoryCommandHandler : DocumentCategoryRequestBase, ICommandHandler<DeleteDocumentCategoryCommand>
{
    public async Task Handle(DeleteDocumentCategoryCommand request, CancellationToken cancellationToken)
    {
        await DocumentCategoryRepository.DeleteAsync(dc => dc.Id == request.Id, cancellationToken: cancellationToken);
    }
}