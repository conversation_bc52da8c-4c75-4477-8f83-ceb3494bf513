using AutoFilterer.Extensions;
using SpareParts.AbpMediatR.Handlers;
using SpareParts.Common.Settings;
using SpareParts.Core.DocumentCategories.Dtos;
using SpareParts.Core.DocumentCategories.Queries;
using SpareParts.Core.Entities.Extensions;
using SpareParts.Core.Entities.Resources;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.ChangeTracking;

namespace SpareParts.Core.DocumentCategories.QueryHandlers;
public class GetDocumentCategoriesQueryHandler : DocumentCategoryRequestBase, IQueryHandler<GetDocumentCategoriesQuery, PagedResultDto<DocumentCategoryDto>>
{
    [DisableEntityChangeTracking]
    public async Task<PagedResultDto<DocumentCategoryDto>> Handle(GetDocumentCategoriesQuery request, CancellationToken cancellationToken)
    {
        request.Filter.Sort ??= nameof(DocumentCategory.CreationTime);

        string defaultLanguage = (await SettingProvider.GetOrNullAsync(CommonSettings.DefaultLanguage))!;
        IQueryable<DocumentCategory> queryable = await DocumentCategoryRepository.WithDetailsAsync(x => x.DocumentInDocumentCategories);
        queryable = ApplyKeywordFilter(queryable, request.Filter.Keyword, defaultLanguage);

        if (string.Equals(request.Filter.Sort, nameof(DocumentCategoryTranslation.Label), StringComparison.CurrentCultureIgnoreCase))
        {
            queryable = ApplyLabelSort(request, defaultLanguage, queryable);
        }

        IQueryable<DocumentCategory>? queryWithoutPagination = queryable.ApplyFilterWithoutPagination(request.Filter);
        long count = await DocumentCategoryRepository.AsyncExecuter.LongCountAsync(queryWithoutPagination, cancellationToken);

        IQueryable<DocumentCategory>? queryWithPagination = queryable.ApplyFilter(request.Filter);
        List<DocumentCategory> documentCategories = await DocumentCategoryRepository.AsyncExecuter.ToListAsync(queryWithPagination, cancellationToken);

        List<DocumentCategoryDto> result = ObjectMapper.Map<List<DocumentCategory>, List<DocumentCategoryDto>>(documentCategories);
        return new PagedResultDto<DocumentCategoryDto>(count, result);
    }

    private static IQueryable<DocumentCategory> ApplyLabelSort(GetDocumentCategoriesQuery request, string defaultLanguage, IQueryable<DocumentCategory> queryable)
    {
        request.Filter.Sort = null;

        Expression<Func<DocumentCategory, string?>> labelSelector =
            dc => dc.Translations
                .Where(t => t.Language == defaultLanguage)
                .Select(t => t.Label)
                .First();

        queryable = request.Filter.SortBy == AutoFilterer.Enums.Sorting.Ascending
            ? queryable.OrderBy(labelSelector)
            : queryable.OrderByDescending(labelSelector);
        return queryable;
    }

    private static IQueryable<DocumentCategory> ApplyKeywordFilter(IQueryable<DocumentCategory> documentCategoryQueryable, string? keyword, string defaultLanguage)
    {
        if (string.IsNullOrEmpty(keyword))
        {
            return documentCategoryQueryable;
        }

        string currentLanguage = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
        documentCategoryQueryable = documentCategoryQueryable.GetDocumentCategorySearchQueryable(keyword, currentLanguage, defaultLanguage);
        return documentCategoryQueryable;
    }
}
