using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoFilterer.Extensions;
using Microsoft.AspNetCore.Authorization;
using SpareParts.AbpMediatR.Handlers;
using SpareParts.Common.DataFilter;
using SpareParts.Common.Settings;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Equipments;
using SpareParts.Core.Entities.Extensions;
using SpareParts.Core.Equipments.Dtos;
using SpareParts.Core.Equipments.Queries;
using SpareParts.Core.Permissions;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Authorization;
using Volo.Abp.Domain.ChangeTracking;

namespace SpareParts.Core.Equipments.QueryHandlers;
public class GetEquipmentsQueryHandler : EquipmentRequestBase, IQueryHandler<GetEquipmentsQuery, PagedResultDto<EquipmentDto>>
{
    protected IAbpAuthorizationService AuthorizationService => LazyServiceProvider.LazyGetRequiredService<IAbpAuthorizationService>();

    [DisableEntityChangeTracking]
    public async Task<PagedResultDto<EquipmentDto>> Handle(GetEquipmentsQuery request, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(request.Filter.Sort))
        {
            request.Filter.Sort = "serialNumber";
        }

        using (DataFilter.Disable<IHasPublic>())
        {
            IQueryable<Equipment> queryable = await GetEquipmentQueryable(request);

            IQueryable<EquipmentDto> query = CreateEquipmentDtoQuery(queryable);

            IQueryable<EquipmentDto> filteredEquipmentsWithoutPagination = query.ApplyFilterWithoutPagination(request.Filter);
            long count = await EquipmentRepository.AsyncExecuter.LongCountAsync(filteredEquipmentsWithoutPagination, cancellationToken);
            IQueryable<EquipmentDto> filteredEquipments = query.ApplyFilter(request.Filter);
            List<EquipmentDto> equipmentsDto = await EquipmentRepository.AsyncExecuter.ToListAsync(filteredEquipments, cancellationToken);
            return new PagedResultDto<EquipmentDto>(count, equipmentsDto);
        }
    }


    private async Task<IQueryable<Equipment>> GetEquipmentQueryable(GetEquipmentsQuery request)
    {
        IQueryable<Equipment> equipmentQueryable = await EquipmentRepository.GetQueryableAsync();
        IQueryable<Product> productQueryable = await ProductRepository.GetQueryableAsync();

        (string keyword, _) = request.Filter;

        if (await AuthorizationService.IsGrantedAsync(CorePermissions.ShowHidden) && string.IsNullOrWhiteSpace(keyword))
        {
            return equipmentQueryable;
        }

        string currentLanguage = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
        string defaultLanguage = (await SettingProvider.GetOrNullAsync(CommonSettings.DefaultLanguage))!;

        equipmentQueryable = equipmentQueryable.GetEquipmentSearchQueryable(productQueryable, keyword, currentLanguage, defaultLanguage);

        return equipmentQueryable;
    }

    private static IQueryable<EquipmentDto> CreateEquipmentDtoQuery(IQueryable<Equipment> query)
    {
        return from equipment in query
               select new EquipmentDto
               {
                   CompanyId = equipment.CompanyId,
                   CreationTime = equipment.CreationTime,
                   CreatorId = equipment.CreatorId,
                   Id = equipment.Id,
                   LastModificationTime = equipment.LastModificationTime,
                   LastModifierId = equipment.LastModifierId,
                   ProductId = equipment.ProductId,
                   SerialNumber = equipment.SerialNumber
               };
    }
}