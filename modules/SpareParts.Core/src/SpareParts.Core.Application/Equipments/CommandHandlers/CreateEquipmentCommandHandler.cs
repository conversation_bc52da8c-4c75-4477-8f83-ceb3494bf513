using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.Entities.Equipments;
using SpareParts.Core.Equipments.Commands;
using SpareParts.Core.Equipments.Dtos;
using System.Threading;
using System.Threading.Tasks;

namespace SpareParts.Core.Equipments.CommandHandlers;

public class CreateEquipmentCommandHandler : EquipmentRequestBase, ICommandHandler<CreateEquipmentCommand, EquipmentDto>
{
    public virtual async Task<EquipmentDto> Handle(CreateEquipmentCommand request, CancellationToken cancellationToken)
    {
        Equipment equipment = await EquipmentDomainService.CreateAsync(request.SerialNumber, request.ProductId, request.CompanyId, cancellationToken);
        equipment = await EquipmentRepository.InsertAsync(equipment, cancellationToken: cancellationToken);
        EquipmentDto equipmentDto = ObjectMapper.Map<Equipment, EquipmentDto>(equipment);
        return equipmentDto;
    }
}