using SpareParts.Core.DomainServices.Equipments;
using SpareParts.Core.Entities.Equipments;
using System;
using Volo.Abp.Data;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Users;

namespace SpareParts.Core.Equipments;
public abstract class EquipmentRequestBase : CoreRequestBase
{
    protected EquipmentDomainService EquipmentDomainService => LazyServiceProvider.LazyGetRequiredService<EquipmentDomainService>();
    protected IRepository<Equipment, Guid> EquipmentRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Equipment, Guid>>();
    protected ICurrentUser CurrentUser => LazyServiceProvider.LazyGetRequiredService<ICurrentUser>();
    protected DataFilter DataFilter => LazyServiceProvider.LazyGetRequiredService<DataFilter>();
}