using Microsoft.Extensions.DependencyInjection;
using SpareParts.AbpMediatR;
using SpareParts.Common;
using SpareParts.Core.DataVisibility;
using Volo.Abp.Application;
using Volo.Abp.AutoMapper;
using Volo.Abp.Modularity;
using Volo.Abp.VirtualFileSystem;

namespace SpareParts.Core;

[DependsOn(
    typeof(CoreDomainModule),
    typeof(CoreApplicationContractsModule),
    typeof(AbpDddApplicationModule),
    typeof(AbpAutoMapperModule),
    typeof(CommonApplicationModule),
    typeof(AbpVirtualFileSystemModule),
    typeof(CqrsModule)
    )]
public class CoreApplicationModule : AbpModule
{
    public override void PreConfigureServices(ServiceConfigurationContext context)
    {
        context.Services.OnRegistered(ComponentVisibilityInterceptorRegistrar.RegisterIfNeeded);
        context.Services.OnRegistered(ResourceVisibilityInterceptorRegistrar.RegisterIfNeeded);
        context.Services.OnRegistered(ProductFamilyVisibilityInterceptorRegistrar.RegisterIfNeeded);
    }

    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        base.ConfigureServices(context);
        context.Services.AddAutoMapperObjectMapper<CoreApplicationModule>();
        Configure<AbpAutoMapperOptions>(options =>
        {
            options.AddMaps<CoreApplicationModule>(validate: true);
        });

        Configure<AbpVirtualFileSystemOptions>(options =>
        {
            options.FileSets.AddEmbedded<CoreApplicationModule>("SpareParts.Core");
        });
    }
}