using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.DataImportMonitoring.Queries;
using SpareParts.Core.Entities.Components;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using SpareParts.Core.Enums;
using Volo.Abp.Domain.ChangeTracking;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Timing;

namespace SpareParts.Core.DataImportMonitoring.QueryHandlers;

[ExcludeFromCodeCoverage]
public class GetImportLogByIdQueryHandler : CoreRequestBase, IQueryHandler<GetImportLogByIdQuery, DetailedImportLogDto>
{
    private IRepository<MasterProposal, Guid> MasterProposalRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<MasterProposal, Guid>>();
    private IRepository<ComponentProposal> ComponentProposalRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<ComponentProposal>>();
    private IRepository<BomProposal> BomProposalRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<BomProposal>>();
    private IRepository<ComponentImageProposal> ComponentImageProposalRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<ComponentImageProposal>>();
    private IRepository<DrawingProposal> DrawingProposalRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<DrawingProposal>>();
    private IRepository<Component, Guid> ComponentRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Component, Guid>>();
    private IClock Clock => LazyServiceProvider.LazyGetRequiredService<IClock>();

    [DisableEntityChangeTracking]
    public virtual async Task<DetailedImportLogDto> Handle(GetImportLogByIdQuery request, CancellationToken cancellationToken)
    {
        IQueryable<MasterProposal> queryable = (await MasterProposalRepository.GetQueryableAsync()).Where(mp => mp.Id == request.Id && mp.ImportStartedAt != null);
        MasterProposal masterProposal = await MasterProposalRepository.AsyncExecuter.FirstAsync(queryable, cancellationToken);
        
        List<ComponentProposal> componentProposals = await GetProposalsAsync(ComponentProposalRepository, request.Id, cancellationToken);
        List<BomProposal> bomProposals = await GetProposalsAsync(BomProposalRepository, request.Id, cancellationToken);
        List<ComponentImageProposal> componentImageProposals = await GetProposalsAsync(ComponentImageProposalRepository, request.Id, cancellationToken);
        List<DrawingProposal> drawingProposals = await GetProposalsAsync(DrawingProposalRepository, request.Id, cancellationToken);
        
        ProposalCounts componentProposalCounts = CalculateProposalCounts(componentProposals);
        ProposalCounts bomProposalCounts = CalculateProposalCounts(bomProposals);
        ProposalCounts componentImageProposalCounts = CalculateProposalCounts(componentImageProposals);
        ProposalCounts drawingProposalCounts = CalculateProposalCounts(drawingProposals);
        
        IProposal[] allProposals = [masterProposal];
        allProposals = allProposals
            .Concat(componentProposals)
            .Concat(bomProposals)
            .Concat(componentImageProposals)
            .Concat(drawingProposals).ToArray();

        List<string> allErrors = CollectAllErrors(allProposals);
        List<string> allWarnings = CollectAllWarnings(allProposals);
        
        Dictionary<string, string> productProposalLabel = GetProductProposalLabel(masterProposal, componentProposals);
        Guid? productImageId = await GetProductImageIdAsync(masterProposal.ProductCode, cancellationToken);

        return MapToDetailedImportLogDto(masterProposal, componentProposalCounts, bomProposalCounts,
            componentImageProposalCounts, drawingProposalCounts, allErrors, allWarnings, productProposalLabel, productImageId);
    }

    private static async Task<List<TProposal>> GetProposalsAsync<TProposal>(
        IRepository<TProposal> repository, Guid masterProposalId, CancellationToken cancellationToken)
        where TProposal : ProposalBase
    {
        IQueryable<TProposal> queryable = await repository.GetQueryableAsync();
        return await repository.AsyncExecuter.ToListAsync(
            queryable.Where(p => p.MasterProposalId == masterProposalId),
            cancellationToken);
    }

    private static ProposalCounts CalculateProposalCounts<TProposal>(List<TProposal> proposals)
        where TProposal : ProposalBase
    {
        int totalCount = proposals.Count;
        int successfulCount = proposals.Count(p => p.Status == ProposalStatus.Completed && p.Error == null);
        int failedCount = proposals.Count(p => p.Error != null);

        return new ProposalCounts(successfulCount, failedCount, totalCount);
    }

    private static List<string> CollectAllErrors(IEnumerable<IProposal> proposals)
    {
        List<string> errors = [];

        foreach (IProposal proposal in proposals)
        {
            if (proposal.Error != null)
            {
                errors.Add(proposal.Error);
            }
        }

        return errors;
    }

    private static List<string> CollectAllWarnings(IEnumerable<IProposal> proposals)
    {
        List<string> warnings = [];

        foreach (IProposal proposal in proposals)
        {
            if (proposal.Warnings.Count > 0)
            {
                warnings.AddRange(proposal.Warnings);
            }
        }

        return warnings;
    }

    private DetailedImportLogDto MapToDetailedImportLogDto(
        MasterProposal masterProposal,
        ProposalCounts componentProposalCounts,
        ProposalCounts bomProposalCounts,
        ProposalCounts componentImageProposalCounts,
        ProposalCounts drawingProposalCounts,
        List<string> allErrors,
        List<string> allWarnings,
        Dictionary<string, string> productProposalLabel,
        Guid? productImageId)
    {
        TimeSpan importTime = CalculateImportTime(masterProposal);

        return new DetailedImportLogDto(
            Id: masterProposal.Id,
            ExternalUserId: masterProposal.ExternalUserId!.Value,
            Origin: masterProposal.Origin.ToString(),
            ProductProposalCode: masterProposal.ProductCode!,
            productProposalLabel,
            productImageId,
            StartedAt: masterProposal.ImportStartedAt!.Value,
            Action: masterProposal.Action.ToString()!,
            Status: MapProposalStatusToImportStatus(masterProposal).ToString(),
            ImportTime: importTime,
            Errors: allErrors,
            Warnings: allWarnings,
            ComponentProposalCounts: componentProposalCounts,
            BomProposalCounts: bomProposalCounts,
            ComponentImageProposalCounts: componentImageProposalCounts,
            DrawingProposalCounts: drawingProposalCounts
        );
    }

    private TimeSpan CalculateImportTime(MasterProposal masterProposal)
    {
        return (masterProposal.ImportFinishedAt ?? Clock.Now) - masterProposal.ImportStartedAt!.Value;
    }

    private static ImportStatus MapProposalStatusToImportStatus(MasterProposal masterProposal)
    {
        if (masterProposal.ImportFinishedAt == null)
        {
            return ImportStatus.InProgress;
        }
        
        if (masterProposal.ImportHasErrors)
        {
            return ImportStatus.CompletedWithErrors;
        }

        return masterProposal.ImportHasWarnings ? ImportStatus.CompletedWithWarnings : ImportStatus.CompletedSuccessfully;
    }

    private static Dictionary<string, string> GetProductProposalLabel(MasterProposal masterProposal, List<ComponentProposal> componentProposals)
    {
        if (string.IsNullOrWhiteSpace(masterProposal.ProductCode))
        {
            return new Dictionary<string, string>();
        }
        
        ComponentProposal? componentProposal = componentProposals.LastOrDefault(cp =>
            string.Equals(cp.Code, masterProposal.ProductCode, StringComparison.CurrentCultureIgnoreCase) &&
            cp.MasterProposalId.Equals(masterProposal.Id) && cp.Type == nameof(ComponentType.Assembly));

        return componentProposal?.Translations ?? new Dictionary<string, string>();
    }

    private async Task<Guid?> GetProductImageIdAsync(string? productCode, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(productCode))
        {
            return null;
        }

        IQueryable<Component> componentQueryable = await ComponentRepository.GetQueryableAsync();
        Component? component = await ComponentRepository.AsyncExecuter.FirstOrDefaultAsync(
            componentQueryable.Where(c => c.Code == productCode),
            cancellationToken);

        return component?.ImageId;
    }
}
