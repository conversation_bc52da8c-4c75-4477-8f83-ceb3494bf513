using System.Threading;
using System.Threading.Tasks;
using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.ProductFamilies.Commands;

namespace SpareParts.Core.ProductFamilies.CommandHandlers;

public class UnlinkProductFromProductFamilyCommandHandler : ProductFamilyRequestBase, ICommandHandler<UnlinkProductFromProductFamilyCommand>
{
    public virtual async Task Handle(UnlinkProductFromProductFamilyCommand request, CancellationToken cancellationToken)
    {
        await ProductInProductFamilyRepository.DeleteAsync(pipf => pipf.ProductFamilyId == request.ProductFamilyId && pipf.ProductId == request.ProductId, cancellationToken: cancellationToken);
    }
}