using System;
using System.Threading;
using System.Threading.Tasks;
using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.Entities.ProductFamilies;
using SpareParts.Core.Entities.Resources;
using SpareParts.Core.ProductFamilies.Commands;
using SpareParts.Core.ProductFamilies.Dtos;
using SpareParts.Core.ProductFamilies.Dtos.Inputs;
using SpareParts.Core.Settings;
using Volo.Abp.Settings;

namespace SpareParts.Core.ProductFamilies.CommandHandlers;

public class UpdateProductFamilyCommandHandler : ProductFamilyRequestBase, ICommandHandler<UpdateProductFamilyCommand, ProductFamilyDto>
{
    public virtual async Task<ProductFamilyDto> Handle(UpdateProductFamilyCommand request, CancellationToken cancellationToken)
    {
        UpdateProductFamilyDto updateProductFamilyDto = request.UpdateProductFamilyDto;

        await CommandSender.Send(new SetProductFamilyTranslationsCommand(request.Id, updateProductFamilyDto.Translations), cancellationToken);
        
        ProductFamily productFamily = await ProductFamilyRepository.GetAsync(request.Id, cancellationToken: cancellationToken);

        productFamily.ChangeRank(updateProductFamilyDto.Rank);
        productFamily.IsVisible = updateProductFamilyDto.IsVisible;
        if (updateProductFamilyDto.ImageId.HasValue)
        {
            Resource? image = await ResourceRepository.FindAsync(updateProductFamilyDto.ImageId.Value, cancellationToken: cancellationToken);
            ArgumentNullException.ThrowIfNull(image);
            productFamily.ImageId = updateProductFamilyDto.ImageId.Value;
        }
        else
        {
            productFamily.ImageId = await SettingProvider.GetAsync<Guid>(CoreSettings.ProductFamilyDefaultImageId);
        }
        await SetParentAsync(productFamily, updateProductFamilyDto.ParentId, cancellationToken);
        
        productFamily = await ProductFamilyRepository.UpdateAsync(productFamily, true, cancellationToken: cancellationToken);

        ProductFamilyDto productFamilyDto = ObjectMapper.Map<ProductFamily, ProductFamilyDto>(productFamily);
        productFamilyDto.IsEdge = await IsEdgeAsync(request.Id, cancellationToken);

        return productFamilyDto;
    }

    private async Task SetParentAsync(ProductFamily productFamily, Guid? parentId, CancellationToken cancellationToken)
    {
        if (productFamily.ParentId != parentId)
        {
            if (parentId.HasValue)
            {
                await ProductFamilyDomainService.ChangeParentAsync(productFamily, parentId.Value, cancellationToken);
            }
            else
            {
                productFamily.SetAsRoot();
            }
        }
    }
}
