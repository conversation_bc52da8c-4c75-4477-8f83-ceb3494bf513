using AutoMapper;
using SpareParts.Common.Dtos;
using SpareParts.Core.Entities.ProductFamilies;
using SpareParts.Core.ProductFamilies.Commands;
using SpareParts.Core.ProductFamilies.Dtos;
using SpareParts.Core.ProductFamilies.Dtos.Inputs;
using Volo.Abp.AutoMapper;

namespace SpareParts.Core.ProductFamilies.Mapping;

public class ProductFamilyMappingProfile : Profile
{
    public ProductFamilyMappingProfile()
    {
        CreateMap<ProductFamilyTranslation, CommonTranslationDto>();
        
        CreateMap<CommonTranslationDto, ProductFamilyTranslation>()
            .ConstructUsing(src => new ProductFamilyTranslation(src.Language, src.Label, src.Description))
            .Ignore(x => x.TenantId)
            .Ignore(x => x.ProductFamilyId);

        CreateMap<ProductFamily, ProductFamilyDto>()
            .ForMember(c => c.ProductsCount,
                opt =>
                    opt.MapFrom(src => src.Products.Count))
            .Ignore(x => x.IsEdge);

        CreateMap<CreateProductFamilyDto, CreateProductFamilyCommand>();
    }
}