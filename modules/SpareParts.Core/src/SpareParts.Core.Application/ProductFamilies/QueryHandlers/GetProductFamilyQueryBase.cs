using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoFilterer.Extensions;
using Microsoft.AspNetCore.Authorization;
using SpareParts.Common.Dtos;
using SpareParts.Core.DataVisibility;
using SpareParts.Core.Entities.ProductFamilies;
using SpareParts.Core.Permissions;
using SpareParts.Core.ProductFamilies.Dtos;
using SpareParts.Core.ProductFamilies.QueryFilters;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Authorization;
using Volo.Abp.Domain.Repositories;

namespace SpareParts.Core.ProductFamilies.QueryHandlers;
public abstract class GetProductFamilyQueryBase : ProductFamilyRequestBase
{
    protected IAbpAuthorizationService AuthorizationService => LazyServiceProvider.LazyGetRequiredService<IAbpAuthorizationService>();
    protected ProductFamilyAccessControlService ProductFamilyAccessControlService => LazyServiceProvider.LazyGetRequiredService<ProductFamilyAccessControlService>();
    protected IReadOnlyRepository<ProductFamilyFlattenHierarchy> ProductFamilyFlattenHierarchyRepository => LazyServiceProvider.LazyGetRequiredService<IReadOnlyRepository<ProductFamilyFlattenHierarchy>>();

    /// <summary>
    /// Return ProductFamilies. If parentId is null, return root product families
    /// </summary>
    /// <param name="filter"></param>
    /// <param name="id"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task<PagedResultDto<ProductFamilyDto>> GetProductFamiliesAsync(ProductFamilyPaginationFilter filter, Guid? id = null, CancellationToken cancellationToken = default)
    {
        await CheckAndThrowIfProductFamilyNotExist(id, cancellationToken);
        if (string.IsNullOrEmpty(filter.Sort))
        {
            filter.Sort = nameof(ProductFamily.Rank);
        }
        return await GetPagedFilteredProductFamilies(filter, id, cancellationToken);
    }

    private async Task<PagedResultDto<ProductFamilyDto>> GetPagedFilteredProductFamilies(ProductFamilyPaginationFilter filter, Guid? parentId = null, CancellationToken cancellationToken = default)
    {
        IQueryable<ProductFamily> mainProductFamilies = await ProductFamilyReadOnlyRepository.GetQueryableAsync();
        mainProductFamilies = mainProductFamilies.Where(x => x.ParentId == parentId);

        if (!await AuthorizationService.IsGrantedAsync(CorePermissions.ShowHidden))
        {
            List<Guid> filteredIds = await ProductFamilyAccessControlService.GetVisibleIdsAsync(parentId, cancellationToken);
            mainProductFamilies = mainProductFamilies.Where(x => filteredIds.Contains(x.Id));
        }

        IQueryable<ProductFamilyDto> query = await GetProductFamilyQueryable(mainProductFamilies);

        long count = await ProductFamilyReadOnlyRepository.AsyncExecuter.LongCountAsync(query, cancellationToken);
        IQueryable<ProductFamilyDto> filteredProductFamilies = query.ApplyFilter(filter);
        List<ProductFamilyDto> result = await ProductFamilyReadOnlyRepository.AsyncExecuter.ToListAsync(filteredProductFamilies, cancellationToken);

        List<Guid> productFamilyIds = result.Select(x => x.Id).ToList();
        Dictionary<Guid, int> productFamilyCount = await GetProductFamilyIdsWithCount(productFamilyIds, cancellationToken);

        MapMissingProductCountProperty(result, productFamilyCount);

        return new PagedResultDto<ProductFamilyDto>(count, result);
    }

    private async Task<IQueryable<ProductFamilyDto>> GetProductFamilyQueryable(IQueryable<ProductFamily> mainProductFamilies)
    {
        IQueryable<ProductFamily> childProductFamiliesQuery = await ProductFamilyReadOnlyRepository.GetQueryableAsync();
        IQueryable<ProductFamilyDto> query = from mainProductFamily in mainProductFamilies
                                             join child in childProductFamiliesQuery on mainProductFamily.Id equals child.ParentId into g
                                             let ChildCount = g.Count()
                                             select new ProductFamilyDto
                                             {
                                                 Id = mainProductFamily.Id,
                                                 Code = mainProductFamily.Code,
                                                 Translations = ObjectMapper.Map<IReadOnlySet<ProductFamilyTranslation>, List<CommonTranslationDto>>(mainProductFamily.Translations),
                                                 ImageId = mainProductFamily.Image.Id,
                                                 Rank = mainProductFamily.Rank,
                                                 IsVisible = mainProductFamily.IsVisible,
                                                 CreationTime = mainProductFamily.CreationTime,
                                                 CreatorId = mainProductFamily.CreatorId,
                                                 LastModificationTime = mainProductFamily.LastModificationTime,
                                                 LastModifierId = mainProductFamily.LastModifierId,
                                                 ParentId = mainProductFamily.ParentId,
                                                 ProductsCount = 0,
                                                 IsEdge = ChildCount == 0
                                             };
        return query;
    }

    protected async Task<Dictionary<Guid, int>> GetProductFamilyIdsWithCount(List<Guid> productFamilyIds, CancellationToken cancellationToken)
    {
        IQueryable<ProductInProductFamily> productInProductFamiliesQueryable = await ProductInProductFamilyRepository.GetQueryableAsync();
        productInProductFamiliesQueryable = productInProductFamiliesQueryable.Where(x => productFamilyIds.Contains(x.ProductFamilyId));

        if (!await AuthorizationService.IsGrantedAsync(CorePermissions.ShowHidden))
        {
            List<Guid> visibleProductIds = await ProductFamilyAccessControlService.GetVisibleProductIdsAsync(productFamilyIds, cancellationToken);
            productInProductFamiliesQueryable = productInProductFamiliesQueryable.Where(x => visibleProductIds.Contains(x.ProductId));
        }
        List<ProductInProductFamily> productInProductFamilies = await ProductInProductFamilyRepository.AsyncExecuter.ToListAsync(productInProductFamiliesQueryable, cancellationToken: cancellationToken);

        Dictionary<Guid, int> productsCountByFamilyId = productInProductFamilies.GroupBy(x => x.ProductFamilyId)
            .ToDictionary(x => x.Key, x => x.Count());

        return productsCountByFamilyId;
    }

    protected static void MapMissingProductCountProperty(List<ProductFamilyDto> productFamilyDtos, Dictionary<Guid, int> productFamilyCount)
    {
        foreach (ProductFamilyDto productFamilyDto in productFamilyDtos)
        {
            if (productFamilyCount.TryGetValue(productFamilyDto.Id, out int count))
            {
                productFamilyDto.ProductsCount = count;
            }
        }
    }

    protected async Task<Dictionary<Guid, int>> GetProductIdsInProductFamilyWithRank(Guid productFamilyId, CancellationToken cancellationToken)
    {
        IQueryable<ProductInProductFamily> productInProductFamiliesQueryable = await ProductInProductFamilyRepository.GetQueryableAsync();
        productInProductFamiliesQueryable = productInProductFamiliesQueryable.Where(x => x.ProductFamilyId == productFamilyId);

        List<ProductInProductFamily> productInProductFamilies = await ProductInProductFamilyRepository.AsyncExecuter.ToListAsync(productInProductFamiliesQueryable, cancellationToken: cancellationToken);

        Dictionary<Guid, int> productsRank = productInProductFamilies.ToDictionary(x => x.ProductId, x => x.Rank);
        return productsRank;
    }

}
