using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SpareParts.Common.Dtos;
using SpareParts.Core.Entities.ProductFamilies;
using SpareParts.Core.ProductFamilies.Dtos;

namespace SpareParts.Core.ProductFamilies.QueryHandlers;

public class GetProductFamilyHierarchyBase : GetProductFamilyQueryBase
{
    protected async Task<Dictionary<Guid, ProductFamilyHierarchyDto>> GetAllProductFamilyHierarchyDtos(List<Guid> productFamilyIds)
    {
        IQueryable<ProductFamily> queryProductFamily = await ProductFamilyRepository.GetQueryableAsync();

        Dictionary<Guid, ProductFamilyHierarchyDto> productFamilyHierarchyDto = queryProductFamily
            .Where(pf => productFamilyIds.Contains(pf.Id))
            .Select(x => new ProductFamilyHierarchyDto
            {
                Id = x.Id,
                Translations = ObjectMapper.Map<IReadOnlySet<ProductFamilyTranslation>, List<CommonTranslationDto>>(x.Translations),
                ImageId = x.Image.Id
            }).ToDictionary(p => p.Id);
        return productFamilyHierarchyDto;
    }

}