using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoFilterer.Extensions;
using Microsoft.AspNetCore.Authorization;
using SpareParts.AbpMediatR.Handlers;
using SpareParts.Common.Dtos;
using SpareParts.Common.Settings;
using SpareParts.Core.Entities.Extensions;
using SpareParts.Core.Entities.ProductFamilies;
using SpareParts.Core.Permissions;
using SpareParts.Core.ProductFamilies.Dtos;
using SpareParts.Core.ProductFamilies.Queries;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.ChangeTracking;

namespace SpareParts.Core.ProductFamilies.QueryHandlers;
public class GetEdgeProductFamiliesQueryHandler : GetProductFamilyQueryBase, IQueryHandler<GetEdgeProductFamiliesQuery, PagedResultDto<ProductFamilyDto>>
{
    [DisableEntityChangeTracking]
    public async Task<PagedResultDto<ProductFamilyDto>> Handle(GetEdgeProductFamiliesQuery request, CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(request.Filter.Sort))
        {
            request.Filter.Sort = nameof(ProductFamily.Code);
        }

        IQueryable<ProductFamily> productFamilies = await ProductFamilyRepository.GetQueryableAsync();

        if (!await AuthorizationService.IsGrantedAsync(CorePermissions.ShowHidden))
        {
            List<Guid> filteredIds = await ProductFamilyAccessControlService.GetVisibleEdgeIdsAsync(cancellationToken);
            productFamilies = productFamilies.Where(x => filteredIds.Contains(x.Id));
        }

        productFamilies = await ApplyKeywordFilter(productFamilies, request.Filter.Keyword);

        IQueryable<ProductFamilyDto> query = await GetProductFamilyDtoQueryable(productFamilies);

        long count = await ProductFamilyRepository.AsyncExecuter.LongCountAsync(query, cancellationToken);
        IQueryable<ProductFamilyDto> filteredProductFamilies = query.ApplyFilter(request.Filter);
        List<ProductFamilyDto> result = await ProductFamilyRepository.AsyncExecuter.ToListAsync(filteredProductFamilies, cancellationToken);

        List<Guid> productFamilyIds = result.Select(x => x.Id).ToList();
        Dictionary<Guid, int> productFamilyCount = await GetProductFamilyIdsWithCount(productFamilyIds, cancellationToken);

        MapMissingProductCountProperty(result, productFamilyCount);

        return new PagedResultDto<ProductFamilyDto>(count, result);
    }

    private async Task<IQueryable<ProductFamily>> ApplyKeywordFilter(IQueryable<ProductFamily> productFamilyQueryable, string? keyword)
    {
        if (string.IsNullOrEmpty(keyword))
        {
            return productFamilyQueryable;
        }

        string currentLanguage = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
        string defaultLanguage = (await SettingProvider.GetOrNullAsync(CommonSettings.DefaultLanguage))!;
        productFamilyQueryable = productFamilyQueryable.GetProductFamilySearchQueryable(keyword, currentLanguage, defaultLanguage);
        return productFamilyQueryable;
    }

    private async Task<IQueryable<ProductFamilyDto>> GetProductFamilyDtoQueryable(IQueryable<ProductFamily> productFamilies)
    {
        IQueryable<ProductFamily> childProductFamilies = await ProductFamilyRepository.GetQueryableAsync();

        IQueryable<ProductFamilyDto> query = from productFamily in productFamilies
                                             join childFamily in childProductFamilies
                                                 on productFamily.Id equals childFamily.ParentId into childFamilies
                                             from childFamily in childFamilies.DefaultIfEmpty()
                                             where childFamily == null
                                             select new ProductFamilyDto()
                                             {
                                                 Id = productFamily.Id,
                                                 Code = productFamily.Code,
                                                 Translations = ObjectMapper.Map<List<ProductFamilyTranslation>, List<CommonTranslationDto>>(productFamily.Translations.ToList()),
                                                 ImageId = productFamily.ImageId,
                                                 Rank = productFamily.Rank,
                                                 IsVisible = productFamily.IsVisible,
                                                 IsEdge = true,
                                                 ParentId = productFamily.ParentId,
                                                 ProductsCount = 0
                                             };
        return query;
    }
}
