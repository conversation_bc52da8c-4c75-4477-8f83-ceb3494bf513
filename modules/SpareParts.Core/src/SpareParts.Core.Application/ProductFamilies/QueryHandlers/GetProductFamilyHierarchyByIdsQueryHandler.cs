using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.Entities.ProductFamilies;
using SpareParts.Core.ProductFamilies.Dtos;
using SpareParts.Core.ProductFamilies.Queries;
using Volo.Abp.Domain.ChangeTracking;

namespace SpareParts.Core.ProductFamilies.QueryHandlers;

[DisableEntityChangeTracking]
public class GetProductFamilyHierarchyByIdsQueryHandler : GetProductFamilyHierarchyBase, IQueryHandler<GetProductFamilyHierarchyByIdsQuery, Dictionary<Guid, List<ProductFamilyHierarchyDto>>>
{
    public async Task<Dictionary<Guid, List<ProductFamilyHierarchyDto>>> Handle(GetProductFamilyHierarchyByIdsQuery request, CancellationToken cancellationToken)
    {
        Dictionary<Guid, List<Guid>> productFamilyHierarchyIds = await GetProductFamilyHierarchyIds(request.Ids);

        List<Guid> allProductFamilyIds = productFamilyHierarchyIds.Values
            .SelectMany(productFamilyId => productFamilyId)
            .Distinct()
            .ToList();

        Dictionary<Guid, ProductFamilyHierarchyDto> productFamilyHierarchyDto = await GetAllProductFamilyHierarchyDtos(allProductFamilyIds);

        Dictionary<Guid, List<ProductFamilyHierarchyDto>> result = GetProductFamilyHierarchyDtoOrdered(productFamilyHierarchyIds, productFamilyHierarchyDto);
        return result;
    }

    private async Task<Dictionary<Guid, List<Guid>>> GetProductFamilyHierarchyIds(HashSet<Guid> productFamilyIds)
    {
        IQueryable<ProductFamilyFlattenHierarchy> queryHierarchy = await ProductFamilyFlattenHierarchyRepository.GetQueryableAsync();

        Dictionary<Guid, List<Guid>> productFamilyHierarchy = queryHierarchy
            .Where(x => productFamilyIds.Contains(x.Id))
            .Select(x => new { x.Id, x.Path })
            .AsEnumerable()
            .GroupBy(x => x.Id)
            .ToDictionary(
                g => g.Key,
                g => g.First().Path
                    .Split('/')
                    .Where(pathId => !string.IsNullOrEmpty(pathId))
                    .Select(Guid.Parse)
                    .Append(g.Key)
                    .ToList()
            );
        return productFamilyHierarchy;
    }

    private static Dictionary<Guid, List<ProductFamilyHierarchyDto>> GetProductFamilyHierarchyDtoOrdered(Dictionary<Guid, List<Guid>> hierarchies, Dictionary<Guid, ProductFamilyHierarchyDto> productFamilyHierarchyDto)
    {
        Dictionary<Guid, List<ProductFamilyHierarchyDto>> result = hierarchies.ToDictionary(
            hierarchy => hierarchy.Key,
            hierarchy =>
            {
                List<ProductFamilyHierarchyDto> productFamilyHierarchy = hierarchy.Value
                    .Select(id => productFamilyHierarchyDto[id])
                    .ToList();

                return productFamilyHierarchy;
            }
        );
        return result;
    }
}