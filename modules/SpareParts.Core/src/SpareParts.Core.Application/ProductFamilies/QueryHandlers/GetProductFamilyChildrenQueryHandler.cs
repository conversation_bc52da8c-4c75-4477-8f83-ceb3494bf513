using System.Threading;
using System.Threading.Tasks;
using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.ProductFamilies.Dtos;
using SpareParts.Core.ProductFamilies.Queries;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.ChangeTracking;

namespace SpareParts.Core.ProductFamilies.QueryHandlers;
public class GetProductFamilyChildrenQueryHandler : GetProductFamilyQueryBase, IQueryHandler<GetProductFamilyChildrenQuery, PagedResultDto<ProductFamilyDto>>
{
    [DisableEntityChangeTracking]
    public async Task<PagedResultDto<ProductFamilyDto>> Handle(GetProductFamilyChildrenQuery request, CancellationToken cancellationToken)
    {
        return await GetProductFamiliesAsync(request.Filter, request.Id, cancellationToken);
    }
}
