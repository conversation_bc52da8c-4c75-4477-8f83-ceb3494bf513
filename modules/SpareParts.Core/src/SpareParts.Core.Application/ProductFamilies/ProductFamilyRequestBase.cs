using System;
using System.Threading;
using System.Threading.Tasks;
using SpareParts.Core.DomainServices.ProductFamilies;
using SpareParts.Core.Entities.ProductFamilies;
using SpareParts.Core.Entities.Resources;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace SpareParts.Core.ProductFamilies;

public abstract class ProductFamilyRequestBase : CoreRequestBase
{
    protected IRepository<ProductFamily, Guid> ProductFamilyRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<ProductFamily, Guid>>();
    protected ProductFamilyDomainService ProductFamilyDomainService => LazyServiceProvider.LazyGetRequiredService<ProductFamilyDomainService>();
    protected IRepository<Resource, Guid> ResourceRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Resource, Guid>>();
    protected IRepository<ProductInProductFamily> ProductInProductFamilyRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<ProductInProductFamily>>();
    protected IReadOnlyRepository<ProductInProductFamily> ProductInProductFamilyReadOnlyRepository => LazyServiceProvider.LazyGetRequiredService<IReadOnlyRepository<ProductInProductFamily>>();
    protected IReadOnlyRepository<ProductFamily, Guid> ProductFamilyReadOnlyRepository => LazyServiceProvider.LazyGetRequiredService<IReadOnlyRepository<ProductFamily, Guid>>();

    protected async Task<bool> IsEdgeAsync(Guid id, CancellationToken cancellationToken)
    {
        return !await ProductFamilyRepository.AnyAsync(pf => pf.ParentId == id, cancellationToken);
    }

    protected async Task CheckAndThrowIfProductFamilyNotExist(Guid? id, CancellationToken cancellationToken)
    {
        if (id.HasValue)
        {
            if (await ProductFamilyRepository.FindAsync(id.Value, false, cancellationToken) == null)
            {
                throw new EntityNotFoundException($"The product family with Id {id.Value} not found");
            }
        }
    }
}