using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.Components.Dtos;
using SpareParts.Core.Components.Queries;
using SpareParts.Core.DataVisibility;
using SpareParts.Core.Entities.Components;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.ChangeTracking;

namespace SpareParts.Core.Components.QueryHandlers;
public class GetComponentByCodeQueryHandler : ComponentRequestBase, IQueryHandler<GetComponentByCodeQuery, ComponentDto>
{
    private readonly ComponentAccessControlService _componentAccessControlService;

    public GetComponentByCodeQueryHandler(ComponentAccessControlService componentAccessControlService)
    {
        _componentAccessControlService = componentAccessControlService;
    }
    [DisableEntityChangeTracking]
    public async Task<ComponentDto> Handle(GetComponentByCodeQuery request, CancellationToken cancellationToken)
    {
        Component component = await _componentAccessControlService.GetByCodeAsync(request.Code);
        return ObjectMapper.Map<Component, ComponentDto>(component);
    }
}
