using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.Components.Dtos.Products;
using SpareParts.Core.Components.Queries.Products;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.ChangeTracking;
using Volo.Abp.Domain.Entities;

namespace SpareParts.Core.Components.QueryHandlers.Products;

public class SearchComponentByProductCodeQueryHandler : SearchComponentByProductPropertyBase, IQueryHandler<SearchComponentByProductCodeQuery, PagedResultDto<ComponentInProductDto>>
{
    [DisableEntityChangeTracking]
    public async Task<PagedResultDto<ComponentInProductDto>> Handle(SearchComponentByProductCodeQuery request, CancellationToken cancellationToken)
    {
        List<(Guid ComponentId, string Path, int Level)> componentsHierarchy = await GetProductComponents(x => x.ProductCode == request.Code);

        if (componentsHierarchy.Count == 0)
        {
            throw new EntityNotFoundException($"Product with Code {request.Code} not found");
        }

        return await GetPaginatedComponentsInProductDto(request.Filter, componentsHierarchy, cancellationToken);
    }
}