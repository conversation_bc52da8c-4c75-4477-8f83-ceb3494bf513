using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.Components.Dtos.Products;
using SpareParts.Core.Components.Queries.Products;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.ChangeTracking;
using Volo.Abp.Domain.Entities;

namespace SpareParts.Core.Components.QueryHandlers.Products;

public class SearchComponentByProductIdQueryHandler : SearchComponentByProductPropertyBase, IQueryHandler<SearchComponentByProductIdQuery, PagedResultDto<ComponentInProductDto>>
{
    [DisableEntityChangeTracking]
    public async Task<PagedResultDto<ComponentInProductDto>> Handle(SearchComponentByProductIdQuery request, CancellationToken cancellationToken)
    {
        List<(Guid ComponentId, string Path, int Level)> productComponents = await GetProductComponents(x => x.ProductId == request.Id);

        if (productComponents.Count == 0)
        {
            throw new EntityNotFoundException($"Product with Id {request.Id} not found");
        }

        return await GetPaginatedComponentsInProductDto(request.Filter, productComponents, cancellationToken);
    }
}