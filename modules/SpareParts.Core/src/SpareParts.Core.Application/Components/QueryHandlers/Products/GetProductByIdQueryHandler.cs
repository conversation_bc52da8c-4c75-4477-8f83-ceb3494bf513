using SpareParts.AbpMediatR.Handlers;
using SpareParts.Common;
using SpareParts.Common.DataFilter;
using SpareParts.Core.Components.Dtos.Products;
using SpareParts.Core.Components.Queries.Products;
using SpareParts.Core.Features;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.ChangeTracking;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.GlobalFeatures;

namespace SpareParts.Core.Components.QueryHandlers.Products;

[DisableEntityChangeTracking]
public class GetProductByIdQueryHandler : GetProductQueryBase, IQueryHandler<GetProductByIdQuery, ProductDto>
{

    public async Task<ProductDto> Handle(GetProductByIdQuery request, CancellationToken cancellationToken)
    {
        using (DataFilter.Disable<IHasPublic>())
        {
            IQueryable<ProductDto> productDtoQueryable = await GetProductDtoQueryable();
            ProductDto product = await ProductRepository.AsyncExecuter.SingleOrDefaultAsync(productDtoQueryable.Where(p => p.Id == request.Id), cancellationToken)
                                 ?? throw new EntityNotFoundException($"Product with id {request.Id} not found");

            if (!product.IsPublic && !CurrentUser.BelongsToInternalCompany())
            {
                bool isEquipmentEnabled = GlobalFeatureManager.Instance.Modules.CoreFeatures().Equipment.IsEnabled;
                if (!isEquipmentEnabled)
                {
                    throw new EntityNotFoundException($"Product with id {request.Id} not found");
                }

                bool hasEquipment = await EquipmentRepository.AnyAsync(x => x.ProductId == request.Id, cancellationToken: cancellationToken);
                if (!hasEquipment)
                {
                    throw new EntityNotFoundException($"Product with id {request.Id} not found");
                }
            }
            product.ChildCount = await GetProductChildCount(product.Id);

            return product;
        }
    }
}
