using SpareParts.AbpMediatR.Handlers;
using SpareParts.Common;
using SpareParts.Core.Components.Dtos.Products;
using SpareParts.Core.Components.Queries.Products;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Users;

namespace SpareParts.Core.Components.QueryHandlers.Products;
public class GetPublishedProductsStatsQueryHandler : ProductRequestBase, IQueryHandler<GetPublishedProductsStatsQuery, PublishedProductsStatsDto>
{
    protected ICurrentUser CurrentUser => LazyServiceProvider.LazyGetRequiredService<ICurrentUser>();

    public async Task<PublishedProductsStatsDto> Handle(GetPublishedProductsStatsQuery request, CancellationToken cancellationToken)
    {
        CurrentUser.ThrowAuthorizationExceptionIfUserDoesNotBelongToInternalCompany();

        int count = await ProductReadOnlyRepository.CountAsync(p => p.IsVisible, cancellationToken: cancellationToken);
        return new PublishedProductsStatsDto(){Count = count};
    }
}
