using AutoFilterer.Extensions;
using SpareParts.Common.Settings;
using SpareParts.Core.Components.Dtos.Products;
using SpareParts.Core.Components.QueryFilters.Products;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Extensions;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace SpareParts.Core.Components.QueryHandlers.Products;

public class SearchComponentByProductPropertyBase : ProductRequestBase
{
    protected IRepository<ProductFlattenHierarchy> ProductFlattenHierarchyRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<ProductFlattenHierarchy>>();

    protected async Task<List<(Guid ComponentId, string Path, int Level)>> GetProductComponents(Expression<Func<ProductFlattenHierarchy, bool>> productFilterExpression)
    {
        IQueryable<ProductFlattenHierarchy> productFlattenHierarchyQueryable = await ProductFlattenHierarchyRepository.GetQueryableAsync();
        productFlattenHierarchyQueryable = productFlattenHierarchyQueryable.Where(productFilterExpression);
        var productComponents = productFlattenHierarchyQueryable.Select(x => new { x.ComponentId, x.Path, x.Level }).ToList();
        return productComponents.Select(x => (x.ComponentId, x.Path, x.Level)).ToList();
    }

    protected async Task<PagedResultDto<ComponentInProductDto>> GetPaginatedComponentsInProductDto(ProductComponentSearchPaginationFilter filter,
                  List<(Guid ComponentId, string Path, int Level)> productComponents, CancellationToken cancellationToken)
    {
        HashSet<Guid> componentIds = productComponents.Where(x => x.Level > 0).Select(x => x.ComponentId).Distinct().ToHashSet();

        IQueryable<Guid> componentIdsFilteredByIdsAndKeywordQuery = await GetComponentIdsFilteredByIdsAndKeywordQuery(filter.Keyword, componentIds);

        long count = await ProductFlattenHierarchyRepository.AsyncExecuter.LongCountAsync(componentIdsFilteredByIdsAndKeywordQuery, cancellationToken);
        List<Guid> componentIdsInProduct = await ProductFamilyReadOnlyRepository.AsyncExecuter.ToListAsync(componentIdsFilteredByIdsAndKeywordQuery.ApplyFilter(filter), cancellationToken);

        List<ComponentInProductDto> componentsInProductDto = GetComponentInProductDto(productComponents, componentIdsInProduct);

        return new PagedResultDto<ComponentInProductDto>(count, componentsInProductDto);
    }

    protected async Task<IQueryable<Guid>> GetComponentIdsFilteredByIdsAndKeywordQuery(string keyword, HashSet<Guid> componentIdsFromProduct)
    {
        string currentLanguage = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
        string defaultLanguage = (await SettingProvider.GetOrNullAsync(CommonSettings.DefaultLanguage))!;

        IQueryable<Component> componentQueryable = await ComponentRepository.GetQueryableAsync();
        componentQueryable = componentQueryable.Where(x => componentIdsFromProduct.Contains(x.Id));

        componentQueryable = componentQueryable.GetComponentSearchQueryable(keyword, currentLanguage, defaultLanguage);
        return componentQueryable.Select(x => x.Id);
    }

    protected static List<ComponentInProductDto> GetComponentInProductDto(List<(Guid ComponentId, string Path, int Level)> productComponents, List<Guid> componentIdsInProduct)
    {
        return productComponents
            .Where(x => componentIdsInProduct.Contains(x.ComponentId))
            .GroupBy(x => x.ComponentId)
            .Select(g => new ComponentInProductDto
            {
                ComponentId = g.First().ComponentId,
                Contexts =
                [
                    ..g.Select(y => new ComponentInProductContextDto() { Level = y.Level, Path = y.Path })
                    .DistinctBy(x=> x.Path).ToList()
                ]
            }).ToList();
    }
}