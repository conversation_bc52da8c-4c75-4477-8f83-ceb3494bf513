using AutoFilterer.Extensions;
using SpareParts.AbpMediatR.Handlers;
using SpareParts.Common.Settings;
using SpareParts.Core.Components.Dtos.Products;
using SpareParts.Core.Components.Queries.Products;
using SpareParts.Core.Components.QueryHandlers.Products.Extensions;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;

namespace SpareParts.Core.Components.QueryHandlers.Products;
public class GetProductsQueryHandler : ProductRequestBase, IQueryHandler<GetProductsQuery, PagedResultDto<ProductDto>>
{
    public async Task<PagedResultDto<ProductDto>> Handle(GetProductsQuery request, CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(request.Filter.Sort))
        {
            request.Filter.Sort = nameof(ProductDto.Id);
        }

        IQueryable<ProductDto> productDtoQueryable = await GetProductDtoQueryable();
        IQueryable<ProductDto> productQueryableFiltered = await ApplyTextFilter(productDtoQueryable, request);

        IQueryable<ProductDto> productQueryableFilteredWithoutPagination = productQueryableFiltered.ApplyFilterWithoutPagination(request.Filter);
        long count = await ProductReadOnlyRepository.AsyncExecuter.LongCountAsync(productQueryableFilteredWithoutPagination, cancellationToken);

        List<ProductDto> products = await ProductReadOnlyRepository.AsyncExecuter.ToListAsync(productQueryableFiltered.ApplyFilter(request.Filter), cancellationToken);

        await MapMissingProductChildCountProperty(products);

        return new PagedResultDto<ProductDto>(count, products);
    }

    private async Task<IQueryable<ProductDto>> ApplyTextFilter(IQueryable<ProductDto> productDtoQueryable, GetProductsQuery request)
    {
        if (string.IsNullOrEmpty(request.Filter.Text))
        {
            return productDtoQueryable;
        }

        string currentLanguage = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
        string defaultLanguage = (await SettingProvider.GetOrNullAsync(CommonSettings.DefaultLanguage))!;
        productDtoQueryable = productDtoQueryable.GetProductDtoSearchQueryable(request.Filter.Text, currentLanguage, defaultLanguage);
        return productDtoQueryable;

    }
}
