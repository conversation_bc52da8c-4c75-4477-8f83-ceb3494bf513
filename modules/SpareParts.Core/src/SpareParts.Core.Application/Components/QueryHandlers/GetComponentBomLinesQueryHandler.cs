using AutoFilterer.Extensions;
using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.Components.Dtos;
using SpareParts.Core.Components.Queries;
using SpareParts.Core.DataVisibility;
using SpareParts.Core.Entities.Boms;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.ChangeTracking;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace SpareParts.Core.Components.QueryHandlers;

public class GetComponentBomLinesQueryHandler : ComponentRequestBase, IQueryHandler<GetComponentBomLinesQuery, PagedResultDto<BomLineDto>>, IComponentVisibilityCheckingEnabled
{
    [DisableEntityChangeTracking]
    public async Task<PagedResultDto<BomLineDto>> Handle(GetComponentBomLinesQuery request, CancellationToken cancellationToken)
    {
        bool exist = await ComponentRepository.AnyAsync(x => x.Id == request.ComponentId, cancellationToken);
        if (!exist)
        {
            throw new EntityNotFoundException($"Component with Id {request.ComponentId} not found");
        }

        IQueryable<BomLine> bomLineQueryable = (await BomLineRepository.GetQueryableAsync()).Where(b => b.ParentAssemblyId == request.ComponentId);
        IQueryable<BomLine> bomLineQueryableFiltered = bomLineQueryable.ApplyFilterWithoutPagination(request.Filter);

        long count = await BomLineRepository.AsyncExecuter.LongCountAsync(bomLineQueryableFiltered, cancellationToken);

        List<BomLine> bomLines = await BomLineRepository.AsyncExecuter.ToListAsync(bomLineQueryable.ApplyFilter(request.Filter), cancellationToken);
        List<BomLineDto> result = ObjectMapper.Map<List<BomLine>, List<BomLineDto>>(bomLines);

        return new PagedResultDto<BomLineDto>(count, result);
    }
}
