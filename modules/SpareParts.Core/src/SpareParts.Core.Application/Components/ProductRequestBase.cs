using SpareParts.Common.Dtos;
using SpareParts.Core.Components.Dtos.Products;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.ProductFamilies;
using System;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace SpareParts.Core.Components;

public class ProductRequestBase : ComponentRequestBase
{
    protected IReadOnlyRepository<ProductInProductFamily> ProductInProductFamilyReadOnlyRepository => LazyServiceProvider.LazyGetRequiredService<IReadOnlyRepository<ProductInProductFamily>>();
    protected IReadOnlyRepository<ProductFamily, Guid> ProductFamilyReadOnlyRepository => LazyServiceProvider.LazyGetRequiredService<IReadOnlyRepository<ProductFamily, Guid>>();

    protected async Task<IQueryable<ProductDto>> GetProductDtoQueryable()
    {
        IQueryable<Product> productQueryable = await ProductReadOnlyRepository.GetQueryableAsync();
        IQueryable<ProductInProductFamily> productInProductFamiliesQueryable = await ProductInProductFamilyReadOnlyRepository.GetQueryableAsync();

        IQueryable<ProductDto> productDtoQueryable =
            from product in productQueryable
            join pf in productInProductFamiliesQueryable on product.Id equals pf.ProductId into joinGroup
            let productFamilyCount = joinGroup.Count()
            select new ProductDto
            {
                Id = product.Id,
                Code = product.Component.Code,
                Translations = product.Component.Translations.Select(t => new CommonTranslationDto{Language = t.Language, Label = t.Label, Description = t.Description}).ToList(),
                OnlineTranslations = product.Component.OnlineTranslations.Select(t => new CommonOnlineTranslationDto { Language = t.Language, Label = t.Label, Description = t.Description }).ToList(),
                IsVisible = product.IsVisible,
                IsPublic = product.IsPublic,
                CreationTime = product.CreationTime,
                CreatorId = product.CreatorId,
                LastModificationTime = product.LastModificationTime,
                LastModifierId = product.LastModifierId,
                ImageId = product.Component.ImageId,
                IsInProductFamily = productFamilyCount > 0,
                ChildCount = 0,
            };
        return productDtoQueryable;
    }
}