using Microsoft.Extensions.DependencyInjection;
using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.Components.Commands;
using SpareParts.Core.DomainServices.Components;
using SpareParts.Core.Entities.Boms;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace SpareParts.Core.Components.CommandHandlers;

public class CreateBomLineCommandHandler : CoreRequestBase, ICommandHandler<CreateBomLineCommand>
{
    private IRepository<BomLine> Repository => LazyServiceProvider.GetRequiredService<IRepository<BomLine>>();
    private BomLineDomainService DomainService => LazyServiceProvider.GetRequiredService<BomLineDomainService>();

    public virtual async Task Handle(CreateBomLineCommand request, CancellationToken cancellationToken)
    {
        BomLine bomLine = await DomainService.CreateAsync(request.ParentAssemblyId, request.ChildComponentId, request.Rank,
            request.Quantity, cancellationToken);
        await Repository.InsertAsync(bomLine, cancellationToken: cancellationToken);
    }
}