using System.Threading;
using System.Threading.Tasks;
using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.Components.Commands;
using SpareParts.Core.Entities.Components;
using Volo.Abp.Domain.Repositories;

namespace SpareParts.Core.Components.CommandHandlers;

public class AttachDocumentToComponentCommandHandler : CoreRequestBase, ICommandHandler<AttachDocumentToComponentCommand>
{
    private IRepository<ComponentDocument> ComponentDocumentRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<ComponentDocument>>();

    public virtual async Task Handle(AttachDocumentToComponentCommand request, CancellationToken cancellationToken)
    {
        await ComponentDocumentRepository.InsertAsync(new ComponentDocument(request.ComponentId, request.DocumentId), cancellationToken: cancellationToken);
    }
}