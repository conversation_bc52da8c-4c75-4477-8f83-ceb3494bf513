using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.Components.Commands;
using SpareParts.Core.DomainServices.Components;
using SpareParts.Core.Entities.Boms;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Enums;
using Volo.Abp.Domain.Repositories;

namespace SpareParts.Core.Components.CommandHandlers;

public class SetBomCommandHandler : CoreRequestBase, ICommandHandler<SetBomCommand>
{
    private IRepository<Component> ComponentRepository => LazyServiceProvider.GetRequiredService<IRepository<Component>>();
    private BomLineDomainService BomLineDomainService => LazyServiceProvider.GetRequiredService<BomLineDomainService>();

    public async Task Handle(SetBomCommand request, CancellationToken cancellationToken)
    {
        Component parentAssembly = await ComponentRepository.AsyncExecuter.FirstAsync(
            await ComponentRepository.WithDetailsAsync(c => c.BomLines), c =>
                c.Id == request.ParentAssemblyId && c.Type == ComponentType.Assembly, cancellationToken);
        HashSet <BomLine> newBomLines = [];
        foreach (ChildBomLine childBomLine in request.ChildBomLines)
        {
            newBomLines.Add(await BomLineDomainService.CreateAsync(request.ParentAssemblyId, childBomLine.ChildComponentId,
                childBomLine.Rank, childBomLine.Quantity, cancellationToken));
        }

        parentAssembly.SetBomLines(newBomLines);
        await ComponentRepository.UpdateAsync(parentAssembly, cancellationToken: cancellationToken);
    }
}