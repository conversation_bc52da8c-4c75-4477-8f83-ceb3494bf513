using System;
using System.Threading;
using System.Threading.Tasks;
using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.Components.Commands.Images;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Resources;
using SpareParts.Core.Settings;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Settings;

namespace SpareParts.Core.Components.CommandHandlers;

public class DeleteComponentImageCommandHandler : CoreRequestBase, ICommandHandler<DeleteComponentImageCommand>
{
    private IRepository<Component, Guid> ComponentRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Component, Guid>>();
    private IRepository<Resource, Guid> ResourceRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Resource, Guid>>();

    public virtual async Task Handle(DeleteComponentImageCommand request, CancellationToken cancellationToken)
    {
        Component component = await ComponentRepository.GetAsync(request.ComponentId, cancellationToken: cancellationToken);
        Guid componentDefaultImageId = await SettingProvider.GetAsync<Guid>(CoreSettings.ComponentDefaultImageId);
        if (component.ImageId == componentDefaultImageId)
        {
            return;
        }

        Resource defaultImage = await ResourceRepository.GetAsync(componentDefaultImageId, cancellationToken: cancellationToken);
        Guid imageId = component.ImageId;
        component.ChangeImage(defaultImage);

        await ComponentRepository.UpdateAsync(component, true, cancellationToken);
        await CommandSender.Send(new DeleteImageCommand(imageId), cancellationToken);
    }
}