using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.Components.Commands.Products;
using SpareParts.Core.Components.Dtos.Products;
using SpareParts.Core.Entities.Components;
using System;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp;

namespace SpareParts.Core.Components.CommandHandlers.Products;

public class UpdateProductPublicFlagCommandHandler : ProductRequestBase, ICommandHandler<UpdateProductPublicFlagCommand, ProductPublicFlagDto>
{
    public virtual async Task<ProductPublicFlagDto> Handle(UpdateProductPublicFlagCommand request, CancellationToken cancellationToken)
    {
        Check.NotDefaultOrNull<Guid>(request.Id, nameof(request.Id));

        Product product = await ProductRepository.GetAsync(request.Id, cancellationToken: cancellationToken);
        product.IsPublic = request.IsPublic;

        product = await ProductRepository.UpdateAsync(product, true, cancellationToken: cancellationToken);

        return ObjectMapper.Map<Product, ProductPublicFlagDto>(product);
    }
}