using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.Components.Commands.Products;
using SpareParts.Core.DomainServices.Components;
using SpareParts.Core.Entities.Components;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace SpareParts.Core.Components.CommandHandlers.Products;

public class CreateProductCommandHandler : CoreRequestBase, ICommandHandler<CreateProductCommand>
{
    private ProductDomainService ProductDomainService => LazyServiceProvider.LazyGetRequiredService<ProductDomainService>();

    public virtual async Task Handle(CreateProductCommand request, CancellationToken cancellationToken)
    {
        if (!await ProductRepository.AnyAsync(p => p.Id == request.AssemblyId, cancellationToken: cancellationToken))
        {
            Product product = await ProductDomainService.CreateAsync(request.AssemblyId, cancellationToken);
            await ProductRepository.InsertAsync(product, cancellationToken: cancellationToken);
        }
    }
}