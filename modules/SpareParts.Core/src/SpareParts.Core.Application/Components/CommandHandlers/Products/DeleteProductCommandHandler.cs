using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.Components.Commands.Products;
using System.Threading;
using System.Threading.Tasks;

namespace SpareParts.Core.Components.CommandHandlers.Products;

public class DeleteProductCommandHandler : CoreRequestBase, ICommandHandler<DeleteProductCommand>
{
    public virtual async Task Handle(DeleteProductCommand request, CancellationToken cancellationToken)
    {
        await ProductRepository.DeleteAsync(p => p.Id == request.Id, cancellationToken: cancellationToken);
    }
}