using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.Components.Commands;
using SpareParts.Core.Entities.Components;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace SpareParts.Core.Components.CommandHandlers;

public class DetachDocumentFromComponentCommandHandler : CoreRequestBase, ICommandHandler<DetachDocumentFromComponentCommand>
{
    private IRepository<ComponentDocument> ComponentDocumentRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<ComponentDocument>>();

    public virtual async Task Handle(DetachDocumentFromComponentCommand request, CancellationToken cancellationToken)
    {
        await ComponentDocumentRepository.DeleteAsync(cd => cd.ComponentId.Equals(request.ComponentId) && cd.DocumentId.Equals(request.DocumentId), cancellationToken: cancellationToken);
    }
}