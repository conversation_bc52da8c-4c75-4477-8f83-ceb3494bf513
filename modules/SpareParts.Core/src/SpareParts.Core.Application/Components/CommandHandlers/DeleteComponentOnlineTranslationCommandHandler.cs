using SpareParts.AbpMediatR.Handlers;
using SpareParts.Common.Translations;
using SpareParts.Core.Components.Commands;
using SpareParts.Core.Entities.Components;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;

namespace SpareParts.Core.Components.CommandHandlers;

public class DeleteComponentOnlineTranslationCommandHandler : CoreRequestBase, ICommandHandler<DeleteComponentOnlineTranslationCommand>
{
    private TranslationValidator TranslationValidator => LazyServiceProvider.LazyGetRequiredService<TranslationValidator>();
    private IRepository<ComponentOnlineTranslation> TranslationRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<ComponentOnlineTranslation>>();

    public virtual async Task Handle(DeleteComponentOnlineTranslationCommand request, CancellationToken cancellationToken)
    {
        Check.NotNullOrWhiteSpace(request.Language, nameof(request.Language));
        await TranslationValidator.CheckLanguageForDeletion(request.Language);
        await TranslationRepository.DeleteAsync(
            t => t.Language.Equals(request.Language) && t.ComponentId == request.Id,
            cancellationToken: cancellationToken
        );
    }
}