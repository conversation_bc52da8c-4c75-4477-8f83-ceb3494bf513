using SpareParts.Common.Dtos;
using SpareParts.Core.Components.Commands;
using SpareParts.Core.Entities.Components;

namespace SpareParts.Core.Components.CommandHandlers;

public class SetComponentTranslationsCommandHandler : SetTranslationsCommandHandler<ComponentTranslation, Component, SetComponentTranslationsCommand>
{
    protected override ComponentTranslation CreateTranslation(CommonTranslationDto translationDto)
    {
        return new ComponentTranslation(translationDto.Language, translationDto.Label, translationDto.Description);
    }
}