using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.Components.Commands;
using SpareParts.Core.Components.Commands.Products;
using SpareParts.Core.Drawings.Commands;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Enums;
using System;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace SpareParts.Core.Components.CommandHandlers;

public class ChangeComponentTypeCommandHandler : CoreRequestBase, ICommandHandler<ChangeComponentTypeCommand>
{
    private IRepository<Component, Guid> ComponentRepository =>
        LazyServiceProvider.LazyGetRequiredService<IRepository<Component, Guid>>();

    public virtual async Task Handle(ChangeComponentTypeCommand request, CancellationToken cancellationToken)
    {
        Component? component = await ComponentRepository.FindAsync(c => c.Id == request.Id && c.Type != request.Type, cancellationToken: cancellationToken);
        if (component == null)
        {
            return;
        }
        if (component.Type == ComponentType.Assembly && request.Type == ComponentType.Part)
        {
            await CommandSender.Send(new DeleteBomLinesCommand(request.Id, null), cancellationToken);
            await CommandSender.Send(new DeleteDrawingsCommand(request.Id), cancellationToken);
            await CommandSender.Send(new DeleteProductCommand(component.Id), cancellationToken);
        }
        component.ChangeType(request.Type);
        await ComponentRepository.UpdateAsync(component, cancellationToken: cancellationToken);
    }
}