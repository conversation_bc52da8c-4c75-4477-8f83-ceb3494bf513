using System;
using System.Threading;
using System.Threading.Tasks;
using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.Components.Commands.Images;
using SpareParts.Core.DomainServices.Resources;
using SpareParts.Core.Entities.Resources;
using SpareParts.Core.Settings;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Settings;

namespace SpareParts.Core.Components.CommandHandlers;

public class DeleteImageCommandHandler : CoreRequestBase, ICommandHandler<DeleteImageCommand>
{
    private IRepository<Resource, Guid> ResourceRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Resource, Guid>>();
    private ImageManager ImageManager => LazyServiceProvider.LazyGetRequiredService<ImageManager>();

    public virtual async Task Handle(DeleteImageCommand request, CancellationToken cancellationToken)
    {
        Guid componentDefaultImageId = await SettingProvider.GetAsync<Guid>(CoreSettings.ComponentDefaultImageId);
        Guid productFamilyDefaultImageId = await SettingProvider.GetAsync<Guid>(CoreSettings.ProductFamilyDefaultImageId);
        if (request.ImageId == componentDefaultImageId || request.ImageId == productFamilyDefaultImageId)
        {
            return;
        }
        await ResourceRepository.DeleteAsync(r => r.Id == request.ImageId, cancellationToken: cancellationToken);
        await ImageManager.DeleteBlobAsync(request.ImageId, cancellationToken);
    }
}