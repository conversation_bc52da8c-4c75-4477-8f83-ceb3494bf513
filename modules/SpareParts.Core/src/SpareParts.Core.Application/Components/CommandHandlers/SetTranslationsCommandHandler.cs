using SpareParts.AbpMediatR.Handlers;
using SpareParts.Common.Dtos;
using SpareParts.Common.Translations;
using SpareParts.Core.Components.Commands;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace SpareParts.Core.Components.CommandHandlers;

public abstract class SetTranslationsCommandHandler<TTranslation, TEntity, TSetTranslationsCommand> : CoreRequestBase, ICommandHandler<TSetTranslationsCommand>
    where TTranslation : CommonTranslation
    where TEntity : class, ITranslatedEntity<TTranslation>, IEntity<Guid>
    where TSetTranslationsCommand : SetTranslationsCommand
{
    private TranslationValidator TranslationValidator => LazyServiceProvider.LazyGetRequiredService<TranslationValidator>();
    private IRepository<TEntity, Guid> EntityRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<TEntity, Guid>>();

    public virtual async Task Handle(TSetTranslationsCommand request, CancellationToken cancellationToken)
    {
        await TranslationValidator.CheckTranslationsForUpsertAsync(request.Translations.Select(x => x.Language).ToList());
        TEntity entity = await EntityRepository.GetAsync(request.Id, cancellationToken: cancellationToken);
        entity.SetTranslations(request.Translations.Select(CreateTranslation).ToHashSet());
        await EntityRepository.UpdateAsync(entity, cancellationToken: cancellationToken);
    }

    protected abstract TTranslation CreateTranslation(CommonTranslationDto translationDto);
}