using AutoMapper;
using SpareParts.Core.Components.Dtos.Products;
using SpareParts.Core.Entities.Components;
using Volo.Abp.AutoMapper;

namespace SpareParts.Core.Components.Mapping.Products;
public class ProductMappingProfile : Profile
{
    public ProductMappingProfile()
    {
        CreateMap<Product, ProductDto>()
            .ForMember(dest => dest.Code, opt => opt.MapFrom(src => src.Component.Code))
            .ForMember(dest => dest.ImageId, opt => opt.MapFrom(src => src.Component.ImageId))
            .ForMember(dest => dest.Translations, opt => opt.MapFrom(src => src.Component.Translations))
            .ForMember(dest => dest.OnlineTranslations, opt => opt.MapFrom(src => src.Component.OnlineTranslations))
            .Ignore(src => src.IsInProductFamily)
            .Ignore(src => src.ChildCount);

        CreateMap<Product, ProductVisibilityDto>();
        CreateMap<Product, ProductPublicFlagDto>();
    }
}
