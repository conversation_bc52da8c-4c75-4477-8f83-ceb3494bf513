using AutoMapper;
using SpareParts.Common.Dtos;
using SpareParts.Core.Components.Dtos;
using SpareParts.Core.Entities.Boms;
using SpareParts.Core.Entities.Components;

namespace SpareParts.Core.Components.Mapping;
public class ComponentMappingProfile : Profile
{
    public ComponentMappingProfile()
    {
        CreateMap<Component, ComponentDto>()
            .ForMember(dest => dest.ChildrenCount,
                opt => opt.MapFrom(src => src.Children.Count));
        CreateMap<ComponentTranslation, CommonTranslationDto>();
        CreateMap<ComponentOnlineTranslation, CommonOnlineTranslationDto>();
        CreateMap<BomLine, BomLineDto>();
    }
}