using Microsoft.AspNetCore.Authorization;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Permissions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Authorization;
using Volo.Abp.Domain.Repositories;

namespace SpareParts.Core.Components;

public abstract class ComponentRequestBase : CoreRequestBase
{
    protected IRepository<Component, Guid> ComponentRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Component, Guid>>();
    protected IReadOnlyRepository<ProductFlattenHierarchy> ProductFlattenHierarchyReadOnlyRepository => LazyServiceProvider.LazyGetRequiredService<IReadOnlyRepository<ProductFlattenHierarchy>>();
    protected IAbpAuthorizationService AuthorizationService => LazyServiceProvider.LazyGetRequiredService<IAbpAuthorizationService>();

    protected async Task<List<Guid>> FilterIdsByAuthorizations(List<Guid> ids, CancellationToken cancellationToken)
    {
        IQueryable<ProductFlattenHierarchy> productFlattenHierarchyQueryable = await ProductFlattenHierarchyReadOnlyRepository.GetQueryableAsync();
        if (await AuthorizationService.IsGrantedAsync(CorePermissions.ShowHidden))
        {
            return ids;
        }
        IQueryable<Guid> idsQueryable = productFlattenHierarchyQueryable.Where(x => ids.Contains(x.ComponentId))
            .Select(x => x.ComponentId);
        return await ProductFlattenHierarchyReadOnlyRepository.AsyncExecuter.ToListAsync(idsQueryable,
            cancellationToken);
    }

}