using FluentValidation;
using SpareParts.Common;
using System.Text.RegularExpressions;
using SpareParts.Core.Documents.Commands;

namespace SpareParts.Core.Documents.Validators;

public class CreateLinkDocumentCommandValidator : CreateDocumentCommandBaseValidator<CreateLinkDocumentCommand, string>
{
    public CreateLinkDocumentCommandValidator()
    {
        RuleFor(x => x.Resource.Value).NotEmpty();
        RuleFor(x => x.Resource.Value).Must(x =>
        {
            try
            {
                return Regex.IsMatch(x, CommonConsts.UrlPattern, RegexOptions.None, CommonConsts.RegexTimeout);
            }
            catch (RegexMatchTimeoutException)
            {
                return false;
            }
        });
    }
}