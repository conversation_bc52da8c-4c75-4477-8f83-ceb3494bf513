using FluentValidation;
using SpareParts.Core.Documents.Commands;

namespace SpareParts.Core.Documents.Validators;

public abstract class CreateDocumentCommandBaseValidator<TCommand, T> : AbstractValidator<TCommand>
    where TCommand : CreateDocumentCommandBase<T>
{
    protected CreateDocumentCommandBaseValidator()
    {
        RuleFor(x => x.Resource).NotNull();
        RuleFor(x => x.Translations).NotEmpty();
    }
}