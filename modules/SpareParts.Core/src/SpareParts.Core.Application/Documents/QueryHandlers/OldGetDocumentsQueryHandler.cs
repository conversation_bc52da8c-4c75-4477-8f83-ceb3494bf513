using AutoFilterer.Extensions;
using Microsoft.AspNetCore.Authorization;
using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.Components.Dtos;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Resources;
using SpareParts.Core.Permissions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using SpareParts.Common.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.ChangeTracking;
using Volo.Abp.Domain.Repositories;
using SpareParts.Core.Components;
using SpareParts.Core.Documents.Dtos;
using SpareParts.Core.Documents.Queries;

namespace SpareParts.Core.Documents.QueryHandlers;

public class OldGetDocumentsQueryHandler : ComponentDocumentRequestBase, IQueryHandler<OldGetDocumentsQuery, PagedResultDto<DocumentDtoWithComponents>>
{
    private IReadOnlyRepository<ProductFlattenHierarchy> ProductFlattenHierarchyRepository => LazyServiceProvider.LazyGetRequiredService<IReadOnlyRepository<ProductFlattenHierarchy>>();

    [DisableEntityChangeTracking]
    public async Task<PagedResultDto<DocumentDtoWithComponents>> Handle(OldGetDocumentsQuery request, CancellationToken cancellationToken)
    {
        IQueryable<ProductFlattenHierarchy> productFlattenHierarchyQueryable = await ProductFlattenHierarchyRepository.GetQueryableAsync();
        IQueryable<Document> documentQueryable = await DocumentRepository.WithDetailsAsync(x => x.Components);

        List<Guid> ids = [];
        bool showHidden = await AuthorizationService.IsGrantedAsync(CorePermissions.ShowHidden);
        if (!showHidden)
        {
            IQueryable<Guid> idsQueryable = productFlattenHierarchyQueryable.Select(x => x.ComponentId);
            ids = await ProductFlattenHierarchyRepository.AsyncExecuter.ToListAsync(idsQueryable, cancellationToken);
        }

        IQueryable<DocumentDtoWithComponents>? queryable = documentQueryable
            .WhereIf(!showHidden, d => d.Components.Any(component => ids.Contains(component.Id))
            ).Select(document => new DocumentDtoWithComponents
            {
                Id = document.Id,
                CreatorId = document.CreatorId,
                CreationTime = document.CreationTime,
                LastModifierId = document.LastModifierId,
                LastModificationTime = document.LastModificationTime,
                Translations = ObjectMapper.Map<List<DocumentTranslation>, List<CommonTranslationDto>>(document.Translations.ToList()),
                Resource = MapToDocumentResourceDto(document),
                Filename = document.Resource != null ? document.Resource.FileName : string.Empty,
                Components = document.Components.Where(component => productFlattenHierarchyQueryable.Select(x => x.ComponentId).ToList()
                    .Contains(component.Id)).Select(c => new BasicComponentDto()
                    {
                        Id = c.Id,
                        Code = c.Code,
                        Translations = ObjectMapper.Map<List<ComponentTranslation>, List<CommonTranslationDto>>(c.Translations.ToList())
                    }).ToList(),
                IsPublic = document.IsPublic,
                Languages = document.Languages
            });
        if (string.IsNullOrWhiteSpace(request.Filter.Sort))
        {
            request.Filter.Sort = "creationTime";
        }

        long totalCount = await DocumentRepository.AsyncExecuter.LongCountAsync(request.Filter.ApplyFilterWithoutPaginationAndOrdering(queryable), cancellationToken);

        queryable = queryable.ApplyFilter(request.Filter);

        List<DocumentDtoWithComponents> results = await DocumentRepository.AsyncExecuter.ToListAsync(queryable, cancellationToken);
        return new PagedResultDto<DocumentDtoWithComponents>(totalCount, results);
    }
}
