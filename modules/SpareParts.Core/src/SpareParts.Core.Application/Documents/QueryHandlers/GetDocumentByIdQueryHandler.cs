using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.Documents.Dtos;
using SpareParts.Core.Documents.Queries;
using SpareParts.Core.Entities.Resources;
using Volo.Abp.Domain.ChangeTracking;
using Volo.Abp.Domain.Entities;

namespace SpareParts.Core.Documents.QueryHandlers;

public class GetDocumentByIdQueryHandler : CoreRequestBase, IQueryHandler<GetDocumentByIdQuery, DocumentDto>
{
    private DocumentQueryBuilder DocumentQueryBuilder => LazyServiceProvider.LazyGetRequiredService<DocumentQueryBuilder>();
    
    [DisableEntityChangeTracking]
    public async Task<DocumentDto> Handle(GetDocumentByIdQuery request, CancellationToken cancellationToken)
    {
        DocumentQueryBuilder builder = await DocumentQueryBuilder.InitializeAsync(cancellationToken);
        IQueryable<DocumentDto> query = builder.FilterByDocumentId(request.Id)
                                                .Build()
                                                .ToDocumentDto(ObjectMapper, builder.ShouldFilter, builder.FilteredComponentIds);
        DocumentDto? result = await builder.AsyncExecuter.FirstOrDefaultAsync(query, cancellationToken);
        
        if (result == null)
        {
            throw new EntityNotFoundException(typeof(Document), request.Id);
        }

        return result;
    }
}