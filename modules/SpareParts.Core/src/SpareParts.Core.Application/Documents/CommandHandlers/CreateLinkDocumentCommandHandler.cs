using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using SpareParts.AbpMediatR.Handlers;
using SpareParts.Common.Dtos;
using SpareParts.Core.Components;
using SpareParts.Core.Documents.Commands;
using SpareParts.Core.Documents.Dtos;
using SpareParts.Core.Entities.Resources;

namespace SpareParts.Core.Documents.CommandHandlers;

public class CreateLinkDocumentCommandHandler : ComponentDocumentRequestBase, ICommandHandler<CreateLinkDocumentCommand, DocumentDto>
{
    public virtual async Task<DocumentDto> Handle(CreateLinkDocumentCommand request, CancellationToken cancellationToken)
    {
        List<DocumentTranslation> translations = ObjectMapper.Map<List<CommonTranslationDto>, List<DocumentTranslation>>(request.Translations);
        return MapToDocumentDto(await DocumentRepository.InsertAsync(await DocumentDomainService.CreateAsync(request.Resource.Value, translations, request.Languages, request.IsPublic), cancellationToken: cancellationToken));
    }
}