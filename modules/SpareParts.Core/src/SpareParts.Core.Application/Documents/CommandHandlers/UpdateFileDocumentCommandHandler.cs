using Microsoft.Extensions.DependencyInjection;
using SpareParts.Core.Entities.Resources;
using System;
using System.Threading;
using System.Threading.Tasks;
using SpareParts.Core.Enums;
using Volo.Abp.Domain.Repositories;
using SpareParts.Core.Documents.Commands;

namespace SpareParts.Core.Documents.CommandHandlers;

public class UpdateFileDocumentCommandHandler : UpdateDocumentBaseCommandHandler<UpdateFileDocumentCommand, Guid>
{
    private IRepository<Resource, Guid> ResourceRepository => LazyServiceProvider.GetRequiredService<IRepository<Resource, Guid>>();

    protected override async Task CustomHandleAsync(UpdateFileDocumentCommand request, Document document, CancellationToken cancellationToken)
    {
        Resource resource = await ResourceRepository.GetAsync(request.Resource.Value, cancellationToken: cancellationToken);
        document.ChangeFileResource(resource);
    }

    protected override DocumentType Type => DocumentType.Resource;
}