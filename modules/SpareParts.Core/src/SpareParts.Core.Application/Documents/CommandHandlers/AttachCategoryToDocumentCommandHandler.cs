using System.Threading;
using System.Threading.Tasks;
using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.Documents.Commands;
using SpareParts.Core.Entities.Resources;
using Volo.Abp.Domain.Repositories;

namespace SpareParts.Core.Documents.CommandHandlers;

public class AttachCategoryToDocumentCommandHandler : CoreRequestBase, ICommandHandler<AttachCategoryToDocumentCommand>
{
    private IRepository<DocumentInDocumentCategory> DocumentInDocumentCategoryRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<DocumentInDocumentCategory>>();

    public async Task Handle(AttachCategoryToDocumentCommand request, CancellationToken cancellationToken)
    {
        await DocumentInDocumentCategoryRepository.InsertAsync(
            new DocumentInDocumentCategory(request.DocumentId, request.DocumentCategoryId), cancellationToken: cancellationToken);
    }
}