using SpareParts.Common.Dtos;
using SpareParts.Core.Components.CommandHandlers;
using SpareParts.Core.Documents.Commands;
using SpareParts.Core.Entities.Resources;

namespace SpareParts.Core.Documents.CommandHandlers;

public class SetDocumentTranslationsCommandHandler : SetTranslationsCommandHandler<DocumentTranslation, Document, SetDocumentTranslationsCommand>
{
    protected override DocumentTranslation CreateTranslation(CommonTranslationDto translationDto)
    {
        return new DocumentTranslation(translationDto.Language, translationDto.Label, translationDto.Description);
    }
}