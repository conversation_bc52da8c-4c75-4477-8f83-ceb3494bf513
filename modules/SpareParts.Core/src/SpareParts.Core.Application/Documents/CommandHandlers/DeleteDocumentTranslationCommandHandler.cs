using System;
using System.Linq.Expressions;
using SpareParts.Core.Components.CommandHandlers;
using SpareParts.Core.Documents.Commands;
using SpareParts.Core.Entities.Resources;

namespace SpareParts.Core.Documents.CommandHandlers;

public class DeleteDocumentTranslationCommandHandler : DeleteTranslationCommandHandler<DeleteDocumentTranslationCommand, DocumentTranslation>
{
    protected override Expression<Func<DocumentTranslation, bool>> Predicate(Guid id)
    {
        return t => t.DocumentId == id;
    }
}