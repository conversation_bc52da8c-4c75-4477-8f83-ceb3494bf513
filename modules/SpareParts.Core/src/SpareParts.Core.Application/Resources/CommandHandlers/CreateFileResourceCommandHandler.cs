using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.DomainServices.Resources;
using SpareParts.Core.Entities.Resources;
using SpareParts.Core.Resources.Commands;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace SpareParts.Core.Resources.CommandHandlers;
public class CreateFileResourceCommandHandler : ResourceRequestBase, ICommandHandler<CreateFileResourceCommand, Guid>
{
    private FileManager FileManager => LazyServiceProvider.LazyGetRequiredService<FileManager>();

    public virtual async Task<Guid> Handle(CreateFileResourceCommand request, CancellationToken cancellationToken)
    {
        Resource file = await FileManager.CreateAsync(request.Content, cancellationToken);
        await ResourceRepository.InsertAsync(file, cancellationToken: cancellationToken);
        return file.Id;
    }
}