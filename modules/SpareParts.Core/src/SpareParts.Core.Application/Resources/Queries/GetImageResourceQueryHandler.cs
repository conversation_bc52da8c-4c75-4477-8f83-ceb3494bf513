using System.IO;
using System.Threading;
using System.Threading.Tasks;
using SpareParts.Core.DomainServices.Resources;

namespace SpareParts.Core.Resources.Queries;

public class GetImageResourceQueryHandler : GetResourceQueryHandlerBase<GetImageResourceQuery>
{
    private ImageManager ImageManager => LazyServiceProvider.LazyGetRequiredService<ImageManager>();

    protected override async Task<Stream> GetStreamAsync(GetImageResourceQuery request, CancellationToken cancellationToken)
    {
        return await ImageManager.GetBlobAsync(request.Id, request.Size, cancellationToken);
    }

    protected override async Task<Stream> GetOriginalStreamAsync(GetImageResourceQuery request, Stream requestedStream, CancellationToken cancellationToken)
    {
        if (!request.Size.HasValue)
        {
            return requestedStream;
        }
        return await ImageManager.GetBlobAsync(request.Id, cancellationToken);
    }
}