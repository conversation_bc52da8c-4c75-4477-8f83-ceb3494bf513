using System.IO;
using System.Threading;
using System.Threading.Tasks;
using MimeKit;
using SpareParts.AbpMediatR.Handlers;
using SpareParts.Common;
using SpareParts.Core.DataVisibility;
using SpareParts.Core.DomainServices.Resources;
using SpareParts.Core.Entities.Resources;

namespace SpareParts.Core.Resources.Queries;

public abstract class GetResourceQueryHandlerBase<T> : ResourceRequestBase, IQueryHandler<T, VersionedRemoteStreamContent>, IResourceVisibilityCheckingEnabled
    where T : GetResourceQueryBase
{
    protected abstract Task<Stream> GetStreamAsync(T request, CancellationToken cancellationToken);
    protected abstract Task<Stream> GetOriginalStreamAsync(T request, Stream requestedStream, CancellationToken cancellationToken);

    public async Task<VersionedRemoteStreamContent> Handle(T request, CancellationToken cancellationToken)
    {
        Resource resource = await ResourceRepository.GetAsync(request.Id, cancellationToken: cancellationToken);

        Stream stream = await GetStreamAsync(request, cancellationToken);
        if (resource.Hash == null)
        {
            resource.ChangeHash(await FileManager.CalculateHashAsync(await GetOriginalStreamAsync(request, stream, cancellationToken), cancellationToken));
            await ResourceRepository.UpdateAsync(resource, cancellationToken: cancellationToken);
        }

        return new VersionedRemoteStreamContent(stream, resource.Hash!, resource.LastModificationTime ?? resource.CreationTime, resource.FileName, MimeTypes.GetMimeType(resource.FileName));
    }
}