using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.Drawings.Commands;
using SpareParts.Core.Entities.Components;
using System.Threading;
using System.Threading.Tasks;

namespace SpareParts.Core.Drawings.CommandHandlers;

public class DeleteDrawingCommandHandler : DeleteDrawingBase<PERSON>ommandHandler, ICommandHandler<DeleteDrawingCommand>
{
    public virtual async Task Handle(DeleteDrawingCommand request, CancellationToken cancellationToken)
    {
        Drawing drawing = await DrawingRepository.GetAsync(request.DrawingId, cancellationToken: cancellationToken);
        await DeleteAsync(drawing, cancellationToken);
    }
}