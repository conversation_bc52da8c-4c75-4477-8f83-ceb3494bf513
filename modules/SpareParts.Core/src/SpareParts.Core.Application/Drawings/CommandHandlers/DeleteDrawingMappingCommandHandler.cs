using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.Drawings.Commands;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Enums;
using System;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp;

namespace SpareParts.Core.Drawings.CommandHandlers;

public class DeleteDrawingMappingCommandHandler : DrawingRequestBase, ICommandHandler<DeleteDrawingMappingCommand>
{
    public virtual async Task Handle(DeleteDrawingMappingCommand request, CancellationToken cancellationToken)
    {
        (Guid drawingId, Guid mappingId, CallerOrigin callerOrigin) = request;

        Drawing drawing = await DrawingRepository.GetAsync(drawingId, cancellationToken: cancellationToken);
        if (callerOrigin == CallerOrigin.Api && drawing.Origin == DrawingOrigin.SolidWorks)
        {
            throw new BusinessException("400", "You can not delete the mapping on a drawing from SolidWorks");
        }

        await DrawingMappingRepository.DeleteAsync(d => d.DrawingId == drawingId && d.Id == mappingId, cancellationToken: cancellationToken);
    }
}