using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.Drawings.Commands;
using SpareParts.Core.Drawings.Dtos;
using SpareParts.Core.Entities.Components;
using System.Threading;
using System.Threading.Tasks;

namespace SpareParts.Core.Drawings.CommandHandlers;

public class ChangeDrawingRankCommandHandler : DrawingRequestBase, ICommandHandler<ChangeDrawingRankCommand, DrawingDto>
{
    public virtual async Task<DrawingDto> Handle(ChangeDrawingRankCommand request, CancellationToken cancellationToken)
    {
        Drawing drawing = await DrawingRepository.GetAsync(request.Id, cancellationToken: cancellationToken);
        drawing.Rank = request.Rank;

        drawing = await DrawingRepository.UpdateAsync(drawing, cancellationToken: cancellationToken);

        return ObjectMapper.Map<Drawing, DrawingDto>(drawing);
    }
}