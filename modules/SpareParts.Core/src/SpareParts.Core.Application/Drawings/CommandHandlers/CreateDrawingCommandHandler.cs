using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.Components;
using SpareParts.Core.DomainServices.Components;
using SpareParts.Core.DomainServices.Resources;
using SpareParts.Core.Drawings.Commands;
using SpareParts.Core.Drawings.Dtos;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Resources;
using System;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Content;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Validation;

namespace SpareParts.Core.Drawings.CommandHandlers;

public class CreateDrawingCommandHandler : DrawingRequestBase, ICommandHandler<CreateDrawingCommand, DrawingDto>
{
    private DrawingDomainService DrawingDomainService => LazyServiceProvider.LazyGetRequiredService<DrawingDomainService>();

    public virtual async Task<DrawingDto> Handle(CreateDrawingCommand request, CancellationToken cancellationToken)
    {
        string drawingFileName = Path.GetFileName(request.DrawingFile.FileName!);
        if (!Path.GetExtension(drawingFileName).Equals(".pdf", StringComparison.CurrentCultureIgnoreCase))
        {
            throw new AbpValidationException("Drawing file must be a PDF file.");
        }

        Resource file = await FileManager.CreateAsync(request.DrawingFile, cancellationToken);
        using RemoteStreamContent extractedImage = await PdfUtilities.ExtractImageAsync(request.DrawingFile);
        Resource image = await ImageManager.CreateAsync(extractedImage, cancellationToken);
        using RemoteStreamContent extractedSvg = await PdfUtilities.ExtractSvgAsync(request.DrawingFile.GetStream(), FileManager.GetBlobName(file.Id), cancellationToken);
        await FileManager.SaveBlobAsync(extractedSvg.FileName!, extractedSvg.GetStream(), cancellationToken);

        IQueryable<double?> maxRankQueryable = (await DrawingRepository.GetQueryableAsync())
            .Where(d => d.AssemblyId == request.AssemblyId)
            .Select(d => (double?)d.Rank);

        double maxRank = (await DrawingRepository.AsyncExecuter.MaxAsync(maxRankQueryable, cancellationToken)) ?? 0.0;
        double rank = Math.Floor(maxRank) + 1.0;

        Drawing drawing = DrawingDomainService.Create(request.AssemblyId, rank, file, image, request.ComponentIdsByIndex, request.Origin);
        drawing.IsVisible = true;

        bool hasMainDrawing = await DrawingRepository.AnyAsync(x => x.AssemblyId == request.AssemblyId && x.IsMain, cancellationToken: cancellationToken);
        if (!hasMainDrawing)
        {
            await DrawingDomainService.SetAsMainAsync(drawing, cancellationToken);
        }

        await DrawingRepository.InsertAsync(drawing, cancellationToken: cancellationToken);

        DrawingDto drawingDto = ObjectMapper.Map<Drawing, DrawingDto>(drawing);
        return drawingDto;
    }
}