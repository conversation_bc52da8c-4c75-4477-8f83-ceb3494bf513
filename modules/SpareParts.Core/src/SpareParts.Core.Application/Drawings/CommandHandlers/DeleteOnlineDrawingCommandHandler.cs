using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.Drawings.Commands;
using SpareParts.Core.Entities.Components;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp;

namespace SpareParts.Core.Drawings.CommandHandlers;

public class DeleteOnlineDrawingCommandHandler : DeleteDrawingBase<PERSON>ommandHandler, ICommandHandler<DeleteOnlineDrawingCommand>
{
    public virtual async Task Handle(DeleteOnlineDrawingCommand request, CancellationToken cancellationToken)
    {
        Drawing drawing = await DrawingRepository.GetAsync(request.DrawingId, cancellationToken: cancellationToken);
        if (drawing.Origin == Enums.DrawingOrigin.SolidWorks)
        {
            throw new BusinessException("400", "You can not delete a drawing from SolidWorks");
        }
        await DeleteAsync(drawing, cancellationToken);
    }
}