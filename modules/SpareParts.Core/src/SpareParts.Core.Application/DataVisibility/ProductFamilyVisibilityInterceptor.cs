using SpareParts.Common.DataFilter;
using System;
using System.Threading.Tasks;
using Volo.Abp.Data;

namespace SpareParts.Core.DataVisibility;

public class ProductFamilyVisibilityInterceptor : BaseVisibilityInterceptor
{
    private readonly ProductFamilyAccessControlService _productFamilyAccessControlService;
    private readonly IDataFilter _dataFilter;

    public ProductFamilyVisibilityInterceptor(ProductFamilyAccessControlService productFamilyAccessControlService, IDataFilter dataFilter)
    {
        _productFamilyAccessControlService = productFamilyAccessControlService;
        _dataFilter = dataFilter;
    }

    protected override async Task CheckAccessAsync(Guid id)
    {
        using (_dataFilter.Disable<IHasPublic>())
        {
            await _productFamilyAccessControlService.CheckByIdAsync(id);
        }
    }
}