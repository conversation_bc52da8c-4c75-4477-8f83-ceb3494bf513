using System;
using Volo.Abp.DependencyInjection;
using Volo.Abp.DynamicProxy;

namespace SpareParts.Core.DataVisibility;

public static class ProductFamilyVisibilityInterceptorRegistrar
{
    public static void RegisterIfNeeded(IOnServiceRegistredContext context)
    {
        if (ShouldIntercept(context.ImplementationType))
        {
            context.Interceptors.TryAdd<ProductFamilyVisibilityInterceptor>();
        }
    }

    private static bool ShouldIntercept(Type type)
    {
        return !DynamicProxyIgnoreTypes.Contains(type) && typeof(IProductFamilyVisibilityCheckingEnabled).IsAssignableFrom(type);
    }
}
