using System;
using Volo.Abp.DependencyInjection;
using Volo.Abp.DynamicProxy;

namespace SpareParts.Core.DataVisibility;

public static class ResourceVisibilityInterceptorRegistrar
{
    public static void RegisterIfNeeded(IOnServiceRegistredContext context)
    {
        if (ShouldIntercept(context.ImplementationType))
        {
            context.Interceptors.TryAdd<ResourceVisibilityInterceptor>();
        }
    }

    private static bool ShouldIntercept(Type type)
    {
        return !DynamicProxyIgnoreTypes.Contains(type) && typeof(IResourceVisibilityCheckingEnabled).IsAssignableFrom(type);
    }
}
