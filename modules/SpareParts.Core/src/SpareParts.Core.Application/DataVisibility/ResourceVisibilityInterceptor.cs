using System;
using System.Threading.Tasks;

namespace SpareParts.Core.DataVisibility;

public class ResourceVisibilityInterceptor : BaseVisibilityInterceptor
{
    private readonly ResourceAccessControlService _resourceAccessControlService;

    public ResourceVisibilityInterceptor(ResourceAccessControlService resourceAccessControlService)
    {
        _resourceAccessControlService = resourceAccessControlService;
    }

    protected override Task CheckAccessAsync(Guid id)
    {
        return _resourceAccessControlService.CheckByIdAsync(id);
    }
}
