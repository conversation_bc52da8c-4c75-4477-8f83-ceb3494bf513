using Microsoft.AspNetCore.Authorization;
using SpareParts.Core.Permissions;
using System;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Volo.Abp.Authorization;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace SpareParts.Core.DataVisibility;

public abstract class BaseAccessControlService<TVisibility, TEntity> : ITransientDependency where TVisibility : class, IEntity where TEntity : class, IEntity<Guid>
{
    protected readonly IReadOnlyRepository<TEntity, Guid> EntityRepository;
    protected readonly IReadOnlyRepository<TVisibility> VisibilityRepository;
    protected readonly IAuthorizationService AuthorizationService;

    protected BaseAccessControlService(IReadOnlyRepository<TVisibility> visibilityRepository, IReadOnlyRepository<TEntity, Guid> entityRepository,
            IAbpAuthorizationService authorizationService)
    {
        AuthorizationService = authorizationService;
        VisibilityRepository = visibilityRepository;
        EntityRepository = entityRepository;
    }

    protected abstract Task<Expression<Func<TVisibility, bool>>> GetFilterExpressionByIdAsync(Guid id);
    protected abstract Task<IQueryable<TEntity>> GetQueryableEntity();

    public virtual async Task<bool> IsGrantedByIdAsync(Guid id)
    {
        if (await AuthorizationService.IsGrantedAsync(CorePermissions.ShowHidden))
        {
            return true;
        }

        Expression<Func<TVisibility, bool>> filterExpression = await GetFilterExpressionByIdAsync(id);
        return await VisibilityRepository.AnyAsync(filterExpression);
    }

    public virtual async Task CheckByIdAsync(Guid id)
    {
        if (!await IsGrantedByIdAsync(id))
        {
            throw new AbpAuthorizationException(code: AbpAuthorizationErrorCodes.GivenPolicyHasNotGrantedForGivenResource)
                .WithData("Id", id);
        }
    }

    public virtual async Task<TEntity> GetByIdAsync(Guid id)
    {
        await CheckByIdAsync(id);

        IQueryable<TEntity> queryable = await GetQueryableEntity();
        return queryable.First(x => x.Id == id);
    }
}