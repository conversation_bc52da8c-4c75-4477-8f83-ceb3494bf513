using CsvHelper;
using CsvHelper.Configuration.Attributes;
using JetBrains.Annotations;
using Microsoft.Extensions.Logging;
using SpareParts.AbpMediatR.Commands;
using SpareParts.AbpMediatR.Notifications;
using SpareParts.Common;
using SpareParts.Common.Dtos;
using SpareParts.Common.Settings;
using SpareParts.Core.Components.Commands;
using SpareParts.Core.Components.Commands.Products;
using SpareParts.Core.Documents.Commands;
using SpareParts.Core.Documents.Dtos;
using SpareParts.Core.Drawings.Commands;
using SpareParts.Core.Enums;
using SpareParts.Core.ProductFamilies.Commands;
using SpareParts.Core.Resources.Commands;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;
using Volo.Abp.Content;
using Volo.Abp.DependencyInjection;

namespace SpareParts.Core;

[ExposeServices(
    typeof(ISeedDataForNewTenantNotificationHandler),
    typeof(INotificationHandler<NewTenantNotification>))
]
public class SeedDataForNewTenantNotificationHandler : ApplicationService, ISeedDataForNewTenantNotificationHandler
{
    private ICommandSender Sender => LazyServiceProvider.LazyGetRequiredService<ICommandSender>();
    private readonly string _location = $"{AppDomain.CurrentDomain.BaseDirectory}{Path.DirectorySeparatorChar}demo-data";
    private readonly CultureInfo _cultureInfo = CultureInfo.GetCultureInfo("fr-FR");
    private readonly Dictionary<string, Guid> _componentIdsByLowerCode = [];

    public virtual async Task Handle(NewTenantNotification notification, CancellationToken cancellationToken)
    {
        try
        {
            Logger.LogInformation("Beginning core demo data seeding for tenant {TenantName}.", notification.TenantName);
            using (CurrentTenant.Change(notification.TenantId, notification.TenantName))
            {
                string defaultLanguage = (await SettingProvider.GetOrNullAsync(CommonSettings.DefaultLanguage))!;

                Guid productFamilyId = await CreateDemoProductFamily(defaultLanguage, cancellationToken);

                await CreateDemoComponents(productFamilyId, cancellationToken);

                await CreateDemoBomLines(cancellationToken);

                await CreateDemoDocuments(defaultLanguage, cancellationToken);

                await CreateDemoPdfDrawings(cancellationToken);
            }
            Logger.LogInformation("Ending core demo data seeding for tenant {TenantName} successfully.", notification.TenantName);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failing core demo data seeding for tenant {TenantName}.", notification.TenantName);
            throw;
        }

    }

    private Dictionary<string, Guid> GetComponentIdsByIndex(CsvReader indexesCsv)
    {
        return indexesCsv.GetRecords<DrawingIndex>()
            .Select(x => new
            {
                x.Index,
                ComponentId = GetComponentId(x.ChildComponentCode)
            }).ToDictionary(x => x.Index, x => x.ComponentId);
    }

    private Guid GetComponentId(string codeWithoutPrefix)
    {
        return _componentIdsByLowerCode[$"{CoreDomainSharedConsts.DemoCodePrefix}{codeWithoutPrefix}".ToLower()];
    }

    private static IEnumerable<CommonTranslationDto> GetComponentTranslations(Component component)
    {
        if (!string.IsNullOrWhiteSpace(component.LabelFr))
        {
            yield return new CommonTranslationDto("fr", component.LabelFr, string.IsNullOrWhiteSpace(component.DescriptionFr) ? null : component.DescriptionFr);
        }
        if (!string.IsNullOrWhiteSpace(component.LabelEn))
        {
            yield return new CommonTranslationDto("en", component.LabelEn, string.IsNullOrWhiteSpace(component.DescriptionEn) ? null : component.DescriptionEn);
        }
        if (!string.IsNullOrWhiteSpace(component.LabelDe))
        {
            yield return new CommonTranslationDto("de", component.LabelDe, string.IsNullOrWhiteSpace(component.DescriptionDe) ? null : component.DescriptionDe);
        }
    }

    private async Task CreateDemoPdfDrawings(CancellationToken cancellationToken)
    {
        Logger.LogInformation("Demo drawings seeding for tenant {CurrentTenantName} has begun", CurrentTenant.Name);

        foreach (string drawingDirectoryPath in Directory.GetDirectories($"{_location}{Path.DirectorySeparatorChar}drawings"))
        {
            string componentCode = new DirectoryInfo(drawingDirectoryPath).Name;
            Guid assemblyId = GetComponentId(componentCode);
            foreach (string drawingPath in Directory.GetFiles(drawingDirectoryPath, "*.pdf", new EnumerationOptions
            {
                MatchCasing = MatchCasing.CaseInsensitive
            }))
            {
                string drawingName = Path.GetFileName(drawingPath);
                Dictionary<string, Guid> componentIdsByIndex = [];
                string? drawingIndexesPath = Directory.GetFiles(drawingDirectoryPath, $"{Path.GetFileNameWithoutExtension(drawingPath)}.csv",
                    new EnumerationOptions { MatchCasing = MatchCasing.CaseInsensitive }).FirstOrDefault();
                if (drawingIndexesPath != null)
                {
                    using StreamReader indexesReader = new(drawingIndexesPath);
                    using CsvReader indexesCsv = new(indexesReader, _cultureInfo);
                    componentIdsByIndex = GetComponentIdsByIndex(indexesCsv);
                }
                await using FileStream drawingStream = new(drawingPath, FileMode.Open, FileAccess.Read);
                await Sender.Send(new CreateDrawingCommand(assemblyId, new RemoteStreamContent(drawingStream, drawingName),
                    componentIdsByIndex, DrawingOrigin.Online), cancellationToken);
            }
        }

        Logger.LogInformation("Demo drawings seeding for tenant {CurrentTenantName} has ended", CurrentTenant.Name);
    }

    private async Task CreateDemoDocuments(string defaultLanguage, CancellationToken cancellationToken)
    {
        Logger.LogInformation("Demo documents seeding for tenant {CurrentTenantName} has begun", CurrentTenant.Name);

        foreach (string docDirectoryPath in Directory.GetDirectories($"{_location}{Path.DirectorySeparatorChar}documents"))
        {
            string componentCode = new DirectoryInfo(docDirectoryPath).Name;
            Guid componentId = GetComponentId(componentCode);
            foreach (string docPath in Directory.GetFiles(docDirectoryPath))
            {
                string docName = Path.GetFileName(docPath);
                await using FileStream docStream = new(docPath, FileMode.Open, FileAccess.Read);
                Guid docId = await Sender.Send(new CreateFileResourceCommand(new RemoteStreamContent(docStream, docName)), cancellationToken);
                string label = Path.GetFileNameWithoutExtension(docPath);
                DocumentDto document = await Sender.Send(new CreateFileDocumentCommand(new DocumentResourceDto<Guid>
                {
                    Type = DocumentType.Resource,
                    Value = docId
                }, [new CommonTranslationDto(defaultLanguage, label)], []), cancellationToken);
                await Sender.Send(new AttachDocumentToComponentCommand(componentId, document.Id), cancellationToken);

            }
        }

        Logger.LogInformation("Demo documents seeding for tenant {CurrentTenantName} has ended", CurrentTenant.Name);
    }

    private async Task CreateDemoBomLines(CancellationToken cancellationToken)
    {
        Logger.LogInformation("Demo BOM lines seeding for tenant {CurrentTenantName} has begun", CurrentTenant.Name);

        string bomlinesCsvPath = Directory.GetFiles(_location, "bomlines.csv",
            new EnumerationOptions { MatchCasing = MatchCasing.CaseInsensitive })[0];
        using StreamReader bomlinesReader = new(bomlinesCsvPath);
        using CsvReader bomlinesCsv = new(bomlinesReader, _cultureInfo);
        IEnumerable<BomLine> bomlines = bomlinesCsv.GetRecords<BomLine>();

        foreach (BomLine bomline in bomlines)
        {
            Guid parentAssemblyId = GetComponentId(bomline.ParentAssemblyCode);
            Guid childComponentId = GetComponentId(bomline.ChildComponentCode);
            await Sender.Send(new CreateBomLineCommand(parentAssemblyId, childComponentId, bomline.Quantity), cancellationToken);
        }

        Logger.LogInformation("Demo BOM lines seeding for tenant {CurrentTenantName} has ended", CurrentTenant.Name);
    }

    private async Task CreateDemoComponents(Guid productFamilyId, CancellationToken cancellationToken)
    {
        Logger.LogInformation("Demo components seeding for tenant {CurrentTenantName} has begun", CurrentTenant.Name);

        string componentsCsvPath = Directory.GetFiles(_location, "components.csv",
            new EnumerationOptions { MatchCasing = MatchCasing.CaseInsensitive })[0];
        using StreamReader componentsReader = new(componentsCsvPath);
        using CsvReader componentsCsv = new(componentsReader, _cultureInfo);
        IEnumerable<Component> components = componentsCsv.GetRecords<Component>();
        foreach (Component component in components)
        {
            string codeWithPrefix = $"{CoreDomainSharedConsts.DemoCodePrefix}{component.Code}";
            string lowerCodeWithPrefix = codeWithPrefix.ToLower();
            Guid? imageId = await CreateImage($"{_location}{Path.DirectorySeparatorChar}images", component.Code, cancellationToken);
            switch (component.Type.ToLower())
            {
                case "part":
                    _componentIdsByLowerCode[lowerCodeWithPrefix] = await Sender.Send(new CreateComponentCommand(codeWithPrefix, GetComponentTranslations(component), ComponentType.Part)
                    {
                        ImageId = imageId
                    }, cancellationToken);
                    break;
                case "assembly":
                    _componentIdsByLowerCode[lowerCodeWithPrefix] = await Sender.Send(new CreateComponentCommand(codeWithPrefix, GetComponentTranslations(component), ComponentType.Assembly)
                    {
                        ImageId = imageId
                    }, cancellationToken);
                    break;
                case "product":
                    Guid assemblyId = _componentIdsByLowerCode[lowerCodeWithPrefix] = await Sender.Send(new CreateComponentCommand(codeWithPrefix, GetComponentTranslations(component), ComponentType.Assembly)
                    {
                        ImageId = imageId
                    }, cancellationToken);
                    await Sender.Send(new CreateProductCommand(assemblyId), cancellationToken);
                    await Sender.Send(new LinkProductToProductFamilyCommand(productFamilyId, assemblyId), cancellationToken);
                    break;
                default:
                    throw new NotSupportedException($"Component type {component.Type} is not supported");
            }
        }
        Logger.LogInformation("Demo components seeding for tenant {CurrentTenantName} has ended", CurrentTenant.Name);
    }

    private async Task<Guid> CreateDemoProductFamily(string defaultLanguage, CancellationToken cancellationToken)
    {
        Logger.LogInformation("Demo product family seeding for tenant {CurrentTenantName} has begun", CurrentTenant.Name);
        Guid? productFamilyImageId = await CreateImage(_location, "productfamily", cancellationToken);

        Logger.LogInformation("Demo product family seeding for tenant {CurrentTenantName} has ended", CurrentTenant.Name);

        return (await Sender.Send(new CreateProductFamilyCommand([new CommonTranslationDto(defaultLanguage, "Demo Vermeer")], true)
        {
            ImageId = productFamilyImageId!.Value,
            Code = $"{CoreDomainSharedConsts.DemoCodePrefix}{Guid.NewGuid():N}"
        }, cancellationToken)).Id;
    }

    private async Task<Guid?> CreateImage(string directory, string imageName, CancellationToken cancellationToken)
    {
        string? file = Directory.GetFiles(directory, $"{imageName}.*", new EnumerationOptions
        {
            MatchCasing = MatchCasing.CaseInsensitive
        }).FirstOrDefault();
        if (file is null)
        {
            return null;
        }
        string imageFileName = Path.GetFileName(file);
        await using FileStream imageStream = new(file, FileMode.Open, FileAccess.Read);
        return await Sender.Send(
            new CreateImageResourceCommand(new RemoteStreamContent(imageStream, imageFileName)), cancellationToken);
    }

    [UsedImplicitly]
    private sealed class DrawingIndex
    {
        [Name("child_component_code")]
        public required string ChildComponentCode { get; set; }
        [Name("index")]
        public required string Index { get; set; }
    }

    [UsedImplicitly]
    private sealed class Component
    {
        [Name("code")]
        public required string Code { get; set; }
        [Name("label_fr")]
        public required string LabelFr { get; set; }
        [Name("description_fr")]
        public required string DescriptionFr { get; set; }
        [Name("label_en")]
        public required string LabelEn { get; set; }
        [Name("description_en")]
        public required string DescriptionEn { get; set; }
        [Name("label_de")]
        public required string LabelDe { get; set; }
        [Name("description_de")]
        public required string DescriptionDe { get; set; }
        [Name("type")]
        public required string Type { get; set; }
    }

    [UsedImplicitly]
    private sealed class BomLine
    {
        [Name("parent_assembly_code")]
        public required string ParentAssemblyCode { get; set; }
        [Name("child_component_code")]
        public required string ChildComponentCode { get; set; }
        [Name("quantity")]
        public required int Quantity { get; set; }
    }
}