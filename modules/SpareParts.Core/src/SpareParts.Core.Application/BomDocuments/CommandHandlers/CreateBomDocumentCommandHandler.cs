using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.BomDocuments.BackgroundJobs;
using SpareParts.Core.BomDocuments.Commands;
using SpareParts.Core.BomDocuments.Dtos;
using SpareParts.Core.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Authorization;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Localization;
using Volo.Abp.MultiTenancy;
using Volo.Abp.Validation;
using BomDocument = SpareParts.Core.Entities.Boms.BomDocument;

namespace SpareParts.Core.BomDocuments.CommandHandlers;

public class CreateBomDocumentCommandHandler : BomDocumentRequestBase, ICommandHandler<CreateBomDocumentCommand, BomDocumentDto>
{
    private CurrentTenant CurrentTenant => LazyServiceProvider.LazyGetRequiredService<CurrentTenant>();
    private IBackgroundJobManager BackgroundJobManager => LazyServiceProvider.LazyGetRequiredService<IBackgroundJobManager>();
    private ILanguageProvider LanguageProvider => LazyServiceProvider.LazyGetRequiredService<ILanguageProvider>();

    public async Task<BomDocumentDto> Handle(CreateBomDocumentCommand command, CancellationToken cancellationToken)
    {
        (Guid mainEntityId, BomDocumentContext context, Guid? assemblyId, string language) = command;

        IReadOnlyList<LanguageInfo> languages = await LanguageProvider.GetLanguagesAsync();
        List<string> cultures = languages.Select(x => x.CultureName).ToList();
        if (!cultures.Contains(language))
        {
            throw new AbpValidationException($"The language {language} is not supported");
        }

        if (context == BomDocumentContext.Product)
        {
            await ProductAccessControlService.CheckByIdAsync(mainEntityId);
        }
        else
        {
            if (!await EquipmentRepository.AnyAsync(x => x.Id == mainEntityId, cancellationToken: cancellationToken))
            {
                throw new AbpAuthorizationException(code: AbpAuthorizationErrorCodes.GivenPolicyHasNotGrantedForGivenResource)
                    .WithData("Id", mainEntityId);

            }
        }

        BomDocument? existingBomDocument = await BomDocumentRepository.FindAsync(x =>
            x.MainEntityId == mainEntityId &&
            x.AssemblyId == assemblyId &&
            x.CreatorId == CurrentUser.Id!.Value &&
            x.Language == command.Language &&
            x.Status != BomDocumentationStatus.Failed &&
            x.Status != BomDocumentationStatus.Expired &&
            (x.LastModificationTime.HasValue && x.LastModificationTime.Value.AddHours(CoreApplicationConsts.BomDocumentation.ExpirationHours) > DateTime.UtcNow),
            cancellationToken: cancellationToken);

        if (existingBomDocument != null)
        {
            throw new AbpValidationException("A bom document already exist for this component");
        }

        Guid bomDocumentId = GuidGenerator.Create();

        string job = await BackgroundJobManager.EnqueueAsync(
                 new BomDocumentGenerationJobArgs
                 {
                     BomDocumentId = bomDocumentId,
                     TenantId = CurrentTenant.Id!.Value,
                     TenantName = CurrentTenant.Name!
                 });

        Guid jobId = Guid.Parse(job);
        BomDocument bomDocument = new(bomDocumentId, context, mainEntityId, assemblyId, language, jobId);
        bomDocument = await BomDocumentRepository.InsertAsync(bomDocument, true, cancellationToken);

        BomDocumentDto bomDocumentDto = ObjectMapper.Map<BomDocument, BomDocumentDto>(bomDocument);
        return bomDocumentDto;
    }
}