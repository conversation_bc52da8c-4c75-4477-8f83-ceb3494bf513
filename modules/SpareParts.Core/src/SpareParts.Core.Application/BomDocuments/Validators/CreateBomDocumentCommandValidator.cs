using FluentValidation;
using SpareParts.Core.BomDocuments.Commands;

namespace SpareParts.Core.BomDocuments.Validators;
public class CreateBomDocumentCommandValidator : AbstractValidator<CreateBomDocumentCommand>
{
    public CreateBomDocumentCommandValidator()
    {
        RuleFor(x => x.MainEntityId).NotEmpty();
        RuleFor(x => x.Language).NotEmpty();
        RuleFor(x => x.Language).MinimumLength(CoreDomainSharedConsts.LanguageMinLength);
        RuleFor(x => x.Language).MaximumLength(CoreDomainSharedConsts.LanguageMaxLength);
    }
}