using AutoFilterer.Extensions;
using Microsoft.AspNetCore.Authorization;
using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.BomDocuments.Dtos;
using SpareParts.Core.BomDocuments.Queries;
using SpareParts.Core.Entities.Boms;
using SpareParts.Core.Enums;
using SpareParts.Core.Permissions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Authorization;
using Volo.Abp.Domain.ChangeTracking;

namespace SpareParts.Core.BomDocuments.QueryHandlers;

public class GetBomDocumentsMetadataQueryHandler : GetBomDocumentQueryBase, IQueryHandler<GetBomDocumentsMetadataQuery, PagedResultDto<BomDocumentDto>>
{
    [DisableEntityChangeTracking]
    public async Task<PagedResultDto<BomDocumentDto>> Handle(GetBomDocumentsMetadataQuery request, CancellationToken cancellationToken)
    {
        if (request.Filter.All && !await AuthorizationService.IsGrantedAsync(CorePermissions.BomDocuments.All))
        {
            throw new AbpAuthorizationException("You can not query the bom's documentations of another user");
        }

        if (string.IsNullOrEmpty(request.Filter.Sort))
        {
            request.Filter.Sort = "creationTime";
        }

        IQueryable<BomDocument> queryable = await BomDocumentRepository.GetQueryableAsync();

        if (!request.Filter.All)
        {
            queryable = queryable.Where(x => x.CreatorId == CurrentUser.Id!.Value);
        }

        string? status = request.Filter.Status;
        if (!string.IsNullOrWhiteSpace(status) && Enum.TryParse(request.Filter.Status, true, out BomDocumentationStatus parsedStatus))
        {
            if (parsedStatus == BomDocumentationStatus.Expired)
            {
                queryable = queryable.Where(x => x.Status == parsedStatus ||
                    (x.Status != BomDocumentationStatus.Failed &&
                    x.LastModificationTime.HasValue && x.LastModificationTime.Value.AddHours(CoreApplicationConsts.BomDocumentation.ExpirationHours) < DateTime.UtcNow));
            }
            else
            {
                queryable = queryable.Where(x => x.Status == parsedStatus);
            }
        }

        IQueryable<BomDocument> queryWithoutPagination = queryable.ApplyFilterWithoutPagination(request.Filter);
        long count = await BomDocumentRepository.AsyncExecuter.LongCountAsync(queryWithoutPagination, cancellationToken);

        IQueryable<BomDocument> queryWithPagination = queryable.ApplyFilter(request.Filter);
        List<BomDocument> bomDocuments = await BomDocumentRepository.AsyncExecuter.ToListAsync(queryWithPagination, cancellationToken);

        List<BomDocumentDto> bomDocumentsDto = ObjectMapper.Map<List<BomDocument>, List<BomDocumentDto>>(bomDocuments);

        return new PagedResultDto<BomDocumentDto>(count, bomDocumentsDto);
    }
}