using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.BomDocuments.Queries;
using SpareParts.Core.Entities.Boms;
using SpareParts.Core.Enums;
using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Content;
using Volo.Abp.Domain.ChangeTracking;
using Volo.Abp.Validation;

namespace SpareParts.Core.BomDocuments.QueryHandlers;

public class GetBomDocumentQueryHandler : GetBomDocumentQueryBase, IQueryHandler<GetBomDocumentQuery, RemoteStreamContent>
{
    [DisableEntityChangeTracking]
    public async Task<RemoteStreamContent> Handle(GetBomDocumentQuery request, CancellationToken cancellationToken)
    {
        BomDocument bomDocument = await GetBomDocument(request.BomDocumentId, cancellationToken);

        if (bomDocument.Status == BomDocumentationStatus.Expired ||
           (bomDocument.LastModificationTime.HasValue && bomDocument.LastModificationTime.Value.AddHours(CoreApplicationConsts.BomDocumentation.ExpirationHours) < DateTime.UtcNow))
        {
            throw new AbpValidationException("The bom's documentation is expired");
        }

        if (bomDocument.Status != BomDocumentationStatus.Completed)
        {
            throw new AbpValidationException("The generation is not completed");
        }

        Stream stream = await BlobContainer.GetAsync($"{bomDocument.Id:N}", cancellationToken: cancellationToken);

        return new RemoteStreamContent(stream, bomDocument.FileName);
    }
}