using SpareParts.AbpMediatR.Handlers;
using SpareParts.Core.BomDocuments.Dtos;
using SpareParts.Core.BomDocuments.Queries;
using SpareParts.Core.Entities.Boms;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.ChangeTracking;

namespace SpareParts.Core.BomDocuments.QueryHandlers;

public class GetBomDocumentMetadataByIdQueryHandler : GetBomDocumentQueryBase, IQueryHandler<GetBomDocumentMetadataByIdQuery, BomDocumentDto>
{
    [DisableEntityChangeTracking]
    public async Task<BomDocumentDto> Handle(GetBomDocumentMetadataByIdQuery request, CancellationToken cancellationToken)
    {
        BomDocument bomDocument = await GetBomDocument(request.BomDocumentId, cancellationToken);

        BomDocumentDto bomDocumentDto = ObjectMapper.Map<BomDocument, BomDocumentDto>(bomDocument);

        return bomDocumentDto;
    }
}