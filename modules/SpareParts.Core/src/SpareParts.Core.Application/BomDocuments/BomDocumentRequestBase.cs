using SpareParts.Core.Containers;
using SpareParts.Core.DataVisibility;
using SpareParts.Core.Entities.Boms;
using SpareParts.Core.Entities.Equipments;
using System;
using Volo.Abp.Authorization;
using Volo.Abp.BlobStoring;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Users;

namespace SpareParts.Core.BomDocuments;
public abstract class BomDocumentRequestBase : CoreRequestBase
{
    protected IRepository<BomDocument, Guid> BomDocumentRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<BomDocument, Guid>>();
    protected IRepository<Equipment, Guid> EquipmentRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Equipment, Guid>>();
    protected ProductAccessControlService ProductAccessControlService => LazyServiceProvider.LazyGetRequiredService<ProductAccessControlService>();
    protected ICurrentUser CurrentUser => LazyServiceProvider.LazyGetRequiredService<ICurrentUser>();
    protected IBlobContainer<BomDocumentsContainer> BlobContainer => LazyServiceProvider.LazyGetRequiredService<IBlobContainer<BomDocumentsContainer>>();
    protected IAbpAuthorizationService AuthorizationService => LazyServiceProvider.LazyGetRequiredService<IAbpAuthorizationService>();
}