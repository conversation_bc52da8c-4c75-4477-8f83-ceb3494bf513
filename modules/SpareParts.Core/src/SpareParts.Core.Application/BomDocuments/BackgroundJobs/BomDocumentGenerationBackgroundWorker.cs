using SpareParts.Core.BomDocuments.QuestPdf;
using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.DependencyInjection;
using Volo.Abp.MultiTenancy;
using Volo.Abp.Uow;

namespace SpareParts.Core.BomDocuments.BackgroundJobs;

[ExcludeFromCodeCoverage]
public class BomDocumentGenerationBackgroundWorker : AsyncBackgroundJob<BomDocumentGenerationJobArgs>, ITransientDependency, IUnitOfWorkEnabled
{
    public IAbpLazyServiceProvider LazyServiceProvider { get; set; } = null!;
    private ICurrentTenant CurrentTenant => LazyServiceProvider.LazyGetRequiredService<ICurrentTenant>();
    private BomPdfDocumentGenerator BomPdfDocumentGenerator => LazyServiceProvider.LazyGetRequiredService<BomPdfDocumentGenerator>();
    

    public override async Task ExecuteAsync(BomDocumentGenerationJobArgs args)
    {
        using (CurrentTenant.Change(args.TenantId, args.TenantName))
        {
            await BomPdfDocumentGenerator.GenerateAndSaveAsync(args);
        }
    }
}