using AutoMapper;
using SpareParts.Core.BomDocuments.Commands;
using SpareParts.Core.BomDocuments.Dtos;
using SpareParts.Core.BomDocuments.Dtos.Inputs;
using SpareParts.Core.Entities.Boms;

namespace SpareParts.Core.BomDocuments.Mapping;

public class BomDocumentMappingProfile : Profile
{
    public BomDocumentMappingProfile()
    {
        CreateMap<CreateBomDocumentDto, CreateBomDocumentCommand>();
        CreateMap<BomDocument, BomDocumentDto>()
            .ForMember(c => c.Status, opt =>
                opt.ConvertUsing<BomDocumentationStatusConverter, BomDocument>(src => src));
    }
}