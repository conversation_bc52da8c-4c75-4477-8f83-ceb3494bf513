using Microsoft.Extensions.Logging;
using SpareParts.Common;
using SpareParts.Core.BomDocuments.BackgroundJobs;
using SpareParts.Core.BomDocuments.DataModels;
using SpareParts.Core.BomDocuments.QuestPdf.BaseDocument;
using SpareParts.Core.Containers;
using SpareParts.Core.Entities.Boms;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Equipments;
using SpareParts.Core.Enums;
using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.BlobStoring;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Localization;
using Volo.Abp.Security.Claims;
using Volo.Abp.Uow;

namespace SpareParts.Core.BomDocuments.QuestPdf;

public class BomPdfDocumentGenerator : ITransientDependency
{
    public IAbpLazyServiceProvider LazyServiceProvider { get; set; } = null!;
    protected ILoggerFactory LoggerFactory => LazyServiceProvider.LazyGetRequiredService<ILoggerFactory>();
    protected ILogger Logger => LazyServiceProvider.LazyGetService<ILogger>(_ => LoggerFactory.CreateLogger(GetType().FullName!));
    private IBlobContainer<BomDocumentsContainer> BlobContainer => LazyServiceProvider.LazyGetRequiredService<IBlobContainer<BomDocumentsContainer>>();
    private IRepository<BomDocument, Guid> BomDocumentRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<BomDocument, Guid>>();
    private BomDocumentDataModelProvider BomDocumentDataModelProvider => LazyServiceProvider.LazyGetRequiredService<BomDocumentDataModelProvider>();
    private DocumentStreamGenerator DocumentStreamGenerator => LazyServiceProvider.LazyGetRequiredService<DocumentStreamGenerator>();
    private BomDocumentGenerator BomDocumentGenerator => LazyServiceProvider.LazyGetRequiredService<BomDocumentGenerator>();
    private IRepository<Component, Guid> ComponentRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Component, Guid>>();
    private IRepository<Equipment, Guid> EquipmentRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Equipment, Guid>>();
    private ICurrentPrincipalAccessor CurrentPrincipalAccessor => LazyServiceProvider.LazyGetRequiredService<ICurrentPrincipalAccessor>();
    private IUnitOfWorkManager UnitOfWorkManager => LazyServiceProvider.LazyGetRequiredService<IUnitOfWorkManager>();

    public virtual async Task GenerateAndSaveAsync(BomDocumentGenerationJobArgs args, CancellationToken cancellationToken = default)
    {
        using IUnitOfWork uow = UnitOfWorkManager.Begin(true);
        BomDocument bomDocument = await BomDocumentRepository.GetAsync(args.BomDocumentId, cancellationToken: cancellationToken);
        using (CurrentPrincipalAccessor.Change(new Claim(AbpClaimTypes.UserId, bomDocument.CreatorId!.Value.ToString())))
        {
            using (CultureHelper.Use(bomDocument.Language))
            {
                BomDocumentGenerationContext generationContext = new()
                {
                    TenantName = args.TenantName,
                    OperationId = bomDocument.OperationId,
                    CreatorId = bomDocument.CreatorId.Value,
                    Context = bomDocument.Context,
                    Language = bomDocument.Language
                };

                Logger.LogInformation("Begin processing BOM document - Generation context : {generationContext}", generationContext);
                bomDocument.ChangeStatus(BomDocumentationStatus.Processing);
                await BomDocumentRepository.UpdateAsync(bomDocument, cancellationToken: cancellationToken);
                await uow.SaveChangesAsync(cancellationToken);

                try
                {
                    long start = Stopwatch.GetTimestamp();
                    Logger.LogInformation("Starting BOM document generation at {now} - Generation context : {generationContext}", DateTime.UtcNow, generationContext);

                    BomDocumentMainPageDataModel bomDocumentMainPageDataModel = await BomDocumentDataModelProvider.BuildAsync(bomDocument, generationContext, cancellationToken);
                    Stream stream = await DocumentStreamGenerator.GenerateStream(BomDocumentGenerator, bomDocumentMainPageDataModel);

                    stream.ResetPositionIfSeekable();
                    await BlobContainer.SaveAsync($"{bomDocument.Id:N}", stream, true, cancellationToken);

                    bomDocument.SetSize(stream.Length);

                    string filename = $"{bomDocumentMainPageDataModel.Label}.pdf";
                    bomDocument.SetFilename(filename);

                    bomDocument.ChangeStatus(BomDocumentationStatus.Completed);

                    TimeSpan duration = Stopwatch.GetElapsedTime(start);
                    bomDocument.SetProcessingTime(duration.TotalMilliseconds);
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, " Error while generating bom document : {message} - Generation context : {generationContext}", ex.Message, generationContext);
                    bomDocument.ChangeStatus(BomDocumentationStatus.Failed);
                }
                finally
                {
                    await BomDocumentRepository.UpdateAsync(bomDocument, cancellationToken: cancellationToken);
                    await uow.CompleteAsync(cancellationToken);

                    string? equipmentSerialNumber = bomDocument.Context == BomDocumentContext.Equipment ? (await EquipmentRepository.GetQueryableAsync())
                        .Where(e => e.Id == bomDocument.MainEntityId).Select(e => e.SerialNumber).First() : null;

                    string? assemblyCode = bomDocument.AssemblyId.HasValue ? (await ComponentRepository.GetQueryableAsync())
                        .Where(c => c.Id == bomDocument.AssemblyId.Value).Select(c => c.Code).First() : null;

                    string productCode = bomDocument.Context switch
                    {
                        BomDocumentContext.Product => (await ComponentRepository.GetQueryableAsync())
                            .Where(c => c.Id == bomDocument.MainEntityId).Select(c => c.Code).First(),
                        BomDocumentContext.Equipment => (await EquipmentRepository.GetQueryableAsync())
                            .Where(e => e.Id == bomDocument.MainEntityId)
                            .Join(await ComponentRepository.GetQueryableAsync(), e => e.ProductId, c => c.Id, (e, c) => c.Code)
                            .First()
                    };

                    Logger.LogInformation("EquipmentSerialNumber={EquipmentSerialNumber};ProductCode={ProductCode};AssemblyCode={AssemblyCode};FileName={FileName};Size={Size} bytes;ProcessingTime={ProcessingTime} ms;"
                        , equipmentSerialNumber, productCode, assemblyCode, bomDocument.FileName, bomDocument.Size, bomDocument.ProcessingTime);
                }
            }
        }
    }
}