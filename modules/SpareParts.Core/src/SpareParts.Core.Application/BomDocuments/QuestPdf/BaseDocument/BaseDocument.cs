using Microsoft.Extensions.FileProviders;
using QuestPDF.Drawing;
using QuestPDF.Infrastructure;
using System.IO;
using Volo.Abp.VirtualFileSystem;

namespace SpareParts.Core.BomDocuments.QuestPdf.BaseDocument;

public abstract class BaseDocument<T> : IDocument where T : class
{
    protected readonly IVirtualFileProvider VirtualFileProvider;
    public T Model { get; }

    protected BaseDocument(IVirtualFileProvider virtualFileProvider, T model)
    {
        VirtualFileProvider = virtualFileProvider;

        IFileInfo fontFile = VirtualFileProvider.GetFileInfo("Fonts/NotoSansSC-VariableFont_wght.ttf");
        using Stream fontFileStream = fontFile.CreateReadStream();
        FontManager.RegisterFontWithCustomName(CoreApplicationConsts.BomPdfDocumentDefaultValues.NotoScFontFamily, fontFileStream);

        QuestPDF.Settings.DocumentLayoutExceptionThreshold = CoreApplicationConsts.BomPdfDocumentDefaultValues.DocumentLayoutExceptionThreshold;
        Model = model;
    }

    public DocumentMetadata GetMetadata() => DocumentMetadata.Default;

    public abstract void Compose(IDocumentContainer container);
}