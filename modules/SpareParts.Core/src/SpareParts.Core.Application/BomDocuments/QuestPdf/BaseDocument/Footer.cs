using Microsoft.Extensions.Localization;
using QuestPDF.Fluent;
using QuestPDF.Infrastructure;

namespace SpareParts.Core.BomDocuments.QuestPdf.BaseDocument;

public class Footer : IComponent
{
    private readonly IStringLocalizer _stringLocalizer;
    private readonly DocumentSettings _documentSettings;

    public Footer(IStringLocalizer stringLocalizer, DocumentSettings documentSettings)
    {
        _stringLocalizer = stringLocalizer;
        _documentSettings = documentSettings;
    }

    public void Compose(IContainer container)
    {
        container.Row(row =>
        {
            row.RelativeItem().Column(column =>
            {
                column.Item()
                    .LineHorizontal(CoreApplicationConsts.BomPdfDocumentDefaultValues.DefaultLineHorizontalSize, Unit.Millimetre)
                    .LineColor(_documentSettings.PrimaryColor);
                column.Item().Row(rowChild =>
                {
                    rowChild.RelativeItem(2)
                        .ExtendHorizontal()
                        .AlignLeft()
                        .AlignMiddle()
                        .Text(_stringLocalizer.GetString("Bom.Pdf.Footer.Copyright"))
                        .FontColor(CoreApplicationConsts.BomPdfDocumentDefaultValues.GreyFontColor)
                        .FontSize(CoreApplicationConsts.BomPdfDocumentDefaultValues.Footer.FontSize)
                        .FontFamily(CoreApplicationConsts.BomPdfDocumentDefaultValues.NotoScFontFamily);

                    rowChild.ConstantItem(150)
                        .AlignCenter()
                        .AlignMiddle()
                        .Text(_stringLocalizer.GetString("Bom.Pdf.Footer.Branding"))
                        .FontColor(CoreApplicationConsts.BomPdfDocumentDefaultValues.GreyFontColor)
                        .FontSize(CoreApplicationConsts.BomPdfDocumentDefaultValues.Footer.FontSize)
                        .FontFamily(CoreApplicationConsts.BomPdfDocumentDefaultValues.NotoScFontFamily);

                    rowChild.RelativeItem(2)
                        .AlignRight()
                        .Padding(CoreApplicationConsts.BomPdfDocumentDefaultValues.Footer.PageCounterPadding)
                        .DefaultTextStyle(x => x.FontFamily(CoreApplicationConsts.BomPdfDocumentDefaultValues.NotoScFontFamily))
                        .DefaultTextStyle(x => x.FontColor(CoreApplicationConsts.BomPdfDocumentDefaultValues.GreyFontColor))
                        .DefaultTextStyle(x => x.FontSize(CoreApplicationConsts.BomPdfDocumentDefaultValues.Footer.FontSize))
                        .Text(x =>
                        {
                            x.CurrentPageNumber();
                            x.Span(" / ");
                            x.TotalPages();
                        });
                });

            });
        });
    }
}