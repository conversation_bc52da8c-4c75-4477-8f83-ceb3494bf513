using Bogus;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Shouldly;
using SpareParts.Common.Translations;
using SpareParts.Core.DomainServices.Components;
using SpareParts.Core.DomainServices.Resources;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Resources;
using System;
using System.Globalization;
using System.IO;
using System.Threading.Tasks;
using SpareParts.Core.Enums;
using Volo.Abp.Content;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Localization;
using Volo.Abp.Modularity;
using Volo.Abp.VirtualFileSystem;
using Xunit;

namespace SpareParts.Core.Translations;

public abstract class MultiLingualObjectManagerTests<TStartupModule> : CoreDomainTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    private readonly MultiLingualObjectManager _multiLingualObjectManager;
    private readonly Faker _faker;
    private readonly ImageManager _imageManager;
    private readonly IVirtualFileProvider _virtualFileProvider;
    private const string LabelInEn = "LabelInEn";
    private const string LabelInFr = "LabelInFr";
    private const string LabelInBe = "LabelInBe";
    private const string OnlineLabelInEn = "OnlineLabelInEn";
    private const string OnlineLabelInFr = "OnlineLabelInFr";
    private ComponentDomainService ComponentDomainService => ServiceProvider.GetRequiredService<ComponentDomainService>();
    private IRepository<Component> ComponentRepository => ServiceProvider.GetRequiredService<IRepository<Component>>();

    protected MultiLingualObjectManagerTests()
    {
        _faker = new Faker();
        _imageManager = ServiceProvider.GetRequiredService<ImageManager>();
        _virtualFileProvider = ServiceProvider.GetRequiredService<IVirtualFileProvider>();
        _multiLingualObjectManager = ServiceProvider.GetRequiredService<MultiLingualObjectManager>();
    }

    private async Task<Component> InsertComponentWithTranslations()
    {
        IFileInfo imageFile = _virtualFileProvider.GetFileInfo("/Files/default-image.png");
        await using Stream imageStream = imageFile.CreateReadStream();
        Resource resource = await _imageManager.CreateAsync(new RemoteStreamContent(imageStream, _faker.System.CommonFileName(CoreTestConsts.ImageExtension)));
        ComponentTranslation translationEn = new("en", LabelInEn);
        ComponentTranslation translationFr = new("fr", LabelInFr);
        ComponentTranslation[] translations = [translationEn, translationFr];
        Component assembly = await ComponentDomainService.CreateAsync(Guid.NewGuid().ToString("N"), translations, ComponentType.Assembly, resource);
        return await ComponentRepository.InsertAsync(assembly);
    }

    private async Task<Component> InsertComponentWithOnLineTranslations()
    {
        IFileInfo imageFile = _virtualFileProvider.GetFileInfo("/Files/default-image.png");
        await using Stream imageStream = imageFile.CreateReadStream();
        Resource resource = await _imageManager.CreateAsync(new RemoteStreamContent(imageStream, _faker.System.CommonFileName(CoreTestConsts.ImageExtension)));
        ComponentTranslation componentTranslation = new("be", LabelInBe);
        Component assembly = await ComponentDomainService.CreateAsync(Guid.NewGuid().ToString("N"), [componentTranslation], ComponentType.Assembly, resource);
        assembly.AddOnlineTranslation(new ComponentOnlineTranslation("en", OnlineLabelInEn));
        assembly.AddOnlineTranslation(new ComponentOnlineTranslation("fr", OnlineLabelInFr));
        return await ComponentRepository.InsertAsync(assembly);
    }

    private async Task<Component> InsertComponentWithoutEnTranslation()
    {
        IFileInfo imageFile = _virtualFileProvider.GetFileInfo("/Files/default-image.png");
        await using Stream imageStream = imageFile.CreateReadStream();
        Resource resource = await _imageManager.CreateAsync(new RemoteStreamContent(imageStream, _faker.System.CommonFileName(CoreTestConsts.ImageExtension)));
        ComponentTranslation translationFr = new("fr", LabelInFr);
        ComponentTranslation[] translations = [translationFr];
        Component assembly = await ComponentDomainService.CreateAsync(Guid.NewGuid().ToString("N"), translations, ComponentType.Assembly, resource);
        return await ComponentRepository.InsertAsync(assembly);
    }

    private async Task<Component> InsertComponentWithoutEnOnlineTranslation()
    {
        IFileInfo imageFile = _virtualFileProvider.GetFileInfo("/Files/default-image.png");
        await using Stream imageStream = imageFile.CreateReadStream();
        Resource resource = await _imageManager.CreateAsync(new RemoteStreamContent(imageStream, _faker.System.CommonFileName(CoreTestConsts.ImageExtension)));
        ComponentTranslation componentTranslation = new("be", LabelInBe);
        Component assembly = await ComponentDomainService.CreateAsync(Guid.NewGuid().ToString("N"), [componentTranslation], ComponentType.Assembly, resource);
        assembly.AddOnlineTranslation(new ComponentOnlineTranslation("fr", OnlineLabelInFr));
        return await ComponentRepository.InsertAsync(assembly);
    }

    [Fact]
    public async Task Should_Get_Default_Translation_Language_If_CultureName_Is_Empty()
    {
        Guid tenantId = GuidGenerator.Create();
        using (CurrentTenant.Change(tenantId))
        {
            using (CultureHelper.Use(""))
            {
                // Arrange
                Component assembly = null!;
                await WithUnitOfWorkAsync(async () =>
                {
                    assembly = await InsertComponentWithTranslations();
                });

                // Act
                await WithUnitOfWorkAsync(async () =>
                {
                    string translatedLabel = await _multiLingualObjectManager.GetTranslationAsync(assembly.Translations, crt => crt.Label,
                        assembly.OnlineTranslations, crt => crt.Label);

                    // Assert
                    translatedLabel.ShouldBe(LabelInEn);
                });

            }
        }
    }

    [Fact]
    public async Task Should_Get_Default_OnlineTranslation_Language_If_CultureName_Is_Empty()
    {
        Guid tenantId = GuidGenerator.Create();
        using (CurrentTenant.Change(tenantId))
        {
            using (CultureHelper.Use(""))
            {
                // Arrange
                Component assembly = null!;
                await WithUnitOfWorkAsync(async () =>
                {
                    assembly = await InsertComponentWithOnLineTranslations();
                });

                // Act
                await WithUnitOfWorkAsync(async () =>
                {
                    string translatedLabel = await _multiLingualObjectManager.GetTranslationAsync(assembly.Translations, crt => crt.Label,
                        assembly.OnlineTranslations, crt => crt.Label);

                    // Assert
                    translatedLabel.ShouldBe(OnlineLabelInEn);
                });

            }
        }
    }

    [Fact]
    public async Task Should_Get_Current_Translation_Language()
    {
        Guid tenantId = GuidGenerator.Create();
        using (CurrentTenant.Change(tenantId))
        {
            using (CultureHelper.Use("fr"))
            {
                // Arrange
                Component assembly = null!;
                await WithUnitOfWorkAsync(async () =>
                {
                    assembly = await InsertComponentWithTranslations();
                });

                // Act
                await WithUnitOfWorkAsync(async () =>
                {
                    string translatedLabel = await _multiLingualObjectManager.GetTranslationAsync(assembly.Translations, crt => crt.Label,
                        assembly.OnlineTranslations, crt => crt.Label);

                    // Assert
                    translatedLabel.ShouldBe(LabelInFr);
                });
            }
        }
    }

    [Fact]
    public async Task Should_Get_Current_OnlineTranslation_Language()
    {
        Guid tenantId = GuidGenerator.Create();
        using (CurrentTenant.Change(tenantId))
        {
            using (CultureHelper.Use("fr"))
            {
                // Arrange
                Component assembly = null!;
                await WithUnitOfWorkAsync(async () =>
                {
                    assembly = await InsertComponentWithOnLineTranslations();
                });

                // Act
                await WithUnitOfWorkAsync(async () =>
                {
                    string translatedLabel = await _multiLingualObjectManager.GetTranslationAsync(assembly.Translations, crt => crt.Label,
                        assembly.OnlineTranslations, crt => crt.Label);

                    // Assert
                    translatedLabel.ShouldBe(OnlineLabelInFr);
                });
            }
        }
    }

    [Fact]
    public async Task Should_Get_Parent_Translation_Language()
    {
        Guid tenantId = GuidGenerator.Create();
        using (CurrentTenant.Change(tenantId))
        {
            using (CultureHelper.Use("fr-CA"))
            {
                // Arrange
                Component assembly = null!;
                await WithUnitOfWorkAsync(async () =>
                {
                    assembly = await InsertComponentWithTranslations();
                });

                // Act
                await WithUnitOfWorkAsync(async () =>
                {
                    string translatedLabel = await _multiLingualObjectManager.GetTranslationAsync(assembly.Translations, crt => crt.Label,
                        assembly.OnlineTranslations, crt => crt.Label);

                    // Assert
                    translatedLabel.ShouldBe(LabelInFr);
                });
            }
        }
    }

    [Fact]
    public async Task Should_Get_Parent_OnlineTranslation_Language()
    {
        Guid tenantId = GuidGenerator.Create();
        using (CurrentTenant.Change(tenantId))
        {
            using (CultureHelper.Use("fr-CA"))
            {
                // Arrange
                Component assembly = null!;
                await WithUnitOfWorkAsync(async () =>
                {
                    assembly = await InsertComponentWithOnLineTranslations();
                });

                // Act
                await WithUnitOfWorkAsync(async () =>
                {
                    string translatedLabel = await _multiLingualObjectManager.GetTranslationAsync(assembly.Translations, crt => crt.Label,
                        assembly.OnlineTranslations, crt => crt.Label);

                    // Assert
                    translatedLabel.ShouldBe(OnlineLabelInFr);
                });
            }
        }
    }

    [Fact]
    public async Task Should_Get_Translation_For_A_Specified_Culture()
    {
        Guid tenantId = GuidGenerator.Create();
        using (CurrentTenant.Change(tenantId))
        {
            using (CultureHelper.Use("fr-fr"))
            {
                // Arrange
                Component assembly = null!;
                await WithUnitOfWorkAsync(async () =>
                {
                    assembly = await InsertComponentWithTranslations();
                });

                // Act
                await WithUnitOfWorkAsync(async () =>
                {
                    string specifiedCulture = "en";
                    string translatedLabel = await _multiLingualObjectManager.GetTranslationAsync(assembly.Translations, crt => crt.Label,
                        assembly.OnlineTranslations, crt => crt.Label, cultureInfo: new CultureInfo(specifiedCulture));

                    // Assert
                    translatedLabel.ShouldBe(LabelInEn);
                });
            }
        }
    }

    [Fact]
    public async Task Should_Get_OnlineTranslation_For_A_Specified_Culture()
    {
        Guid tenantId = GuidGenerator.Create();
        using (CurrentTenant.Change(tenantId))
        {
            using (CultureHelper.Use("fr-fr"))
            {
                // Arrange
                Component assembly = null!;
                await WithUnitOfWorkAsync(async () =>
                {
                    assembly = await InsertComponentWithOnLineTranslations();
                });

                // Act
                await WithUnitOfWorkAsync(async () =>
                {
                    string specifiedCulture = "en";
                    string translatedLabel = await _multiLingualObjectManager.GetTranslationAsync(assembly.Translations, crt => crt.Label,
                        assembly.OnlineTranslations, crt => crt.Label, cultureInfo: new CultureInfo(specifiedCulture));

                    // Assert
                    translatedLabel.ShouldBe(OnlineLabelInEn);
                });
            }
        }
    }

    [Fact]
    public async Task Should_Get_Default_Translation_Language()
    {
        Guid tenantId = GuidGenerator.Create();
        using (CurrentTenant.Change(tenantId))
        {
            using (CultureHelper.Use("es"))
            {
                // Arrange
                Component assembly = null!;
                await WithUnitOfWorkAsync(async () =>
                {
                    assembly = await InsertComponentWithTranslations();
                });

                // Act
                await WithUnitOfWorkAsync(async () =>
                {
                    string translatedLabel = await _multiLingualObjectManager.GetTranslationAsync(assembly.Translations, crt => crt.Label,
                        assembly.OnlineTranslations, crt => crt.Label);

                    // Assert
                    translatedLabel.ShouldBe(LabelInEn);
                });
            }
        }
    }

    [Fact]
    public async Task Should_Get_Default_OnlineTranslation_Language()
    {
        Guid tenantId = GuidGenerator.Create();
        using (CurrentTenant.Change(tenantId))
        {
            using (CultureHelper.Use("es"))
            {
                // Arrange
                Component assembly = null!;
                await WithUnitOfWorkAsync(async () =>
                {
                    assembly = await InsertComponentWithOnLineTranslations();
                });

                // Act
                await WithUnitOfWorkAsync(async () =>
                {
                    string translatedLabel = await _multiLingualObjectManager.GetTranslationAsync(assembly.Translations, crt => crt.Label,
                            assembly.OnlineTranslations, crt => crt.Label);

                    // Assert
                    translatedLabel.ShouldBe(OnlineLabelInEn);
                });
            }
        }
    }

    [Fact]
    public async Task Should_Return_First_Translation_In_Collection_If_No_Matching()
    {
        Guid tenantId = GuidGenerator.Create();
        using (CurrentTenant.Change(tenantId))
        {
            using (CultureHelper.Use("es"))
            {
                // Arrange
                Component assembly = null!;
                await WithUnitOfWorkAsync(async () =>
                {
                    assembly = await InsertComponentWithoutEnTranslation();
                });


                // Act
                await WithUnitOfWorkAsync(async () =>
                {
                    string translatedLabel = await _multiLingualObjectManager.GetTranslationAsync(assembly.Translations, crt => crt.Label,
                        assembly.OnlineTranslations, crt => crt.Label);

                    // Assert
                    translatedLabel.ShouldBe(LabelInFr);
                });
            }
        }
    }

    [Fact]
    public async Task Should_Return_First_Online_Translation_In_Collection_If_No_Matching()
    {
        Guid tenantId = GuidGenerator.Create();
        using (CurrentTenant.Change(tenantId))
        {
            using (CultureHelper.Use("es"))
            {
                // Arrange
                Component assembly = null!;
                await WithUnitOfWorkAsync(async () =>
                {
                    assembly = await InsertComponentWithoutEnOnlineTranslation();
                });


                // Act
                await WithUnitOfWorkAsync(async () =>
                {
                    string translatedLabel = await _multiLingualObjectManager.GetTranslationAsync(assembly.Translations, crt => crt.Label,
                        assembly.OnlineTranslations, crt => crt.Label);

                    // Assert
                    translatedLabel.ShouldBe(OnlineLabelInFr);
                });
            }
        }
    }

    [Fact]
    public async Task Should_Return_Null_Description_Of_First_Translation_In_Collection_If_No_Matching()
    {
        Guid tenantId = GuidGenerator.Create();
        using (CurrentTenant.Change(tenantId))
        {
            using (CultureHelper.Use("es"))
            {
                // Arrange
                Component assembly = null!;
                await WithUnitOfWorkAsync(async () =>
                {
                    assembly = await InsertComponentWithoutEnTranslation();
                });


                // Act
                await WithUnitOfWorkAsync(async () =>
                {
                    string? translatedLabel = await _multiLingualObjectManager.GetTranslationAsync(assembly.Translations, crt => crt.Description,
                        assembly.OnlineTranslations, crt => crt.Description);

                    // Assert
                    translatedLabel.ShouldBeNull();
                });
            }
        }
    }
}