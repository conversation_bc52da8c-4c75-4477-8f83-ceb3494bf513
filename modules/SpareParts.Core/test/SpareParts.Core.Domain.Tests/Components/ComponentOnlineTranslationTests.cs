using Bogus;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Shouldly;
using SpareParts.Core.DomainServices.Components;
using SpareParts.Core.DomainServices.Resources;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Resources;
using System;
using System.IO;
using System.Threading.Tasks;
using SpareParts.Core.Enums;
using Volo.Abp.Content;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;
using Volo.Abp.VirtualFileSystem;
using Xunit;

namespace SpareParts.Core.Components;

public abstract class ComponentOnlineTranslationTests<TStartupModule> : CoreDomainTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    private readonly Faker _faker;
    private readonly ImageManager _imageManager;
    private readonly IVirtualFileProvider _virtualFileProvider;
    private readonly IRepository<ComponentOnlineTranslation> _componentOnlineTranslationRepository;
    private ComponentDomainService ComponentDomainService => ServiceProvider.GetRequiredService<ComponentDomainService>();
    private IRepository<Component> ComponentRepository => ServiceProvider.GetRequiredService<IRepository<Component>>();

    protected ComponentOnlineTranslationTests()
    {
        _faker = new Faker();
        _imageManager = ServiceProvider.GetRequiredService<ImageManager>();
        _virtualFileProvider = ServiceProvider.GetRequiredService<IVirtualFileProvider>();

        _componentOnlineTranslationRepository = ServiceProvider.GetRequiredService<IRepository<ComponentOnlineTranslation>>();
    }

    [Fact]
    public void Instantiation_Should_Succeed()
    {
        // Arrange
        Guid tenantId = GuidGenerator.Create();
        Guid componentId = Guid.NewGuid();
        string language = "fr";
        string label = "label";
        string description = "description";

        using (CurrentTenant.Change(tenantId))
        {
            // Act 
            ComponentOnlineTranslation componentOnlineTranslation = new(language, label, description)
            {
                ComponentId = componentId
            };

            //Assert
            componentOnlineTranslation.TenantId.ShouldBe(tenantId);
            componentOnlineTranslation.Language.ShouldBe(language);
            componentOnlineTranslation.Label.ShouldBe(label);
            componentOnlineTranslation.Description.ShouldBe(description);
            componentOnlineTranslation.ComponentId.ShouldBe(componentId);
        }
    }

    [Fact]
    public void Instantiation_Should_Succeed_With_Nullable_Values()
    {
        // Arrange
        Guid tenantId = GuidGenerator.Create();
        Guid componentId = Guid.NewGuid();
        string language = "fr";

        using (CurrentTenant.Change(tenantId))
        {
            // Act 
            ComponentOnlineTranslation componentOnlineTranslation = new(language)
            {
                ComponentId = componentId
            };

            //Assert
            componentOnlineTranslation.TenantId.ShouldBe(tenantId);
            componentOnlineTranslation.Language.ShouldBe(language);
            componentOnlineTranslation.Label.ShouldBeNull();
            componentOnlineTranslation.Description.ShouldBeNull();
            componentOnlineTranslation.ComponentId.ShouldBe(componentId);
        }
    }

    [Fact]
    public async Task Should_Insert_Translation_In_Database()
    {
        // Arrange
        Guid tenantId = GuidGenerator.Create();
        ComponentOnlineTranslation componentOnlineTranslation = default!;

        using (CurrentTenant.Change(tenantId))
        {
            // Act 
            await WithUnitOfWorkAsync(async () =>
            {
                string code = _faker.Commerce.Product();
                ComponentTranslation translation = new(_faker.Locale, _faker.Commerce.Product());

                IFileInfo imageFile = _virtualFileProvider.GetFileInfo("/Files/default-image.png");
                await using Stream imageStream = imageFile.CreateReadStream();
                Resource resource = await _imageManager.CreateAsync(new RemoteStreamContent(imageStream, _faker.System.CommonFileName(CoreTestConsts.ImageExtension)));
                Component assembly = await ComponentDomainService.CreateAsync(code, [translation], ComponentType.Assembly ,resource);
                assembly = await ComponentRepository.InsertAsync(assembly, true);

                componentOnlineTranslation = new("fr", "label", "description")
                {
                    ComponentId = assembly.Id
                };
                await _componentOnlineTranslationRepository.InsertAsync(componentOnlineTranslation);
            });

            await WithUnitOfWorkAsync(async () =>
            {
                //Assert
                ComponentOnlineTranslation result = await _componentOnlineTranslationRepository.GetAsync(x => x.Language == componentOnlineTranslation.Language && x.ComponentId == componentOnlineTranslation.ComponentId);
                result.TenantId.ShouldBe(tenantId);
                result.Language.ShouldBe(componentOnlineTranslation.Language);
                result.Label.ShouldBe(componentOnlineTranslation.Label);
                result.Description.ShouldBe(componentOnlineTranslation.Description);
                result.ComponentId.ShouldBe(componentOnlineTranslation.ComponentId);
            });
        }
    }

    [Fact]
    public void Instantiation_Should_Throw_Exception_If_Data_Is_Incorrect()
    {
        // Arrange
        Guid tenantId = GuidGenerator.Create();
        string language = "fr";
        string label = "label";
        string description = "description";

        using (CurrentTenant.Change(tenantId))
        {
            // Act & Assert
            Should.Throw<ArgumentException>(() => new ComponentOnlineTranslation(string.Empty, label, description));
            Should.Throw<ArgumentException>(() => new ComponentOnlineTranslation(language, string.Empty, description));
        }
    }

    [Fact]
    public void ResetLabel_Should_Set_Label_To_Null()
    {
        // Arrange
        Guid tenantId = GuidGenerator.Create();
        Guid componentId = Guid.NewGuid();
        string language = "fr";

        using (CurrentTenant.Change(tenantId))
        {
            ComponentOnlineTranslation componentOnlineTranslation = new(language, "label", "desc")
            {
                ComponentId = componentId
            };

            // Act
            componentOnlineTranslation.ResetLabel();

            //Assert
            componentOnlineTranslation.Label.ShouldBeNull();
        }
    }

    [Fact]
    public void ResetDescription_Should_Set_Description_To_Null()
    {
        // Arrange
        Guid tenantId = GuidGenerator.Create();
        Guid componentId = Guid.NewGuid();
        string language = "fr";

        using (CurrentTenant.Change(tenantId))
        {
            ComponentOnlineTranslation componentOnlineTranslation = new(language, "label", "desc")
            {
                ComponentId = componentId
            };

            // Act
            componentOnlineTranslation.ResetDescription();

            //Assert
            componentOnlineTranslation.Description.ShouldBeNull();
        }
    }

    [Fact]
    public void Equals_Should_Compare_Correctly()
    {
        // Arrange
        Guid tenantId = GuidGenerator.Create();
        Guid componentId = Guid.NewGuid();
        string language = "fr";

        using (CurrentTenant.Change(tenantId))
        {
            ComponentOnlineTranslation componentOnlineTranslation1 = new(language, "label", "desc")
            {
                ComponentId = componentId
            };

            ComponentOnlineTranslation componentOnlineTranslation2 = new(language, "label", "desc")
            {
                ComponentId = componentId
            };

            ComponentOnlineTranslation componentOnlineTranslation3 = new("en", "label", "desc2")
            {
                ComponentId = componentId
            };

            // Act
            bool areEqual = componentOnlineTranslation1.Equals(componentOnlineTranslation2);
            bool areNotEqual = componentOnlineTranslation1.Equals(componentOnlineTranslation3);

            //Assert
            areEqual.ShouldBeTrue();
            areNotEqual.ShouldBeFalse();
        }
    }
}