using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Shouldly;
using SpareParts.Core.DomainServices.Components;
using SpareParts.Core.DomainServices.Resources;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Resources;
using SpareParts.Core.Enums;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Content;
using Volo.Abp.Data;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;
using Volo.Abp.VirtualFileSystem;
using Xunit;

namespace SpareParts.Core.Components;

public abstract class DrawingDomainServiceTests<TStartupModule> : CoreDomainTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    private readonly DrawingDomainService _drawingDomainService;
    private readonly ComponentDomainService _componentDomainService;
    private readonly IRepository<Component> _componentRepository;
    private readonly IRepository<Drawing, Guid> _drawingRepository;
    private readonly FileManager _fileManager;
    private readonly ImageManager _imageManager;
    private readonly IDataSeeder _dataSeeder;
    private readonly IVirtualFileProvider _virtualFileProvider;

    protected DrawingDomainServiceTests()
    {
        _componentRepository = ServiceProvider.GetRequiredService<IRepository<Component>>();
        _componentDomainService = ServiceProvider.GetRequiredService<ComponentDomainService>();
        _drawingDomainService = ServiceProvider.GetRequiredService<DrawingDomainService>();
        _drawingRepository = ServiceProvider.GetRequiredService<IRepository<Drawing, Guid>>();
        _fileManager = ServiceProvider.GetRequiredService<FileManager>();
        _imageManager = ServiceProvider.GetRequiredService<ImageManager>();
        _dataSeeder = ServiceProvider.GetRequiredService<IDataSeeder>();
        _virtualFileProvider = ServiceProvider.GetRequiredService<IVirtualFileProvider>();
    }

    [Fact]
    public async Task Should_Change_To_Main_My_Drawing_And_False_On_The_Current_One()
    {
        Guid tenantId = Guid.NewGuid();
        await _dataSeeder.SeedAsync(tenantId);

        using (CurrentTenant.Change(tenantId))
        {
            // Arrange
            const string index = "testIndex";

            Guid drawing1Id = Guid.Empty;
            Guid drawing2Id = Guid.Empty;
            await WithUnitOfWorkAsync(async () =>
            {
                Component assembly = await _componentDomainService.CreateAsync("code",
                    [new ComponentTranslation("en", "label")], ComponentType.Assembly);
                await _componentRepository.InsertAsync(assembly);

                IFileInfo imageFile = _virtualFileProvider.GetFileInfo("/Files/default-image.png");
                await using Stream imageStream = imageFile.CreateReadStream();
                IFileInfo pdfFile = _virtualFileProvider.GetFileInfo("/Files/21841215.pdf");
                await using Stream pdfStream = pdfFile.CreateReadStream();
                Resource file = await _fileManager.CreateAsync(new RemoteStreamContent(pdfStream, "asa.pdf"));
                Resource image = await _imageManager.CreateAsync(new RemoteStreamContent(imageStream, "asa.png"));

                Drawing drawing1 = _drawingDomainService.Create(assembly.Id, 1, file, image,
                    new Dictionary<string, Guid> { { index, assembly.Id } }, DrawingOrigin.Online);
                drawing1.IsVisible = true;
                drawing1 = await _drawingRepository.InsertAsync(drawing1);
                drawing1Id = drawing1.Id;

                Drawing drawing2 = _drawingDomainService.Create(assembly.Id, 2, file, image,
                    new Dictionary<string, Guid> { { index, assembly.Id } }, DrawingOrigin.Online);
                drawing2.IsVisible = true;

                await _drawingDomainService.SetAsMainAsync(drawing2);
                drawing2 = await _drawingRepository.InsertAsync(drawing2);
                drawing2Id = drawing2.Id;
            });

            await WithUnitOfWorkAsync(async () =>
            {
                // Act
                Drawing drawing1 = await _drawingRepository.GetAsync(drawing1Id);
                Drawing drawing2 = await _drawingRepository.GetAsync(drawing2Id);

                DrawingDomainService.ChangeToMain(drawing1, drawing2);
                await _drawingRepository.UpdateAsync(drawing1);
            });

            // Assert
            await WithUnitOfWorkAsync(async () =>
            {
                Drawing drawing1 = await _drawingRepository.GetAsync(drawing1Id);
                Drawing drawing2 = await _drawingRepository.GetAsync(drawing2Id);
                drawing1.IsMain.ShouldBe(true);
                drawing2.IsMain.ShouldBe(false);
            });

        }
    }

    [Fact]
    public async Task Should_Set_As_Main_My_Drawing()
    {
        Guid tenantId = Guid.NewGuid();
        await _dataSeeder.SeedAsync(tenantId);

        using (CurrentTenant.Change(tenantId))
        {
            // Arrange
            const string index = "testIndex";

            Guid drawing1Id = Guid.Empty;
            await WithUnitOfWorkAsync(async () =>
            {
                Component assembly = await _componentDomainService.CreateAsync("code",
                    [new ComponentTranslation("en", "label")], ComponentType.Assembly);
                await _componentRepository.InsertAsync(assembly);

                IFileInfo imageFile = _virtualFileProvider.GetFileInfo("/Files/default-image.png");
                await using Stream imageStream = imageFile.CreateReadStream();
                IFileInfo pdfFile = _virtualFileProvider.GetFileInfo("/Files/21841215.pdf");
                await using Stream pdfStream = pdfFile.CreateReadStream();
                Resource file = await _fileManager.CreateAsync(new RemoteStreamContent(pdfStream, "asa.pdf"));
                Resource image = await _imageManager.CreateAsync(new RemoteStreamContent(imageStream, "asa.png"));

                Drawing drawing1 = _drawingDomainService.Create(assembly.Id, 1, file, image,
                    new Dictionary<string, Guid> { { index, assembly.Id } }, DrawingOrigin.Online);
                drawing1.IsVisible = true;
                drawing1 = await _drawingRepository.InsertAsync(drawing1);
                drawing1Id = drawing1.Id;
            });

            await WithUnitOfWorkAsync(async () =>
            {
                // Act
                Drawing drawing1 = await _drawingRepository.GetAsync(drawing1Id);

                await _drawingDomainService.SetAsMainAsync(drawing1);
                await _drawingRepository.UpdateAsync(drawing1);
            });

            // Assert
            await WithUnitOfWorkAsync(async () =>
            {
                Drawing drawing1 = await _drawingRepository.GetAsync(drawing1Id);
                drawing1.IsMain.ShouldBe(true);
            });

        }
    }

    [Fact]
    public async Task ChangeToMain_Should_Throw_Exception_If_Not_Same_Assembly()
    {
        Guid tenantId = Guid.NewGuid();
        await _dataSeeder.SeedAsync(tenantId);

        using (CurrentTenant.Change(tenantId))
        {
            // Arrange
            const string index = "testIndex";

            Guid drawing1Id = Guid.Empty;
            Guid drawing2Id = Guid.Empty;
            await WithUnitOfWorkAsync(async () =>
            {
                Component assembly1 = await _componentDomainService.CreateAsync("code1",
                    [new ComponentTranslation("en", "label")], ComponentType.Assembly);
                await _componentRepository.InsertAsync(assembly1);

                Component assembly2 = await _componentDomainService.CreateAsync("code2",
                    [new ComponentTranslation("en", "label")], ComponentType.Assembly);
                await _componentRepository.InsertAsync(assembly2);

                IFileInfo imageFile = _virtualFileProvider.GetFileInfo("/Files/default-image.png");
                await using Stream imageStream = imageFile.CreateReadStream();
                IFileInfo pdfFile = _virtualFileProvider.GetFileInfo("/Files/21841215.pdf");
                await using Stream pdfStream = pdfFile.CreateReadStream();
                Resource file = await _fileManager.CreateAsync(new RemoteStreamContent(pdfStream, "asa.pdf"));
                Resource image = await _imageManager.CreateAsync(new RemoteStreamContent(imageStream, "asa.png"));

                Drawing drawing1 = _drawingDomainService.Create(assembly1.Id, 1, file, image,
                    new Dictionary<string, Guid> { { index, assembly1.Id } }, DrawingOrigin.Online);
                drawing1.IsVisible = true;
                drawing1 = await _drawingRepository.InsertAsync(drawing1);
                drawing1Id = drawing1.Id;

                Drawing drawing2 = _drawingDomainService.Create(assembly2.Id, 2, file, image,
                    new Dictionary<string, Guid> { { index, assembly2.Id } }, DrawingOrigin.Online);
                drawing2.IsVisible = true;
                await _drawingDomainService.SetAsMainAsync(drawing2);
                drawing2 = await _drawingRepository.InsertAsync(drawing2);
                drawing2Id = drawing2.Id;
            });

            await WithUnitOfWorkAsync(async () =>
            {
                Drawing drawing1 = await _drawingRepository.GetAsync(drawing1Id);
                Drawing drawing2 = await _drawingRepository.GetAsync(drawing2Id);

                //Act & Assert
                Should.Throw<BusinessException>(() => DrawingDomainService.ChangeToMain(drawing1, drawing2));
            });

        }
    }

    [Fact]
    public async Task ChangeToMainShould_Throw_Exception_If_Old_Drawing_Is_Not_Main()
    {
        Guid tenantId = Guid.NewGuid();
        await _dataSeeder.SeedAsync(tenantId);

        using (CurrentTenant.Change(tenantId))
        {
            // Arrange
            const string index = "testIndex";

            Guid drawing1Id = Guid.Empty;
            Guid drawing2Id = Guid.Empty;
            await WithUnitOfWorkAsync(async () =>
            {
                Component assembly = await _componentDomainService.CreateAsync("code",
                    [new ComponentTranslation("en", "label")], ComponentType.Assembly);
                await _componentRepository.InsertAsync(assembly);

                IFileInfo imageFile = _virtualFileProvider.GetFileInfo("/Files/default-image.png");
                await using Stream imageStream = imageFile.CreateReadStream();
                IFileInfo pdfFile = _virtualFileProvider.GetFileInfo("/Files/21841215.pdf");
                await using Stream pdfStream = pdfFile.CreateReadStream();
                Resource file = await _fileManager.CreateAsync(new RemoteStreamContent(pdfStream, "asa.pdf"));
                Resource image = await _imageManager.CreateAsync(new RemoteStreamContent(imageStream, "asa.png"));

                Drawing drawing1 = _drawingDomainService.Create(assembly.Id, 1, file, image,
                    new Dictionary<string, Guid> { { index, assembly.Id } }, DrawingOrigin.Online);
                drawing1.IsVisible = true;
                drawing1 = await _drawingRepository.InsertAsync(drawing1);
                drawing1Id = drawing1.Id;

                Drawing drawing2 = _drawingDomainService.Create(assembly.Id, 2, file, image,
                    new Dictionary<string, Guid> { { index, assembly.Id } }, DrawingOrigin.Online);
                drawing2.IsVisible = true;
                drawing2 = await _drawingRepository.InsertAsync(drawing2);
                drawing2Id = drawing2.Id;
            });

            await WithUnitOfWorkAsync(async () =>
            {
                Drawing drawing1 = await _drawingRepository.GetAsync(drawing1Id);
                Drawing drawing2 = await _drawingRepository.GetAsync(drawing2Id);

                //Act & Assert
                Should.Throw<BusinessException>(() => DrawingDomainService.ChangeToMain(drawing1, drawing2));
            });

        }
    }

    [Fact]
    public async Task SetAsMain_Should_Throw_Exception_If_Another_Drawing_Is_Main()
    {
        Guid tenantId = Guid.NewGuid();
        await _dataSeeder.SeedAsync(tenantId);

        using (CurrentTenant.Change(tenantId))
        {
            // Arrange
            const string index = "testIndex";

            Guid drawing1Id = Guid.Empty;
            await WithUnitOfWorkAsync(async () =>
            {
                Component assembly = await _componentDomainService.CreateAsync("code",
                    [new ComponentTranslation("en", "label")], ComponentType.Assembly);
                await _componentRepository.InsertAsync(assembly);

                IFileInfo imageFile = _virtualFileProvider.GetFileInfo("/Files/default-image.png");
                await using Stream imageStream = imageFile.CreateReadStream();
                IFileInfo pdfFile = _virtualFileProvider.GetFileInfo("/Files/21841215.pdf");
                await using Stream pdfStream = pdfFile.CreateReadStream();
                Resource file = await _fileManager.CreateAsync(new RemoteStreamContent(pdfStream, "asa.pdf"));
                Resource image = await _imageManager.CreateAsync(new RemoteStreamContent(imageStream, "asa.png"));

                Drawing drawing1 = _drawingDomainService.Create(assembly.Id, 1, file, image,
                    new Dictionary<string, Guid> { { index, assembly.Id } }, DrawingOrigin.Online);
                drawing1.IsVisible = true;
                drawing1 = await _drawingRepository.InsertAsync(drawing1);
                drawing1Id = drawing1.Id;

                Drawing drawing2 = _drawingDomainService.Create(assembly.Id, 2, file, image,
                    new Dictionary<string, Guid> { { index, assembly.Id } }, DrawingOrigin.Online);
                drawing2.IsVisible = true;
                await _drawingDomainService.SetAsMainAsync(drawing2);
                await _drawingRepository.InsertAsync(drawing2);
            });

            await WithUnitOfWorkAsync(async () =>
            {
                Drawing drawing1 = await _drawingRepository.GetAsync(drawing1Id);

                //Act & Assert
                Should.Throw<BusinessException>(() => _drawingDomainService.SetAsMainAsync(drawing1));
            });

        }
    }
}