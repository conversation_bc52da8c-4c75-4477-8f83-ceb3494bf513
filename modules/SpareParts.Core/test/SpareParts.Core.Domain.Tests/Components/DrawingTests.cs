using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Shouldly;
using SpareParts.Core.DomainServices.Components;
using SpareParts.Core.DomainServices.Resources;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Resources;
using SpareParts.Core.Enums;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Volo.Abp.Content;
using Volo.Abp.Data;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;
using Volo.Abp.VirtualFileSystem;
using Xunit;

namespace SpareParts.Core.Components;

public abstract class DrawingTests<TStartupModule> : CoreDomainTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    private readonly ComponentDomainService _componentDomainService;
    private readonly IRepository<Component> _assemblyRepository;
    private readonly IRepository<Drawing, Guid> _drawingRepository;
    private readonly FileManager _fileManager;
    private readonly ImageManager _imageManager;
    private readonly IDataSeeder _dataSeeder;
    private readonly IVirtualFileProvider _virtualFileProvider;
    private readonly DrawingDomainService _drawingDomainService;

    protected DrawingTests()
    {
        _assemblyRepository = ServiceProvider.GetRequiredService<IRepository<Component>>();
        _componentDomainService = ServiceProvider.GetRequiredService<ComponentDomainService>();
        _drawingRepository = ServiceProvider.GetRequiredService<IRepository<Drawing, Guid>>();
        _fileManager = ServiceProvider.GetRequiredService<FileManager>();
        _imageManager = ServiceProvider.GetRequiredService<ImageManager>();
        _dataSeeder = ServiceProvider.GetRequiredService<IDataSeeder>();
        _virtualFileProvider = ServiceProvider.GetRequiredService<IVirtualFileProvider>();
        _drawingDomainService = ServiceProvider.GetRequiredService<DrawingDomainService>();
    }

    [Fact]
    public async Task Should_Initialize_Properties()
    {
        Guid tenantId = Guid.NewGuid();
        await _dataSeeder.SeedAsync(tenantId);
        using (CurrentTenant.Change(tenantId))
        {
            // Arrange
            const string index = "testIndex";

            Component assembly = null!;
            Resource file = null!;
            Resource image = null!;
            await WithUnitOfWorkAsync(async () =>
             {
                 assembly = await _componentDomainService.CreateAsync("code", [new ComponentTranslation("en", "label")], ComponentType.Assembly);
                 await _assemblyRepository.InsertAsync(assembly);

                 IFileInfo imageFile = _virtualFileProvider.GetFileInfo("/Files/default-image.png");
                 await using Stream imageStream = imageFile.CreateReadStream();
                 IFileInfo pdfFile = _virtualFileProvider.GetFileInfo("/Files/21841215.pdf");
                 await using Stream pdfStream = pdfFile.CreateReadStream();
                 file = await _fileManager.CreateAsync(new RemoteStreamContent(pdfStream, "asa.pdf"));
                 image = await _imageManager.CreateAsync(new RemoteStreamContent(imageStream, "asa.png"));
             });

            // Act
            Drawing drawing = null!;
            await WithUnitOfWorkAsync(async () =>
             {
                 drawing = _drawingDomainService.Create(assembly.Id, 1, file, image,
                     new Dictionary<string, Guid> { { index, assembly.Id } }, DrawingOrigin.Online);
                 drawing.IsVisible = true;
                 await _drawingDomainService.SetAsMainAsync(drawing);
                 drawing = await _drawingRepository.InsertAsync(drawing);
             });


            // Assert
            drawing.AssemblyId.ShouldBe(assembly.Id);
            drawing.Rank.ShouldBe(1);
            drawing.SourceFileId.ShouldBe(file.Id);
            drawing.SourceFile.Id.ShouldBe(file.Id);
            drawing.Image.Id.ShouldBe(image.Id);
            drawing.IsMain.ShouldBe(true);
            drawing.Origin.ShouldBe(DrawingOrigin.Online);
            drawing.IsVisible.ShouldBe(true);
        }
    }
}