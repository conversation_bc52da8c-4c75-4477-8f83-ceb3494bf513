using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Shouldly;
using SpareParts.Core.DomainServices.Components;
using SpareParts.Core.DomainServices.Resources;
using SpareParts.Core.Entities.Boms;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Resources;
using SpareParts.Core.Enums;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Content;
using Volo.Abp.Data;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;
using Volo.Abp.VirtualFileSystem;
using Xunit;

namespace SpareParts.Core.Components;

public abstract class DrawingMappingDomainServiceTests<TStartupModule> : CoreDomainTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    private readonly DrawingMappingDomainService _drawingMappingDomainService;
    private readonly ComponentDomainService _componentDomainService;
    private readonly BomLineDomainService _bomLineDomainService;
    private readonly IRepository<Component> _componentRepository;
    private readonly IRepository<BomLine> _bomLineRepository;
    private readonly IRepository<Drawing, Guid> _drawingRepository;
    private readonly IRepository<DrawingMapping> _drawingMappingRepository;
    private readonly FileManager _fileManager;
    private readonly ImageManager _imageManager;
    private readonly IDataSeeder _dataSeeder;
    private readonly IVirtualFileProvider _virtualFileProvider;
    private readonly DrawingDomainService _drawingDomainService;

    protected DrawingMappingDomainServiceTests()
    {
        _componentRepository = ServiceProvider.GetRequiredService<IRepository<Component>>();
        _bomLineRepository = ServiceProvider.GetRequiredService<IRepository<BomLine>>();
        _componentDomainService = ServiceProvider.GetRequiredService<ComponentDomainService>();
        _bomLineDomainService = ServiceProvider.GetRequiredService<BomLineDomainService>();
        _drawingMappingDomainService = ServiceProvider.GetRequiredService<DrawingMappingDomainService>();
        _drawingRepository = ServiceProvider.GetRequiredService<IRepository<Drawing, Guid>>();
        _drawingMappingRepository = ServiceProvider.GetRequiredService<IRepository<DrawingMapping>>();
        _fileManager = ServiceProvider.GetRequiredService<FileManager>();
        _imageManager = ServiceProvider.GetRequiredService<ImageManager>();
        _dataSeeder = ServiceProvider.GetRequiredService<IDataSeeder>();
        _virtualFileProvider = ServiceProvider.GetRequiredService<IVirtualFileProvider>();
        _drawingDomainService = ServiceProvider.GetRequiredService<DrawingDomainService>();
    }

    [Fact]
    public async Task Should_Change_Index()
    {
        Guid tenantId = Guid.NewGuid();
        await _dataSeeder.SeedAsync(tenantId);

        using (CurrentTenant.Change(tenantId))
        {
            // Arrange
            const string index = "testIndex";
            const string newIndex = "newIndex";

            Guid drawing1Id = Guid.Empty;
            Guid partId = Guid.Empty;
            await WithUnitOfWorkAsync(async () =>
            {
                Component assembly = await _componentDomainService.CreateAsync("code",
                    [new ComponentTranslation("en", "label")], ComponentType.Assembly);
                await _componentRepository.InsertAsync(assembly);

                Component part = await _componentDomainService.CreateAsync("part",
                    [new ComponentTranslation("en", "label")], ComponentType.Part);
                await _componentRepository.InsertAsync(part);
                partId = part.Id;
                BomLine bomLine = await _bomLineDomainService.CreateAsync(assembly.Id, partId, 1, 1);
                await _bomLineRepository.InsertAsync(bomLine);

                IFileInfo imageFile = _virtualFileProvider.GetFileInfo("/Files/default-image.png");
                await using Stream imageStream = imageFile.CreateReadStream();
                IFileInfo pdfFile = _virtualFileProvider.GetFileInfo("/Files/21841215.pdf");
                await using Stream pdfStream = pdfFile.CreateReadStream();
                Resource file = await _fileManager.CreateAsync(new RemoteStreamContent(pdfStream, "asa.pdf"));
                Resource image = await _imageManager.CreateAsync(new RemoteStreamContent(imageStream, "asa.png"));

                Drawing drawing1 = _drawingDomainService.Create(assembly.Id, 1, file, image,
                    new Dictionary<string, Guid> { { index, partId } }, DrawingOrigin.Online);

                drawing1.IsVisible = true;
                drawing1 = await _drawingRepository.InsertAsync(drawing1);
                drawing1Id = drawing1.Id;
            });

            await WithUnitOfWorkAsync(async () =>
            {
                // Act
                DrawingMapping drawingMapping = await _drawingMappingRepository.GetAsync(x =>
                    x.DrawingId == drawing1Id && x.ComponentId == partId);

                await _drawingMappingDomainService.ChangeIndexAsync(drawingMapping, newIndex);

                // Assert
                drawingMapping.Index.ShouldBe(newIndex);
            });
        }
    }

    [Fact]
    public async Task ChangeIndex_Should_Throw_Exception_If_Index_Already_In_Use()
    {
        Guid tenantId = Guid.NewGuid();
        await _dataSeeder.SeedAsync(tenantId);

        using (CurrentTenant.Change(tenantId))
        {
            // Arrange
            const string index = "testIndex";

            Guid drawing1Id = Guid.Empty;
            Guid part2Id = Guid.Empty;
            await WithUnitOfWorkAsync(async () =>
            {
                Component assembly = await _componentDomainService.CreateAsync("code1",
                    [new ComponentTranslation("en", "label")], ComponentType.Assembly);
                await _componentRepository.InsertAsync(assembly);

                Component part1 = await _componentDomainService.CreateAsync("part1",
                    [new ComponentTranslation("en", "label")], ComponentType.Part);
                await _componentRepository.InsertAsync(part1);
                BomLine bomLine1 = await _bomLineDomainService.CreateAsync(assembly.Id, part1.Id, 1, 1);
                await _bomLineRepository.InsertAsync(bomLine1);

                Component part2 = await _componentDomainService.CreateAsync("part2",
      [new ComponentTranslation("en", "label")], ComponentType.Assembly);
                await _componentRepository.InsertAsync(part2);
                part2Id = part2.Id;
                BomLine bomLine2 = await _bomLineDomainService.CreateAsync(assembly.Id, part2.Id, 1, 1);
                await _bomLineRepository.InsertAsync(bomLine2);

                IFileInfo imageFile = _virtualFileProvider.GetFileInfo("/Files/default-image.png");
                await using Stream imageStream = imageFile.CreateReadStream();
                IFileInfo pdfFile = _virtualFileProvider.GetFileInfo("/Files/21841215.pdf");
                await using Stream pdfStream = pdfFile.CreateReadStream();
                Resource file = await _fileManager.CreateAsync(new RemoteStreamContent(pdfStream, "asa.pdf"));
                Resource image = await _imageManager.CreateAsync(new RemoteStreamContent(imageStream, "asa.png"));

                Drawing drawing1 = _drawingDomainService.Create(assembly.Id, 1, file, image,
                    new Dictionary<string, Guid> { { index, part1.Id }, { "1", part2Id } }, DrawingOrigin.Online);
                drawing1.IsVisible = true;
                drawing1 = await _drawingRepository.InsertAsync(drawing1);
                drawing1Id = drawing1.Id;
            });

            await WithUnitOfWorkAsync(async () =>
            {
                DrawingMapping drawingMapping = await _drawingMappingRepository.GetAsync(x =>
                    x.DrawingId == drawing1Id && x.ComponentId == part2Id);

                //Act & Assert
                Should.Throw<BusinessException>(() => _drawingMappingDomainService.ChangeIndexAsync(drawingMapping, index));
            });

        }
    }

    [Fact]
    public async Task Should_Change_Component()
    {
        Guid tenantId = Guid.NewGuid();
        await _dataSeeder.SeedAsync(tenantId);

        using (CurrentTenant.Change(tenantId))
        {
            // Arrange
            Guid assemblyId = Guid.Empty;
            Guid drawing1Id = Guid.Empty;
            Guid part2Id = Guid.Empty;
            await WithUnitOfWorkAsync(async () =>
            {
                Component assembly = await _componentDomainService.CreateAsync("code",
                    [new ComponentTranslation("en", "label")], ComponentType.Assembly);
                await _componentRepository.InsertAsync(assembly);
                assemblyId = assembly.Id;

                Component part1 = await _componentDomainService.CreateAsync("part1",
                    [new ComponentTranslation("en", "label")], ComponentType.Part);
                await _componentRepository.InsertAsync(part1);
                BomLine bomLine1 = await _bomLineDomainService.CreateAsync(assembly.Id, part1.Id, 1, 1);
                await _bomLineRepository.InsertAsync(bomLine1);

                Component part2 = await _componentDomainService.CreateAsync("part2",
                    [new ComponentTranslation("en", "label")], ComponentType.Part);
                await _componentRepository.InsertAsync(part2);
                part2Id = part2.Id;
                BomLine bomLine2 = await _bomLineDomainService.CreateAsync(assembly.Id, part2.Id, 1, 1);
                await _bomLineRepository.InsertAsync(bomLine2);

                IFileInfo imageFile = _virtualFileProvider.GetFileInfo("/Files/default-image.png");
                await using Stream imageStream = imageFile.CreateReadStream();
                IFileInfo pdfFile = _virtualFileProvider.GetFileInfo("/Files/21841215.pdf");
                await using Stream pdfStream = pdfFile.CreateReadStream();
                Resource file = await _fileManager.CreateAsync(new RemoteStreamContent(pdfStream, "asa.pdf"));
                Resource image = await _imageManager.CreateAsync(new RemoteStreamContent(imageStream, "asa.png"));

                Drawing drawing = _drawingDomainService.Create(assembly.Id, 1, file, image,
                    new Dictionary<string, Guid> { { "testIndex1", part1.Id } }, DrawingOrigin.Online);
                drawing.IsVisible = true;
                drawing = await _drawingRepository.InsertAsync(drawing);
                drawing1Id = drawing.Id;
            });

            await WithUnitOfWorkAsync(async () =>
            {
                // Act
                DrawingMapping drawingMapping = await _drawingMappingRepository.GetAsync(x =>
                    x.DrawingId == drawing1Id && x.Index == "testIndex1");

                await _drawingMappingDomainService.ChangeComponentIdAsync(assemblyId, drawingMapping, part2Id);

                // Assert
                drawingMapping.ComponentId.ShouldBe(part2Id);
            });
        }
    }

    [Fact]
    public async Task ChangeComponent_Should_Throw_Exception_If_Component_Already_In_Use()
    {
        Guid tenantId = Guid.NewGuid();
        await _dataSeeder.SeedAsync(tenantId);

        using (CurrentTenant.Change(tenantId))
        {
            // Arrange
            Guid drawing1Id = Guid.Empty;
            Guid part2Id = Guid.Empty;
            Guid assemblyId = Guid.Empty;
            await WithUnitOfWorkAsync(async () =>
            {
                Component assembly = await _componentDomainService.CreateAsync("code",
                    [new ComponentTranslation("en", "label")], ComponentType.Assembly);
                await _componentRepository.InsertAsync(assembly);
                assemblyId = assembly.Id;

                Component part1 = await _componentDomainService.CreateAsync("part1",
                    [new ComponentTranslation("en", "label")], ComponentType.Part);
                await _componentRepository.InsertAsync(part1);
                BomLine bomLine1 = await _bomLineDomainService.CreateAsync(assembly.Id, part1.Id, 1, 1);
                await _bomLineRepository.InsertAsync(bomLine1);

                Component part2 = await _componentDomainService.CreateAsync("part2",
                    [new ComponentTranslation("en", "label")], ComponentType.Part);
                await _componentRepository.InsertAsync(part2);
                part2Id = part2.Id;
                BomLine bomLine2 = await _bomLineDomainService.CreateAsync(assembly.Id, part2.Id, 1, 1);
                await _bomLineRepository.InsertAsync(bomLine2);

                IFileInfo imageFile = _virtualFileProvider.GetFileInfo("/Files/default-image.png");
                await using Stream imageStream = imageFile.CreateReadStream();
                IFileInfo pdfFile = _virtualFileProvider.GetFileInfo("/Files/21841215.pdf");
                await using Stream pdfStream = pdfFile.CreateReadStream();
                Resource file = await _fileManager.CreateAsync(new RemoteStreamContent(pdfStream, "asa.pdf"));
                Resource image = await _imageManager.CreateAsync(new RemoteStreamContent(imageStream, "asa.png"));

                Drawing drawing = _drawingDomainService.Create(assembly.Id, 1, file, image,
                    new Dictionary<string, Guid> { { "testIndex1", part1.Id }, { "testIndex2", part2.Id } }, DrawingOrigin.Online);
                drawing.IsVisible = true;
                drawing = await _drawingRepository.InsertAsync(drawing);
                drawing1Id = drawing.Id;
            });

            await WithUnitOfWorkAsync(async () =>
            {
                DrawingMapping componentDrawingIndex = await _drawingMappingRepository.GetAsync(x =>
                    x.DrawingId == drawing1Id && x.Index == "testIndex1");

                //Act & Assert
                Should.Throw<BusinessException>(() => _drawingMappingDomainService.ChangeComponentIdAsync(assemblyId, componentDrawingIndex, part2Id));
            });

        }
    }

    [Fact]
    public async Task ChangeComponent_Should_Throw_Exception_If_Component_Not_In_Bom_Of_Assembly()
    {
        Guid tenantId = Guid.NewGuid();
        await _dataSeeder.SeedAsync(tenantId);

        using (CurrentTenant.Change(tenantId))
        {
            // Arrange
            Guid drawing1Id = Guid.Empty;
            Guid part2Id = Guid.Empty;
            Guid assemblyId = Guid.Empty;
            await WithUnitOfWorkAsync(async () =>
            {
                Component assembly = await _componentDomainService.CreateAsync("code",
                    [new ComponentTranslation("en", "label")], ComponentType.Assembly);
                await _componentRepository.InsertAsync(assembly);
                assemblyId = assembly.Id;

                Component part1 = await _componentDomainService.CreateAsync("part1",
                    [new ComponentTranslation("en", "label")], ComponentType.Part);
                await _componentRepository.InsertAsync(part1);
                BomLine bomLine1 = await _bomLineDomainService.CreateAsync(assembly.Id, part1.Id, 1, 1);
                await _bomLineRepository.InsertAsync(bomLine1);

                Component part2 = await _componentDomainService.CreateAsync("part2",
                    [new ComponentTranslation("en", "label")], ComponentType.Part);
                await _componentRepository.InsertAsync(part2);
                part2Id = part2.Id;

                IFileInfo imageFile = _virtualFileProvider.GetFileInfo("/Files/default-image.png");
                await using Stream imageStream = imageFile.CreateReadStream();
                IFileInfo pdfFile = _virtualFileProvider.GetFileInfo("/Files/21841215.pdf");
                await using Stream pdfStream = pdfFile.CreateReadStream();
                Resource file = await _fileManager.CreateAsync(new RemoteStreamContent(pdfStream, "asa.pdf"));
                Resource image = await _imageManager.CreateAsync(new RemoteStreamContent(imageStream, "asa.png"));

                Drawing drawing = _drawingDomainService.Create(assembly.Id, 1, file, image,
                    new Dictionary<string, Guid> { { "testIndex1", part1.Id } }, DrawingOrigin.Online);
                drawing.IsVisible = true;
                drawing = await _drawingRepository.InsertAsync(drawing);
                drawing1Id = drawing.Id;
            });

            await WithUnitOfWorkAsync(async () =>
            {
                DrawingMapping componentDrawingIndex = await _drawingMappingRepository.GetAsync(x =>
                    x.DrawingId == drawing1Id && x.Index == "testIndex1");

                //Act & Assert
                Should.Throw<BusinessException>(() => _drawingMappingDomainService.ChangeComponentIdAsync(assemblyId, componentDrawingIndex, part2Id));
            });

        }
    }
}