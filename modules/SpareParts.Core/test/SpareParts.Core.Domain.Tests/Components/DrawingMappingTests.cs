using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using SpareParts.Core.DomainServices.Components;
using SpareParts.Core.DomainServices.Resources;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Resources;
using SpareParts.Core.Enums;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Volo.Abp.Content;
using Volo.Abp.Data;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;
using Volo.Abp.VirtualFileSystem;
using Xunit;

namespace SpareParts.Core.Components;

public abstract class DrawingMappingTests<TStartupModule> : CoreDomainTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    private readonly DrawingDomainService _drawingDomainService;
    private readonly IRepository<Drawing, Guid> _drawingRepo;
    private readonly FileManager _fileManager;
    private readonly ImageManager _imageManager;
    private readonly IDataSeeder _dataSeeder;
    private readonly IVirtualFileProvider _virtualFileProvider;
    private ComponentDomainService ComponentDomainService => ServiceProvider.GetRequiredService<ComponentDomainService>();
    private IRepository<Component> ComponentRepository => ServiceProvider.GetRequiredService<IRepository<Component>>();

    protected DrawingMappingTests()
    {
        _drawingDomainService = ServiceProvider.GetRequiredService<DrawingDomainService>();
        _drawingRepo = ServiceProvider.GetRequiredService<IRepository<Drawing, Guid>>();
        _fileManager = ServiceProvider.GetRequiredService<FileManager>();
        _imageManager = ServiceProvider.GetRequiredService<ImageManager>();
        _dataSeeder = ServiceProvider.GetRequiredService<IDataSeeder>();
        _virtualFileProvider = ServiceProvider.GetRequiredService<IVirtualFileProvider>();
    }

    [Fact]
    public async Task Constructor_ShouldInitializeProperties()
    {
        Guid tenantId = Guid.NewGuid();
        await _dataSeeder.SeedAsync(tenantId);
        using (CurrentTenant.Change(tenantId))
        {
            const string index = "testIndex";

            Component assembly = null!;
            await WithUnitOfWorkAsync(async () =>
            {
                assembly = await ComponentDomainService.CreateAsync("code", [new ComponentTranslation("en", "label")], ComponentType.Assembly);
                await ComponentRepository.InsertAsync(assembly);
            });

            IFileInfo imageFile = _virtualFileProvider.GetFileInfo("/Files/default-image.png");
            await using Stream imageStream = imageFile.CreateReadStream();
            IFileInfo pdfFile = _virtualFileProvider.GetFileInfo("/Files/21841215.pdf");
            await using Stream pdfStream = pdfFile.CreateReadStream();
            Resource file = await _fileManager.CreateAsync(new RemoteStreamContent(pdfStream, "asa.pdf"));
            Resource image = await _imageManager.CreateAsync(new RemoteStreamContent(imageStream, "asa.png"));
            Drawing drawing = _drawingDomainService.Create(assembly.Id, 1, file, image,
                new Dictionary<string, Guid> { { index, assembly.Id } }, DrawingOrigin.Online);
            drawing = await _drawingRepo.InsertAsync(drawing, true);

            drawing = await _drawingRepo.GetAsync(drawing.Id);
            DrawingMapping drawingMapping = drawing.DrawingMappings[0];

            Assert.Equal(assembly.Id, drawingMapping.ComponentId);
            Assert.Equal(drawing.Id, drawingMapping.DrawingId);
            Assert.Equal(index, drawingMapping.Index);
        }
    }
}