using Bogus;
using Bogus.Extensions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Shouldly;
using SpareParts.Core.DomainServices.Resources;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Volo.Abp.Content;
using Volo.Abp.Data;
using Volo.Abp.Modularity;
using Volo.Abp.VirtualFileSystem;
using Xunit;
using SpareParts.Core.Entities.Resources;

namespace SpareParts.Core.Resources;

public abstract class DocumentTests<TStartupModule> : CoreDomainTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    private readonly Faker _faker;
    private readonly ImageManager _imageManager;
    private readonly IDataSeeder _dataSeeder;
    private readonly DocumentDomainService _documentDomainService;
    private readonly IVirtualFileProvider _virtualFileProvider;

    protected DocumentTests()
    {
        _faker = new Faker();
        _imageManager = ServiceProvider.GetRequiredService<ImageManager>();
        _dataSeeder = ServiceProvider.GetRequiredService<IDataSeeder>();
        _documentDomainService = ServiceProvider.GetRequiredService<DocumentDomainService>();
        _virtualFileProvider = ServiceProvider.GetRequiredService<IVirtualFileProvider>();
    }

    [Fact]
    public async Task Link_Instantiation_Should_Succeed()
    {
        // Arrange
        Guid tenantId = GuidGenerator.Create();
        using (CurrentTenant.Change(tenantId))
        {
            string url = _faker.Internet.Url();
            List<DocumentTranslation> documentTranslations = [new(_faker.Locale, _faker.Lorem.Word().ClampLength(2), _faker.Lorem.Word())];

            // Act 
            Document document = await _documentDomainService.CreateAsync(url, documentTranslations, []);

            //Assert
            document.Type.ShouldBe(Enums.DocumentType.Link);
            document.Resource.ShouldBeNull();
            document.Link.ShouldBe(url);
            document.Translations.ShouldNotBeNull();
        }
    }

    [Fact]
    public async Task Link_Instantiation_Should_Throw_Exception_If_Url_Is_Incorrect()
    {
        // Arrange
        Guid tenantId = GuidGenerator.Create();
        using (CurrentTenant.Change(tenantId))
        {
            string url = "htp:incorrectUrl";
            List<DocumentTranslation> documentTranslations = [new(_faker.Locale, _faker.Lorem.Word().ClampLength(2), _faker.Lorem.Word())];

            // Act & Assert
            await Should.ThrowAsync<ArgumentException>(async () => await _documentDomainService.CreateAsync(url, documentTranslations, []));
        }
    }

    [Fact]
    public async Task Link_Instantiation_Should_Throw_NullReferenceException_If_Translation_Is_Null()
    {
        // Arrange
        Guid tenantId = GuidGenerator.Create();
        using (CurrentTenant.Change(tenantId))
        {
            string url = _faker.Internet.Url();

            // Act & Assert
            await Should.ThrowAsync<ArgumentNullException>(async () => await _documentDomainService.CreateAsync(url, null!, []));
        }
    }

    [Fact]
    public async Task Resource_Instantiation_Should_Succeed()
    {
        // Arrange
        Guid tenantId = GuidGenerator.Create();
        await WithUnitOfWorkAsync(async () =>
        {
            await _dataSeeder.SeedAsync(new DataSeedContext(tenantId));
        });
        using (CurrentTenant.Change(tenantId))
        {
            IFileInfo imageFile = _virtualFileProvider.GetFileInfo("/Files/default-image.png");
            await using Stream imageStream = imageFile.CreateReadStream();
            const string fileName = "myImageName.png";
            Resource resource = await _imageManager.CreateAsync(new RemoteStreamContent(imageStream, fileName));
            List<DocumentTranslation> documentTranslations = [new(_faker.Locale, _faker.Lorem.Word().ClampLength(2), _faker.Lorem.Word())];

            // Act 
            Document document = await _documentDomainService.CreateAsync(resource, documentTranslations, []);

            //Assert
            document.Type.ShouldBe(Enums.DocumentType.Resource);
            document.Resource.ShouldNotBeNull();
            document.Link.ShouldBeNull();
            document.Translations.ShouldNotBeNull();
        }
    }

    [Fact]
    public async Task Resource_Instantiation_Should_Throw_Exception_If_Resource_Is_Null()
    {
        // Arrange
        Guid tenantId = GuidGenerator.Create();
        using (CurrentTenant.Change(tenantId))
        {
            Resource resource = default!;
            List<DocumentTranslation> documentTranslations = [new(_faker.Locale, _faker.Lorem.Word().ClampLength(2), _faker.Lorem.Word())];

            // Act & Assert
            await Should.ThrowAsync<ArgumentException>(async () => await _documentDomainService.CreateAsync(resource, documentTranslations, []));
        }
    }

    [Fact]
    public async Task Resource_Instantiation_Should_Throw_NullReferenceException_If_Translation_Is_Null()
    {
        // Arrange
        Guid tenantId = GuidGenerator.Create();
        await WithUnitOfWorkAsync(async () =>
        {
            await _dataSeeder.SeedAsync(new DataSeedContext(tenantId));
        });
        using (CurrentTenant.Change(tenantId))
        {
            IFileInfo imageFile = _virtualFileProvider.GetFileInfo("/Files/default-image.png");
            await using Stream imageStream = imageFile.CreateReadStream();
            const string fileName = "myImageName.png";
            Resource resource = await _imageManager.CreateAsync(new RemoteStreamContent(imageStream, fileName));

            // Act & Assert
            await Should.ThrowAsync<ArgumentNullException>(async () => await _documentDomainService.CreateAsync(resource, null!, []));
        }
    }

    [Fact]
    public async Task Link_Update_Should_Throw_Exception_If_Url_Is_Incorrect()
    {
        // Arrange
        Guid tenantId = GuidGenerator.Create();
        using (CurrentTenant.Change(tenantId))
        {
            Document document = await _documentDomainService.CreateAsync(_faker.Internet.Url(),
                [new(_faker.Locale, _faker.Lorem.Word().ClampLength(2), _faker.Lorem.Word())], []);

            string url = "htp:incorrectUrl";

            // Act & Assert
            Should.Throw<ArgumentException>(() => document.ChangeLink(url));
        }
    }

    [Fact]
    public async Task Link_Update_Should_Throw_Exception_If_Resource_Is_Null()
    {
        // Arrange
        Guid tenantId = GuidGenerator.Create();
        using (CurrentTenant.Change(tenantId))
        {
            Document document = await _documentDomainService.CreateAsync(_faker.Internet.Url(),
                [new(_faker.Locale, _faker.Lorem.Word().ClampLength(2), _faker.Lorem.Word())], []);

            // Act & Assert
            Should.Throw<ArgumentException>(() => document.ChangeLink(string.Empty));
        }
    }

}