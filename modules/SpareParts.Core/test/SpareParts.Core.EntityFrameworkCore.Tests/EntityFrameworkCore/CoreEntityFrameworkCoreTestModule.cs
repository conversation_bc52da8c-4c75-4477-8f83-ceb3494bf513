using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.Extensions.DependencyInjection;
using System.Runtime.CompilerServices;
using Volo.Abp.Auditing;
using Volo.Abp.BlobStoring;
using Volo.Abp.BlobStoring.Azure;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore.SqlServer;
using Volo.Abp.Modularity;
using Volo.Abp.PermissionManagement;
using Volo.Abp.SettingManagement;
using Volo.Abp.VirtualFileSystem;
using static SpareParts.Common.SqlScriptProvider;

namespace SpareParts.Core.EntityFrameworkCore;

[DependsOn(
    typeof(CoreApplicationTestModule),
    typeof(CoreEntityFrameworkCoreModule),
    typeof(AbpEntityFrameworkCoreSqlServerModule),
    typeof(AbpBlobStoringAzureModule)
)]
public class CoreEntityFrameworkCoreTestModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        Configure<SettingManagementOptions>(options =>
        {
            options.IsDynamicSettingStoreEnabled = false;
            options.SaveStaticSettingsToDatabase = false;
        });
        Configure<PermissionManagementOptions>(options =>
        {
            options.SaveStaticPermissionsToDatabase = false;
            options.IsDynamicPermissionStoreEnabled = false;
        });
        Configure<AbpAuditingOptions>(opts =>
        {
            opts.IsEnabled = false;
        });

        Configure<AbpBlobStoringOptions>(options =>
        {
            options.Containers.ConfigureDefault(container =>
            {
                container.UseAzure(azure =>
                {
                    azure.ConnectionString = "DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://127.0.0.1:10000/devstoreaccount1;";
                    azure.CreateContainerIfNotExists = true;
                });
            });
        });
        ConfigureMsSqlDatabase(context.Services);
        Configure<AbpVirtualFileSystemOptions>(options =>
        {
            options.FileSets.AddEmbedded<CoreEntityFrameworkCoreTestModule>("SpareParts.Core");
        });

    }

    private static void ConfigureMsSqlDatabase(IServiceCollection services)
    {
        string connectionString = DataBaseFixture.ConnectionString;
        using (CoreDbContext context = new(new DbContextOptionsBuilder<CoreDbContext>()
                   .UseSqlServer(connectionString)
                   .Options))
        {
            IRelationalDatabaseCreator relationalDatabaseCreator = context.GetService<IRelationalDatabaseCreator>();
            if (!relationalDatabaseCreator.HasTables())
            {
                relationalDatabaseCreator.CreateTables();
                ExecuteSqlScript(context, ScriptName.ProductFlattenHierarchySqlView);
                ExecuteSqlScript(context, ScriptName.ResourceVisibilitySqlView);
                ExecuteSqlScript(context, ScriptName.ProductFamilyFlattenHierarchySqlView);
                ExecuteSqlScript(context, ScriptName.BomPathsSqlView);
                ExecuteSqlScript(context, ScriptName.BeforeDeleteFromComponentsTrigger);
                ExecuteSqlScript(context, ScriptName.BeforeDeleteFromDrawingsTrigger);
                ExecuteSqlScript(context, ScriptName.InsertIntoBomHierarchyHistoryProcedure);
                ExecuteSqlScript(context, ScriptName.BeforeDeleteFromProductFamiliesTrigger);
            }
        }

        services.Configure<AbpDbContextOptions>(options =>
        {
            options.Configure(context =>
            {
                context.DbContextOptions.UseSqlServer(connectionString);
            });
        });
    }

    private static void ExecuteSqlScript(CoreDbContext context, ScriptName scriptName)
    {
        context.Database.ExecuteSql(FormattableStringFactory.Create(GetSqlQuery(scriptName.ToString())));
    }
}