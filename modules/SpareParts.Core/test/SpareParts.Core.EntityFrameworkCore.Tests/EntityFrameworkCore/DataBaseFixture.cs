using System.Threading.Tasks;
using Testcontainers.MsSql;
using Xunit;

namespace SpareParts.Core.EntityFrameworkCore;

public class DataBaseFixture : IAsyncLifetime
{
    private static readonly MsSqlContainer SQLEdgeContainer = new MsSqlBuilder()
        .WithName("core-module-test-container")
        .WithPassword("1ntergr@tionTests")
        .Build();

    public static string ConnectionString => SQLEdgeContainer.GetConnectionString();

    public Task InitializeAsync() => SQLEdgeContainer.StartAsync();

    /// <summary>
    /// No disposing action needed
    /// Resource Reaper will take care of the remaining Docker resources and removes them.
    /// </summary>
    /// <returns></returns>
    public Task DisposeAsync() => Task.CompletedTask;
}