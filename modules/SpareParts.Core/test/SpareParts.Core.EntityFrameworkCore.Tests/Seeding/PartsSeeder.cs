using SpareParts.Core.Entities.Components;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Volo.Abp.Content;
using Volo.Abp.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using SpareParts.Core.Entities.Resources;
using System;
using SpareParts.Core.Enums;

namespace SpareParts.Core.Seeding;

public class PartsSeeder : SeederBase, ITransientDependency
{
    public async Task Seed(CoreTestsSeedingDataStore storeContext, SeedingContext seedingContext)
    {
        List<Component> parts = [];
        for (int i = 1; i <= storeContext.PartCount; i++)
        {
            IFileInfo imageFile = VirtualFileProvider.GetFileInfo("/Files/default-image.png");
            await using Stream imageStream = imageFile.CreateReadStream();
            Resource partResource = await ImageManager.CreateAsync(new RemoteStreamContent(imageStream, Faker.System.CommonFileName(CoreTestConsts.ImageExtension)));
            ComponentTranslation partTranslation = new(Faker.Locale, Faker.Commerce.Product() + Guid.NewGuid().ToString("N"));
            string partCode = storeContext.PartCodePrefix + i;
            Component part = await ComponentDomainService.CreateAsync(partCode, [partTranslation], ComponentType.Part, partResource);
            parts.Add(part);
        }
        await ComponentRepository.InsertManyAsync(parts, true);
        seedingContext.Parts.AddRange(parts);
    }
}