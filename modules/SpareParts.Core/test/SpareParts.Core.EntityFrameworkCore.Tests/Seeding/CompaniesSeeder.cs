using Bogus.Extensions.UnitedStates;
using SpareParts.Common.Companies;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;

namespace SpareParts.Core.Seeding;

public class CompaniesSeeder : SeederBase, ITransientDependency
{
    private CompanyDomainService CompanyDomainService => LazyServiceProvider.LazyGetRequiredService<CompanyDomainService>();
    private IRepository<Company, Guid> CompanyRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Company, Guid>>();

    public async Task Seed(CoreTestsSeedingDataStore storeContext, SeedingContext seedingContext)
    {
        seedingContext.Companies.AddRange(await InsertManyCompaniesAsync(storeContext));
    }

    private async Task<List<Company>> InsertManyCompaniesAsync(CoreTestsSeedingDataStore storeContext)
    {
        List<Company> companies = [];
        for (int i = 1; i <= storeContext.MaxCompanies; i++)
        {
            bool isInternal = i % 2 == 0;
            Guid companyId = GuidGenerator.Create();
            CompanyType companyType = isInternal ? CompanyType.Internal : CompanyType.External;
            Company company = await CompanyDomainService.CreateAsync(companyId, Faker.Company.Ein(), Faker.Company.CompanyName(), Faker.Company.CompanyName(), companyType);
            companies.Add(company);
            if (isInternal)
            {
                storeContext.CompaniesInternal.Add(company.Id);
            }
            else
            {
                storeContext.CompaniesExternal.Add(company.Id);
            }
        }
        await CompanyRepository.InsertManyAsync(companies, true);
        return companies;
    }

}