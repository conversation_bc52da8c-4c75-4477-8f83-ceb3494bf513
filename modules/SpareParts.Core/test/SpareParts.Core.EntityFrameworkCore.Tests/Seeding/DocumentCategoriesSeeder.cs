using SpareParts.Core.DomainServices.DocumentCategories;
using SpareParts.Core.Entities.Resources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;

namespace SpareParts.Core.Seeding;

public class DocumentCategoriesSeeder : SeederBase, ITransientDependency
{
    private IRepository<DocumentCategory, Guid> DocumentCategoryRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<DocumentCategory, Guid>>();
    private DocumentCategoryDomainService DocumentCategoryDomainService => LazyServiceProvider.LazyGetRequiredService<DocumentCategoryDomainService>();

    public async Task Seed(CoreTestsSeedingDataStore storeContext)
    {
        List<DocumentCategory> documentCategories = [];
        for (int i = 0; i < storeContext.MaxDocumentCategories; i++)
        {
            documentCategories.Add(await CreateDocumentCategories(i));
        }
        storeContext.DocumentCategories.AddRange(documentCategories.Select(x => x.Id));
        await DocumentCategoryRepository.InsertManyAsync(documentCategories);
    }

    private async Task<DocumentCategory> CreateDocumentCategories(int i)
    {
        int colorValue = (i + 1) & 0xFFFFFF;
        string newHexColor = $"#{colorValue:X6}";

        DocumentCategoryTranslation translation = new(Faker.Locale, Faker.Commerce.Product() + Guid.NewGuid().ToString("N"));
        if (i % 2 == 0)
        {
            DocumentCategoryTranslation translationFr = new("fr", Faker.Commerce.Product() + Guid.NewGuid().ToString("N"));
            return await DocumentCategoryDomainService.CreateAsync(newHexColor, [translation, translationFr]);
        }
        return await DocumentCategoryDomainService.CreateAsync(newHexColor, [translation]);
    }
}