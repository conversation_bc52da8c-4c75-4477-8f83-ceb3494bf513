using SpareParts.Core.Entities.Components;
using System.Collections.Generic;
using System.Threading.Tasks;
using SpareParts.Core.Enums;
using Volo.Abp.DependencyInjection;

namespace SpareParts.Core.Seeding;

public class AssembliesSeeder : SeederBase, ITransientDependency
{
    public async Task Seed(CoreTestsSeedingDataStore storeContext, SeedingContext seedingContext)
    {

        List<Component> assemblies = [];
        for (int i = 1; i <= storeContext.AssemblyCount; i++)
        {
            string assemblyLabel = storeContext.AssemblyLabelPrefix + i;
            ComponentTranslation assemblyTranslation = new(Faker.Locale, assemblyLabel);
            string assemblyOnlineLabel = storeContext.OnlineAssemblyLabelPrefix + i;
            ComponentOnlineTranslation assemblyOnlineTranslation = new(Faker.Locale, assemblyOnlineLabel);
            string assemblyCode = storeContext.AssemblyCodePrefix + i;
            Component assembly = await ComponentDomainService.CreateAsync(assemblyCode, [assemblyTranslation], ComponentType.Assembly);
            assembly.AddOnlineTranslation(assemblyOnlineTranslation);
            assemblies.Add(assembly);
            storeContext.AssemblyIdByCodes.Add(assemblyCode, assembly.Id);
        }
        await ComponentRepository.InsertManyAsync(assemblies, true);
        seedingContext.Assemblies.AddRange(assemblies);
    }
}