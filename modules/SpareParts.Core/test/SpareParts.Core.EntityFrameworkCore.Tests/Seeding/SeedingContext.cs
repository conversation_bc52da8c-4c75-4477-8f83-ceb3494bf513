using SpareParts.Common.Companies;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.ProductFamilies;
using System.Collections.Generic;

namespace SpareParts.Core.Seeding;

public class SeedingContext
{
    public List<ProductFamily> ProductFamiliesWithProducts { get; set; } = [];
    public List<Product> Products { get; set; } = [];
    public List<Product> ProductsWithoutEquipment { get; set; } = [];
    public List<Product> ProductsWithEquipment { get; set; } = [];
    public List<Component> Assemblies { get; set; } = [];
    public List<Component> Parts { get; set; } = [];
    public List<Company> Companies { get; set; } = [];
}