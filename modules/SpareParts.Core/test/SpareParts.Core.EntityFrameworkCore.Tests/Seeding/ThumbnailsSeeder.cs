using Microsoft.Extensions.FileProviders;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Resources;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Volo.Abp.Content;
using Volo.Abp.DependencyInjection;

namespace SpareParts.Core.Seeding;

public class ThumbnailsSeeder : SeederBase, ITransientDependency
{
    public async Task SeedForProducts(CoreTestsSeedingDataStore storeContext, SeedingContext seedingContext)
    {
        List<Product> products = await ProductRepository.GetListAsync();
        foreach (Product product in products)
        {
            IFileInfo image = VirtualFileProvider.GetFileInfo("/Files/default-image.png");
            await using Stream stream = image.CreateReadStream();
            Resource assemblyResource = await ImageManager.CreateAsync(new RemoteStreamContent(stream, Faker.System.CommonFileName(CoreTestConsts.ImageExtension)));
            product.Component.ChangeImage(assemblyResource);
        }
        await ProductRepository.UpdateManyAsync(products, true);
    }
}