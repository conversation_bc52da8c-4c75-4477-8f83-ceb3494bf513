using Microsoft.Extensions.FileProviders;
using SpareParts.Core.DomainServices.ProductFamilies;
using SpareParts.Core.Entities.ProductFamilies;
using SpareParts.Core.Entities.Resources;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Volo.Abp.Content;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;

namespace SpareParts.Core.Seeding;

public class ProductFamiliesSeeder : SeederBase, ITransientDependency
{
    private IRepository<ProductFamily, Guid> ProductFamilyRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<ProductFamily, Guid>>();
    private ProductFamilyDomainService ProductFamilyDomainService => LazyServiceProvider.LazyGetRequiredService<ProductFamilyDomainService>();

    public async Task Seed(CoreTestsSeedingDataStore storeContext, SeedingContext seedingContext)
    {
        ProductFamily grandParentProductFamily = await CreateProductFamilyAsync(storeContext, 1, true, null, "");
        storeContext.ProductFamilyIdByCodes.Add(grandParentProductFamily.Code, grandParentProductFamily.Id);
        storeContext.ProductFamilyWithChildIdByCodes.Add(grandParentProductFamily.Code, grandParentProductFamily.Id);
        storeContext.RootProductFamilyIdByCodes.Add(grandParentProductFamily.Code, grandParentProductFamily.Id);
        storeContext.ProductFamilyHierarchy.Add(grandParentProductFamily.Id, []);
        await ProductFamilyRepository.InsertAsync(grandParentProductFamily, true);

        List<ProductFamily> productFamilies = [];
        for (int i = 0; i < 3; i++)
        {
            ProductFamily childProductFamily = await CreateProductFamilyAsync(storeContext, i + 1, true, grandParentProductFamily.Id, "-1");
            seedingContext.ProductFamiliesWithProducts.Add(childProductFamily);
            storeContext.ProductFamilyIdByCodes.Add(childProductFamily.Code, childProductFamily.Id);
            storeContext.EdgeProductFamilyIdByCodes.Add(childProductFamily.Code, childProductFamily.Id);
            storeContext.EdgeProductFamilyWithProductIdByCodes.Add(childProductFamily.Code, childProductFamily.Id);
            storeContext.ProductFamilyHierarchy[grandParentProductFamily.Id].Add(childProductFamily.Id);
            productFamilies.Add(childProductFamily);
        }

        ProductFamily parentProductFamily = await CreateProductFamilyAsync(storeContext, 4, true, grandParentProductFamily.Id, "-1");
        storeContext.ProductFamilyIdByCodes.Add(parentProductFamily.Code, parentProductFamily.Id);
        storeContext.ProductFamilyHierarchy[grandParentProductFamily.Id].Add(parentProductFamily.Id);
        storeContext.ProductFamilyHierarchy.Add(parentProductFamily.Id, []);
        await ProductFamilyRepository.InsertAsync(parentProductFamily, true);

        for (int i = 0; i < 2; i++)
        {
            ProductFamily grandChildProductFamily = await CreateProductFamilyAsync(storeContext, i + 1, true, parentProductFamily.Id, "-1-4");
            storeContext.ProductFamilyIdByCodes.Add(grandChildProductFamily.Code, grandChildProductFamily.Id);
            storeContext.EdgeProductFamilyIdByCodes.Add(grandChildProductFamily.Code, grandChildProductFamily.Id);
            storeContext.EdgeProductFamilyWithoutProductIdByCodes.Add(grandChildProductFamily.Code, grandChildProductFamily.Id);
            storeContext.ProductFamilyHierarchy[parentProductFamily.Id].Add(grandChildProductFamily.Id);
            productFamilies.Add(grandChildProductFamily);
        }

        ProductFamily productFamily1 = await CreateProductFamilyAsync(storeContext, 2, true, null, "");
        seedingContext.ProductFamiliesWithProducts.Add(productFamily1);
        storeContext.ProductFamilyIdByCodes.Add(productFamily1.Code, productFamily1.Id);
        storeContext.EdgeProductFamilyIdByCodes.Add(productFamily1.Code, productFamily1.Id);
        storeContext.RootProductFamilyIdByCodes.Add(productFamily1.Code, productFamily1.Id);
        storeContext.EdgeProductFamilyWithProductIdByCodes.Add(productFamily1.Code, productFamily1.Id);
        productFamilies.Add(productFamily1);

        ProductFamily productFamilyWithoutProducts = await CreateProductFamilyAsync(storeContext, 3, true, null, "");
        storeContext.ProductFamilyIdByCodes.Add(productFamilyWithoutProducts.Code, productFamilyWithoutProducts.Id);
        storeContext.EdgeProductFamilyIdByCodes.Add(productFamilyWithoutProducts.Code, productFamilyWithoutProducts.Id);
        storeContext.EdgeProductFamilyWithoutProductIdByCodes.Add(productFamilyWithoutProducts.Code, productFamilyWithoutProducts.Id);
        storeContext.RootProductFamilyIdByCodes.Add(productFamilyWithoutProducts.Code, productFamilyWithoutProducts.Id);
        productFamilies.Add(productFamilyWithoutProducts);

        await ProductFamilyRepository.InsertManyAsync(productFamilies, true);

    }

    private async Task<ProductFamily> CreateProductFamilyAsync(CoreTestsSeedingDataStore storeContext, int rank,
        bool isVisible, Guid? parentId, string parentSuffix)
    {
        Resource resource = await GetResource();
        ProductFamilyTranslation translation = new(Faker.Locale, Faker.Commerce.Product() + Guid.NewGuid().ToString("N"));
        string suffix = parentSuffix + "-" + rank;
        string productFamilyCode = storeContext.ProductFamilyCodePrefix + suffix;
        ProductFamilyTranslation translationFr = new("fr", Faker.Commerce.Product() + Guid.NewGuid().ToString("N"));
        ProductFamily productFamily = await ProductFamilyDomainService.CreateAsync(productFamilyCode, [translation, translationFr], resource, parentId);
        productFamily.IsVisible = isVisible;

        productFamily.ChangeRank(rank);

        return productFamily;
    }

    public async Task<Resource> GetResource()
    {
        IFileInfo image = VirtualFileProvider.GetFileInfo("/Files/default-image.png");
        await using Stream stream = image.CreateReadStream();
        return await ImageManager.CreateAsync(new RemoteStreamContent(stream, Faker.System.CommonFileName(CoreTestConsts.ImageExtension)));
    }
}