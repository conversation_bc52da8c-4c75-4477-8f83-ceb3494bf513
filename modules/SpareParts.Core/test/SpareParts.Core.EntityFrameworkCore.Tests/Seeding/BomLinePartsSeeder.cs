using SpareParts.Core.DomainServices.Components;
using SpareParts.Core.Entities.Boms;
using SpareParts.Core.Entities.Components;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;
namespace SpareParts.Core.Seeding;

public class BomLinePartsSeeder : SeederBase, ITransientDependency
{
    private BomLineDomainService BomLineDomainService => LazyServiceProvider.LazyGetRequiredService<BomLineDomainService>();
    private IRepository<BomLine> BomLineRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<BomLine>>();

    public async Task SeedBomLinePartsForAssemblies(CoreTestsSeedingDataStore storeContext, SeedingContext seedingContext)
    {
        List<Guid> childIds = seedingContext.Parts.Select(a => a.Id).ToList();
        foreach (Component assembly in seedingContext.Assemblies)
        {
            await InsertBomLinesParts(storeContext, assembly.Id, assembly.Code, childIds, 3, storeContext.PartCount);
        }
    }

    public async Task SeedBomLinePartsForProducts(CoreTestsSeedingDataStore storeContext, SeedingContext seedingContext)
    {

        List<Guid> childIds = seedingContext.Parts.Select(a => a.Id).ToList();
        foreach (Product product in seedingContext.Products)
        {
            await InsertBomLinesParts(storeContext, product.Id, product.Component.Code, childIds, 3, storeContext.PartCount);
        }

    }

    public async Task SeedBomLineAssembliesForProducts(CoreTestsSeedingDataStore storeContext, SeedingContext seedingContext)
    {

        List<Guid> childIds = seedingContext.Assemblies.Select(a => a.Id).ToList();
        foreach (Product product in seedingContext.Products)
        {
            await InsertBomLinesParts(storeContext, product.Id, product.Component.Code, childIds, 1,
                storeContext.MaxAssemblyInProduct);
        }
    }

    private async Task InsertBomLinesParts(CoreTestsSeedingDataStore storeContext, Guid parentId, string parentCode, List<Guid> childIds, int min, int max)
    {
        List<BomLine> bomLines = [];
        List<Guid> ids = [.. childIds];
        int randomChild = Random.Next(min, max);

        if (!storeContext.ComponentBomLinesCountByCodes.TryAdd(parentCode, randomChild))
        {
            storeContext.ComponentBomLinesCountByCodes[parentCode] += randomChild;
        }

        for (int i = 0; i < randomChild; i++)
        {
            int randomIndex = Random.Next(0, ids.Count);
            Guid childId = ids[randomIndex];
            ids.RemoveAt(randomIndex);

            BomLine bomLine = await BomLineDomainService.CreateAsync(parentId, childId, 1, 1);
            bomLines.Add(bomLine);
            storeContext.AddComponentIdsByParentIds(bomLine.ParentAssemblyId, bomLine.ChildComponentId);
        }
        await BomLineRepository.InsertManyAsync(bomLines, true);
    }
}