using SpareParts.Core.DataVisibility;
using SpareParts.Core.Seeding;
using System.Threading.Tasks;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.MultiTenancy;

namespace SpareParts.Core;


public class EfCoreTestDataProvider : ITransientDependency, ITestDataProvider
{
    public IAbpLazyServiceProvider LazyServiceProvider { get; set; } = default!;
    private EquipmentsSeeder EquipmentsSeeder => LazyServiceProvider.LazyGetRequiredService<EquipmentsSeeder>();
    private CompaniesSeeder CompaniesSeeder => LazyServiceProvider.LazyGetRequiredService<CompaniesSeeder>();
    private DocumentsSeeder DocumentsSeeder => LazyServiceProvider.LazyGetRequiredService<DocumentsSeeder>();
    private DrawingsSeeder DrawingsSeeder => LazyServiceProvider.LazyGetRequiredService<DrawingsSeeder>();
    private ProductFamiliesSeeder ProductFamiliesSeeder => LazyServiceProvider.LazyGetRequiredService<ProductFamiliesSeeder>();
    private ProductsSeeder ProductsSeeder => LazyServiceProvider.LazyGetRequiredService<ProductsSeeder>();
    private ProductsInProductFamiliesSeeder ProductsInProductFamiliesSeeder => LazyServiceProvider.LazyGetRequiredService<ProductsInProductFamiliesSeeder>();
    private AssembliesSeeder AssembliesSeeder => LazyServiceProvider.LazyGetRequiredService<AssembliesSeeder>();
    private PartsSeeder PartsSeeder => LazyServiceProvider.LazyGetRequiredService<PartsSeeder>();
    private BomLinePartsSeeder BomLinePartsSeeder => LazyServiceProvider.LazyGetRequiredService<BomLinePartsSeeder>();
    private ThumbnailsSeeder ThumbnailsSeeder => LazyServiceProvider.LazyGetRequiredService<ThumbnailsSeeder>();
    private DocumentCategoriesSeeder DocumentCategoriesSeeder => LazyServiceProvider.LazyGetRequiredService<DocumentCategoriesSeeder>();


    private ICurrentTenant CurrentTenant => LazyServiceProvider.LazyGetRequiredService<ICurrentTenant>();
    protected IDataFilter<IHasVisibility> IsVisibleProductDataFilter => LazyServiceProvider.LazyGetRequiredService<IDataFilter<IHasVisibility>>();

    public async Task<CoreTestsSeedingDataStore> SeedTestDataAsync(TestDataContext context)
    {
        CoreTestsSeedingDataStore storeContext = new(context.TenantId);
        SeedingContext seedingContext = new();

        using (CurrentTenant.Change(context.TenantId))
        {
            using (IsVisibleProductDataFilter.Disable())
            {
                await SeedProductFamilies(context, storeContext, seedingContext);

                await SeedProducts(context, storeContext, seedingContext);

                await SeedProductsInProductFamilies(context, storeContext, seedingContext);

                await SeedAssemblies(context, storeContext, seedingContext);

                await SeedParts(context, storeContext, seedingContext);

                await SeedBomLineAssembliesForProducts(context, storeContext, seedingContext);

                await SeedBomLinePartsForProducts(context, storeContext, seedingContext);

                await SeedBomLinePartsForAssemblies(context, storeContext, seedingContext);

                await SeedDrawings(context, storeContext, seedingContext);

                await SeedDocumentCategories(context, storeContext);

                await SeedDocuments(context, storeContext, seedingContext);

                await SeedCompanies(context, storeContext, seedingContext);

                await SeedEquipments(context, storeContext, seedingContext);

                await SeedThumbnails(context, storeContext, seedingContext);
            }
        }
        return storeContext;
    }

    private async Task SeedThumbnails(TestDataContext context, CoreTestsSeedingDataStore storeContext, SeedingContext seedingContext)
    {
        if (context is { GenerateThumbnailsForProducts: true, GenerateProducts: true })
        {
            await ThumbnailsSeeder.SeedForProducts(storeContext, seedingContext);
        }
    }

    private async Task SeedEquipments(TestDataContext context, CoreTestsSeedingDataStore storeContext,
        SeedingContext seedingContext)
    {
        if (context is { GenerateEquipments: true, GenerateProducts: true })
        {
            if (context.GenerateCompanies)
            {
                await EquipmentsSeeder.SeedWithCompanies(storeContext, seedingContext);
            }
            else
            {
                await EquipmentsSeeder.SeedWithoutCompanies(storeContext, seedingContext);
            }
        }
    }

    private async Task SeedCompanies(TestDataContext context, CoreTestsSeedingDataStore storeContext,
        SeedingContext seedingContext)
    {
        if (context.GenerateCompanies)
        {
            await CompaniesSeeder.Seed(storeContext, seedingContext);
        }
    }

    private async Task SeedDocuments(TestDataContext context, CoreTestsSeedingDataStore storeContext,
        SeedingContext seedingContext)
    {
        if (context is { GenerateProducts: true, GenerateDocumentsForProducts: true })
        {
            await DocumentsSeeder.Seed(storeContext, seedingContext);
        }
    }

    private async Task SeedDrawings(TestDataContext context, CoreTestsSeedingDataStore storeContext,
        SeedingContext seedingContext)
    {
        if (context is { GenerateProducts: true, GenerateAssemblies: true, GenerateDrawingsForProducts: true } ||
            context is { GenerateProducts: true, GeneratePartsForProducts: true, GenerateDrawingsForProducts: true })
        {
            await DrawingsSeeder.SeedForProducts(storeContext, seedingContext);
        }
        if (context is { GenerateAssemblies: true, GenerateDrawingsForAssemblies: true } ||
            context is { GenerateAssemblies: true, GeneratePartsForAssemblies: true, GenerateDrawingsForAssemblies: true })
        {
            await DrawingsSeeder.SeedForAssemblies(storeContext, seedingContext);
        }
    }

    private async Task SeedBomLinePartsForAssemblies(TestDataContext context, CoreTestsSeedingDataStore storeContext,
        SeedingContext seedingContext)
    {
        if (context is { GenerateAssemblies: true, GeneratePartsForAssemblies: true })
        {
            await BomLinePartsSeeder.SeedBomLinePartsForAssemblies(storeContext, seedingContext);
        }
    }

    private async Task SeedBomLinePartsForProducts(TestDataContext context, CoreTestsSeedingDataStore storeContext,
        SeedingContext seedingContext)
    {
        if (context is { GenerateProducts: true, GeneratePartsForProducts: true })
        {
            await BomLinePartsSeeder.SeedBomLinePartsForProducts(storeContext, seedingContext);
        }
    }

    private async Task SeedBomLineAssembliesForProducts(TestDataContext context, CoreTestsSeedingDataStore storeContext,
        SeedingContext seedingContext)
    {
        if (context is { GenerateProducts: true, GenerateAssemblies: true })
        {
            await BomLinePartsSeeder.SeedBomLineAssembliesForProducts(storeContext, seedingContext);
        }
    }

    private async Task SeedParts(TestDataContext context, CoreTestsSeedingDataStore storeContext,
        SeedingContext seedingContext)
    {
        if (context.GeneratePartsForProducts || context.GeneratePartsForAssemblies)
        {
            await PartsSeeder.Seed(storeContext, seedingContext);
        }
    }

    private async Task SeedAssemblies(TestDataContext context, CoreTestsSeedingDataStore storeContext,
        SeedingContext seedingContext)
    {
        if (context.GenerateAssemblies)
        {
            await AssembliesSeeder.Seed(storeContext, seedingContext);
        }
    }

    private async Task SeedProductsInProductFamilies(TestDataContext context, CoreTestsSeedingDataStore storeContext,
        SeedingContext seedingContext)
    {
        if (context is { GenerateProductFamilies: true, GenerateProducts: true })
        {
            await ProductsInProductFamiliesSeeder.Seed(storeContext, seedingContext);
        }
    }

    private async Task SeedProducts(TestDataContext context, CoreTestsSeedingDataStore storeContext,
        SeedingContext seedingContext)
    {
        if (context.GenerateProducts)
        {
            await ProductsSeeder.Seed(storeContext, seedingContext);
        }
    }

    private async Task SeedProductFamilies(TestDataContext context, CoreTestsSeedingDataStore storeContext,
        SeedingContext seedingContext)
    {
        if (context.GenerateProductFamilies)
        {
            await ProductFamiliesSeeder.Seed(storeContext, seedingContext);
        }
    }

    private async Task SeedDocumentCategories(TestDataContext context, CoreTestsSeedingDataStore storeContext)
    {
        if (context.GenerateDocumentCategories)
        {
            await DocumentCategoriesSeeder.Seed(storeContext);
        }
    }
}
