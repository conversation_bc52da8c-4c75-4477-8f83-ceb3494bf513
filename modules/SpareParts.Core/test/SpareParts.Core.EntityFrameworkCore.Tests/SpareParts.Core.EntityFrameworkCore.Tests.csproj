<Project Sdk="Microsoft.NET.Sdk">

	<Import Project="..\..\..\..\common.props" />

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<RootNamespace>SpareParts.Core</RootNamespace>
		<UserSecretsId>1eb06437-8411-461e-8e3a-ef937e6c702f</UserSecretsId>
	</PropertyGroup>

	<ItemGroup>
		<EmbeddedResource Include="Files\*.*" />
	</ItemGroup>

	<ItemGroup>
		<None Remove="Files\*.*" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="HarfBuzzSharp.NativeAssets.Linux" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Proxies" />
		<PackageReference Include="Testcontainers.MsSql" />
		<ProjectReference Include="..\..\src\SpareParts.Core.EntityFrameworkCore\SpareParts.Core.EntityFrameworkCore.csproj" />
		<ProjectReference Include="..\SpareParts.Core.Application.Tests\SpareParts.Core.Application.Tests.csproj" />
		<PackageReference Include="Volo.Abp.EntityFrameworkCore.SqlServer" />
		<PackageReference Include="Volo.Abp.BlobStoring.Azure" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Update="coverlet.collector">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
		</PackageReference>
		<PackageReference Update="Fody">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
		</PackageReference>
	</ItemGroup>

	<Target Name="CopyDemoData" AfterTargets="Build">
		<RemoveDir Directories="$(OutputPath)demo-data" />
		<ItemGroup>
			<DemoDataFiles Include=".\demo-data\**\*" />
		</ItemGroup>
		<Copy SourceFiles="@(DemoDataFiles)" DestinationFolder="$(OutputPath)demo-data\%(RecursiveDir)" />
	</Target>

</Project>