using SpareParts.Core.EntityFrameworkCore;
using System;
using Xunit;

namespace SpareParts.Core.Components.CommandHandlers;

[Collection(CoreTestConsts.CollectionDefinitionName)]
public class DeleteComponentOnlineTranslationCommandHandlerEfCoreTests : DeleteComponentOnlineTranslationCommandHandlerTests<CoreEntityFrameworkCoreTestModule>
{
    protected override string GetConcreteComponentCode(CoreTestsSeedingDataStore storeContext)
    {
        return storeContext.AssemblyCodePrefix + "1";
    }

    protected override Guid GetComponentId(CoreTestsSeedingDataStore storeContext)
    {
        return storeContext.AssemblyIdByCodes[GetConcreteComponentCode(storeContext)];
    }

    protected override TestDataContext GetTestDataContext(Guid tenantId)
    {
        return new TestDataContext(GuidGenerator.Create())
        {
            TenantId = tenantId,
            GenerateAssemblies = true
        };
    }
}