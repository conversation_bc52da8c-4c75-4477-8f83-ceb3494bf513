using System.Threading.Tasks;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Guids;
using Volo.Abp.MultiTenancy;

namespace SpareParts.Core;

public class CoreDataSeedContributor(IGuidGenerator guidGenerator, ICurrentTenant currentTenant)
    : IDataSeedContributor, ITransientDependency
{
    private readonly IGuidGenerator _guidGenerator = guidGenerator;

    public Task SeedAsync(DataSeedContext context)
    {
        /* Instead of returning the Task.CompletedTask, you can insert your test data
         * at this point!
         */

        using (currentTenant.Change(context?.TenantId))
        {
            return Task.CompletedTask;
        }
    }
}
