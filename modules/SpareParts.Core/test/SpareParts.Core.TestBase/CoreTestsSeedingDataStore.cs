using System;
using System.Collections.Generic;

namespace SpareParts.Core;
public class CoreTestsSeedingDataStore
{
    public Dictionary<string, Guid> ProductFamilyIdByCodes { get; set; } = [];
    public Dictionary<string, Guid> RootProductFamilyIdByCodes { get; set; } = [];
    public Dictionary<string, Guid> EdgeProductFamilyIdByCodes { get; set; } = [];
    public Dictionary<string, Guid> EdgeProductFamilyWithProductIdByCodes { get; set; } = [];
    public Dictionary<string, Guid> EdgeProductFamilyWithoutProductIdByCodes { get; set; } = [];
    public Dictionary<string, Guid> ProductFamilyWithChildIdByCodes { get; set; } = [];
    public Dictionary<string, Guid> ProductFamilyLinkedToProductIdByCodes { get; set; } = [];
    public Dictionary<Guid, List<Guid>> ProductFamilyHierarchy { get; set; } = [];

    public Dictionary<Guid, List<Guid>> ProductFamilyIdWithProductIds { get; set; } = [];

    public Dictionary<string, Guid> ProductIdByCodes { get; set; } = [];
    public Dictionary<string, Guid> AssemblyIdByCodes { get; set; } = [];
    public HashSet<Guid> ProductIdsLinkedToProductFamily { get; set; } = [];
    public Dictionary<string, Guid> ProductNotLinkedToProductFamilyIdByCodes { get; set; } = [];
    public HashSet<Guid> ProductsWithEquipment { get; set; } = [];
    public HashSet<Guid> ProductsNotPublicWithEquipment { get; set; } = [];
    public HashSet<Guid> ProductsNotPublicWithoutEquipment { get; set; } = [];
    public Dictionary<Guid, List<Guid>> ProductsWithEquipments { get; set; } = [];

    public Dictionary<string, int> ComponentBomLinesCountByCodes { get; set; } = [];
    public Dictionary<Guid, List<Guid>> ComponentIdsByParentId { get; set; } = [];

    public List<Guid> EquipmentsWithCompany { get; set; } = [];
    public List<Guid> EquipmentsWithOutCompany { get; set; } = [];

    public List<Guid> CompaniesInternal { get; set; } = [];
    public List<Guid> CompaniesExternal { get; set; } = [];

    public List<Guid> DocumentCategories { get; set; } = [];

    public int AssemblyCount { get; set; } = CoreTestConsts.AssemblyCount;
    public int PartCount { get; set; } = CoreTestConsts.PartCount;
    public int MaxAssemblyInProduct { get; set; } = CoreTestConsts.MaxAssemblyInProduct;
    public int MaxDrawings { get; set; } = CoreTestConsts.MaxDrawings;
    public int MaxDocuments { get; set; } = CoreTestConsts.MaxDocuments;
    public int MaxCompanies { get; set; } = CoreTestConsts.MaxCompanies;
    public int MaxDocumentCategories { get; set; } = CoreTestConsts.MaxDocumentCategories;

    public string ProductCodePrefix { get; set; } = CoreTestConsts.ProductCodePrefix;
    public string AssemblyCodePrefix { get; set; } = CoreTestConsts.AssemblyCodePrefix;
    public string PartCodePrefix { get; set; } = CoreTestConsts.PartCodePrefix;
    public string ProductFamilyCodePrefix { get; set; } = CoreTestConsts.ProductFamilyCodePrefix;

    public string ProductLabelPrefix { get; set; } = CoreTestConsts.ProductLabelPrefix;
    public string ProductDescriptionPrefix { get; set; } = CoreTestConsts.ProductDescriptionPrefix;
    public string AssemblyLabelPrefix { get; set; } = CoreTestConsts.AssemblyLabelPrefix;
    public string OnlineProductLabelPrefix { get; set; } = CoreTestConsts.OnlineProductLabelPrefix;
    public string OnlineProductDescriptionPrefix { get; set; } = CoreTestConsts.OnlineProductDescriptionPrefix;
    public string OnlineAssemblyLabelPrefix { get; set; } = CoreTestConsts.OnlineAssemblyLabelPrefix;

    public Guid TenantId { get; private set; }

    public CoreTestsSeedingDataStore(Guid tenantId)
    {
        TenantId = tenantId;
    }

    public void AddComponentIdsByParentIds(Guid parentId, Guid childId)
    {
        if (ComponentIdsByParentId.TryGetValue(parentId, out List<Guid>? ids))
        {
            ids.Add(childId);
        }
        else
        {
            ComponentIdsByParentId.Add(parentId, [childId]);
        }
    }
}