using Volo.Abp.Modularity;
using Volo.Abp.VirtualFileSystem;

namespace SpareParts.Core;

[DependsOn(
    typeof(CoreApplicationModule),
    typeof(CoreDomainTestModule)
    )]
public class CoreApplicationTestModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        base.ConfigureServices(context);
        Configure<AbpVirtualFileSystemOptions>(options =>
        {
            options.FileSets.AddEmbedded<CoreApplicationTestModule>("SpareParts.Core");
        });

    }
}
