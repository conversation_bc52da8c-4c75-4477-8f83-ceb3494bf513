using System;
using System.Linq;
using System.Threading.Tasks;
using Shouldly;
using SpareParts.Core.Entities.ProductFamilies;
using SpareParts.Core.ProductFamilies.Commands;
using Volo.Abp.Modularity;
using Volo.Abp.Validation;
using Xunit;

namespace SpareParts.Core.ProductFamilies.CommandHandlers;
public abstract class DeleteProductFamilyCommandHandlerTests<TStartupModule> : ProductFamilyTestsBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    [Fact]
    public async Task DeleteProductFamilyCommandHandler_Should_Delete_Product_Family()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            // Arrange
            Guid id = storeContext.EdgeProductFamilyWithoutProductIdByCodes.First().Value;
            await WithUnitOfWorkAsync(async () =>
            {
                // Act
                await CommandSender.Send(new DeleteProductFamilyCommand(id));
            });

            // Assert
            await WithUnitOfWorkAsync(async () =>
            {
                ProductFamily? productFamilyResult = await ProductFamilyRepository.FindAsync(id);
                productFamilyResult.ShouldBeNull();
            });
        }
    }

    [Fact]
    public async Task DeleteProductFamilyCommandHandler_Should_Throw_Exception_If_Have_Sub_ProductFamily()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await base.WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid id = storeContext.ProductFamilyWithChildIdByCodes.First().Value;

                // Assert
                await CommandSender.Send(new DeleteProductFamilyCommand(id)).ShouldThrowAsync<AbpValidationException>();
            });
        }
    }

    [Fact]
    public async Task DeleteProductFamilyCommandHandler_Should_Throw_Exception_If_Have_Product()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid id = storeContext.ProductFamilyLinkedToProductIdByCodes.First().Value;

                // Assert
                await CommandSender.Send(new DeleteProductFamilyCommand(id)).ShouldThrowAsync<AbpValidationException>();
            });
        }
    }
}
