using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Bogus.Extensions;
using Microsoft.Extensions.FileProviders;
using Shouldly;
using SpareParts.Common.Dtos;
using SpareParts.Common.Exceptions;
using SpareParts.Core.Entities.ProductFamilies;
using SpareParts.Core.Entities.Resources;
using SpareParts.Core.ProductFamilies.Commands;
using SpareParts.Core.ProductFamilies.Dtos;
using SpareParts.Core.ProductFamilies.Dtos.Inputs;
using SpareParts.Core.Settings;
using Volo.Abp;
using Volo.Abp.Content;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Modularity;
using Volo.Abp.Settings;
using Volo.Abp.Validation;
using Xunit;

namespace SpareParts.Core.ProductFamilies.CommandHandlers;

public abstract class UpdateProductFamilyCommandHandlerTests<TStartupModule> : ProductFamilyTestsBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    [Fact]
    public async Task UpdateProductFamilyCommandHandler_Should_Update_ProductFamily()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            ProductFamily productFamily = default!;
            Resource newResource = default!;

            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid id = storeContext.EdgeProductFamilyIdByCodes.First().Value;
                productFamily = await ProductFamilyRepository.GetAsync(id);

                IFileInfo imageFile = VirtualFileProvider.GetFileInfo("/Files/default-image.png");
                await using Stream imageStream = imageFile.CreateReadStream();
                newResource = await ImageManager.CreateAsync(new RemoteStreamContent(imageStream, Faker.System.CommonFileName(CoreTestConsts.ImageExtension)));
                await ResourceRepository.InsertAsync(newResource, true);
            });

            await WithUnitOfWorkAsync(async () =>
            {
                UpdateProductFamilyDto updateProductFamilyDto = new(ObjectMapper.Map<List<ProductFamilyTranslation>, List<CommonTranslationDto>>(
                        productFamily.Translations.ToList()),
                    2,
                    true)
                {
                    ImageId = newResource.Id,
                    ParentId = null
                };
                UpdateProductFamilyCommand updateProductFamilyCommand = new(productFamily.Id, updateProductFamilyDto);

                // Act
                ProductFamilyDto productFamilyDto = await CommandSender.Send(updateProductFamilyCommand);

                // Assert
                productFamilyDto.Code.ShouldBe(productFamily.Code);
                productFamilyDto.ImageId.ShouldBe(newResource.Id);
                productFamilyDto.IsEdge.ShouldBeTrue();
                productFamilyDto.IsVisible.ShouldBe(updateProductFamilyCommand.UpdateProductFamilyDto.IsVisible);
                productFamilyDto.ParentId.ShouldBeNull();
                productFamilyDto.Rank.ShouldBe(updateProductFamilyCommand.UpdateProductFamilyDto.Rank);
                productFamilyDto.ProductsCount.ShouldBe(productFamily.Products.Count);
                productFamilyDto.Translations.Count.ShouldBe(2);
                foreach (CommonTranslationDto commonTranslationDto in productFamilyDto.Translations)
                {
                    ProductFamilyTranslation? translation =
                        productFamily.Translations.FirstOrDefault(t => t.Language == commonTranslationDto.Language);
                    translation.ShouldNotBeNull();
                    commonTranslationDto.Label.ShouldBe(translation.Label);
                    commonTranslationDto.Description.ShouldBe(translation.Description);
                }
            });
        }
    }

    [Fact]
    public async Task UpdateProductFamilyCommandHandler_Should_Update_ProductFamily_Without_Image()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            // Arrange
            Guid id = storeContext.EdgeProductFamilyIdByCodes.First().Value;
            ProductFamily productFamily = await ProductFamilyRepository.GetAsync(id);

            await WithUnitOfWorkAsync(async () =>
            {
                UpdateProductFamilyDto updateProductFamilyDto = new(ObjectMapper.Map<List<ProductFamilyTranslation>, List<CommonTranslationDto>>(
                        productFamily.Translations.ToList()),
                2,
                true)
                {
                    ParentId = null
                };
                UpdateProductFamilyCommand updateProductFamilyCommand = new(productFamily.Id, updateProductFamilyDto);

                // Act
                ProductFamilyDto productFamilyDto = await CommandSender.Send(updateProductFamilyCommand);

                // Assert
                productFamilyDto.Code.ShouldBe(productFamily.Code);
                productFamilyDto.ImageId.ShouldBe(await SettingProvider.GetAsync<Guid>(CoreSettings.ProductFamilyDefaultImageId));
                productFamilyDto.IsEdge.ShouldBeTrue();
                productFamilyDto.IsVisible.ShouldBe(updateProductFamilyCommand.UpdateProductFamilyDto.IsVisible);
                productFamilyDto.ParentId.ShouldBeNull();
                productFamilyDto.Rank.ShouldBe(updateProductFamilyCommand.UpdateProductFamilyDto.Rank);
                productFamilyDto.ProductsCount.ShouldBe(productFamily.Products.Count);
                productFamilyDto.Translations.Count.ShouldBe(2);
                foreach (CommonTranslationDto commonTranslationDto in productFamilyDto.Translations)
                {
                    ProductFamilyTranslation? translation =
                        productFamily.Translations.FirstOrDefault(t => t.Language == commonTranslationDto.Language);
                    translation.ShouldNotBeNull();
                    commonTranslationDto.Label.ShouldBe(translation.Label);
                    commonTranslationDto.Description.ShouldBe(translation.Description);
                }
            });
        }
    }

    [Fact]
    public async Task UpdateProductFamilyCommandHandler_Should_Change_ProductFamily_Parent()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid id = storeContext.RootProductFamilyIdByCodes.First().Value;
                ProductFamily productFamily = await ProductFamilyRepository.GetAsync(id);

                Guid parentId = storeContext.RootProductFamilyIdByCodes.First().Value;

                UpdateProductFamilyDto updateProductFamilyDto = new([
                        new CommonTranslationDto(productFamily.Translations.ElementAt(0)
                                .Language,
                            productFamily.Translations.ElementAt(0)
                                .Label,
                            productFamily.Translations.ElementAt(0)
                                .Description!)
                    ],
                    1,
                    true)
                {
                    ParentId = parentId,
                    ImageId = productFamily.ImageId
                };
                UpdateProductFamilyCommand updateProductFamilyCommand = new(productFamily.Id, updateProductFamilyDto);

                // Act
                ProductFamilyDto productFamilyDto = await CommandSender.Send(updateProductFamilyCommand);

                // Assert
                productFamilyDto.ParentId.ShouldBe(parentId);
            });
        }
    }

    [Fact]
    public async Task UpdateProductFamilyCommandHandler_Should_Add_Translation()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            CommonTranslationDto addedTranslation = new("en", Faker.Lorem.Word().ClampLength(2), Faker.Lorem.Word());
            ProductFamilyDto productFamilyDto = await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid id = storeContext.RootProductFamilyIdByCodes.First().Value;
                ProductFamily productFamily = await ProductFamilyRepository.GetAsync(id);

                
                List<CommonTranslationDto> newTranslations = [addedTranslation];

                UpdateProductFamilyDto updateProductFamilyDto = new(newTranslations,
                    2,
                    true)
                {
                    ImageId = productFamily.ImageId,
                    ParentId = null
                };
                UpdateProductFamilyCommand updateProductFamilyCommand = new(productFamily.Id, updateProductFamilyDto);

                // Act
                 return await CommandSender.Send(updateProductFamilyCommand);
            });
            // Assert
            productFamilyDto.Translations.Count.ShouldBe(1);
            CommonTranslationDto addedTranslationDto = productFamilyDto.Translations.First();
            addedTranslationDto.Label.ShouldBe(addedTranslation.Label);
            addedTranslationDto.Description.ShouldBe(addedTranslation.Description);
        }
    }

    [Fact]
    public async Task UpdateProductFamilyCommandHandler_Should_Update_Translation()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid id = storeContext.RootProductFamilyIdByCodes.First().Value;
                ProductFamily productFamily = await ProductFamilyRepository.GetAsync(id);

                List<CommonTranslationDto> newTranslations = [];
                CommonTranslationDto updatedTranslation = new(Faker.Locale, Faker.Lorem.Word().ClampLength(2), Faker.Lorem.Word());
                newTranslations.Add(updatedTranslation);

                UpdateProductFamilyDto updateProductFamilyDto = new(newTranslations,
                    2,
                    true)
                {
                    ImageId = productFamily.ImageId,
                    ParentId = null
                };
                UpdateProductFamilyCommand updateProductFamilyCommand = new(productFamily.Id, updateProductFamilyDto);

                // Act
                ProductFamilyDto productFamilyDto = await CommandSender.Send(updateProductFamilyCommand);

                // Assert
                productFamilyDto.Translations.Count.ShouldBe(1);
                CommonTranslationDto updatedTranslationDto = productFamilyDto.Translations[0];
                updatedTranslationDto.Language.ShouldBe(updatedTranslation.Language);
                updatedTranslationDto.Label.ShouldBe(updatedTranslation.Label);
                updatedTranslationDto.Description.ShouldBe(updatedTranslation.Description);
            });
        }
    }

    [Fact]
    public async Task UpdateProductFamilyCommandHandler_Should_Throw_Exception_If_ProductFamily_Not_Exist()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid id = storeContext.RootProductFamilyIdByCodes.First().Value;
                ProductFamily productFamily = await ProductFamilyRepository.GetAsync(id);

                UpdateProductFamilyDto updateProductFamilyDto = new([
                        new CommonTranslationDto(productFamily.Translations.ElementAt(0)
                                .Language,
                            productFamily.Translations.ElementAt(0)
                                .Label,
                            productFamily.Translations.ElementAt(0)
                                .Description!)
                    ],
                    1,
                    true)
                {
                    ImageId = productFamily.ImageId,
                    ParentId = null
                };
                UpdateProductFamilyCommand updateProductFamilyCommand =
                    new(GuidGenerator.Create(), updateProductFamilyDto);

                // Assert
                await CommandSender.Send(updateProductFamilyCommand).ShouldThrowAsync<EntityNotFoundException>();
            });
        }
    }

    [Fact]
    public async Task UpdateProductFamilyCommandHandler_Should_Throw_Exception_If_No_Translation()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid id = storeContext.RootProductFamilyIdByCodes.First().Value;
                ProductFamily productFamily = await ProductFamilyRepository.GetAsync(id);

                UpdateProductFamilyDto updateProductFamilyDto = new([], 1, true)
                {
                    ImageId = productFamily.ImageId,
                    ParentId = null
                };
                UpdateProductFamilyCommand updateProductFamilyCommand = new(productFamily.Id, updateProductFamilyDto);

                // Assert
                await CommandSender.Send(updateProductFamilyCommand).ShouldThrowAsync<MissingDefaultLanguageException>();
            });
        }
    }

    [Fact]
    public async Task UpdateProductFamilyCommandHandler_Should_Throw_Exception_If_Parent_ProductFamily_Not_Exist()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid id = storeContext.RootProductFamilyIdByCodes.First().Value;
                ProductFamily productFamily = await ProductFamilyRepository.GetAsync(id);

                UpdateProductFamilyDto updateProductFamilyDto = new([
                        new CommonTranslationDto(productFamily.Translations.ElementAt(0)
                                .Language,
                            productFamily.Translations.ElementAt(0)
                                .Label,
                            productFamily.Translations.ElementAt(0)
                                .Description!)
                    ],
                    1,
                    true)
                {
                    ImageId = productFamily.ImageId,
                    ParentId = GuidGenerator.Create()
                };
                UpdateProductFamilyCommand updateProductFamilyCommand = new(productFamily.Id, updateProductFamilyDto);

                // Assert
                await CommandSender.Send(updateProductFamilyCommand).ShouldThrowAsync<BusinessException>();
            });
        }
    }

    [Fact]
    public async Task UpdateProductFamilyCommandHandler_Should_Throw_Exception_If_Translation_Label_Is_Empty()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid id = storeContext.RootProductFamilyIdByCodes.First().Value;
                ProductFamily productFamily = await ProductFamilyRepository.GetAsync(id);

                UpdateProductFamilyDto updateProductFamilyDto = new([
                        new CommonTranslationDto(productFamily.Translations.ElementAt(0)
                                .Language,
                            string.Empty,
                            productFamily.Translations.ElementAt(0)
                                .Description)
                    ],
                    1,
                    true)
                {
                    ImageId = productFamily.ImageId,
                    ParentId = null
                };
                UpdateProductFamilyCommand updateProductFamilyCommand = new(productFamily.Id, updateProductFamilyDto);

                // Assert
                await CommandSender.Send(updateProductFamilyCommand).ShouldThrowAsync<AbpValidationException>();
            });
        }
    }

    [Fact]
    public async Task UpdateProductFamilyCommandHandler_Should_Throw_Exception_If_Image_Not_Exist()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid id = storeContext.RootProductFamilyIdByCodes.First().Value;
                ProductFamily productFamily = await ProductFamilyRepository.GetAsync(id);

                UpdateProductFamilyDto updateProductFamilyDto = new([
                        new CommonTranslationDto(productFamily.Translations.ElementAt(0)
                                .Language,
                            productFamily.Translations.ElementAt(0)
                                .Label,
                            productFamily.Translations.ElementAt(0)
                                .Description!)
                    ],
                    1,
                    true)
                {
                    ImageId = GuidGenerator.Create(),
                    ParentId = null
                };
                UpdateProductFamilyCommand updateProductFamilyCommand = new(productFamily.Id, updateProductFamilyDto);

                // Assert
                await CommandSender.Send(updateProductFamilyCommand).ShouldThrowAsync<ArgumentException>();
            });
        }
    }
}