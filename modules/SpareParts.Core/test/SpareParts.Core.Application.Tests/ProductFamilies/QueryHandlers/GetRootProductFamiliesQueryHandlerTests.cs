using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using NSubstitute;
using Shouldly;
using SpareParts.Common;
using SpareParts.Common.Companies;
using SpareParts.Common.DataFilter;
using SpareParts.Core.DataVisibility;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.ProductFamilies;
using SpareParts.Core.ProductFamilies.Dtos;
using SpareParts.Core.ProductFamilies.Queries;
using SpareParts.Core.ProductFamilies.QueryFilters;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Authorization;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;
using Volo.Abp.Security.Claims;
using Xunit;

namespace SpareParts.Core.ProductFamilies.QueryHandlers;
public abstract class GetRootProductFamiliesQueryHandlerTests<TStartupModule> : ProductFamilyTestsBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    private bool _authorizationResultSuccess;
    private IAbpAuthorizationService _authorizationService = default!;

    protected override void AfterAddApplication(IServiceCollection services)
    {
        _authorizationService = Substitute.For<IAbpAuthorizationService>();
        _authorizationService.AuthorizeAsync(Arg.Any<ClaimsPrincipal>(), Arg.Any<object?>(), Arg.Any<string>())
            .Returns(_ => _authorizationResultSuccess ? AuthorizationResult.Success() : AuthorizationResult.Failed());

        services.Replace(ServiceDescriptor.Singleton(_authorizationService));
    }

    [Fact]
    public async Task GetRootProductFamiliesQueryHandler_Should_Return_Root_Product_Families()
    {
        _authorizationResultSuccess = true;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid id = storeContext.RootProductFamilyIdByCodes.First().Value;
                ProductFamily productFamily = await ProductFamilyRepository.FirstAsync(x => x.Id == id);

                List<ProductInProductFamily> productInProductFamilies = await ProductInProductFamilyRepository.GetListAsync(x => x.ProductFamilyId == id);
                List<Guid> productsId = productInProductFamilies.Select(x => x.ProductId).ToList();
                int productCounts = await ProductRepository.CountAsync(x => productsId.Contains(x.Id));

                // Act
                PagedResultDto<ProductFamilyDto> productFamilies = await QuerySender.Send(new GetRootProductFamiliesQuery(new ProductFamilyPaginationFilter()));

                // Assert
                productFamilies.TotalCount.ShouldBe(storeContext.RootProductFamilyIdByCodes.Count);
                ProductFamilyDto? productFamilyDto = productFamilies.Items.FirstOrDefault(x => x.Id == id);
                productFamilyDto.ShouldNotBeNull();
                productFamilyDto.Code.ShouldBe(productFamily.Code);
                productFamilyDto.ImageId.ShouldBe(productFamily.ImageId);
                productFamilyDto.Translations.Count.ShouldBe(productFamily.Translations.Count);
                productFamilyDto.Translations[0].Language.ShouldBe(productFamily.Translations.First().Language);
                productFamilyDto.Translations[0].Label.ShouldBe(productFamily.Translations.First().Label);
                productFamilyDto.Translations[0].Description.ShouldBe(productFamily.Translations.First().Description);
                productFamilyDto.IsVisible.ShouldBe(productFamily.IsVisible);
                productFamilyDto.Rank.ShouldBe(productFamily.Rank);
                productFamilyDto.ProductsCount.ShouldBe(productCounts);
            });
        }
    }

    [Fact]
    public async Task GetRootProductFamiliesQueryHandler_Should_Return_Paged_And_Sort_Results()
    {
        _authorizationResultSuccess = true;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange & Act
                ProductFamilyPaginationFilter filter = new() { Page = 1, PerPage = 2, Sort = "Code", SortBy = AutoFilterer.Enums.Sorting.Ascending };
                PagedResultDto<ProductFamilyDto> productFamilies = await QuerySender.Send(new GetRootProductFamiliesQuery(filter));

                // Assert
                productFamilies.TotalCount.ShouldBe(storeContext.RootProductFamilyIdByCodes.Count);
                productFamilies.Items.Count.ShouldBe(2);
                IOrderedEnumerable<ProductFamilyDto> expectedList = productFamilies.Items.OrderBy(x => x.Code);
                expectedList.SequenceEqual(productFamilies.Items).ShouldBeTrue();
            });
        }
    }

    [Fact]
    public async Task GetRootProductFamiliesQuery_Should_Return_Result_If_User_Is_Content_Manager_Not_Contains_Public_Product_And_Not_Contains_Equipment()
    {
        _authorizationResultSuccess = true;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        Guid companyId = Guid.NewGuid();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.Internal.ToString())]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                Guid productFamilyId = Guid.Empty;
                await WithUnitOfWorkAsync(async () =>
                {
                    // Arrange
                    productFamilyId = storeContext.RootProductFamilyIdByCodes.First().Value;
                    List<ProductInProductFamily> productsInProductFamily = await ProductInProductFamilyRepository.GetListAsync(x => x.ProductFamilyId == productFamilyId);

                    List<Guid> productIds = productsInProductFamily.Select(x => x.ProductId).ToList();
                    List<Product> products = await ProductRepository.GetListAsync(x => productIds.Contains(x.Id));
                    foreach (Product product in products)
                    {
                        product.IsPublic = false;
                    }
                    await ProductRepository.UpdateManyAsync(products);
                });

                await WithUnitOfWorkAsync(async () =>
                {
                    // Act 
                    PagedResultDto<ProductFamilyDto> results = await QuerySender.Send(new GetRootProductFamiliesQuery(new ProductFamilyPaginationFilter()));

                    // Assert
                    bool exist = results.Items.Any(x => x.Id == productFamilyId);
                    exist.ShouldBeTrue();
                });
            }
        }
    }

    [Fact]
    public async Task GetRootProductFamiliesQuery_Should_Return_Results_If_User_Is_Content_Manager_Not_Contains_Visible_Product_And_Not_Contains_Equipment()
    {
        _authorizationResultSuccess = true;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        Guid companyId = Guid.NewGuid();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.Internal.ToString())]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                Guid productFamilyId = Guid.Empty;
                await WithUnitOfWorkAsync(async () =>
                {
                    // Arrange
                    productFamilyId = storeContext.RootProductFamilyIdByCodes.First().Value;
                    List<ProductInProductFamily> productsInProductFamily = await ProductInProductFamilyRepository.GetListAsync(x => x.ProductFamilyId == productFamilyId);

                    List<Guid> productIds = productsInProductFamily.Select(x => x.ProductId).ToList();

                    List<Product> products = await ProductRepository.GetListAsync(x => productIds.Contains(x.Id));
                    foreach (Product product in products)
                    {
                        product.IsVisible = false;
                    }
                    await ProductRepository.UpdateManyAsync(products);
                });

                await WithUnitOfWorkAsync(async () =>
                {
                    // Act 
                    PagedResultDto<ProductFamilyDto> results = await QuerySender.Send(new GetRootProductFamiliesQuery(new ProductFamilyPaginationFilter()));

                    // Assert
                    bool exist = results.Items.Any(x => x.Id == productFamilyId);
                    exist.ShouldBeTrue();
                });
            }
        }
    }

    [Fact]
    public async Task GetRootProductFamiliesQuery_Should_Return_Results_If_User_Is_Internal_Content_Viewer_Not_Contains_Public_Product_And_Not_Contains_Equipment()
    {
        _authorizationResultSuccess = false;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        Guid companyId = Guid.NewGuid();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.Internal.ToString())]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                Guid productFamilyId = Guid.Empty;
                await WithUnitOfWorkAsync(async () =>
                {
                    // Arrange
                    productFamilyId = storeContext.RootProductFamilyIdByCodes.First().Value;
                    List<ProductInProductFamily> productsInProductFamily = await ProductInProductFamilyRepository.GetListAsync(x => x.ProductFamilyId == productFamilyId);

                    List<Guid> productIds = productsInProductFamily.Select(x => x.ProductId).ToList();

                    List<Product> products = await ProductRepository.GetListAsync(x => productIds.Contains(x.Id));
                    foreach (Product product in products)
                    {
                        product.IsPublic = false;
                    }
                    await ProductRepository.UpdateManyAsync(products);
                });

                await WithUnitOfWorkAsync(async () =>
                {
                    // Act 
                    PagedResultDto<ProductFamilyDto> results = await QuerySender.Send(new GetRootProductFamiliesQuery(new ProductFamilyPaginationFilter()));

                    // Assert
                    bool exist = results.Items.Any(x => x.Id == productFamilyId);
                    exist.ShouldBeTrue();
                });
            }
        }
    }

    [Fact]
    public async Task GetRootProductFamiliesQuery_Should_Not_Return_Result_If_User_Is_Internal_Content_Viewer_Not_Contains_Visible_Product_And_Not_Contains_Equipment()
    {
        _authorizationResultSuccess = false;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true, GenerateEquipments = true, GenerateCompanies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        Guid companyId = storeContext.CompaniesInternal.First();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.Internal.ToString())]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                using (DataFilter.Enable<IHasVisibility>())
                {
                    Guid productFamilyId = Guid.Empty;
                    await WithUnitOfWorkAsync(async () =>
                    {
                        productFamilyId = storeContext.RootProductFamilyIdByCodes.Values.Intersect(storeContext.EdgeProductFamilyWithProductIdByCodes.Values).First();
                        List<ProductInProductFamily> productsInProductFamily = await ProductInProductFamilyRepository.GetListAsync(x => x.ProductFamilyId == productFamilyId);

                        List<Guid> productIds = productsInProductFamily.Select(x => x.ProductId).ToList();

                        List<Product> products = await ProductRepository.GetListAsync(x => productIds.Contains(x.Id));
                        foreach (Product product in products)
                        {
                            product.IsVisible = false;
                        }
                        await ProductRepository.UpdateManyAsync(products);
                    });

                    await WithUnitOfWorkAsync(async () =>
                    {
                        // Act 
                        PagedResultDto<ProductFamilyDto> results = await QuerySender.Send(new GetRootProductFamiliesQuery(new ProductFamilyPaginationFilter()));

                        // Assert
                        bool exist = results.Items.Any(x => x.Id == productFamilyId);
                        exist.ShouldBeFalse();
                    });
                }
            }
        }
    }

    [Fact]
    public async Task GetRootProductFamiliesQuery_Should_Return_Results_If_User_Is_External_Content_Viewer_Contains_Public_Product_And_Not_Contains_Equipment()
    {
        _authorizationResultSuccess = false;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        Guid companyId = Guid.NewGuid();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString())]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                using (DataFilter.Enable<IHasVisibility>())
                {
                    Guid productFamilyId = Guid.Empty;
                    await WithUnitOfWorkAsync(async () =>
                    {
                        // Arrange
                        productFamilyId = storeContext.RootProductFamilyIdByCodes.First().Value;
                        List<ProductInProductFamily> productsInProductFamily = await ProductInProductFamilyRepository.GetListAsync(x => x.ProductFamilyId == productFamilyId);

                        List<Guid> productIds = productsInProductFamily.Select(x => x.ProductId).ToList();

                        List<Product> products = await ProductRepository.GetListAsync(x => productIds.Contains(x.Id));
                        foreach (Product product in products)
                        {
                            product.IsPublic = true;
                        }
                        await ProductRepository.UpdateManyAsync(products);
                    });

                    using (DataFilter.Enable<IHasPublic>())
                    {
                        await WithUnitOfWorkAsync(async () =>
                        {
                            // Act & Assert
                            PagedResultDto<ProductFamilyDto> results = await QuerySender.Send(new GetRootProductFamiliesQuery(new ProductFamilyPaginationFilter()));

                            // Assert
                            bool exist = results.Items.Any(x => x.Id == productFamilyId);
                            exist.ShouldBeTrue();
                        });
                    }
                }
            }
        }
    }

    [Fact]
    public async Task GetRootProductFamiliesQuery_Should_Return_Result_If_User_Is_External_Content_Viewer_Not_Contains_Public_Product_And_Contains_Equipment()
    {
        _authorizationResultSuccess = false;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true, GenerateEquipments = true, GenerateCompanies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        Guid companyId = storeContext.CompaniesExternal.First();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString())]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                using (DataFilter.Enable<IHasVisibility>())
                {
                    Guid productFamilyId = Guid.Empty;
                    await WithUnitOfWorkAsync(async () =>
                    {
                        productFamilyId = storeContext.RootProductFamilyIdByCodes.First().Value;
                        List<ProductInProductFamily> productsInProductFamily = await ProductInProductFamilyRepository.GetListAsync(x => x.ProductFamilyId == productFamilyId);

                        List<Guid> productIds = productsInProductFamily.Select(x => x.ProductId).ToList();

                        List<Product> products = await ProductRepository.GetListAsync(x => productIds.Contains(x.Id));
                        foreach (Product product in products)
                        {
                            product.IsPublic = false;
                        }
                        await ProductRepository.UpdateManyAsync(products);
                    });

                    using (DataFilter.Enable<IHasPublic>())
                    {
                        await WithUnitOfWorkAsync(async () =>
                        {
                            // Act 
                            PagedResultDto<ProductFamilyDto> results = await QuerySender.Send(new GetRootProductFamiliesQuery(new ProductFamilyPaginationFilter()));

                            // Assert
                            bool exist = results.Items.Any(x => x.Id == productFamilyId);
                            exist.ShouldBeTrue();
                        });
                    }
                }
            }
        }
    }

    [Fact]
    public async Task GetRootProductFamiliesQuery_Should_Not_Return_Result_If_User_Is_External_Content_Viewer_Not_Contains_Public_Product_And_Not_Contains_Equipment()
    {
        _authorizationResultSuccess = false;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true, GenerateCompanies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        Guid companyId = storeContext.CompaniesExternal.First();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString())]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                using (DataFilter.Enable<IHasVisibility>())
                {
                    using (DataFilter.Enable<IHasPublic>())
                    {
                        await WithUnitOfWorkAsync(async () =>
                        {
                            //Arrange
                            Guid productFamilyId = storeContext.RootProductFamilyIdByCodes.Values.Intersect(storeContext.EdgeProductFamilyWithoutProductIdByCodes.Values).First();

                            // Act 
                            PagedResultDto<ProductFamilyDto> results = await QuerySender.Send(new GetRootProductFamiliesQuery(new ProductFamilyPaginationFilter()));

                            // Assert
                            bool exist = results.Items.Any(x => x.Id == productFamilyId);
                            exist.ShouldBeFalse();
                        });
                    }
                }
            }
        }
    }

    [Fact]
    public async Task GetRootProductFamiliesQuery_Should_Not_Return_Result_If_User_Is_External_Content_Viewer_Not_Contains_Visible_Product_And_Not_Contains_Equipment()
    {
        _authorizationResultSuccess = false;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true, GenerateCompanies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        Guid companyId = storeContext.CompaniesExternal.First();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString())]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                Guid productFamilyId = Guid.Empty;
                await WithUnitOfWorkAsync(async () =>
                {
                    productFamilyId = storeContext.RootProductFamilyIdByCodes.Values.Intersect(storeContext.EdgeProductFamilyWithProductIdByCodes.Values).First();
                    List<ProductInProductFamily> productsInProductFamily = await ProductInProductFamilyRepository.GetListAsync(x => x.ProductFamilyId == productFamilyId);

                    List<Guid> productIds = productsInProductFamily.Select(x => x.ProductId).ToList();

                    List<Product> products = await ProductRepository.GetListAsync(x => productIds.Contains(x.Id));
                    foreach (Product product in products)
                    {
                        product.IsVisible = false;
                    }
                    await ProductRepository.UpdateManyAsync(products);
                });

                using (DataFilter.Enable<IHasVisibility>())
                {
                    using (DataFilter.Enable<IHasPublic>())
                    {
                        await WithUnitOfWorkAsync(async () =>
                        {
                            // Act
                            PagedResultDto<ProductFamilyDto> results = await QuerySender.Send(new GetRootProductFamiliesQuery(new ProductFamilyPaginationFilter()));

                            // Assert
                            bool exist = results.Items.Any(x => x.Id == productFamilyId);
                            exist.ShouldBeFalse();
                        });
                    }
                }
            }
        }
    }

    [Fact]
    public async Task GetRootProductFamiliesQuery_Should_Not_Return_Result_If_User_Is_External_Content_Viewer_Not_Contains_Visible_Product_And_Contains_Equipment()
    {
        _authorizationResultSuccess = false;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true, GenerateEquipments = true, GenerateCompanies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        Guid companyId = storeContext.CompaniesExternal.First();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString())]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                Guid productFamilyId = Guid.Empty;
                await WithUnitOfWorkAsync(async () =>
                {
                    productFamilyId = storeContext.RootProductFamilyIdByCodes.Values.Intersect(storeContext.EdgeProductFamilyWithProductIdByCodes.Values).First();

                    List<ProductInProductFamily> productsInProductFamily = await ProductInProductFamilyRepository.GetListAsync(x => x.ProductFamilyId == productFamilyId);

                    List<Guid> productIds = productsInProductFamily.Select(x => x.ProductId).ToList();

                    List<Product> products = await ProductRepository.GetListAsync(x => productIds.Contains(x.Id));
                    foreach (Product product in products)
                    {
                        product.IsVisible = false;
                    }
                    await ProductRepository.UpdateManyAsync(products);
                });

                using (DataFilter.Enable<IHasVisibility>())
                {
                    using (DataFilter.Enable<IHasPublic>())
                    {
                        await WithUnitOfWorkAsync(async () =>
                        {
                            //Act
                            PagedResultDto<ProductFamilyDto> results = await QuerySender.Send(new GetRootProductFamiliesQuery(new ProductFamilyPaginationFilter()));

                            // Assert
                            bool exist = results.Items.Any(x => x.Id == productFamilyId);
                            exist.ShouldBeFalse();
                        });
                    }
                }
            }
        }
    }
}
