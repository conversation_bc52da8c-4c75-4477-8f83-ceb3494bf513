using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using NSubstitute;
using Shouldly;
using SpareParts.Common;
using SpareParts.Common.Companies;
using SpareParts.Common.DataFilter;
using SpareParts.Core.DataVisibility;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.ProductFamilies;
using SpareParts.Core.ProductFamilies.Dtos;
using SpareParts.Core.ProductFamilies.Queries;
using Volo.Abp.Authorization;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;
using Volo.Abp.Security.Claims;
using Xunit;

namespace SpareParts.Core.ProductFamilies.QueryHandlers;
public abstract class GetProductFamilyByIdQueryHandlerTests<TStartupModule> : ProductFamilyTestsBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    private bool _authorizationResultSuccess;
    private IAbpAuthorizationService _authorizationService = default!;

    protected override void AfterAddApplication(IServiceCollection services)
    {
        _authorizationService = Substitute.For<IAbpAuthorizationService>();
        _authorizationService.AuthorizeAsync(Arg.Any<ClaimsPrincipal>(), Arg.Any<object?>(), Arg.Any<string>())
            .Returns(_ => _authorizationResultSuccess ? AuthorizationResult.Success() : AuthorizationResult.Failed());

        services.Replace(ServiceDescriptor.Singleton(_authorizationService));
    }

    [Fact]
    public async Task GetProductFamilyByIdQuery_Should_Return_ProductFamily()
    {
        _authorizationResultSuccess = true;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid id = storeContext.EdgeProductFamilyIdByCodes.First().Value;
                ProductFamily productFamily = await ProductFamilyRepository.FirstAsync(x => x.Id == id);

                List<ProductInProductFamily> productInProductFamilies = await ProductInProductFamilyRepository.GetListAsync(x => x.ProductFamilyId == id);
                List<Guid> productsId = productInProductFamilies.Select(x => x.ProductId).ToList();
                int productCounts = await ProductRepository.CountAsync(x => productsId.Contains(x.Id));

                // Act
                ProductFamilyDto result = await QuerySender.Send(new GetProductFamilyByIdQuery(productFamily.Id));

                // Assert
                result.ShouldNotBeNull();
                result.Code.ShouldBe(productFamily.Code);
                result.IsEdge.ShouldBeTrue();
                result.IsVisible.ShouldBe(productFamily.IsVisible);
                result.ImageId.ShouldBe(productFamily.ImageId);
                result.Rank.ShouldBe(productFamily.Rank);
                result.Translations.Count.ShouldBe(productFamily.Translations.Count);
                result.Translations[0].Language.ShouldBe(productFamily.Translations.First().Language);
                result.Translations[0].Label.ShouldBe(productFamily.Translations.First().Label);
                result.Translations[0].Description.ShouldBe(productFamily.Translations.First().Description);
                result.ProductsCount.ShouldBe(productCounts);
            });
        }
    }

    [Fact]
    public async Task GetProductFamilyByIdQuery_Should_Throw_Exception_If_Not_Exist()
    {
        _authorizationResultSuccess = true;
        Guid tenantId = GuidGenerator.Create();
        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Assert
                await QuerySender.Send(new GetProductFamilyByIdQuery(GuidGenerator.Create()))
                    .ShouldThrowAsync<EntityNotFoundException>();
            });
        }
    }

    [Fact]
    public async Task GetProductFamilyByIdQuery_Should_Return_Result_If_User_Is_Content_Manager_Not_Contains_Public_Product_And_Not_Contains_Equipment()
    {
        _authorizationResultSuccess = true;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        Guid companyId = Guid.NewGuid();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.Internal.ToString())]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                int productsCount = 0;
                Guid productFamilyId = Guid.Empty;
                await WithUnitOfWorkAsync(async () =>
                {
                    // Arrange
                    productFamilyId = storeContext.ProductFamilyLinkedToProductIdByCodes.First().Value;

                    List<ProductFamilyFlattenHierarchy> familyFlattenHierarchies = await ProductFamilyFlattenHierarchyRepository
                        .GetListAsync(x => x.Id == productFamilyId && x.ProductId != null);

                    List<Guid> productIds = familyFlattenHierarchies.Select(x => x.ProductId!.Value).Distinct().ToList();
                    productsCount = productIds.Count;

                    List<Product> products = await ProductRepository.GetListAsync(x => productIds.Contains(x.Id));
                    foreach (Product product in products)
                    {
                        product.IsPublic = false;
                    }
                    await ProductRepository.UpdateManyAsync(products);
                });

                await WithUnitOfWorkAsync(async () =>
                {
                    // Act 
                    ProductFamilyDto result = await QuerySender.Send(new GetProductFamilyByIdQuery(productFamilyId));

                    // Assert
                    result.ProductsCount.ShouldBe(productsCount);
                });
            }
        }

    }

    [Fact]
    public async Task GetProductFamilyByIdQuery_Should_Return_Results_If_User_Is_Content_Manager_Not_Contains_Visible_Product_And_Not_Contains_Equipment()
    {
        _authorizationResultSuccess = true;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        Guid companyId = Guid.NewGuid();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.Internal.ToString())]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                int productsCount = 0;
                Guid productFamilyId = Guid.Empty;
                await WithUnitOfWorkAsync(async () =>
                {
                    // Arrange
                    productFamilyId = storeContext.ProductFamilyLinkedToProductIdByCodes.First().Value;

                    List<ProductFamilyFlattenHierarchy> familyFlattenHierarchies = await ProductFamilyFlattenHierarchyRepository
                        .GetListAsync(x => x.Id == productFamilyId && x.ProductId != null);

                    List<Guid> productIds = familyFlattenHierarchies.Select(x => x.ProductId!.Value).Distinct().ToList();
                    productsCount = productIds.Count;

                    List<Product> products = await ProductRepository.GetListAsync(x => productIds.Contains(x.Id));
                    foreach (Product product in products)
                    {
                        product.IsVisible = false;
                    }
                    await ProductRepository.UpdateManyAsync(products);
                });

                await WithUnitOfWorkAsync(async () =>
                {
                    // Act 
                    ProductFamilyDto result = await QuerySender.Send(new GetProductFamilyByIdQuery(productFamilyId));

                    // Assert
                    result.ProductsCount.ShouldBe(productsCount);
                });
            }
        }
    }

    [Fact]
    public async Task GetProductFamilyByIdQuery_Should_Return_Results_If_User_Is_Internal_Content_Viewer_Not_Contains_Public_Product_And_Not_Contains_Equipment()
    {
        _authorizationResultSuccess = false;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        Guid companyId = Guid.NewGuid();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.Internal.ToString())]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                int productsCount = 0;
                Guid productFamilyId = Guid.Empty;
                await WithUnitOfWorkAsync(async () =>
                {
                    // Arrange
                    productFamilyId = storeContext.ProductFamilyLinkedToProductIdByCodes.First().Value;

                    List<ProductFamilyFlattenHierarchy> familyFlattenHierarchies = await ProductFamilyFlattenHierarchyRepository
                        .GetListAsync(x => x.Id == productFamilyId && x.ProductId != null);

                    List<Guid> productIds = familyFlattenHierarchies.Select(x => x.ProductId!.Value).Distinct().ToList();
                    productsCount = productIds.Count;

                    List<Product> products = await ProductRepository.GetListAsync(x => productIds.Contains(x.Id));
                    foreach (Product product in products)
                    {
                        product.IsPublic = false;
                    }
                    await ProductRepository.UpdateManyAsync(products);
                });

                await WithUnitOfWorkAsync(async () =>
                {
                    // Act 
                    ProductFamilyDto result = await QuerySender.Send(new GetProductFamilyByIdQuery(productFamilyId));

                    // Assert
                    result.ProductsCount.ShouldBe(productsCount);
                });
            }
        }
    }

    [Fact]
    public async Task GetProductFamilyByIdQuery_Should_Throw_Exception_If_User_Is_Internal_Content_Viewer_Not_Contains_Visible_Product_And_Not_Contains_Equipment()
    {
        _authorizationResultSuccess = false;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true, GenerateEquipments = true, GenerateCompanies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        Guid companyId = storeContext.CompaniesInternal.First();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.Internal.ToString())]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                using (DataFilter.Enable<IHasVisibility>())
                {
                    Guid productFamilyId = Guid.Empty;
                    await WithUnitOfWorkAsync(async () =>
                    {
                        // Arrange
                        productFamilyId = storeContext.ProductFamilyLinkedToProductIdByCodes.First().Value;

                        List<ProductFamilyFlattenHierarchy> familyFlattenHierarchies = await ProductFamilyFlattenHierarchyRepository
                            .GetListAsync(x => x.Id == productFamilyId && x.ProductId != null);

                        List<Guid> productIds = familyFlattenHierarchies.Select(x => x.ProductId!.Value).Distinct().ToList();

                        List<Product> products = await ProductRepository.GetListAsync(x => productIds.Contains(x.Id));
                        foreach (Product product in products)
                        {
                            product.IsVisible = false;
                        }
                        await ProductRepository.UpdateManyAsync(products);
                    });

                    await WithUnitOfWorkAsync(async () =>
                    {
                        // Act & Assert
                        await QuerySender.Send(new GetProductFamilyByIdQuery(productFamilyId)).ShouldThrowAsync<AbpAuthorizationException>();
                    });
                }
            }
        }
    }

    [Fact]
    public async Task GetProductFamilyByIdQuery_Should_Return_Results_If_User_Is_External_Content_Viewer_Contains_Public_Product_And_Not_Contains_Equipment()
    {
        _authorizationResultSuccess = false;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        Guid companyId = Guid.NewGuid();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString())]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                using (DataFilter.Enable<IHasVisibility>())
                {
                    int productsCount = 0;
                    Guid productFamilyId = Guid.Empty;
                    await WithUnitOfWorkAsync(async () =>
                    {
                        // Arrange
                        productFamilyId = storeContext.ProductFamilyLinkedToProductIdByCodes.First().Value;

                        List<ProductFamilyFlattenHierarchy> familyFlattenHierarchies = await ProductFamilyFlattenHierarchyRepository
                            .GetListAsync(x => x.Id == productFamilyId && x.ProductId != null);

                        List<Guid> productIds = familyFlattenHierarchies.Select(x => x.ProductId!.Value).Distinct().ToList();
                        productsCount = productIds.Count;

                        List<Product> products = await ProductRepository.GetListAsync(x => productIds.Contains(x.Id));
                        foreach (Product product in products)
                        {
                            product.IsPublic = true;
                        }
                        await ProductRepository.UpdateManyAsync(products);
                    });

                    using (DataFilter.Enable<IHasPublic>())
                    {
                        await WithUnitOfWorkAsync(async () =>
                        {
                            // Act & Assert
                            ProductFamilyDto result = await QuerySender.Send(new GetProductFamilyByIdQuery(productFamilyId));

                            // Assert
                            result.ProductsCount.ShouldBe(productsCount);
                        });
                    }
                }
            }
        }
    }

    [Fact]
    public async Task GetProductFamilyByIdQuery_Should_Return_Result_If_User_Is_External_Content_Viewer_Not_Contains_Public_Product_And_Contains_Equipment()
    {
        _authorizationResultSuccess = false;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true, GenerateEquipments = true, GenerateCompanies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        Guid companyId = storeContext.CompaniesExternal.First();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString())]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                using (DataFilter.Enable<IHasVisibility>())
                {
                    int productsCount = 0;
                    Guid productFamilyId = Guid.Empty;
                    await WithUnitOfWorkAsync(async () =>
                    {
                        // Arrange
                        List<Guid> productWithEquipmentIds = (await EquipmentRepository.GetListAsync(x => x.CompanyId == companyId))
                            .Select(x => x.ProductId).Distinct().ToList();

                        productFamilyId = storeContext.ProductFamilyLinkedToProductIdByCodes.First().Value;

                        List<ProductFamilyFlattenHierarchy> familyFlattenHierarchies = await ProductFamilyFlattenHierarchyRepository
                            .GetListAsync(x => x.Id == productFamilyId && x.ProductId != null);

                        List<Guid> productIds = familyFlattenHierarchies.Select(x => x.ProductId!.Value).Distinct().ToList();
                        List<Product> products = await ProductRepository.GetListAsync(x => productIds.Contains(x.Id));

                        productsCount = products.Select(x => x.Id).Count(x => productWithEquipmentIds.Contains(x));

                        foreach (Product product in products)
                        {
                            product.IsPublic = false;
                        }
                        await ProductRepository.UpdateManyAsync(products);
                    });

                    using (DataFilter.Enable<IHasPublic>())
                    {
                        await WithUnitOfWorkAsync(async () =>
                        {
                            // Act 
                            ProductFamilyDto result = await QuerySender.Send(new GetProductFamilyByIdQuery(productFamilyId));

                            // Assert
                            result.ProductsCount.ShouldBe(productsCount);
                        });
                    }
                }
            }
        }
    }

    [Fact]
    public async Task GetProductFamilyByIdQuery_Should_Throw_Exception_If_User_Is_External_Content_Viewer_Not_Contains_Public_Product_And_Not_Contains_Equipment()
    {
        _authorizationResultSuccess = false;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true, GenerateCompanies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        Guid companyId = storeContext.CompaniesExternal.First();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString())]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                using (DataFilter.Enable<IHasVisibility>())
                {
                    Guid productFamilyId = Guid.Empty;
                    await WithUnitOfWorkAsync(async () =>
                    {
                        // Arrange
                        productFamilyId = storeContext.ProductFamilyLinkedToProductIdByCodes.First().Value;

                        List<ProductFamilyFlattenHierarchy> familyFlattenHierarchies = await ProductFamilyFlattenHierarchyRepository
                            .GetListAsync(x => x.Id == productFamilyId && x.ProductId != null);

                        List<Guid> productIds = familyFlattenHierarchies.Select(x => x.ProductId!.Value).Distinct().ToList();

                        List<Product> products = await ProductRepository.GetListAsync(x => productIds.Contains(x.Id));
                        foreach (Product product in products)
                        {
                            product.IsPublic = false;
                        }
                        await ProductRepository.UpdateManyAsync(products);
                    });

                    using (DataFilter.Enable<IHasPublic>())
                    {
                        await WithUnitOfWorkAsync(async () =>
                        {
                            // Act & Assert
                            await QuerySender.Send(new GetProductFamilyByIdQuery(productFamilyId)).ShouldThrowAsync<AbpAuthorizationException>();
                        });
                    }
                }
            }
        }
    }

    [Fact]
    public async Task GetProductFamilyByIdQuery_Should_Throw_Exception_If_User_Is_External_Content_Viewer_Not_Contains_Visible_Product_And_Not_Contains_Equipment()
    {
        _authorizationResultSuccess = false;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true, GenerateCompanies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        Guid companyId = storeContext.CompaniesExternal.First();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString())]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                Guid productFamilyId = Guid.Empty;
                await WithUnitOfWorkAsync(async () =>
                {
                    // Arrange
                    productFamilyId = storeContext.ProductFamilyLinkedToProductIdByCodes.First().Value;

                    List<ProductFamilyFlattenHierarchy> familyFlattenHierarchies = await ProductFamilyFlattenHierarchyRepository
                        .GetListAsync(x => x.Id == productFamilyId && x.ProductId != null);

                    List<Guid> productIds = familyFlattenHierarchies.Select(x => x.ProductId!.Value).Distinct().ToList();

                    List<Product> products = await ProductRepository.GetListAsync(x => productIds.Contains(x.Id));
                    foreach (Product product in products)
                    {
                        product.IsVisible = false;
                    }
                    await ProductRepository.UpdateManyAsync(products);
                });

                using (DataFilter.Enable<IHasVisibility>())
                {
                    using (DataFilter.Enable<IHasPublic>())
                    {
                        await WithUnitOfWorkAsync(async () =>
                        {
                            // Act & Assert
                            await QuerySender.Send(new GetProductFamilyByIdQuery(productFamilyId)).ShouldThrowAsync<AbpAuthorizationException>();
                        });
                    }
                }
            }
        }
    }

    [Fact]
    public async Task GetProductFamilyByIdQuery_Should_Throw_Exception_If_User_Is_External_Content_Viewer_Not_Contains_Visible_Product_And_Contains_Equipment()
    {
        _authorizationResultSuccess = false;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true, GenerateEquipments = true, GenerateCompanies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        Guid companyId = storeContext.CompaniesExternal.First();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString())]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                Guid productFamilyId = Guid.Empty;
                await WithUnitOfWorkAsync(async () =>
                {
                    // Arrange
                    productFamilyId = storeContext.ProductFamilyLinkedToProductIdByCodes.First().Value;

                    List<ProductFamilyFlattenHierarchy> familyFlattenHierarchies = await ProductFamilyFlattenHierarchyRepository
                        .GetListAsync(x => x.Id == productFamilyId && x.ProductId != null);

                    List<Guid> productIds = familyFlattenHierarchies.Select(x => x.ProductId!.Value).Distinct().ToList();

                    List<Product> products = await ProductRepository.GetListAsync(x => productIds.Contains(x.Id));
                    foreach (Product product in products)
                    {
                        product.IsVisible = false;
                    }
                    await ProductRepository.UpdateManyAsync(products);
                });

                using (DataFilter.Enable<IHasVisibility>())
                {
                    using (DataFilter.Enable<IHasPublic>())
                    {
                        await WithUnitOfWorkAsync(async () =>
                        {
                            //Act & Assert
                            await QuerySender.Send(new GetProductFamilyByIdQuery(productFamilyId)).ShouldThrowAsync<AbpAuthorizationException>();
                        });
                    }
                }
            }
        }
    }
}
