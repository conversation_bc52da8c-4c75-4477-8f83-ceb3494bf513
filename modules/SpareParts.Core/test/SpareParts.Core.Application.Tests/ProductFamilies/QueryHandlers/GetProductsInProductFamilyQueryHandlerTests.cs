using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using NSubstitute;
using Shouldly;
using SpareParts.Common;
using SpareParts.Common.Companies;
using SpareParts.Common.DataFilter;
using SpareParts.Core.Components.Dtos.Products;
using SpareParts.Core.DataVisibility;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.ProductFamilies;
using SpareParts.Core.ProductFamilies.Queries;
using SpareParts.Core.ProductFamilies.QueryFilters;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Authorization;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;
using Volo.Abp.Security.Claims;
using Xunit;

namespace SpareParts.Core.ProductFamilies.QueryHandlers;
public abstract class GetProductsInProductFamilyQueryHandlerTests<TStartupModule> : ProductFamilyTestsBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    private bool _authorizationResultSuccess;
    private IAbpAuthorizationService _authorizationService = default!;

    protected override void AfterAddApplication(IServiceCollection services)
    {
        _authorizationService = Substitute.For<IAbpAuthorizationService>();
        _authorizationService.AuthorizeAsync(Arg.Any<ClaimsPrincipal>(), Arg.Any<object?>(), Arg.Any<string>())
            .Returns(_ => _authorizationResultSuccess ? AuthorizationResult.Success() : AuthorizationResult.Failed());

        services.Replace(ServiceDescriptor.Singleton(_authorizationService));
    }

    [Fact]
    public async Task GetProductsInProductFamilyQuery_Should_Return_Paginated_Products()
    {
        _authorizationResultSuccess = true;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true, GenerateAssemblies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid id = storeContext.ProductFamilyLinkedToProductIdByCodes.First().Value;
                List<ProductInProductFamily> productsInProductFamily = await ProductInProductFamilyRepository.GetListAsync(x => x.ProductFamilyId == id);

                List<Guid> productIds = productsInProductFamily.Select(x => x.ProductId).ToList();
                List<Product> products = await ProductRepository.GetListAsync(x => productIds.Contains(x.Id));
                Product product = products.First();
                int childCount = await BomLineRepository.CountAsync(x => x.ParentAssemblyId == product.Id);

                // Act
                ProductsInProductFamilyPaginationFilter filter = new() { Page = 1, PerPage = 1, Sort = "Code", SortBy = AutoFilterer.Enums.Sorting.Ascending };
                PagedResultDto<ProductInProductFamilyDto> result = await QuerySender.Send(new GetProductsInProductFamilyQuery(id, filter));

                // Assert
                result.TotalCount.ShouldBe(products.Count);
                result.Items.Count.ShouldBe(1);

                ProductInProductFamilyDto productFamilyDto = result.Items.First(x => x.Id == product.Id);

                productFamilyDto.Code.ShouldBe(product.Component.Code);
                productFamilyDto.IsVisible.ShouldBe(product.IsVisible);
                productFamilyDto.IsPublic.ShouldBe(product.IsPublic);
                productFamilyDto.CreationTime.ShouldBe(product.CreationTime);
                productFamilyDto.CreatorId.ShouldBe(product.CreatorId);
                productFamilyDto.LastModificationTime.ShouldBe(product.LastModificationTime);
                productFamilyDto.LastModifierId.ShouldBe(product.LastModifierId);
                productFamilyDto.ImageId.ShouldBe(product.Component.ImageId);
                int rank = productsInProductFamily.First(x => x.ProductId == product.Id).Rank;
                productFamilyDto.Rank.ShouldBe(rank);
                productFamilyDto.ChildCount.ShouldBe(childCount);
                productFamilyDto.IsInProductFamily.ShouldBeTrue();
                productFamilyDto.Translations.Count.ShouldBe(product.Component.Translations.Count);
                productFamilyDto.Translations[0].Language.ShouldBe(product.Component.Translations.First().Language);
                productFamilyDto.Translations[0].Label.ShouldBe(product.Component.Translations.First().Label);
                productFamilyDto.Translations[0].Description.ShouldBe(product.Component.Translations.First().Description);
                productFamilyDto.OnlineTranslations.Count.ShouldBe(product.Component.OnlineTranslations.Count);
                productFamilyDto.OnlineTranslations[0].Language.ShouldBe(product.Component.OnlineTranslations.First().Language);
                productFamilyDto.OnlineTranslations[0].Label.ShouldBe(product.Component.OnlineTranslations.First().Label);
                productFamilyDto.OnlineTranslations[0].Description.ShouldBe(product.Component.OnlineTranslations.First().Description);
            });
        }
    }

    [Fact]
    public async Task GetProductsInProductFamilyQuery_Should_Return_Sorted_Products_By_Code()
    {
        _authorizationResultSuccess = true;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid id = storeContext.ProductFamilyLinkedToProductIdByCodes.First().Value;
                ProductFamily productFamily = await ProductFamilyRepository.GetAsync(id);

                // Act
                ProductsInProductFamilyPaginationFilter productsInProductFamilyPaginationFilter = new()
                {
                    Sort = "Code"
                };

                PagedResultDto<ProductInProductFamilyDto> result = await QuerySender.Send(
                    new GetProductsInProductFamilyQuery(productFamily.Id, productsInProductFamilyPaginationFilter));

                // Assert
                IOrderedEnumerable<ProductDto> expectedList = result.Items.OrderBy(x => x.Code);
                expectedList.SequenceEqual(result.Items).ShouldBeTrue();
            });
        }
    }

    [Fact]
    public async Task GetProductsInProductFamilyQuery_Should_Throw_Exception_If_ProductFamily_Not_Exist()
    {
        _authorizationResultSuccess = true;
        using (CurrentTenant.Change(GuidGenerator.Create()))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                Guid companyId = Guid.NewGuid();
                using (CurrentPrincipalAccessor.Change([
                           new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                           new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString())]))
                {
                    // Assert
                    await QuerySender.Send(new GetProductsInProductFamilyQuery(GuidGenerator.Create(), new ProductsInProductFamilyPaginationFilter()))
                        .ShouldThrowAsync<EntityNotFoundException>();
                }
            });
        }
    }

    [Fact]
    public async Task GetProductsInProductFamilyQuery_Should_Throw_AbpAuthorizationException_If_CompanyId_Is_Null()
    {
        _authorizationResultSuccess = false;

        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, string.Empty),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString())]))
        {
            using (CurrentTenant.Change(GuidGenerator.Create()))
            {
                await WithUnitOfWorkAsync(async () =>
                {
                    // Act & Assert
                    await QuerySender.Send(new GetProductsInProductFamilyQuery(GuidGenerator.Create(), new ProductsInProductFamilyPaginationFilter()))
                    .ShouldThrowAsync<AbpAuthorizationException>();
                });
            }
        }
    }

    [Fact]
    public async Task GetProductsInProductFamilyQuery_Should_Return_Results_If_User_Is_Content_Manager_Not_Contains_Public_Product_And_Not_Contains_Equipment()
    {
        _authorizationResultSuccess = true;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        Guid companyId = Guid.NewGuid();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.Internal.ToString())]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                Guid productFamilyId = Guid.Empty;
                int productsCount = 0;
                await WithUnitOfWorkAsync(async () =>
                {
                    // Arrange
                    productFamilyId = storeContext.ProductFamilyLinkedToProductIdByCodes.First().Value;
                    List<ProductInProductFamily> productsInProductFamily = await ProductInProductFamilyRepository.GetListAsync(x => x.ProductFamilyId == productFamilyId);

                    List<Guid> productIds = productsInProductFamily.Select(x => x.ProductId).ToList();
                    productsCount = await ProductRepository.CountAsync(x => productIds.Contains(x.Id));

                    List<Product> products = await ProductRepository.GetListAsync(x => productIds.Contains(x.Id));
                    foreach (Product product in products)
                    {
                        product.IsPublic = false;
                    }
                    await ProductRepository.UpdateManyAsync(products);
                });

                await WithUnitOfWorkAsync(async () =>
                {
                    // Act 
                    PagedResultDto<ProductInProductFamilyDto> results = await QuerySender.Send(new GetProductsInProductFamilyQuery(productFamilyId, new ProductsInProductFamilyPaginationFilter()));

                    // Assert
                    results.TotalCount.ShouldBe(productsCount);
                });
            }
        }
    }

    [Fact]
    public async Task GetProductsInProductFamilyQuery_Should_Return_Results_If_User_Is_Content_Manager_Not_Contains_Visible_Product_And_Not_Contains_Equipment()
    {
        _authorizationResultSuccess = true;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        Guid companyId = Guid.NewGuid();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.Internal.ToString())]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                Guid productFamilyId = Guid.Empty;
                int productsCount = 0;
                await WithUnitOfWorkAsync(async () =>
                {
                    // Arrange
                    productFamilyId = storeContext.ProductFamilyLinkedToProductIdByCodes.First().Value;
                    List<ProductInProductFamily> productsInProductFamily = await ProductInProductFamilyRepository.GetListAsync(x => x.ProductFamilyId == productFamilyId);

                    List<Guid> productIds = productsInProductFamily.Select(x => x.ProductId).ToList();
                    productsCount = await ProductRepository.CountAsync(x => productIds.Contains(x.Id));

                    List<Product> products = await ProductRepository.GetListAsync(x => productIds.Contains(x.Id));
                    foreach (Product product in products)
                    {
                        product.IsVisible = false;
                    }
                    await ProductRepository.UpdateManyAsync(products);
                });

                await WithUnitOfWorkAsync(async () =>
                {
                    // Act 
                    PagedResultDto<ProductInProductFamilyDto> results = await QuerySender.Send(new GetProductsInProductFamilyQuery(productFamilyId, new ProductsInProductFamilyPaginationFilter()));

                    // Assert
                    results.TotalCount.ShouldBe(productsCount);
                });
            }
        }
    }

    [Fact]
    public async Task GetProductsInProductFamilyQuery_Should_Return_Results_If_User_Is_Internal_Content_Viewer_Not_Contains_Public_Product_And_Not_Contains_Equipment()
    {
        _authorizationResultSuccess = false;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        Guid companyId = Guid.NewGuid();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.Internal.ToString())]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                Guid productFamilyId = Guid.Empty;
                int productsCount = 0;
                await WithUnitOfWorkAsync(async () =>
                {
                    // Arrange
                    productFamilyId = storeContext.ProductFamilyLinkedToProductIdByCodes.First().Value;
                    List<ProductInProductFamily> productsInProductFamily = await ProductInProductFamilyRepository.GetListAsync(x => x.ProductFamilyId == productFamilyId);

                    List<Guid> productIds = productsInProductFamily.Select(x => x.ProductId).ToList();
                    productsCount = await ProductRepository.CountAsync(x => productIds.Contains(x.Id));

                    List<Product> products = await ProductRepository.GetListAsync(x => productIds.Contains(x.Id));
                    foreach (Product product in products)
                    {
                        product.IsPublic = false;
                    }
                    await ProductRepository.UpdateManyAsync(products);
                });

                await WithUnitOfWorkAsync(async () =>
                {
                    // Act 
                    PagedResultDto<ProductInProductFamilyDto> results = await QuerySender.Send(new GetProductsInProductFamilyQuery(productFamilyId, new ProductsInProductFamilyPaginationFilter()));

                    // Assert
                    results.TotalCount.ShouldBe(productsCount);
                });
            }
        }
    }

    [Fact]
    public async Task GetProductsInProductFamilyQuery_Should_Throw_Exception_If_User_Is_Internal_Content_Viewer_Not_Contains_Visible_Product_And_Not_Contains_Equipment()
    {
        _authorizationResultSuccess = false;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true, GenerateEquipments = true, GenerateCompanies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        Guid companyId = storeContext.CompaniesInternal.First();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.Internal.ToString())]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                using (DataFilter.Enable<IHasVisibility>())
                {
                    Guid productFamilyId = Guid.Empty;
                    await WithUnitOfWorkAsync(async () =>
                    {
                        productFamilyId = storeContext.ProductFamilyLinkedToProductIdByCodes.First().Value;
                        List<ProductInProductFamily> productsInProductFamily = await ProductInProductFamilyRepository.GetListAsync(x => x.ProductFamilyId == productFamilyId);

                        List<Guid> productIds = productsInProductFamily.Select(x => x.ProductId).ToList();

                        List<Product> products = await ProductRepository.GetListAsync(x => productIds.Contains(x.Id));
                        foreach (Product product in products)
                        {
                            product.IsVisible = false;
                        }
                        await ProductRepository.UpdateManyAsync(products);
                    });

                    await WithUnitOfWorkAsync(async () =>
                    {
                        // Act & Assert
                        await QuerySender.Send(new GetProductsInProductFamilyQuery(productFamilyId, new ProductsInProductFamilyPaginationFilter()))
                        .ShouldThrowAsync<AbpAuthorizationException>();
                    });
                }
            }
        }
    }

    [Fact]
    public async Task GetProductsInProductFamilyQuery_Should_Return_Results_If_User_Is_External_Content_Viewer_Contains_Public_Product_And_Not_Contains_Equipment()
    {
        _authorizationResultSuccess = false;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        Guid companyId = Guid.NewGuid();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString())]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                using (DataFilter.Enable<IHasVisibility>())
                {
                    int productsCount = 0;
                    Guid productFamilyId = Guid.Empty;
                    await WithUnitOfWorkAsync(async () =>
                    {
                        // Arrange
                        productFamilyId = storeContext.ProductFamilyLinkedToProductIdByCodes.First().Value;
                        List<ProductInProductFamily> productsInProductFamily = await ProductInProductFamilyRepository.GetListAsync(x => x.ProductFamilyId == productFamilyId);

                        List<Guid> productIds = productsInProductFamily.Select(x => x.ProductId).ToList();

                        List<Product> products = await ProductRepository.GetListAsync(x => productIds.Contains(x.Id));
                        foreach (Product product in products)
                        {
                            product.IsPublic = true;
                        }
                        await ProductRepository.UpdateManyAsync(products);
                        productsCount = products.Count;
                    });

                    using (DataFilter.Enable<IHasPublic>())
                    {
                        await WithUnitOfWorkAsync(async () =>
                            {
                                // Act & Assert
                                PagedResultDto<ProductInProductFamilyDto> results = await QuerySender.Send(new GetProductsInProductFamilyQuery(productFamilyId, new ProductsInProductFamilyPaginationFilter()));

                                // Assert
                                results.TotalCount.ShouldBe(productsCount);
                            });
                    }
                }
            }
        }
    }

    [Fact]
    public async Task GetProductsInProductFamilyQuery_Should_Return_Empty_If_User_Is_External_Content_Viewer_Not_Contains_Public_Product_And_Contains_Equipment()
    {
        _authorizationResultSuccess = false;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true, GenerateEquipments = true, GenerateCompanies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        Guid companyId = storeContext.CompaniesExternal.First();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString())]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                using (DataFilter.Enable<IHasVisibility>())
                {
                    Guid productFamilyId = Guid.Empty;
                    await WithUnitOfWorkAsync(async () =>
                    {
                        productFamilyId = storeContext.ProductFamilyLinkedToProductIdByCodes.First().Value;
                        List<ProductInProductFamily> productsInProductFamily = await ProductInProductFamilyRepository.GetListAsync(x => x.ProductFamilyId == productFamilyId);

                        List<Guid> productIds = productsInProductFamily.Select(x => x.ProductId).ToList();

                        List<Product> products = await ProductRepository.GetListAsync(x => productIds.Contains(x.Id));
                        foreach (Product product in products)
                        {
                            product.IsPublic = false;
                        }
                        await ProductRepository.UpdateManyAsync(products);
                    });

                    using (DataFilter.Enable<IHasPublic>())
                    {
                        await WithUnitOfWorkAsync(async () =>
                            {
                                // Act 
                                PagedResultDto<ProductInProductFamilyDto> results = await QuerySender.Send(new GetProductsInProductFamilyQuery(productFamilyId, new ProductsInProductFamilyPaginationFilter()));

                                // Assert
                                results.TotalCount.ShouldBe(0);
                            });
                    }
                }
            }
        }
    }

    [Fact]
    public async Task GetProductsInProductFamilyQuery_Should_Throw_Exception_If_User_Is_External_Content_Viewer_Not_Contains_Public_Product_And_Not_Contains_Equipment()
    {
        _authorizationResultSuccess = false;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true, GenerateCompanies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        Guid companyId = storeContext.CompaniesExternal.First();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString())]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                using (DataFilter.Enable<IHasVisibility>())
                {
                    Guid productFamilyId = Guid.Empty;
                    await WithUnitOfWorkAsync(async () =>
                    {
                        productFamilyId = storeContext.ProductFamilyLinkedToProductIdByCodes.First().Value;
                        List<ProductInProductFamily> productsInProductFamily = await ProductInProductFamilyRepository.GetListAsync(x => x.ProductFamilyId == productFamilyId);

                        List<Guid> productIds = productsInProductFamily.Select(x => x.ProductId).ToList();

                        List<Product> products = await ProductRepository.GetListAsync(x => productIds.Contains(x.Id));
                        foreach (Product product in products)
                        {
                            product.IsPublic = false;
                        }
                        await ProductRepository.UpdateManyAsync(products);
                    });

                    using (DataFilter.Enable<IHasPublic>())
                    {
                        await WithUnitOfWorkAsync(async () =>
                                   {
                                       // Act & Assert
                                       await QuerySender.Send(new GetProductsInProductFamilyQuery(productFamilyId, new ProductsInProductFamilyPaginationFilter()))
                                       .ShouldThrowAsync<AbpAuthorizationException>();
                                   });
                    }
                }
            }
        }
    }

    [Fact]
    public async Task GetProductsInProductFamilyQuery_Should_Throw_Exception_If_User_Is_External_Content_Viewer_Not_Contains_Visible_Product_And_Not_Contains_Equipment()
    {
        _authorizationResultSuccess = false;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true, GenerateCompanies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        Guid companyId = storeContext.CompaniesExternal.First();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString())]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                Guid productFamilyId = Guid.Empty;
                await WithUnitOfWorkAsync(async () =>
                {
                    productFamilyId = storeContext.ProductFamilyLinkedToProductIdByCodes.First().Value;
                    List<ProductInProductFamily> productsInProductFamily = await ProductInProductFamilyRepository.GetListAsync(x => x.ProductFamilyId == productFamilyId);

                    List<Guid> productIds = productsInProductFamily.Select(x => x.ProductId).ToList();

                    List<Product> products = await ProductRepository.GetListAsync(x => productIds.Contains(x.Id));
                    foreach (Product product in products)
                    {
                        product.IsVisible = false;
                    }
                    await ProductRepository.UpdateManyAsync(products);
                });

                using (DataFilter.Enable<IHasVisibility>())
                {
                    using (DataFilter.Enable<IHasPublic>())
                    {
                        await WithUnitOfWorkAsync(async () =>
                            {
                                // Act & Assert
                                await QuerySender.Send(new GetProductsInProductFamilyQuery(productFamilyId, new ProductsInProductFamilyPaginationFilter()))
                                .ShouldThrowAsync<AbpAuthorizationException>();
                            });
                    }
                }
            }
        }
    }

    [Fact]
    public async Task GetProductsInProductFamilyQuery_Should_Return_Empty_If_User_Is_External_Content_Viewer_Not_Contains_Visible_Product_And_Contains_Equipment()
    {
        _authorizationResultSuccess = false;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true, GenerateEquipments = true, GenerateCompanies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        Guid companyId = storeContext.CompaniesExternal.First();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString())]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                Guid productFamilyId = Guid.Empty;
                await WithUnitOfWorkAsync(async () =>
                {
                    productFamilyId = storeContext.ProductFamilyLinkedToProductIdByCodes.First().Value;
                    List<ProductInProductFamily> productsInProductFamily = await ProductInProductFamilyRepository.GetListAsync(x => x.ProductFamilyId == productFamilyId);

                    List<Guid> productIds = productsInProductFamily.Select(x => x.ProductId).ToList();

                    List<Product> products = await ProductRepository.GetListAsync(x => productIds.Contains(x.Id));
                    foreach (Product product in products)
                    {
                        product.IsVisible = false;
                    }
                    await ProductRepository.UpdateManyAsync(products);
                });

                using (DataFilter.Enable<IHasVisibility>())
                {
                    using (DataFilter.Enable<IHasPublic>())
                    {
                        await WithUnitOfWorkAsync(async () =>
                            {
                                // Act & Assert
                                await QuerySender.Send(new GetProductsInProductFamilyQuery(productFamilyId, new ProductsInProductFamilyPaginationFilter()))
                                .ShouldThrowAsync<AbpAuthorizationException>();
                            });
                    }
                }
            }
        }
    }

    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    public async Task GetProductsInProductFamilyQuery_Should_Return_Products_Filter_By_IsPublic(bool isVisible)
    {
        _authorizationResultSuccess = true;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid id = storeContext.ProductFamilyLinkedToProductIdByCodes.First().Value;
                ProductFamily productFamily = await ProductFamilyRepository.GetAsync(id);

                // Act
                ProductsInProductFamilyPaginationFilter productsInProductFamilyPaginationFilter = new()
                {
                    IsPublic = isVisible
                };

                PagedResultDto<ProductInProductFamilyDto> result = await QuerySender.Send(
                    new GetProductsInProductFamilyQuery(productFamily.Id, productsInProductFamilyPaginationFilter));

                // Assert
                List<ProductInProductFamilyDto> unfilteredResult = result.Items.Where(x => x.IsPublic != isVisible).ToList();
                unfilteredResult.Count.ShouldBe(0);
            });
        }
    }

    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    public async Task GetProductsInProductFamilyQuery_Should_Return_Products_Filter_By_IsVisible(bool isVisible)
    {
        _authorizationResultSuccess = true;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid id = storeContext.ProductFamilyLinkedToProductIdByCodes.First().Value;
                ProductFamily productFamily = await ProductFamilyRepository.GetAsync(id);

                // Act
                ProductsInProductFamilyPaginationFilter productsInProductFamilyPaginationFilter = new()
                {
                    IsVisible = isVisible
                };

                PagedResultDto<ProductInProductFamilyDto> result = await QuerySender.Send(
                    new GetProductsInProductFamilyQuery(productFamily.Id, productsInProductFamilyPaginationFilter));

                // Assert
                List<ProductInProductFamilyDto> unfilteredResult = result.Items.Where(x => x.IsVisible != isVisible).ToList();
                unfilteredResult.Count.ShouldBe(0);
            });
        }
    }

    [Fact]
    public async Task GetProductsInProductFamilyQuery_Should_Return_Products_Filter_By_Keyword()
    {
        _authorizationResultSuccess = true;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid productFamilyId = storeContext.ProductFamilyIdWithProductIds.First().Key;
                Guid productId = storeContext.ProductFamilyIdWithProductIds.First().Value.First();
                Product product = await ProductRepository.GetAsync(productId);

                // Act
                ProductsInProductFamilyPaginationFilter productsInProductFamilyPaginationFilter = new()
                {
                    Keyword = product.Component.Translations.First().Label
                };

                PagedResultDto<ProductInProductFamilyDto> result = await QuerySender.Send(
                    new GetProductsInProductFamilyQuery(productFamilyId, productsInProductFamilyPaginationFilter));

                // Assert
                result.TotalCount.ShouldBe(1);
                ProductInProductFamilyDto productResult = result.Items[0];
                productResult.Id.ShouldBe(productId);
            });
        }
    }
}