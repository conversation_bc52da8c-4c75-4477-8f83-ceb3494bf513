using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Shouldly;
using SpareParts.Common;
using SpareParts.Common.Companies;
using SpareParts.Common.DataFilter;
using SpareParts.Core.DataVisibility;
using SpareParts.Core.Entities.ProductFamilies;
using SpareParts.Core.ProductFamilies.Dtos;
using SpareParts.Core.ProductFamilies.Queries;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;
using Volo.Abp.Security.Claims;
using Xunit;

namespace SpareParts.Core.ProductFamilies.QueryHandlers;

public abstract class GetProductFamilyHierarchyByIdsQueryHandlerTests<TStartupModule> : ProductFamilyTestsBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    [Fact]
    public async Task GetProductFamilyByIdQuery_Should_Return_ProductFamily()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid grandParentId = storeContext.ProductFamilyIdByCodes["productFamily-1"];
                Guid parentId = storeContext.ProductFamilyIdByCodes["productFamily-1-4"];
                List<Guid> sonIds = storeContext.ProductFamilyHierarchy[parentId];

                ProductFamily grandParentProductFamily = await ProductFamilyRepository.FirstAsync(x => x.Id == grandParentId);

                // Act
                Dictionary<Guid, List<ProductFamilyHierarchyDto>> result = await QuerySender.Send(new GetProductFamilyHierarchyByIdsQuery(sonIds.ToHashSet()));

                // Assert
                result.Count.ShouldBe(sonIds.Count);

                List<ProductFamilyHierarchyDto> firstGrandSon = result[sonIds[0]];
                firstGrandSon.Count.ShouldBe(3);

                ProductFamilyHierarchyDto grandParentDto = firstGrandSon[0];
                grandParentDto.Id.ShouldBe(grandParentProductFamily.Id);
                grandParentDto.ImageId.ShouldBe(grandParentProductFamily.ImageId);
                grandParentDto.Translations.Count.ShouldBe(grandParentProductFamily.Translations.Count);

                ProductFamilyHierarchyDto parentDto = firstGrandSon[1];
                parentDto.Id.ShouldBe(parentId);
                ProductFamilyHierarchyDto childDto = firstGrandSon[2];
                childDto.Id.ShouldBe(sonIds[0]);
            });
        }
    }

    [Fact]
    public async Task GetProductFamilyByIdQuery_Should_Return_Empty_Result_If_ProductFamilies_Not_Exist()
    {
        TestDataContext context = new(GuidGenerator.Create());
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Act
                Dictionary<Guid, List<ProductFamilyHierarchyDto>> result = await QuerySender.Send(new GetProductFamilyHierarchyByIdsQuery([Guid.NewGuid()]));

                // Assert
                result.Count.ShouldBe(0);
            });
        }
    }

    [Fact]
    public async Task GetProductFamilyByIdQuery_Should_Not_Return_Result_If_User_Is_External_Content_Viewer_And_ProductFamily_Empty_And_Public_Filter_Enable()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        Guid companyId = Guid.NewGuid();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString())]))
        {
            using (DataFilter.Enable<IHasPublic>())
            {
                using (CurrentTenant.Change(context.TenantId))
                {
                    await WithUnitOfWorkAsync(async () =>
                    {
                        // Arrange
                        Guid id = storeContext.ProductFamilyIdByCodes["productFamily-1-4-1"];

                        // Act & Assert
                        Dictionary<Guid, List<ProductFamilyHierarchyDto>> result = await QuerySender.Send(new GetProductFamilyHierarchyByIdsQuery([id]));

                        // Assert
                        result.Count.ShouldBe(0);

                    });
                }
            }
        }
    }

    [Fact]
    public async Task GetProductFamilyByIdQuery_Should_Not_Return_Result_If_User_Is_External_Content_Viewer_And_ProductFamily_Empty_And_Visibility_Filter_Enable()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        Guid companyId = Guid.NewGuid();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString())]))
        {
            using (DataFilter.Enable<IHasVisibility>())
            {
                using (CurrentTenant.Change(context.TenantId))
                {
                    await WithUnitOfWorkAsync(async () =>
                    {
                        // Arrange
                        Guid id = storeContext.ProductFamilyIdByCodes["productFamily-1-4-1"];

                        // Act & Assert
                        Dictionary<Guid, List<ProductFamilyHierarchyDto>> result = await QuerySender.Send(new GetProductFamilyHierarchyByIdsQuery([id]));

                        // Assert
                        result.Count.ShouldBe(0);

                    });
                }
            }
        }
    }
}