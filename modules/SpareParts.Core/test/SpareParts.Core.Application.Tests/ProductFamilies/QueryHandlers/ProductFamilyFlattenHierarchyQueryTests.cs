using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Shouldly;
using SpareParts.Common.DataFilter;
using SpareParts.Core.DataVisibility;
using SpareParts.Core.DomainServices.ProductFamilies;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.ProductFamilies;
using Volo.Abp.Data;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Core.ProductFamilies.QueryHandlers;

public abstract class ProductFamilyFlattenHierarchyQueryTests<TStartupModule> : ProductFamilyTestsBase<TStartupModule> where TStartupModule : IAbpModule
{
    private readonly IDataFilter _dataFilter;
    private readonly ProductFamilyDomainService _productFamilyDomainService;

    protected ProductFamilyFlattenHierarchyQueryTests()
    {
        _dataFilter = ServiceProvider.GetRequiredService<IDataFilter>();
        _productFamilyDomainService = ServiceProvider.GetRequiredService<ProductFamilyDomainService>();
    }

    [Fact]
    public async Task Should_Return_A_ProductFamilyFlattenHierarchy()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid id = storeContext.ProductFamilyLinkedToProductIdByCodes.First().Value;
                ProductFamily productFamily = await ProductFamilyRepository.FirstAsync(x => x.Id == id);

                // Act
                ProductFamilyFlattenHierarchy productFamilyFlattenHierarchy = await ProductFamilyFlattenHierarchyRepository.FirstAsync(x => x.Id == id);

                // Assert
                Product product = await ProductRepository.GetAsync(productFamilyFlattenHierarchy.ProductId!.Value);
                productFamilyFlattenHierarchy.ShouldNotBeNull();
                productFamilyFlattenHierarchy.TenantId.ShouldBe(productFamily.TenantId);
                productFamilyFlattenHierarchy.Id.ShouldBe(productFamily.Id);
                productFamilyFlattenHierarchy.ParentId.ShouldBe(productFamily.ParentId);
                productFamilyFlattenHierarchy.IsVisible.ShouldBe(product.IsVisible);
                productFamilyFlattenHierarchy.IsPublic.ShouldBe(product.IsPublic);
                productFamilyFlattenHierarchy.GetKeys().ShouldBe([productFamilyFlattenHierarchy.Id, productFamilyFlattenHierarchy.ProductId, productFamilyFlattenHierarchy.Path]);
                if (productFamily.ParentId.HasValue)
                {
                    productFamilyFlattenHierarchy.Path.ShouldBe("/" + productFamily.ParentId.Value.ToString().ToUpper() + "/");
                }
                else
                {
                    productFamilyFlattenHierarchy.Path.ShouldBe("/");
                }
            });
        }
    }

    [Fact]
    public async Task Should_Return_A_Result_When_Sub_ProductFamily_Has_Product()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        using (CurrentTenant.Change(context.TenantId))
        {
            Guid id = storeContext.ProductFamilyWithChildIdByCodes.First().Value;

            await WithUnitOfWorkAsync(async () =>
            {
                //Arrange
                IQueryable<ProductFamily> query = await ProductFamilyRepository.WithDetailsAsync(x => x.Products, x => x.SubProductFamilies);
                ProductFamily productFamily = query.First(x => x.Id == id);

                foreach (Product product in productFamily.Products)
                {
                    await ProductRepository.UpdateAsync(product);
                }
            });

            await WithUnitOfWorkAsync(async () =>
            {
                // Act
                List<ProductFamilyFlattenHierarchy> productFamilyFlattenHierarchies =
                    await ProductFamilyFlattenHierarchyRepository.GetListAsync();

                // Assert
                productFamilyFlattenHierarchies.Count(x => x.Id == id).ShouldBeGreaterThan(0);
                productFamilyFlattenHierarchies.Count(x => x.Path.Contains(id.ToString(), StringComparison.CurrentCultureIgnoreCase)).ShouldBeGreaterThan(0);
            });
        }
    }

    [Fact]
    public async Task Should_Not_Return_Results_Of_Other_Tenant()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        await SeedDataForTenantAsync(context);

        using (CurrentTenant.Change(GuidGenerator.Create()))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Act
                List<ProductFamilyFlattenHierarchy> productFamilyFlattenHierarchies =
                    await ProductFamilyFlattenHierarchyRepository.GetListAsync();

                // Assert
                productFamilyFlattenHierarchies.Count.ShouldBe(0);
            });
        }
    }

    [Fact]
    public async Task Should_Return_Result_ProductFamily_Without_Product()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        using (CurrentTenant.Change(context.TenantId))
        {
            Guid id = storeContext.ProductFamilyLinkedToProductIdByCodes.First().Value;

            await WithUnitOfWorkAsync(async () =>
            {
                //Arrange
                IQueryable<ProductFamily> query = await ProductFamilyRepository.WithDetailsAsync(x => x.Products, x => x.SubProductFamilies);
                ProductFamily productFamily = query.First(x => x.Id == id);

                foreach (Product product in productFamily.Products)
                {
                    await ProductRepository.DeleteAsync(p => p.Id == product.Id);
                }

                await ProductFamilyRepository.DeleteManyAsync(productFamily.SubProductFamilies.Select(p => p.Id));
            });

            await WithUnitOfWorkAsync(async () =>
            {
                // Act
                List<ProductFamilyFlattenHierarchy> productFamilyFlattenHierarchies =
                    await ProductFamilyFlattenHierarchyRepository.GetListAsync(x => x.Id == id);

                // Assert
                productFamilyFlattenHierarchies.Count.ShouldBe(1);
                productFamilyFlattenHierarchies[0].ProductId.ShouldBeNull();
            });
        }
    }

    [Fact]
    public async Task Should_Not_Return_Result_If_ProductFamily_Has_No_Public_Product()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        using (CurrentTenant.Change(context.TenantId))
        {
            Guid id = storeContext.ProductFamilyLinkedToProductIdByCodes.First().Value;

            await WithUnitOfWorkAsync(async () =>
            {
                //Arrange
                IQueryable<ProductFamily> query = await ProductFamilyRepository.WithDetailsAsync(x => x.Products, x => x.SubProductFamilies);
                ProductFamily productFamily = query.First(x => x.Id == id);

                foreach (Product product in productFamily.Products)
                {
                    product.IsPublic = false;
                    await ProductRepository.UpdateAsync(product);
                }

                await ProductFamilyRepository.DeleteManyAsync(productFamily.SubProductFamilies.Select(p => p.Id));
            });

            await WithUnitOfWorkAsync(async () =>
            {
                using (_dataFilter.Enable<IHasPublic>())
                {
                    // Act
                    List<ProductFamilyFlattenHierarchy> productFamilyFlattenHierarchies =
                        await ProductFamilyFlattenHierarchyRepository.GetListAsync(x => x.Id == id);

                    // Assert
                    productFamilyFlattenHierarchies.Count.ShouldBe(0);
                }
            });
        }
    }

    [Fact]
    public async Task Should_Not_Return_Result_If_ProductFamily_Has_No_Visible_Product()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        using (CurrentTenant.Change(context.TenantId))
        {
            Guid id = storeContext.ProductFamilyLinkedToProductIdByCodes.First().Value;

            await WithUnitOfWorkAsync(async () =>
            {
                //Arrange
                IQueryable<ProductFamily> query = await ProductFamilyRepository.WithDetailsAsync(x => x.Products, x => x.SubProductFamilies);
                ProductFamily productFamily = query.First(x => x.Id == id);

                foreach (Product product in productFamily.Products)
                {
                    product.IsVisible = false;
                    await ProductRepository.UpdateAsync(product);
                }

                await ProductFamilyRepository.DeleteManyAsync(productFamily.SubProductFamilies.Select(p => p.Id));
            });

            await WithUnitOfWorkAsync(async () =>
            {
                using (_dataFilter.Enable<IHasVisibility>())
                {
                    // Act
                    List<ProductFamilyFlattenHierarchy> productFamilyFlattenHierarchies =
                        await ProductFamilyFlattenHierarchyRepository.GetListAsync(x => x.Id == id);

                    // Assert
                    productFamilyFlattenHierarchies.Count.ShouldBe(0);
                }
            });
        }
    }

    [Fact]
    public async Task Should_Return_Result_If_ProductFamily_Has_At_Least_One_Public_Product()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        using (CurrentTenant.Change(context.TenantId))
        {
            Guid id = storeContext.ProductFamilyLinkedToProductIdByCodes.First().Value;

            await WithUnitOfWorkAsync(async () =>
            {
                //Arrange
                IQueryable<ProductFamily> query = await ProductFamilyRepository.WithDetailsAsync(x => x.Products, x => x.SubProductFamilies);
                ProductFamily productFamily = query.First(x => x.Id == id);

                for (int i = 0; i < productFamily.Products.Count; i++)
                {
                    Product product = await ProductRepository.GetAsync(productFamily.Products[i].Id);
                    product.IsPublic = i < 1;
                    await ProductRepository.UpdateAsync(product);
                }

                await ProductFamilyRepository.DeleteManyAsync(productFamily.SubProductFamilies.Select(p => p.Id));
            });

            await WithUnitOfWorkAsync(async () =>
            {
                using (_dataFilter.Enable<IHasPublic>())
                {
                    // Act
                    List<ProductFamilyFlattenHierarchy> productFamilyFlattenHierarchies =
                        await ProductFamilyFlattenHierarchyRepository.GetListAsync(x => x.Id == id);

                    // Assert
                    productFamilyFlattenHierarchies.Count.ShouldBeGreaterThan(0);
                }
            });
        }
    }

    [Fact]
    public async Task Should_Return_Result_If_ProductFamily_Has_At_Least_One_Visible_Product()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        using (CurrentTenant.Change(context.TenantId))
        {
            Guid id = storeContext.ProductFamilyLinkedToProductIdByCodes.First().Value;

            await WithUnitOfWorkAsync(async () =>
            {
                //Arrange
                IQueryable<ProductFamily> query =
                    await ProductFamilyRepository.WithDetailsAsync(x => x.Products, x => x.SubProductFamilies);
                ProductFamily productFamily = query.First(x => x.Id == id);

                for (int i = 0; i < productFamily.Products.Count; i++)
                {
                    Product product = await ProductRepository.GetAsync(productFamily.Products[i].Id);
                    product.IsVisible = i < 1;
                    await ProductRepository.UpdateAsync(product);
                }

                await ProductFamilyRepository.DeleteManyAsync(productFamily.SubProductFamilies.Select(p => p.Id));
            });

            await WithUnitOfWorkAsync(async () =>
            {
                using (_dataFilter.Enable<IHasVisibility>())
                {
                    // Act
                    List<ProductFamilyFlattenHierarchy> productFamilyFlattenHierarchies =
                        await ProductFamilyFlattenHierarchyRepository.GetListAsync(x => x.Id == id);

                    // Assert
                    productFamilyFlattenHierarchies.Count.ShouldBeGreaterThan(0);
                }
            });
        }
    }

    [Fact]
    public async Task Should_Not_Throw_Exception_If_Contains_Recursive()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                //Arrange
                IQueryable<ProductFamily> query = await ProductFamilyRepository.WithDetailsAsync(x => x.SubProductFamilies);
                ProductFamily grandFatherProductFamily = query.First(x => x.Id == storeContext.ProductFamilyWithChildIdByCodes.First().Value);
                List<Guid> grandParentChildIds = grandFatherProductFamily.SubProductFamilies.Select(x => x.Id).ToList();

                Guid parentProductFamilyId = grandParentChildIds.First();

                ProductFamily grandSonProductFamily = await ProductFamilyRepository.FirstAsync(x => x.Id != grandFatherProductFamily.Id && !grandParentChildIds.Contains(x.Id));
                await _productFamilyDomainService.ChangeParentAsync(grandSonProductFamily, parentProductFamilyId);
                await ProductFamilyRepository.UpdateAsync(grandSonProductFamily);

                await _productFamilyDomainService.ChangeParentAsync(grandFatherProductFamily, grandSonProductFamily.Id);
                await ProductFamilyRepository.UpdateAsync(grandFatherProductFamily);
            });

            await WithUnitOfWorkAsync(async () =>
            {
                // Act & Assert
                await ProductFamilyFlattenHierarchyRepository.GetListAsync().ShouldNotThrowAsync();
            });
        }
    }
}