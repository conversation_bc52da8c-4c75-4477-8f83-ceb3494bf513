using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using Shouldly;
using SpareParts.Common;
using SpareParts.Common.Companies;
using SpareParts.Common.DataFilter;
using SpareParts.Core.DataVisibility;
using SpareParts.Core.Entities.ProductFamilies;
using SpareParts.Core.ProductFamilies.Dtos;
using SpareParts.Core.ProductFamilies.Queries;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;
using Volo.Abp.Security.Claims;
using Xunit;

namespace SpareParts.Core.ProductFamilies.QueryHandlers;

public abstract class GetProductFamilyHierarchyByIdQueryHandlerTests<TStartupModule> : ProductFamilyTestsBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    [Fact]
    public async Task GetProductFamilyByIdQuery_Should_Return_ProductFamily()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid grandParentId = storeContext.ProductFamilyIdByCodes["productFamily-1"];
                Guid parentId = storeContext.ProductFamilyIdByCodes["productFamily-1-4"];
                Guid grandSonId = storeContext.ProductFamilyIdByCodes["productFamily-1-4-1"];

                ProductFamily grandParentProductFamily = await ProductFamilyRepository.FirstAsync(x => x.Id == grandParentId);

                // Act
                List<ProductFamilyHierarchyDto> result = await QuerySender.Send(new GetProductFamilyHierarchyByIdQuery(grandSonId));

                // Assert
                result.Count.ShouldBe(3);

                ProductFamilyHierarchyDto grandParentDto = result[0];
                grandParentDto.Id.ShouldBe(grandParentProductFamily.Id);
                grandParentDto.ImageId.ShouldBe(grandParentProductFamily.ImageId);
                grandParentDto.Translations.Count.ShouldBe(grandParentProductFamily.Translations.Count);

                ProductFamilyHierarchyDto parentDto = result[1];
                parentDto.Id.ShouldBe(parentId);
                ProductFamilyHierarchyDto childDto = result[2];
                childDto.Id.ShouldBe(grandSonId);
            });
        }
    }

    [Fact]
    public async Task GetProductFamilyByIdQuery_Should_Throw_Exception_If_ProductFamily_Not_Exist()
    {
        TestDataContext context = new(GuidGenerator.Create());
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Act & Assert
                await Should.ThrowAsync<EntityNotFoundException>(async () => await QuerySender.Send(new GetProductFamilyHierarchyByIdQuery(Guid.NewGuid())));
            });
        }
    }

    [Fact]
    public async Task GetProductFamilyByIdQuery_Should_Throw_Exception_If_User_Is_External_Content_Viewer_And_ProductFamily_Empty_And_Visibility_Filter_Enable()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        Guid companyId = Guid.NewGuid();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString())]))
        {
            using (DataFilter.Enable<IHasVisibility>())
            {
                using (CurrentTenant.Change(context.TenantId))
                {
                    await WithUnitOfWorkAsync(async () =>
                    {
                        // Arrange
                        Guid id = storeContext.ProductFamilyIdByCodes["productFamily-1-4-1"];

                        // Act & Assert
                        await Should.ThrowAsync<EntityNotFoundException>(async () => await QuerySender.Send(new GetProductFamilyHierarchyByIdQuery(id)));
                    });
                }
            }
        }
    }

    [Fact]
    public async Task GetProductFamilyByIdQuery_Should_Throw_Exception_If_User_Is_External_Content_Viewer_And_ProductFamily_Empty_And_Public_Filter_Enable()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        Guid companyId = Guid.NewGuid();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString())]))
        {
            using (DataFilter.Enable<IHasPublic>())
            {
                using (CurrentTenant.Change(context.TenantId))
                {
                    await WithUnitOfWorkAsync(async () =>
                    {
                        // Arrange
                        Guid id = storeContext.ProductFamilyIdByCodes["productFamily-1-4-1"];

                        // Act & Assert
                        await Should.ThrowAsync<EntityNotFoundException>(async () => await QuerySender.Send(new GetProductFamilyHierarchyByIdQuery(id)));
                    });
                }
            }
        }
    }
}