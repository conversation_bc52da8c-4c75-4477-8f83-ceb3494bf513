using System;
using System.IO;
using System.Threading.Tasks;
using Bogus.Extensions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using SpareParts.Core.Entities.Equipments;
using SpareParts.Core.Entities.ProductFamilies;
using SpareParts.Core.Entities.Resources;
using Volo.Abp.Content;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;
using Volo.Abp.Settings;

namespace SpareParts.Core.ProductFamilies;
public abstract class ProductFamilyTestsBase<TStartupModule> : CoreApplicationTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    protected readonly ISettingProvider SettingProvider;
    protected readonly IRepository<Equipment, Guid> EquipmentRepository;
    protected readonly IReadOnlyRepository<ProductFamilyFlattenHierarchy> ProductFamilyFlattenHierarchyRepository;

    protected ProductFamilyTestsBase()
    {
        EquipmentRepository = ServiceProvider.GetRequiredService<IRepository<Equipment, Guid>>();
        ProductFamilyFlattenHierarchyRepository = ServiceProvider.GetRequiredService<IRepository<ProductFamilyFlattenHierarchy>>();
        SettingProvider = ServiceProvider.GetRequiredService<ISettingProvider>();
    }

    protected async Task<ProductFamily> CreateProductFamily(Guid? parentId = null)
    {
        IFileInfo imageFile = VirtualFileProvider.GetFileInfo("/Files/default-image.png");
        await using Stream imageStream = imageFile.CreateReadStream();
        Resource resource = await ImageManager.CreateAsync(new RemoteStreamContent(imageStream, Faker.System.CommonFileName(CoreTestConsts.ImageExtension)));
        ProductFamilyTranslation translation = new(Faker.Locale, Faker.Lorem.Word().ClampLength(2), Faker.Lorem.Word());
        ProductFamily productFamily = await ProductFamilyDomainService.CreateAsync(Faker.Commerce.Ean8(), [translation], resource, parentId);
        await ProductFamilyRepository.InsertAsync(productFamily, true);
        return productFamily;
    }
}