using System;
using System.Threading.Tasks;
using Shouldly;
using SpareParts.Common.Companies;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Equipments.Commands;
using SpareParts.Core.Equipments.Dtos;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;
using Volo.Abp.Validation;
using Xunit;

namespace SpareParts.Core.Equipments.CommandHandlers;
public abstract class CreateEquipmentCommandHandlerTests<TStartupModule> : EquipmentTestsBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    [Fact]
    public async Task Create_Should_Succeed()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateCompanies = true, GenerateProducts = true, GenerateEquipments = true };
        await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                string serialNumber = "-\\_/";
                Product product = await ProductRepository.FirstAsync(x => x.IsVisible && x.IsPublic);
                Company company = await CompanyRepository.FirstAsync();

                // Act
                CreateEquipmentCommand createEquipmentCommand = new() { SerialNumber = serialNumber, ProductId = product.Id, CompanyId = company.Id };
                EquipmentDto equipmentDto = await CommandSender.Send(createEquipmentCommand);

                // Assert
                equipmentDto.Id.ShouldNotBe(Guid.Empty);
                equipmentDto.SerialNumber.ShouldBe(serialNumber);
                equipmentDto.ProductId.ShouldBe(product.Id);
                equipmentDto.CompanyId.ShouldBe(company.Id);
            });
        }
    }

    [Fact]
    public async Task Create_Should_Throw_Exception_With_Incorrect_Arguments()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateCompanies = true, GenerateProducts = true, GenerateEquipments = true };
        await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                string serialNumber = "-\\_/";
                Product product = await ProductRepository.FirstAsync(x => x.IsVisible && x.IsPublic);
                Company company = await CompanyRepository.FirstAsync();

                // Act & Assert
                await CommandSender.Send(new CreateEquipmentCommand() { SerialNumber = string.Empty, ProductId = product.Id, CompanyId = company.Id }).ShouldThrowAsync<AbpValidationException>();
                await CommandSender.Send(new CreateEquipmentCommand() { SerialNumber = "a$b", ProductId = product.Id, CompanyId = company.Id }).ShouldThrowAsync<AbpValidationException>();
                await CommandSender.Send(new CreateEquipmentCommand()
                {
                    SerialNumber = new string('x', CoreDomainSharedConsts.EquipmentConsts.SerialNumberMaxLength + 1),
                    ProductId = product.Id,
                    CompanyId = company.Id
                }).ShouldThrowAsync<AbpValidationException>();
                await CommandSender.Send(new CreateEquipmentCommand() { SerialNumber = serialNumber, ProductId = Guid.Empty, CompanyId = company.Id }).ShouldThrowAsync<AbpValidationException>();
                await CommandSender.Send(new CreateEquipmentCommand() { SerialNumber = serialNumber, ProductId = product.Id, CompanyId = Guid.Empty }).ShouldThrowAsync<AbpValidationException>();
            });
        }
    }
}