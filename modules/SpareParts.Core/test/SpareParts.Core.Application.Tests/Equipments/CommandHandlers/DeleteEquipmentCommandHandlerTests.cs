using System;
using System.Threading.Tasks;
using Shouldly;
using SpareParts.Core.Entities.Equipments;
using SpareParts.Core.Equipments.Commands;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Core.Equipments.CommandHandlers;
public abstract class DeleteEquipmentCommandHandlerTests<TStartupModule> : EquipmentTestsBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    [Fact]
    public async Task DeleteEquipmentCommandHandler_Should_Delete_Equipment()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateCompanies = true, GenerateEquipments = true, GenerateProducts = true };
        await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            Guid equipmentId = Guid.Empty;
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Equipment equipment = await EquipmentRepository.FirstAsync();
                equipmentId = equipment.Id;

                // Act
                await CommandSender.Send(new DeleteEquipmentCommand(equipment.Id));
            });

            // Assert
            await WithUnitOfWorkAsync(async () =>
            {
                Equipment? equipmentResult = await EquipmentRepository.FindAsync(equipmentId);
                equipmentResult.ShouldBeNull();
            });
        }
    }

    [Fact]
    public async Task DeleteEquipment_When_Id_Does_Not_Exist_Should_Not_Throw_EntityNotFoundException()
    {
        using (CurrentTenant.Change(Guid.NewGuid()))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Assert & Act
                await Should.NotThrowAsync(CommandSender.Send(new DeleteEquipmentCommand(Guid.NewGuid())));
            });
        }
    }
}
