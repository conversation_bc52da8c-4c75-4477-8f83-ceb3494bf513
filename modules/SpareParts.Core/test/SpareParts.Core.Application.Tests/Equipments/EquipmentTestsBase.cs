using Microsoft.Extensions.DependencyInjection;
using SpareParts.Common.Companies;
using SpareParts.Core.DomainServices.Equipments;
using SpareParts.Core.Entities.Equipments;
using System;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;

namespace SpareParts.Core.Equipments;

public abstract class EquipmentTestsBase<TStartupModule> : CoreApplicationTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    protected readonly IRepository<Company, Guid> CompanyRepository;
    protected readonly IRepository<Equipment, Guid> EquipmentRepository;
    protected readonly EquipmentDomainService EquipmentDomainService;

    protected EquipmentTestsBase()
    {
        CompanyRepository = ServiceProvider.GetRequiredService<IRepository<Company, Guid>>();
        EquipmentRepository = ServiceProvider.GetRequiredService<IRepository<Equipment, Guid>>();
        EquipmentDomainService = ServiceProvider.GetRequiredService<EquipmentDomainService>();
    }
}