using System;
using System.IO;
using System.Reflection;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Shouldly;
using SpareParts.Common;
using SpareParts.Common.Resources;
using SpareParts.Core.Entities.Resources;
using SpareParts.Core.Enums;
using SpareParts.Core.Resources.Commands;
using SpareParts.Core.Resources.Queries;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Content;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Core.Resources.QueryHandlers;

public abstract class GetImageResourceQueryHandlerTests<TStartupModule> : CoreApplicationTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    private readonly IBackgroundJobRepository _backgroundJobRepository;

    protected GetImageResourceQueryHandlerTests()
    {
        _backgroundJobRepository = ServiceProvider.GetRequiredService<IBackgroundJobRepository>();
    }

    [Fact]
    public async Task Handle_ShouldReturnVersionedRemoteStreamContent_WhenResourceHashIsNull()
    {
        Guid tenantId = Guid.NewGuid();
        Guid imageId = Guid.Empty;
        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                IFileInfo imageFile = VirtualFileProvider.GetFileInfo("/Files/default-image.png");
                await using Stream imageStream = imageFile.CreateReadStream();
                CreateImageResourceCommand imageCommand = new(new RemoteStreamContent(imageStream, "default-image.png"));
                imageId = await CommandSender.Send(imageCommand);
            });
            await WithUnitOfWorkAsync(async () =>
            {
                Resource image = await ResourceRepository.GetAsync(imageId);
                PropertyInfo? property = typeof(AbstractResource).GetProperty(nameof(AbstractResource.Hash), BindingFlags.Instance | BindingFlags.NonPublic | BindingFlags.Public);
                property!.SetValue(image, null);
                await ResourceRepository.UpdateAsync(image);
            });
            GetImageResourceQuery imageQuery = new(imageId);
            VersionedRemoteStreamContent image = await QuerySender.Send(imageQuery);
            image.ContentLength.ShouldBe(480);
            int count;
            do
            {
                count = (await _backgroundJobRepository.GetWaitingListAsync(null,1)).Count;
            } while (count == 1);
            GetImageResourceQuery resizedImageQuery = new(imageId, ImageSize.Xs);
            VersionedRemoteStreamContent resizedImage = await QuerySender.Send(resizedImageQuery);
            resizedImage.ContentLength.ShouldBe(84);
            resizedImage.Hash.ShouldBe(image.Hash);
            resizedImage.LastModificationTime.ShouldBe(image.LastModificationTime);
            resizedImage.ContentType.ShouldBe(image.ContentType);
            resizedImage.FileName.ShouldBe(image.FileName);
        }
    }

    [Fact]
    public async Task Handle_ShouldReturnVersionedRemoteStreamContent_WhenResourceHashIsNotNull()
    {
        Guid tenantId = Guid.NewGuid();
        Guid imageId = Guid.Empty;
        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                IFileInfo imageFile = VirtualFileProvider.GetFileInfo("/Files/default-image.png");
                await using Stream imageStream = imageFile.CreateReadStream();
                CreateImageResourceCommand imageCommand = new(new RemoteStreamContent(imageStream, "default-image.png"));
                imageId = await CommandSender.Send(imageCommand);
            });
            GetImageResourceQuery imageQuery = new(imageId);
            VersionedRemoteStreamContent image = await QuerySender.Send(imageQuery);
            image.ContentLength.ShouldBe(480);
            int count;
            do
            {
                count = (await _backgroundJobRepository.GetWaitingListAsync(null,1)).Count;
            } while (count == 1);
            GetImageResourceQuery resizedImageQuery = new(imageId, ImageSize.Xs);
            VersionedRemoteStreamContent resizedImage = await QuerySender.Send(resizedImageQuery);
            resizedImage.ContentLength.ShouldBe(84);
            resizedImage.Hash.ShouldBe(image.Hash);
            resizedImage.LastModificationTime.ShouldBe(image.LastModificationTime);
            resizedImage.ContentType.ShouldBe(image.ContentType);
            resizedImage.FileName.ShouldBe(image.FileName);
        }
    }
}