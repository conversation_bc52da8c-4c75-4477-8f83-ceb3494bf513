using System;
using System.IO;
using System.Threading.Tasks;
using FluentValidation;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Shouldly;
using SpareParts.Core.Containers;
using SpareParts.Core.Entities.Resources;
using SpareParts.Core.Resources.Commands;
using Volo.Abp.BlobStoring;
using Volo.Abp.Content;
using Volo.Abp.Modularity;
using Xunit;


namespace SpareParts.Core.Resources.CommandHandlers;
public abstract class CreateFileResourceCommandHandlerTests<TStartupModule> : CoreApplicationTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    private readonly IBlobContainer<FilesContainer> _blobContainer;

    protected CreateFileResourceCommandHandlerTests()
    {
        _blobContainer = ServiceProvider.GetRequiredService<IBlobContainer<FilesContainer>>();
    }

    [Fact]
    public async Task CreateFileResourceCommandHandler_Should_Throw_Exception_If_No_Filename()
    {
        using (CurrentTenant.Change(GuidGenerator.Create()))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                CreateFileResourceCommand createFileResourceCommand = new(new RemoteStreamContent(Stream.Null, ""));

                // Act
                await CommandSender.Send(createFileResourceCommand).ShouldThrowAsync<ValidationException>();
            });
        }
    }

    [Fact]
    public async Task CreateFileResourceCommandHandler_Should_Throw_Exception_If_Extension_Not_Allowed()
    {
        using (CurrentTenant.Change(GuidGenerator.Create()))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                CreateFileResourceCommand createFileResourceCommand = new(new RemoteStreamContent(Stream.Null, "filename.png"));

                // Act
                await CommandSender.Send(createFileResourceCommand).ShouldThrowAsync<ValidationException>();
            });
        }
    }

    [Fact]
    public async Task CreateFileResourceCommandHandler_Should_Create_Resource()
    {
        using (CurrentTenant.Change(GuidGenerator.Create()))
        {
            // Arrange
            IFileInfo pdfFile = VirtualFileProvider.GetFileInfo("/Files/21841215.pdf");
            await using Stream pdf = pdfFile.CreateReadStream();
            const string fileName = "myFileName.pdf";
            CreateFileResourceCommand createFileResourceCommand = new(new RemoteStreamContent(pdf, fileName));
            Guid fileId = Guid.Empty;
            await WithUnitOfWorkAsync(async () =>
            {
                // Act
                fileId = await CommandSender.Send(createFileResourceCommand);
            });
            // Assert
            fileId.ShouldNotBe(Guid.Empty);

            await WithUnitOfWorkAsync(async () =>
            {
                Resource file = await ResourceRepository.GetAsync(fileId);
                file.ShouldNotBeNull();
                file.FileName.ShouldBe(fileName);

                bool resourceExists = await _blobContainer.ExistsAsync($"{fileId:N}");
                resourceExists.ShouldBeTrue();
            });
        }
    }
}