using Microsoft.Extensions.DependencyInjection;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Equipments;
using System;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;
using Volo.Abp.Settings;

namespace SpareParts.Core.Components;
public abstract class ComponentTestBase<TStartupModule>
    : CoreApplicationTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    protected readonly ISettingProvider SettingProvider;
    protected readonly IRepository<ComponentDocument> ComponentDocumentRepository;
    protected readonly IRepository<Equipment, Guid> EquipmentRepository;

    protected ComponentTestBase()
    {
        SettingProvider = ServiceProvider.GetRequiredService<ISettingProvider>();
        ComponentDocumentRepository = ServiceProvider.GetRequiredService<IRepository<ComponentDocument>>();
        EquipmentRepository = ServiceProvider.GetRequiredService<IRepository<Equipment, Guid>>();
    }
}