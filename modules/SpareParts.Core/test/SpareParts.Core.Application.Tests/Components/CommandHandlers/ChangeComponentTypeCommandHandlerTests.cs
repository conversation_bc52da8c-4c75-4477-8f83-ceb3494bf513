using System;
using System.Threading.Tasks;
using Shouldly;
using SpareParts.Core.Components.Commands;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Enums;
using Volo.Abp.Data;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Core.Components.CommandHandlers;

public abstract class ChangeComponentTypeCommandHandlerTests<TStartupModule> : ComponentTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    [Fact]
    public async Task Send_ChangeComponentTypeCommand_From_Part_To_Assembly_Should_Change_Part_To_Assembly()
    {
        // Arrange
        Guid tenantId = GuidGenerator.Create();
        await WithUnitOfWorkAsync(async () =>
        {
            await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        });

        using (CurrentTenant.Change(tenantId))
        {
            Component part = default!;
            await WithUnitOfWorkAsync(async () =>
            {
                part = await ComponentDomainService.CreateAsync("code",
                    [new ComponentTranslation("en", "label")], ComponentType.Part);
                part = await ComponentRepository.InsertAsync(part);
            });

            // Act
            await WithUnitOfWorkAsync(async () =>
            {
                await CommandSender.Send(new ChangeComponentTypeCommand(part.Id, ComponentType.Assembly));
            });

            //Arrange
            await WithUnitOfWorkAsync(async () =>
            {
                Component? assembly = await ComponentRepository.FindAsync(part.Id);
                assembly.ShouldNotBeNull();
                assembly.Type.ShouldBe(ComponentType.Assembly);
            });
        }
    }


    [Fact]
    public async Task Send_ChangeComponentTypeCommand_From_Assembly_To_Part_Should_Change_Assembly_To_Part()
    {
        // Arrange
        Guid tenantId = GuidGenerator.Create();
        await WithUnitOfWorkAsync(async () =>
        {
            await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        });

        using (CurrentTenant.Change(tenantId))
        {
            Component assembly = await WithUnitOfWorkAsync(async () =>
            {
                assembly = await ComponentDomainService.CreateAsync("code",
                    [new ComponentTranslation("en", "label")], ComponentType.Assembly);
                return assembly = await ComponentRepository.InsertAsync(assembly);
            });

            // Act
            await WithUnitOfWorkAsync(async () =>
            {
                await CommandSender.Send(new ChangeComponentTypeCommand(assembly.Id, ComponentType.Part));
            });

            //Arrange
            await WithUnitOfWorkAsync(async () =>
            {
                Component? deletedAssembly = await ComponentRepository.FindAsync(c => c.Id  == assembly.Id && c.Type == ComponentType.Assembly);
                deletedAssembly.ShouldBeNull();

                Component? part = await ComponentRepository.FindAsync(c => c.Id == assembly.Id && c.Type == ComponentType.Part);
                part.ShouldNotBeNull();
            });
        }
    }
}