using System;
using System.Threading.Tasks;
using Shouldly;
using SpareParts.Core.Components.Commands.Products;
using SpareParts.Core.Entities.Components;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Core.Components.CommandHandlers.Products;
public abstract class UpdateProductPublicFlagCommandHandlerTests<TStartupModule> : ComponentTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    [Fact]
    public async Task UpdateProductPublicFlagCommand_Should_Update_Product_Public_Flag()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            string productCode = storeContext.ProductCodePrefix + "1";
            bool updatedPublicFlag = false;

            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Product product = await ProductRepository.GetAsync(storeContext.ProductIdByCodes[productCode]);
                updatedPublicFlag = !product.IsPublic;

                UpdateProductPublicFlagCommand updateProductPublicFlagCommand = new(product.Id, updatedPublicFlag);

                // Act
                await CommandSender.Send(updateProductPublicFlagCommand);
            });

            // Assert
            await WithUnitOfWorkAsync(async () =>
                {
                    Product product = await ProductRepository.GetAsync(storeContext.ProductIdByCodes[productCode]);
                    product.IsPublic.ShouldBe(updatedPublicFlag);
                });
        }
    }

    [Fact]
    public async Task UpdateProductPublicFlagCommand_When_Id_Does_Not_Exist_Should_Throw_EntityNotFoundException()
    {
        using (CurrentTenant.Change(GuidGenerator.Create()))
        {
            // Arrange
            Guid productId = GuidGenerator.Create();

            await WithUnitOfWorkAsync(async () =>
            {
                UpdateProductPublicFlagCommand updateProductPublicFlagCommand = new(productId, true);

                // Act & Assert
                await Should.ThrowAsync<EntityNotFoundException>(CommandSender.Send(updateProductPublicFlagCommand));
            });
        }
    }
}