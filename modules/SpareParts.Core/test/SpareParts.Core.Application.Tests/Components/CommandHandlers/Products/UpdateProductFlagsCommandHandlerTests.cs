using System.Threading.Tasks;
using Shouldly;
using SpareParts.Core.Components.Commands.Products;
using SpareParts.Core.Entities.Components;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Core.Components.CommandHandlers.Products;
public abstract class UpdateProductFlagsCommandHandlerTests<TStartupModule> : ComponentTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    [Fact]
    public async Task UpdateProductCommand_Should_Update_Product_Public_Flag()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            string productCode = storeContext.ProductCodePrefix + "1";
            bool updatedPublicFlag = false;
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Product product = await ProductRepository.GetAsync(storeContext.ProductIdByCodes[productCode]);
                updatedPublicFlag = !product.IsPublic;

                UpdateProductCommand updateProductCommand = new(product.Id, product.IsVisible, updatedPublicFlag);

                // Act
                await CommandSender.Send(updateProductCommand);
            });

            await WithUnitOfWorkAsync(async () =>
            {
                // Assert
                Product product = await ProductRepository.GetAsync(storeContext.ProductIdByCodes[productCode]);
                product.IsPublic.ShouldBe(updatedPublicFlag);
            });
        }
    }

    [Fact]
    public async Task UpdateProductCommand_Should_Update_Product_IsVisible_Flag()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            string productCode = storeContext.ProductCodePrefix + "1";
            bool updatedIsVisibleFlag = false;

            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Product product = await ProductRepository.GetAsync(storeContext.ProductIdByCodes[productCode]);
                updatedIsVisibleFlag = !product.IsVisible;

                UpdateProductCommand updateProductCommand = new(product.Id, updatedIsVisibleFlag, product.IsPublic);

                // Act
                await CommandSender.Send(updateProductCommand);
            });

            await WithUnitOfWorkAsync(async () =>
            {

                // Assert
                Product product = await ProductRepository.GetAsync(storeContext.ProductIdByCodes[productCode]);
                product.IsVisible.ShouldBe(updatedIsVisibleFlag);
            });
        }
    }

    [Fact]
    public async Task UpdateProductCommand_Should_Not_Update_Product_Flag()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            string productCode = storeContext.ProductCodePrefix + "1";
            bool isPublic = false;
            bool isVisible = false;
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Product product = await ProductRepository.GetAsync(storeContext.ProductIdByCodes[productCode]);
                isPublic = product.IsPublic;
                isVisible = product.IsVisible;

                UpdateProductCommand updateProductCommand = new(product.Id);

                // Act
                await CommandSender.Send(updateProductCommand);
            });

            await WithUnitOfWorkAsync(async () =>
                {
                    // Assert
                    Product product = await ProductRepository.GetAsync(storeContext.ProductIdByCodes[productCode]);
                    product.IsPublic.ShouldBe(isPublic);
                    product.IsVisible.ShouldBe(isVisible);
                });
        }
    }
}
