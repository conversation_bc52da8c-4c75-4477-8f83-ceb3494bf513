using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Shouldly;
using SpareParts.Core.Components.Commands.Products;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Equipments;
using SpareParts.Core.Entities.ProductFamilies;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Core.Components.CommandHandlers.Products;
public abstract class DeleteProductCommandHandlerTests<TStartupModule> : ComponentTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    [Fact]
    public async Task DeleteProductCommand_Should_Succeed()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateEquipments = true, GenerateCompanies = true, GenerateProductFamilies = true, GenerateProducts = true, GenerateAssemblies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            // Arrange
            Guid productId = Guid.Empty;
            await WithUnitOfWorkAsync(async () =>
            {
                Product product = await ProductRepository.GetAsync(storeContext.ProductIdByCodes.First().Value);
                productId = product.Id;

                DeleteProductCommand deleteProductCommand = new(productId);

                // Act
                await CommandSender.Send(deleteProductCommand);
            });

            // Assert
            await WithUnitOfWorkAsync(async () =>
            {
                Product? productResult = await ProductRepository.FindAsync(productId);
                productResult.ShouldBeNull();
                List<ProductInProductFamily> productInProductFamilyResult = await ProductInProductFamilyRepository.GetListAsync(x => x.ProductId == productId);
                productInProductFamilyResult.Count.ShouldBe(0);
                List<Equipment> equipmentResult = await EquipmentRepository.GetListAsync(x => x.ProductId == productId);
                equipmentResult.Count.ShouldBe(0);
            });
        }
    }

    [Fact]
    public async Task DeleteProductCommand_When_Id_Does_Not_Exist_Should_Not_Throw_EntityNotFoundException()
    {
        using (CurrentTenant.Change(GuidGenerator.Create()))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid productId = GuidGenerator.Create();
                DeleteProductCommand deleteProductCommand = new(productId);

                // Act & Assert
                await Should.NotThrowAsync(CommandSender.Send(deleteProductCommand));
            });
        }
    }

}