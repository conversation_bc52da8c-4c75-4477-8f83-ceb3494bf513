using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Extensions.FileProviders;
using Shouldly;
using SpareParts.Core.Components.Commands.Images;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Resources;
using SpareParts.Core.Enums;
using SpareParts.Core.Settings;
using Volo.Abp.Content;
using Volo.Abp.Data;
using Volo.Abp.Modularity;
using Volo.Abp.Settings;
using Xunit;

namespace SpareParts.Core.Components.CommandHandlers;

public abstract class DeleteComponentImageCommandHandlerTests<TStartupModule> : ComponentTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    [Fact]
    public async Task Send_DeleteComponentImageCommand_Should_Not_Do_Anything_If_Image_Is_Default()
    {
        // Arrange
        Component assembly = null!;
        Guid tenantId = Guid.NewGuid();
        CurrentTenant.Change(tenantId);
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        Guid defaultImageId = Guid.Empty;
        await WithUnitOfWorkAsync(async () =>
        {
            assembly = await ComponentDomainService.CreateAsync("assCode", [new ComponentTranslation("en", "assLabel")], ComponentType.Assembly);
            assembly = await ComponentRepository.InsertAsync(assembly);
            defaultImageId = assembly.ImageId;
        });
        // Act
        await WithUnitOfWorkAsync(async () =>
        {
            await CommandSender.Send(new DeleteComponentImageCommand(assembly.Id));
        });
        // Assert
        await WithUnitOfWorkAsync(async () =>
        {
            Component unchangedAssembly = await ComponentRepository.GetAsync(assembly.Id);
            unchangedAssembly.Image.Id.ShouldBe(defaultImageId);
            Guid componentDefaultImageId = await SettingProvider.GetAsync<Guid>(CoreSettings.ComponentDefaultImageId);
            defaultImageId.ShouldBe(componentDefaultImageId);
        });
    }

    [Fact]
    public async Task Send_DeleteComponentImageCommand_Should_Delete_Image_If_Image_Is_Not_Default()
    {
        // Arrange
        Component assembly = null!;
        Guid tenantId = Guid.NewGuid();
        CurrentTenant.Change(tenantId);
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        Guid imageId = Guid.Empty;
        await WithUnitOfWorkAsync(async () =>
        {
            IFileInfo imageFile = VirtualFileProvider.GetFileInfo("/Files/default-image.png");
            await using Stream imageStream = imageFile.CreateReadStream();
            Resource image = await ImageManager.CreateAsync(new RemoteStreamContent(imageStream, "assembly.png"));
            imageId = image.Id;
            assembly = await ComponentDomainService.CreateAsync("assCode", [new ComponentTranslation("en", "assLabel")], ComponentType.Assembly, image);
            await ComponentRepository.InsertAsync(assembly);
        });
        // Act
        await WithUnitOfWorkAsync(async () =>
        {
            await CommandSender.Send(new DeleteComponentImageCommand(assembly.Id));
        });
        // Assert
        await WithUnitOfWorkAsync(async () =>
        {
            Component updatedAssembly = await ComponentRepository.GetAsync(assembly.Id);
            Guid componentDefaultImageId = await SettingProvider.GetAsync<Guid>(CoreSettings.ComponentDefaultImageId);
            updatedAssembly.Image.Id.ShouldBe(componentDefaultImageId);

            Resource? deletedImage = await ResourceRepository.FindAsync(imageId);
            deletedImage.ShouldBeNull();

            bool blobFound = await ImageManager.BlobExistsAsync(imageId);
            blobFound.ShouldBeFalse();
        });
    }
}