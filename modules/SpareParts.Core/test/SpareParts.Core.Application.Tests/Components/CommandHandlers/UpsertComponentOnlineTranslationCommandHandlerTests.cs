using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Shouldly;
using SpareParts.Common.Dtos;
using SpareParts.Core.Components.Commands;
using SpareParts.Core.Components.Dtos;
using SpareParts.Core.Entities.Components;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Core.Components.CommandHandlers;

public abstract class UpsertComponentOnlineTranslationCommandHandlerTests<TStartupModule> : ConcreteComponentTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    private readonly IRepository<ComponentOnlineTranslation> _componentOnlineTranslationRepository;

    protected UpsertComponentOnlineTranslationCommandHandlerTests()
    {
        _componentOnlineTranslationRepository = ServiceProvider.GetRequiredService<IRepository<ComponentOnlineTranslation>>();
    }

    protected UpsertComponentOnlineTranslationCommand CreateCommand(Guid id, string language, string? label,
        string? description = null)
    {
        return new UpsertComponentOnlineTranslationCommand(id, new CommonOnlineTranslationDto(language, label, description));
    }

    [Fact]
    public async Task UpdateOnlineTranslationCommand_Should_Add_OnlineTranslation()
    {
        Guid tenantId = GuidGenerator.Create();
        TestDataContext context = GetTestDataContext(tenantId);
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        using (CurrentTenant.Change(tenantId))
        {
            Guid componentId = GetComponentId(storeContext);
            const string language = "jp";
            const string label = "label";
            const string description = "description";
            CommonOnlineTranslationDto? onlineTranslationDto = default!;

            await WithUnitOfWorkAsync(async () =>
            {
                await ComponentRepository.GetAsync(componentId);
                UpsertComponentOnlineTranslationCommand upsertComponentCommand = CreateCommand(componentId, language, label, description);

                // Act
                ComponentDto componentDto = await CommandSender.Send(upsertComponentCommand);
                onlineTranslationDto = componentDto.OnlineTranslations.FirstOrDefault(x => x.Language == language);
            });

            await WithUnitOfWorkAsync(async () =>
            {
                // Assert
                onlineTranslationDto.ShouldNotBeNull();
                onlineTranslationDto.Language.ShouldBe(language);
                onlineTranslationDto.Label.ShouldBe(label);
                onlineTranslationDto.Description.ShouldBe(description);

                Component component = await ComponentRepository.GetAsync(componentId);

                ComponentOnlineTranslation? translation = component.OnlineTranslations.FirstOrDefault(x => x.Language == language);
                translation.ShouldNotBeNull();
                translation.Language.ShouldBe(language);
                translation.Label.ShouldBe(label);
                translation.Description.ShouldBe(description);
            });
        }
    }

    [Fact]
    public async Task UpdateOnlineTranslationCommand_Should_Update_Existing_OnlineTranslation()
    {
        Guid tenantId = GuidGenerator.Create();
        TestDataContext context = GetTestDataContext(tenantId);
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        using (CurrentTenant.Change(tenantId))
        {
            Guid componentId = GetComponentId(storeContext);
            string language = Guid.NewGuid().ToString("N");
            const string newLabel = "newLabel";
            const string newDescription = "newDescription";
            CommonOnlineTranslationDto? onlineTranslationDto = default!;

            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange

                ComponentOnlineTranslation componentOnlineTranslation = new(language, "label", "description")
                {
                    ComponentId = componentId
                };
                await _componentOnlineTranslationRepository.InsertAsync(componentOnlineTranslation);

            });

            await WithUnitOfWorkAsync(async () =>
            {
                await ComponentRepository.GetAsync(componentId);

                UpsertComponentOnlineTranslationCommand upsertComponentCommand = CreateCommand(componentId, language, newLabel, newDescription);

                // Act
                ComponentDto componentDto = await CommandSender.Send(upsertComponentCommand);
                onlineTranslationDto = componentDto.OnlineTranslations.FirstOrDefault(x => x.Language == language);
            });

            await WithUnitOfWorkAsync(async () =>
            {
                // Assert
                onlineTranslationDto.ShouldNotBeNull();
                onlineTranslationDto.Language.ShouldBe(language);
                onlineTranslationDto.Label.ShouldBe(newLabel);
                onlineTranslationDto.Description.ShouldBe(newDescription);

                Component updatedComponent = await ComponentRepository.GetAsync(componentId);
                ComponentOnlineTranslation? translation = updatedComponent.OnlineTranslations.FirstOrDefault(x => x.ComponentId == componentId && x.Language == language);
                translation.ShouldNotBeNull();
                translation.Language.ShouldBe(language);
                translation.Label.ShouldBe(newLabel);
                translation.Description.ShouldBe(newDescription);
            });
        }
    }

    [Fact]
    public async Task UpdateOnlineTranslationCommand_With_Null_Values_Should_Update_Existing_OnlineTranslation()
    {
        Guid tenantId = GuidGenerator.Create();
        TestDataContext context = GetTestDataContext(tenantId);
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        using (CurrentTenant.Change(tenantId))
        {
            Guid componentId = GetComponentId(storeContext);
            string language = Guid.NewGuid().ToString("N");
            CommonOnlineTranslationDto? onlineTranslationDto = default!;

            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange

                ComponentOnlineTranslation componentOnlineTranslation = new(language, "label", "description")
                {
                    ComponentId = componentId
                };
                await _componentOnlineTranslationRepository.InsertAsync(componentOnlineTranslation);

            });

            await WithUnitOfWorkAsync(async () =>
            {
                await ComponentRepository.GetAsync(componentId);

                UpsertComponentOnlineTranslationCommand upsertComponentCommand = CreateCommand(componentId, language, null);

                // Act
                ComponentDto componentDto = await CommandSender.Send(upsertComponentCommand);
                onlineTranslationDto = componentDto.OnlineTranslations.FirstOrDefault(x => x.Language == language);
            });

            await WithUnitOfWorkAsync(async () =>
            {
                // Assert
                onlineTranslationDto.ShouldNotBeNull();
                onlineTranslationDto.Language.ShouldBe(language);
                onlineTranslationDto.Label.ShouldBeNull();
                onlineTranslationDto.Description.ShouldBeNull();

                Component updatedComponent = await ComponentRepository.GetAsync(componentId);
                ComponentOnlineTranslation? translation = updatedComponent.OnlineTranslations.FirstOrDefault(x => x.ComponentId == componentId && x.Language == language);
                translation.ShouldNotBeNull();
                translation.Language.ShouldBe(language);
                translation.Label.ShouldBeNull();
                translation.Description.ShouldBeNull();
            });
        }
    }

    [Fact]
    public async Task UpdateOnlineTranslationCommand_Should_Throw_EntityNotFoundException_If_Component_Not_Exist()
    {
        using (CurrentTenant.Change(GuidGenerator.Create()))
        {
            // Act and Assert
            await Should.ThrowAsync<EntityNotFoundException>(CommandSender.Send(CreateCommand(GuidGenerator.Create(), "fr", "label")));
        }
    }
}