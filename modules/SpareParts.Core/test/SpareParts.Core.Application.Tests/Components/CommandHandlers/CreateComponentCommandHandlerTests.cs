using Microsoft.Extensions.DependencyInjection;
using Shouldly;
using SpareParts.Common.Dtos;
using SpareParts.Core.Components.Commands;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Resources;
using SpareParts.Core.Enums;
using SpareParts.Core.Settings;
using System;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Data;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;
using Volo.Abp.Settings;
using Xunit;

namespace SpareParts.Core.Components.CommandHandlers;

public abstract class CreateComponentCommandHandlerTests<TStartupModule> : ComponentTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    private readonly IRepository<Component> _repository;

    protected CreateComponentCommandHandlerTests()
    {
        _repository = ServiceProvider.GetRequiredService<IRepository<Component>>();
    }

    private CreateComponentCommand CreateCommand(string code, CommonTranslationDto translation)
    {
        return new CreateComponentCommand(code, [translation], ComponentType);
    }

    protected abstract ComponentType ComponentType { get; }

    [Fact]
    public async Task CreateComponentCommandHandler_Creates_New_Component()
    {
        // Arrange
        Guid tenantId = GuidGenerator.Create();
        const string code = "code";
        const string language = "en";
        const string label = "label";
        const string description = "description";
        await WithUnitOfWorkAsync(async () =>
        {
            await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        });

        using (CurrentTenant.Change(tenantId))
        {
            // Act
            await WithUnitOfWorkAsync(async () =>
            {
                await CommandSender.Send(CreateCommand(code,
                    new CommonTranslationDto(language, label, description)));
            });

            // Assert
            await WithUnitOfWorkAsync(async () =>
            {
                Component component = await _repository.FirstAsync(a => a.Code.Equals(code));

                component.Id.ShouldNotBe(Guid.Empty);
                component.TenantId.ShouldBe(tenantId);

                component.Code.ShouldBe(code);
                component.Type.ShouldBe(ComponentType);
                component.Id.ShouldNotBe(Guid.Empty);
                component.Id.ShouldBe(component.Id);
                component.TenantId.ShouldBe(tenantId);

                Resource image = component.Image;
                image.Id.ShouldNotBe(Guid.Empty);
                image.Id.ShouldBe(component.ImageId);
                image.TenantId.ShouldBe(tenantId);
                Guid componentDefaultImageId = await SettingProvider.GetAsync<Guid>(CoreSettings.ComponentDefaultImageId);
                image.Id.ShouldBe(componentDefaultImageId);

                ComponentTranslation translation = component.Translations.First();
                translation.TenantId.ShouldBe(tenantId);
                translation.Language.ShouldBe(language);
                translation.Label.ShouldBe(label);
                translation.Description.ShouldBe(description);

                component.OnlineTranslations.ShouldBeEmpty();
            });
        }
    }
}