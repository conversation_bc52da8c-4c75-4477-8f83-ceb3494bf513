using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Extensions.FileProviders;
using Shouldly;
using SpareParts.Core.Components.Commands.Images;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Resources;
using SpareParts.Core.Enums;
using Volo.Abp.Content;
using Volo.Abp.Data;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Core.Components.CommandHandlers;

public abstract class ChangeComponentImageCommandHandlerTests<TStartupModule> : ComponentTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    [Fact]
    public async Task ChangeComponentImageFromDefaultImage()
    {
        // Arrange
        Guid tenantId = GuidGenerator.Create();
        await WithUnitOfWorkAsync(async () =>
        {
            await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        });
        const string fileName = "part.png";

        using (CurrentTenant.Change(tenantId))
        {
            Component part = default!;
            await WithUnitOfWorkAsync(async () =>
            {
                part = await ComponentDomainService.CreateAsync("code",
                    [new ComponentTranslation("en", "label")], ComponentType.Part);
                part = await ComponentRepository.InsertAsync(part);
            });
            // Act
            IFileInfo imageFile = VirtualFileProvider.GetFileInfo("/Files/fff.png");
            await using Stream image = imageFile.CreateReadStream();
            await WithUnitOfWorkAsync(async () =>
            {
                await CommandSender.Send(new ChangeComponentImageCommand(part.Id, new RemoteStreamContent(image, fileName)));
            });
            // Assert
            await WithUnitOfWorkAsync(async () =>
            {
                Component updatedPart = await ComponentRepository.GetAsync(part.Id);
                updatedPart.Image.FileName.ShouldBe(fileName);
                updatedPart.Image.Id.ShouldNotBe(part.ImageId);
                updatedPart.Image.TenantId.ShouldBe(tenantId);
                (await (await ImageManager.GetBlobAsync(updatedPart.ImageId)).GetAllBytesAsync()).ShouldBe(await imageFile.ReadBytesAsync());
            });
        }
    }

    [Fact]
    public async Task ChangeComponentImageFromNonDefaultImage()
    {
        // Arrange
        Guid tenantId = GuidGenerator.Create();
        await WithUnitOfWorkAsync(async () =>
        {
            await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        });

        using (CurrentTenant.Change(tenantId))
        {
            Component part = default!;
            Guid imageId = Guid.Empty;
            await WithUnitOfWorkAsync(async () =>
            {
                const string oldFileName = "oldPart.png";
                IFileInfo imageFile = VirtualFileProvider.GetFileInfo("/Files/default-image.png");
                await using Stream imageStream = imageFile.CreateReadStream();
                Resource resource = await ImageManager.CreateAsync(new RemoteStreamContent(imageStream, oldFileName));
                imageId = resource.Id;
                part = await ComponentDomainService.CreateAsync("code",
                    [new ComponentTranslation("en", "label")], ComponentType.Part, resource);
                part = await ComponentRepository.InsertAsync(part);
            });
            // Act
            const string newFileName = "newPart.png";
            IFileInfo imageFile = VirtualFileProvider.GetFileInfo("/Files/fff.png");
            await using Stream image = imageFile.CreateReadStream();
            await WithUnitOfWorkAsync(async () =>
            {
                await CommandSender.Send(new ChangeComponentImageCommand(part.Id, new RemoteStreamContent(image, newFileName)));
            });
            // Assert
            await WithUnitOfWorkAsync(async () =>
            {
                Component updatedPart = await ComponentRepository.GetAsync(part.Id);
                updatedPart.Image.FileName.ShouldBe(newFileName);
                updatedPart.Image.Id.ShouldBe(imageId);
                updatedPart.Image.TenantId.ShouldBe(tenantId);
                (await ImageManager.BlobExistsAsync(imageId)).ShouldBeTrue();
                (await (await ImageManager.GetBlobAsync(imageId)).GetAllBytesAsync()).ShouldBe(await imageFile.ReadBytesAsync());
            });
        }
    }
}