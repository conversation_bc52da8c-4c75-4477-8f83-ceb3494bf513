using System;
using System.Threading.Tasks;
using Shouldly;
using SpareParts.Core.Components.Commands;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Resources;
using SpareParts.Core.Enums;
using Volo.Abp.Data;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Core.Components.CommandHandlers;

public abstract class DetachDocumentFromComponentCommandHandlerTests<TStartupModule> : ComponentTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    [Fact]
    public async Task Send_DeleteComponentDocumentCommand_Should_Delete_Resource_Document_From_Database_And_Storage()
    {
        Guid tenantId = GuidGenerator.Create();
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        using (CurrentTenant.Change(tenantId))
        {
            // Arrange
            Component assembly = null!;
            Guid documentId = Guid.NewGuid();
            Guid resourceId = Guid.NewGuid();
            await WithUnitOfWorkAsync(async () =>
            {
                Document document = await CreateFileDocument();
                documentId = document.Id;
                resourceId = document.Resource!.Id;

                assembly = await ComponentDomainService.CreateAsync(Faker.Commerce.Product(), [new ComponentTranslation(Faker.Locale, Faker.Commerce.ProductName())], ComponentType.Assembly);
                await ComponentDocumentRepository.InsertAsync(new ComponentDocument(assembly.Id, documentId));
                await ComponentRepository.InsertAsync(assembly);
            });

            // Act
            await WithUnitOfWorkAsync(async () =>
            {
                await CommandSender.Send(new DetachDocumentFromComponentCommand(assembly.Id, documentId));
            });

            // Assert
            await WithUnitOfWorkAsync(async () =>
            {
                Component unchangedAssembly = await ComponentRepository.GetAsync(assembly.Id);
                unchangedAssembly.Documents.ShouldBeEmpty();

                Document? document = await DocumentRepository.FindAsync(documentId);
                document.ShouldNotBeNull();

                Resource? resource = await ResourceRepository.FindAsync(resourceId);
                resource.ShouldNotBeNull();

                bool blobFound = await FileManager.BlobExistsAsync(resourceId);
                blobFound.ShouldBeTrue();
            });
        }
    }

    [Fact]
    public async Task Send_DeleteComponentDocumentCommand_Should_Delete_Link_Document_From_Database()
    {
        Guid tenantId = GuidGenerator.Create();
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        using (CurrentTenant.Change(tenantId))
        {
            // Arrange
            Component assembly = null!;
            Guid documentId = Guid.NewGuid();
            await WithUnitOfWorkAsync(async () =>
            {
                Document document = await CreateLinkDocument();
                documentId = document.Id;

                assembly = await ComponentDomainService.CreateAsync(Faker.Commerce.Product(), [new ComponentTranslation(Faker.Locale, Faker.Commerce.ProductName())], ComponentType.Assembly);
                await ComponentDocumentRepository.InsertAsync(new ComponentDocument(assembly.Id, document.Id));
                await ComponentRepository.InsertAsync(assembly);
            });

            // Act
            await WithUnitOfWorkAsync(async () =>
            {
                await CommandSender.Send(new DetachDocumentFromComponentCommand(assembly.Id, documentId));
            });

            // Assert
            await WithUnitOfWorkAsync(async () =>
            {
                Component unchangedAssembly = await ComponentRepository.GetAsync(assembly.Id);
                unchangedAssembly.Documents.ShouldBeEmpty();

                Document? deletedDocument = await DocumentRepository.FindAsync(documentId);
                deletedDocument.ShouldNotBeNull();
            });
        }
    }
}