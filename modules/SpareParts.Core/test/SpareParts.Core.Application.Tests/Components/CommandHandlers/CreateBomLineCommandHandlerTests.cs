using Shouldly;
using SpareParts.Core.Components.Commands;
using SpareParts.Core.Entities.Boms;
using SpareParts.Core.Entities.Components;
using System;
using System.Threading.Tasks;
using SpareParts.Core.Enums;
using Volo.Abp;
using Volo.Abp.Data;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Core.Components.CommandHandlers;

public abstract class CreateBomLineCommandHandlerTests<TStartupModule, TException> : ComponentTestBase<TStartupModule>
    where TStartupModule : IAbpModule
    where TException : Exception
{
    [Fact]
    public async Task Send_CreateBomLineCommand_Should_Create_BomLine()
    {
        // Arrange
        Component assembly = null!;
        Component part = null!;
        Guid tenantId = Guid.NewGuid();
        CurrentTenant.Change(tenantId);
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        await WithUnitOfWorkAsync(async () =>
        {
            assembly = await ComponentDomainService.CreateAsync("assCode", [new ComponentTranslation("en", "assLabel")], ComponentType.Assembly);
            await ComponentRepository.InsertAsync(assembly);
            part = await ComponentDomainService.CreateAsync("partCode", [new ComponentTranslation("en", "partLabel")], ComponentType.Part);
            await ComponentRepository.InsertAsync(part);
        });
        // Act
        await WithUnitOfWorkAsync(async () =>
        {
            await CommandSender.Send(new CreateBomLineCommand(assembly.Id, part.Id, 1));
        });
        // Assert
        await WithUnitOfWorkAsync(async () =>
        {
            BomLine? bomLine = await BomLineRepository.FindAsync(bl => bl.ChildComponentId.Equals(part.Id) && bl.ParentAssemblyId.Equals(assembly.Id));
            bomLine.ShouldNotBeNull();
            bomLine.Quantity.ShouldBe(1);
            bomLine.Rank.ShouldBe(1);
        });
    }

    [Fact]
    public async Task Send_CreateBomLineCommand_With_Direct_Cycling_Should_Throw_BusinessException()
    {
        Component assembly1 = null!;
        Component assembly2 = null!;
        Guid tenantId = Guid.NewGuid();
        CurrentTenant.Change(tenantId);
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        await WithUnitOfWorkAsync(async () =>
        {
            assembly1 = await ComponentDomainService.CreateAsync("ass1Code", [new ComponentTranslation("en", "ass1Label")], ComponentType.Assembly);
            await ComponentRepository.InsertAsync(assembly1);
            assembly2 = await ComponentDomainService.CreateAsync("ass2Code", [new ComponentTranslation("en", "ass2Label")], ComponentType.Assembly);
            await ComponentRepository.InsertAsync(assembly2);
            await CommandSender.Send(new CreateBomLineCommand(assembly1.Id, assembly2.Id, 1));
        });
        await WithUnitOfWorkAsync(async () =>
        {
            await CommandSender.Send(new CreateBomLineCommand(assembly2.Id, assembly1.Id, 1)).ShouldThrowAsync<BusinessException>();
        });
    }

    [Fact]
    public async Task Send_CreateBomLineCommand_With_Duplicates_Should_Throw_Exception()
    {
        Component assembly1 = null!;
        Component assembly2 = null!;
        Guid tenantId = Guid.NewGuid();
        CurrentTenant.Change(tenantId);
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        await WithUnitOfWorkAsync(async () =>
        {
            assembly1 = await ComponentDomainService.CreateAsync("ass1Code", [new ComponentTranslation("en", "ass1Label")], ComponentType.Assembly);
            await ComponentRepository.InsertAsync(assembly1);
            assembly2 = await ComponentDomainService.CreateAsync("ass2Code", [new ComponentTranslation("en", "ass2Label")], ComponentType.Assembly);
            await ComponentRepository.InsertAsync(assembly2);
            await CommandSender.Send(new CreateBomLineCommand(assembly1.Id, assembly2.Id, 1));
        });
        await WithUnitOfWorkAsync(async () =>
        {
            await CommandSender.Send(new CreateBomLineCommand(assembly1.Id, assembly2.Id, 1));
        }).ShouldThrowAsync<TException>();
    }

    [Fact]
    public async Task Send_CreateBomLineCommand_With_Part_As_Parent_Should_Throw_BusinessException()
    {
        // Arrange
        Component assembly = null!;
        Component part = null!;
        Guid tenantId = Guid.NewGuid();
        CurrentTenant.Change(tenantId);
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        await WithUnitOfWorkAsync(async () =>
        {
            assembly = await ComponentDomainService.CreateAsync("assCode", [new ComponentTranslation("en", "assLabel")], ComponentType.Assembly);
            await ComponentRepository.InsertAsync(assembly);
            part = await ComponentDomainService.CreateAsync("partCode", [new ComponentTranslation("en", "partLabel")], ComponentType.Part);
            await ComponentRepository.InsertAsync(part);
        });
        await WithUnitOfWorkAsync(async () =>
        {
            await CommandSender.Send(new CreateBomLineCommand(part.Id, assembly.Id, 1)).ShouldThrowAsync<BusinessException>();
        });
    }

    [Fact]
    public async Task Send_CreateBomLineCommand_With_Depp_Cycling_Should_Throw_BusinessException()
    {
        Component assembly1 = null!;
        Component assembly3 = null!;
        Guid tenantId = Guid.NewGuid();
        CurrentTenant.Change(tenantId);
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        await WithUnitOfWorkAsync(async () =>
        {
            assembly1 = await ComponentDomainService.CreateAsync("ass1Code", [new ComponentTranslation("en", "ass1Label")], ComponentType.Assembly);
            await ComponentRepository.InsertAsync(assembly1);
            Component assembly2 = await ComponentDomainService.CreateAsync("ass2Code", [new ComponentTranslation("en", "ass2Label")], ComponentType.Assembly);
            await ComponentRepository.InsertAsync(assembly2);
            assembly3 = await ComponentDomainService.CreateAsync("ass3Code", [new ComponentTranslation("en", "ass3Label")], ComponentType.Assembly);
            await ComponentRepository.InsertAsync(assembly3);
            await CommandSender.Send(new CreateBomLineCommand(assembly1.Id, assembly2.Id, 1));
            await CommandSender.Send(new CreateBomLineCommand(assembly2.Id, assembly3.Id, 1));
        });
        await WithUnitOfWorkAsync(async () =>
        {
            await CommandSender.Send(new CreateBomLineCommand(assembly3.Id, assembly1.Id, 1)).ShouldThrowAsync<BusinessException>();
        });
    }
}