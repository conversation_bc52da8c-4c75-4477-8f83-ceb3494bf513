using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using NSubstitute;
using Shouldly;
using SpareParts.Core.Components.Queries;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Resources;
using Volo.Abp.Authorization;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Core.Components.QueryHandlers;
public abstract class GetDocumentCountByComponentQueryHandlerTests<TStartupModule> : ComponentTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    private IAbpAuthorizationService _authorizationService = default!;
    private bool _authorizationResultSuccess;

    protected override void AfterAddApplication(IServiceCollection services)
    {
        _authorizationService = Substitute.For<IAbpAuthorizationService>();
        _authorizationService.AuthorizeAsync(Arg.Any<ClaimsPrincipal>(), Arg.Any<object?>(), Arg.Any<string>())
            .Returns(_ => _authorizationResultSuccess ? AuthorizationResult.Success() : AuthorizationResult.Failed());

        services.Replace(ServiceDescriptor.Singleton(_authorizationService));
    }

    [Fact]
    public async Task GetDocumentCountByComponent_Should_Return_Documents_Count()
    {
        _authorizationResultSuccess = true;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true, GenerateDocumentsForProducts = true };
        await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                List<Product> products = await ProductRepository.GetListAsync();
                var firstProduct = products.First();
                List<ComponentDocument> componentDocuments = await ComponentDocumentRepository.GetListAsync(x => x.ComponentId == firstProduct.Id);
                List<Guid> documentIds = componentDocuments.Select(x => x.DocumentId).ToList();
                List<Document> documents = await DocumentRepository.GetListAsync(x => documentIds.Contains(x.Id));

                // Act
                List<Guid> ids = products.Select(x => x.Id).ToList();
                Dictionary<Guid, int> results = await QuerySender.Send(new GetDocumentCountByComponentQuery(ids));

                // Assert
                results.Count.ShouldBe(ids.Count);
                int firstCounter = results[firstProduct.Id];
                firstCounter.ShouldBe(documents.Count);
            });
        }
    }

    [Fact]
    public async Task GetDocumentCountByComponent_Should_Return_Zero_If_Component_Not_Public_And_User_Not_Authorized()
    {
        _authorizationResultSuccess = false;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true };
        await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            // Arrange
            Product product = default!;
            await WithUnitOfWorkAsync(async () =>
            {
                product = await ProductRepository.FirstAsync();
                if (product.IsVisible)
                {
                    product.IsVisible = true;
                    product.IsPublic = false;
                    await ProductRepository.UpdateAsync(product);
                }
            });
            await WithUnitOfWorkAsync(async () =>
            {
                // Act
                Dictionary<Guid, int> results = await QuerySender.Send(new GetDocumentCountByComponentQuery([product.Id]));

                // Assert
                results.Count.ShouldBe(0);
            });
        }
    }

    [Fact]
    public async Task GetDocumentCountByComponent_Should_Return_Zero_If_Component_Not_Visible_And_User_Not_Authorized()
    {
        _authorizationResultSuccess = false;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true };
        await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            // Arrange
            Product product = default!;
            await WithUnitOfWorkAsync(async () =>
            {
                product = await ProductRepository.FirstAsync();
                if (product.IsVisible)
                {
                    product.IsVisible = false;
                    product.IsPublic = true;
                    await ProductRepository.UpdateAsync(product);
                }
            });
            await WithUnitOfWorkAsync(async () =>
            {
                // Act
                Dictionary<Guid, int> results = await QuerySender.Send(new GetDocumentCountByComponentQuery([product.Id]));

                // Assert
                results.Count.ShouldBe(0);
            });
        }
    }

    [Fact]
    public async Task GetDocumentCountByComponent_Should_Return_Zero_If_Component_Deleted_And_User_Not_Authorized()
    {
        _authorizationResultSuccess = false;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true };
        await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            // Arrange
            Product product = default!;
            await WithUnitOfWorkAsync(async () =>
            {
                product = await ProductRepository.FirstAsync();
                if (product.IsVisible)
                {
                    product.IsVisible = true;
                    product.IsPublic = true;
                    await ProductRepository.UpdateAsync(product);
                }
            });
            await WithUnitOfWorkAsync(async () =>
            {
                // Act
                Dictionary<Guid, int> results = await QuerySender.Send(new GetDocumentCountByComponentQuery([product.Id]));

                // Assert
                results.Count.ShouldBe(0);
            });
        }
    }
}
