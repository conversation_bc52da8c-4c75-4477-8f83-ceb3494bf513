using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Shouldly;
using SpareParts.Core.Components.Queries;
using SpareParts.Core.Components.QueryFilters;
using SpareParts.Core.Documents.Dtos;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Resources;
using SpareParts.Core.Enums;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Data;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Localization;
using Volo.Abp.Modularity;
using Volo.Abp.Validation;
using Xunit;

namespace SpareParts.Core.Components.QueryHandlers;
public abstract class GetComponentDocumentsQueryHandlerTests<TStartupModule> : ComponentTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    [Fact]
    public async Task GetComponentDocuments_Should_Return_Empty_List()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true };
        await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            // Arrange
            await WithUnitOfWorkAsync(async () =>
            {
                IQueryable<Product> productQueryable = await ProductRepository.WithDetailsAsync(x => x.Component.Documents);
                Product product = productQueryable.First();

                // Act
                PagedResultDto<DocumentDto> documents = await QuerySender.Send(
                    new GetComponentDocumentsQuery(product.Id, new ComponentDocumentPaginationFilter()));

                // Assert
                documents.Items.ShouldBeEmpty();
            });

        }
    }

    [Fact]
    public async Task GetComponentDocuments_Should_Return_Documents()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true, GenerateDocumentsForProducts = true };
        await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                IQueryable<Product> productQueryable = await ProductRepository.WithDetailsAsync(x => x.Component.Documents);
                productQueryable = productQueryable.Where(x => x.Component.Documents.Count > 0);
                Product product = productQueryable.First();

                // Act
                PagedResultDto<DocumentDto> results = await QuerySender.Send(
                    new GetComponentDocumentsQuery(product.Id, new ComponentDocumentPaginationFilter()));

                // Assert
                List<Guid> ids = product.Component.Documents.Select(x => x.Id).OrderBy(d => d).ToList();
                results.TotalCount.ShouldBe(product.Component.Documents.Count);
                results.Items.Select(d => d.Id).ToList().ShouldBeEquivalentTo(ids);
            });
        }
    }

    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    public async Task GetComponentDocuments_Should_Sort_Documents_By_Ascending_Label(bool ascending)
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true, GenerateDocumentsForProducts = true };
        await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            // Arrange
            await WithUnitOfWorkAsync(async () =>
            {
                IQueryable<Product> productQueryable = await ProductRepository.WithDetailsAsync(x => x.Component.Documents);
                productQueryable = productQueryable.Where(x => x.Component.Documents.Count > 1);
                Product product = productQueryable.First();

                using (CultureHelper.Use("en"))
                {
                    ComponentDocumentPaginationFilter filter = new()
                    {
                        Sort = "label",
                        SortBy = ascending ? AutoFilterer.Enums.Sorting.Ascending : AutoFilterer.Enums.Sorting.Descending
                    };

                    // Act
                    PagedResultDto<DocumentDto> results = await QuerySender.Send(new GetComponentDocumentsQuery(product.Id, filter));

                    // Assert
                    List<Guid> orderedDocumentIds = ascending
                        ? product.Component.Documents.OrderBy(d => d.Translations.First(t => t.Language == "en").Label).Select(x => x.Id).ToList()
                        : product.Component.Documents.OrderByDescending(d => d.Translations.First(t => t.Language == "en").Label).Select(x => x.Id).ToList();

                    results.TotalCount.ShouldBe(product.Component.Documents.Count);
                    results.Items.Select(d => d.Id).ToList().ShouldBeEquivalentTo(orderedDocumentIds);
                }
            });
        }
    }

    [Fact]
    public async Task GetComponentDocuments_With_Unauthorized_Sort_Should_Throw_Exception()
    {
        Guid tenantId = GuidGenerator.Create();
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        using (CurrentTenant.Change(tenantId))
        {
            // Arrange
            Component assembly = null!;
            await base.WithUnitOfWorkAsync(async () =>
            {
                DocumentTranslation document1Translation = new(Faker.Locale, "document b", Faker.Lorem.Word());
                Document document = await DocumentDomainService.CreateAsync(Faker.Internet.Url(), [document1Translation], []);
                await DocumentRepository.InsertAsync(document, true);

                assembly = await ComponentDomainService.CreateAsync(Faker.Commerce.Product(), [new ComponentTranslation(Faker.Locale, Faker.Commerce.ProductName())], ComponentType.Assembly);
                await ComponentDocumentRepository.InsertAsync(new ComponentDocument(assembly.Id, document.Id));
                await ComponentRepository.InsertAsync(assembly);
            });

            await WithUnitOfWorkAsync(async () =>
            {
                using (CultureHelper.Use("fr"))
                {
                    ComponentDocumentPaginationFilter filter = new()
                    {
                        Sort = "Unknown"
                    };

                    // Act
                    await QuerySender.Send(new GetComponentDocumentsQuery(assembly.Id, filter)).ShouldThrowAsync<AbpValidationException>();
                }
            });
        }
    }

    [Fact]
    public async Task GetComponentDocuments_Should_Throw_Exception_If_Component_Not_Exist()
    {
        Guid tenantId = GuidGenerator.Create();
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        using (CurrentTenant.Change(tenantId))
        {
            // Arrange
            await WithUnitOfWorkAsync(async () =>
            {
                using (CultureHelper.Use("fr"))
                {
                    // Act & Assert
                    await QuerySender.Send(new GetComponentDocumentsQuery(Guid.NewGuid(), new ComponentDocumentPaginationFilter())).ShouldThrowAsync<EntityNotFoundException>();
                }
            });
        }
    }
}
