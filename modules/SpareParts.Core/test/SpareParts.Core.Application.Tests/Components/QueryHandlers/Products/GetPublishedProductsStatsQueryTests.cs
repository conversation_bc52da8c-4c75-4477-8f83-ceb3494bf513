using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Shouldly;
using SpareParts.Common;
using SpareParts.Common.Companies;
using SpareParts.Core.Components.Dtos.Products;
using SpareParts.Core.Components.Queries.Products;
using SpareParts.Core.Entities.Components;
using Volo.Abp.Authorization;
using Volo.Abp.Modularity;
using Volo.Abp.Security.Claims;
using Xunit;

namespace SpareParts.Core.Components.QueryHandlers.Products;
public abstract class GetPublishedProductsStatsQueryTests<TStartupModule> : ComponentTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    private readonly ICurrentPrincipalAccessor _currentPrincipalAccessor;

    protected GetPublishedProductsStatsQueryTests()
    {
        _currentPrincipalAccessor = ServiceProvider.GetRequiredService<ICurrentPrincipalAccessor>();
    }

    [Fact]
    public async Task GetPublishedProductsQuery_Should_Return_Stats()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true };
        await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                //Arrange
                List<Product> visibleProducts = await ProductRepository.GetListAsync(x => x.IsVisible);

                using (_currentPrincipalAccessor.Change([new Claim(CommonClaimTypes.CompanyType, CompanyType.Internal.ToString())]))
                {
                    // Act
                    PublishedProductsStatsDto result = await QuerySender.Send(new GetPublishedProductsStatsQuery());

                    // Assert
                    result.Count.ShouldBe(visibleProducts.Count);
                }
            });
        }
    }

    [Fact]
    public async Task GetPublishedProductsQuery_With_External_User_Should_Throw_Authorization_Exception()
    {
        TestDataContext context = new(GuidGenerator.Create());
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                using (_currentPrincipalAccessor.Change([new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString())]))
                {
                    // Act
                    await QuerySender.Send(new GetPublishedProductsStatsQuery()).ShouldThrowAsync<AbpAuthorizationException>();
                }
            });
        }
    }
}
