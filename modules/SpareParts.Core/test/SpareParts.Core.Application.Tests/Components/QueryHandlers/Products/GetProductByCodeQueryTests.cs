using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Shouldly;
using SpareParts.Common;
using SpareParts.Common.Companies;
using SpareParts.Common.DataFilter;
using SpareParts.Core.Components.Dtos.Products;
using SpareParts.Core.Components.Queries.Products;
using SpareParts.Core.DataVisibility;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Equipments;
using SpareParts.Core.Features;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.GlobalFeatures;
using Volo.Abp.Modularity;
using Volo.Abp.Security.Claims;
using Xunit;

namespace SpareParts.Core.Components.QueryHandlers.Products;

public abstract class GetProductByCodeQueryTests<TStartupModule> : ComponentTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    [Fact]
    public async Task GetProductByCode_When_Code_Does_Not_Exist_Should_Throw_EntityNotFoundException()
    {
        using (CurrentTenant.Change(GuidGenerator.Create()))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Act and Assert
                await Should.ThrowAsync<EntityNotFoundException>(QuerySender.Send(new GetProductByCodeQuery(GuidGenerator.Create().ToString("N"))));
            });
        }
    }

    [Fact]
    public async Task GetProductByCode_With_Existing_Code_Should_Return_Product_With_Correct_Fields()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid productId = storeContext.ProductNotLinkedToProductFamilyIdByCodes.First().Value;
                Product product = await ProductRepository.GetAsync(productId);
                int childCount = await BomLineRepository.CountAsync(x => x.ParentAssemblyId == product.Id);

                // Act
                ProductDto productDto = await QuerySender.Send(new GetProductByCodeQuery(product.Component.Code));

                // Assert
                productDto.Id.ShouldBe(product.Id);
                productDto.Code.ShouldBe(product.Component.Code);
                productDto.IsVisible.ShouldBe(product.IsVisible);
                productDto.IsPublic.ShouldBe(product.IsPublic);
                productDto.ImageId.ShouldBe(product.Component.ImageId);
                productDto.Translations.Count.ShouldBe(1);
                productDto.Translations[0].Language.ShouldBe("en");
                productDto.Translations[0].Label.ShouldBe(product.Component.Translations.First().Label);
                productDto.OnlineTranslations.Count.ShouldBe(1);
                productDto.OnlineTranslations[0].Language.ShouldBe("en");
                productDto.OnlineTranslations[0].Label.ShouldBe(product.Component.OnlineTranslations.First().Label);
                productDto.IsInProductFamily.ShouldBeFalse();
                productDto.ChildCount.ShouldBe(childCount);
                productDto.CreationTime.ShouldBe(product.CreationTime);
                productDto.CreatorId.ShouldBe(product.CreatorId);
                productDto.LastModificationTime.ShouldBe(product.LastModificationTime);
                productDto.LastModifierId.ShouldBe(product.LastModifierId);
            });
        }
    }

    [Fact]
    public async Task GetProductsByCode_Should_Not_Return_Result_If_User_Is_External_Content_Viewer_And_Product_Is_Not_Public()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        Guid companyId = Guid.NewGuid();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString())]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                string productCode = string.Empty;
                await WithUnitOfWorkAsync(async () =>
                {
                    // Arrange
                    Guid productId = storeContext.ProductIdByCodes.First().Value;
                    Product product = await ProductRepository.GetAsync(productId);
                    productCode = product.Component.Code;

                    product.IsPublic = false;
                    product.IsVisible = true;
                    await ProductRepository.UpdateAsync(product);
                });

                using (DataFilter.Enable<IHasVisibility>())
                {
                    using (DataFilter.Enable<IHasPublic>())
                    {
                        await WithUnitOfWorkAsync(async () =>
                        {
                            // Act & Assert
                            await QuerySender.Send(new GetProductByCodeQuery(productCode)).ShouldThrowAsync<EntityNotFoundException>();
                        });
                    }
                }
            }
        }
    }

    [Fact]
    public async Task GetProductsByCode_Should_Not_Return_Result_If_User_Is_External_Content_Viewer_And_Product_Is_Not_Public_Without_Equipment_And_Equipments_Enabled()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true, GenerateCompanies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        GlobalFeatureManager.Instance.Modules.CoreFeatures().Equipment.Enable();

        Guid companyId = storeContext.CompaniesInternal.First();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString())]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                string productCode = string.Empty;
                await WithUnitOfWorkAsync(async () =>
                {
                    // Arrange
                    Guid productId = storeContext.ProductIdByCodes.First().Value;
                    Product product = await ProductRepository.GetAsync(productId);
                    productCode = product.Component.Code;

                    product.IsPublic = false;
                    product.IsVisible = true;
                    await ProductRepository.UpdateAsync(product);
                });

                using (DataFilter.Enable<IHasVisibility>())
                {
                    using (DataFilter.Enable<IHasPublic>())
                    {
                        await WithUnitOfWorkAsync(async () =>
                        {
                            // Act & Assert
                            await QuerySender.Send(new GetProductByCodeQuery(productCode)).ShouldThrowAsync<EntityNotFoundException>();
                        });
                    }
                }
            }
        }
    }

    [Fact]
    public async Task GetProductsByCode_Should_Return_Result_If_User_Is_External_Content_Viewer_And_Product_Is_Not_Public_With_Equipment_And_Equipments_Enabled()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true, GenerateEquipments = true, GenerateCompanies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        GlobalFeatureManager.Instance.Modules.CoreFeatures().Equipment.Enable();

        Guid companyId = storeContext.CompaniesInternal.First();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString())]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                string productCode = string.Empty;
                await WithUnitOfWorkAsync(async () =>
                {
                    // Arrange
                    Guid equipmentId = storeContext.EquipmentsWithCompany.First();
                    Equipment equipment = await EquipmentRepository.GetAsync(equipmentId);

                    Guid productId = equipment.ProductId;
                    Product product = await ProductRepository.GetAsync(productId);
                    productCode = product.Component.Code;

                    product.IsPublic = false;
                    product.IsVisible = true;
                    await ProductRepository.UpdateAsync(product);
                });

                using (DataFilter.Enable<IHasVisibility>())
                {
                    using (DataFilter.Enable<IHasPublic>())
                    {
                        await WithUnitOfWorkAsync(async () =>
                        {
                            // Act 
                            ProductDto result = await QuerySender.Send(new GetProductByCodeQuery(productCode));

                            //Assert
                            result.ShouldNotBeNull();
                        });
                    }
                }
            }
        }
    }

    [Fact]
    public async Task GetProductsByCode_Should_Not_Return_Result_If_User_Is_External_Content_Viewer_And_Product_Is_Not_Public_With_Equipment_And_Equipments_Disabled()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true, GenerateEquipments = true, GenerateCompanies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        GlobalFeatureManager.Instance.Modules.CoreFeatures().Equipment.Disable();

        Guid companyId = storeContext.CompaniesInternal.First();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString())]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                string productCode = string.Empty;
                await WithUnitOfWorkAsync(async () =>
                {
                    // Arrange
                    Guid equipmentId = storeContext.EquipmentsWithCompany.First();
                    Equipment equipment = await EquipmentRepository.GetAsync(equipmentId);

                    Guid productId = equipment.ProductId;
                    Product product = await ProductRepository.GetAsync(productId);
                    productCode = product.Component.Code;

                    product.IsPublic = false;
                    product.IsVisible = true;
                    await ProductRepository.UpdateAsync(product);
                });

                using (DataFilter.Enable<IHasVisibility>())
                {
                    using (DataFilter.Enable<IHasPublic>())
                    {
                        await WithUnitOfWorkAsync(async () =>
                        {
                            // Act & Assert
                            await QuerySender.Send(new GetProductByCodeQuery(productCode)).ShouldThrowAsync<EntityNotFoundException>();
                        });
                    }
                }
            }
        }
    }
}
