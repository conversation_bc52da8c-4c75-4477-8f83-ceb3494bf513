using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Shouldly;
using SpareParts.Common;
using SpareParts.Common.Companies;
using SpareParts.Common.DataFilter;
using SpareParts.Core.Components.Dtos.Products;
using SpareParts.Core.Components.Queries.Products;
using SpareParts.Core.DataVisibility;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Equipments;
using SpareParts.Core.Features;
using Volo.Abp.GlobalFeatures;
using Volo.Abp.Modularity;
using Volo.Abp.Security.Claims;
using Xunit;

namespace SpareParts.Core.Components.QueryHandlers.Products;

public abstract class GetProductsByIdsQueryTests<TStartupModule> : ComponentTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    [Fact]
    public async Task GetProductsByIds_Should_Return_Empty_List_With_Not_Existing_Products()
    {
        using (CurrentTenant.Change(GuidGenerator.Create()))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                List<ProductDto> results = await QuerySender.Send(new GetProductsByIdsQuery([Guid.NewGuid(), Guid.NewGuid()]));

                results.ShouldBeEmpty();
            });
        }
    }

    [Fact]
    public async Task GetProductsByIds_Should_Return_Results_If_User_Is_External_Content_Viewer_And_Products_Are_Public_And_Visible()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        Guid companyId = Guid.NewGuid();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, nameof(CompanyType.External))]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                List<Guid> productsId = [Guid.Empty];
                await WithUnitOfWorkAsync(async () =>
                {
                    // Arrange
                    productsId = storeContext.ProductIdByCodes.Values.ToList();

                    List<Product> products = await ProductRepository.GetListAsync(x => productsId.Contains(x.Id));
                    foreach (Product product in products)
                    {
                        product.IsPublic = true;
                        product.IsVisible = true;
                    }
                    await ProductRepository.UpdateManyAsync(products);
                });

                using (DataFilter.Enable<IHasVisibility>())
                {
                    using (DataFilter.Enable<IHasPublic>())
                    {
                        await WithUnitOfWorkAsync(async () =>
                        {
                            // Act & Assert
                            List<ProductDto> results = await QuerySender.Send(new GetProductsByIdsQuery(productsId));

                            // Assert
                            results.Count.ShouldBe(productsId.Count);

                            IEnumerable<Guid> ids = results.Select(x => x.Id).Intersect(productsId);
                            ids.Count().ShouldBe(results.Count);
                        });
                    }
                }
            }
        }
    }

    [Fact]
    public async Task GetProductsByIds_Should_Not_Return_Results_If_User_Is_External_Content_Viewer_And_Products_Are_Not_Public()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        Guid companyId = Guid.NewGuid();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, nameof(CompanyType.External))]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                List<Guid> productsId = [Guid.Empty];
                await WithUnitOfWorkAsync(async () =>
                {
                    // Arrange
                    productsId = storeContext.ProductIdByCodes.Values.ToList();

                    List<Product> products = await ProductRepository.GetListAsync(x => productsId.Contains(x.Id));
                    foreach (Product product in products)
                    {
                        product.IsPublic = false;
                        product.IsVisible = true;
                    }
                    await ProductRepository.UpdateManyAsync(products);
                });

                using (DataFilter.Enable<IHasVisibility>())
                {
                    using (DataFilter.Enable<IHasPublic>())
                    {
                        await WithUnitOfWorkAsync(async () =>
                        {
                            // Act & Assert
                            List<ProductDto> results = await QuerySender.Send(new GetProductsByIdsQuery(productsId));

                            // Assert
                            results.Count.ShouldBe(0);
                        });
                    }
                }
            }
        }
    }

    [Fact]
    public async Task GetProductsByIds_Should_Not_Return_Results_If_User_Is_External_Content_Viewer_And_Products_Are_Not_Visible()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProductFamilies = true, GenerateProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        Guid companyId = Guid.NewGuid();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, nameof(CompanyType.External))]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                List<Guid> productsId = [Guid.Empty];
                await WithUnitOfWorkAsync(async () =>
                {
                    // Arrange
                    productsId = storeContext.ProductIdByCodes.Values.ToList();

                    List<Product> products = await ProductRepository.GetListAsync(x => productsId.Contains(x.Id));
                    foreach (Product product in products)
                    {
                        product.IsPublic = true;
                        product.IsVisible = false;
                    }
                    await ProductRepository.UpdateManyAsync(products);
                });

                using (DataFilter.Enable<IHasVisibility>())
                {
                    using (DataFilter.Enable<IHasPublic>())
                    {
                        await WithUnitOfWorkAsync(async () =>
                        {
                            // Act & Assert
                            List<ProductDto> results = await QuerySender.Send(new GetProductsByIdsQuery(productsId));

                            // Assert
                            results.Count.ShouldBe(0);
                        });
                    }
                }
            }
        }
    }

    [Fact]
    public async Task GetProductsByIds_Should_Return_Results_If_User_Is_External_Content_Viewer_And_Products_Are_Not_Public_With_Equipments_And_Equipments_Enabled()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true, GenerateEquipments = true, GenerateCompanies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        GlobalFeatureManager.Instance.Modules.CoreFeatures().Equipment.Enable();

        Guid companyId = storeContext.CompaniesInternal.First();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, nameof(CompanyType.External))]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                List<Guid> productsId = [Guid.Empty];
                await WithUnitOfWorkAsync(async () =>
                {
                    // Arrange
                    Equipment equipment = await EquipmentRepository.GetAsync(storeContext.EquipmentsWithCompany.First());
                    productsId = [equipment.ProductId];

                    Product product = await ProductRepository.GetAsync(equipment.ProductId);
                    product.IsPublic = false;
                    product.IsVisible = true;
                    await ProductRepository.UpdateAsync(product);
                });

                using (DataFilter.Enable<IHasVisibility>())
                {
                    using (DataFilter.Enable<IHasPublic>())
                    {
                        await WithUnitOfWorkAsync(async () =>
                        {
                            // Act & Assert
                            List<ProductDto> results = await QuerySender.Send(new GetProductsByIdsQuery(productsId));

                            // Assert
                            results.Count.ShouldBe(1);
                            results[0].Id.ShouldBe(productsId[0]);
                        });
                    }
                }
            }
        }
    }

    [Fact]
    public async Task GetProductsByIds_Should_Return_Results_If_User_Is_External_Content_Viewer_And_Products_Are_Not_Public_With_Equipments_And_Equipments_Disabled()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true, GenerateEquipments = true, GenerateCompanies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);

        GlobalFeatureManager.Instance.Modules.CoreFeatures().Equipment.Disable();

        Guid companyId = storeContext.CompaniesInternal.First();
        using (CurrentPrincipalAccessor.Change([
                   new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                   new Claim(CommonClaimTypes.CompanyType, nameof(CompanyType.External))]))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                List<Guid> productsId = [Guid.Empty];
                await WithUnitOfWorkAsync(async () =>
                {
                    // Arrange
                    Equipment equipment = await EquipmentRepository.GetAsync(storeContext.EquipmentsWithCompany.First());
                    productsId = [equipment.ProductId];

                    Product product = await ProductRepository.GetAsync(equipment.ProductId);
                    product.IsPublic = false;
                    product.IsVisible = true;
                    await ProductRepository.UpdateAsync(product);
                });

                using (DataFilter.Enable<IHasVisibility>())
                {
                    using (DataFilter.Enable<IHasPublic>())
                    {
                        await WithUnitOfWorkAsync(async () =>
                        {
                            // Act & Assert
                            List<ProductDto> results = await QuerySender.Send(new GetProductsByIdsQuery(productsId));

                            // Assert
                            results.Count.ShouldBe(0);
                        });
                    }
                }
            }
        }
    }
}