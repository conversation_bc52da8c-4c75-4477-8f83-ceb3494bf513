using System;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using NSubstitute;
using Shouldly;
using SpareParts.Core.Components.Dtos;
using SpareParts.Core.Components.Queries;
using SpareParts.Core.Entities.Components;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Authorization;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Core.Components.QueryHandlers;

public abstract class GetComponentBomLinesQueryTests<TStartupModule> : ComponentTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    private IAbpAuthorizationService _authorizationService = default!;
    private bool _authorizationResultSuccess;

    protected override void AfterAddApplication(IServiceCollection services)
    {
        _authorizationService = Substitute.For<IAbpAuthorizationService>();
        _authorizationService.AuthorizeAsync(Arg.Any<ClaimsPrincipal>(), Arg.Any<object?>(), Arg.Any<string>())
            .Returns(_ => _authorizationResultSuccess ? AuthorizationResult.Success() : AuthorizationResult.Failed());

        services.Replace(ServiceDescriptor.Singleton(_authorizationService));
    }

    [Fact]
    public async Task GetComponentBomLines_With_Invalid_ComponentId_Should_Throw_AbpAuthorizationException()
    {
        _authorizationResultSuccess = false;
        using (CurrentTenant.Change(GuidGenerator.Create()))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                await QuerySender.Send(new GetComponentBomLinesQuery(GuidGenerator.Create(), new QueryFilters.BomLinePaginationFilter()))
                    .ShouldThrowAsync<AbpAuthorizationException>();
            });
        }
    }

    [Fact]
    public async Task GetComponentBomLines_With_ComponentId_Should_Return_Bom_Lines()
    {
        _authorizationResultSuccess = true;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true, GenerateAssemblies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
              {
                  // Arrange
                  IRepository<Product, Guid> repository = GetRequiredService<IRepository<Product, Guid>>();
                  string productCode = storeContext.ProductCodePrefix + "1";
                  Product product = await repository.GetAsync(storeContext.ProductIdByCodes[productCode]);

                  // Act
                  PagedResultDto<BomLineDto> results = await QuerySender.Send(new GetComponentBomLinesQuery(product.Id, new QueryFilters.BomLinePaginationFilter()));

                  // Assert
                  results.ShouldNotBeNull();
                  results.TotalCount.ShouldBe(storeContext.ComponentBomLinesCountByCodes[productCode]);
                  results.Items.ShouldNotBeNull();
                  results.Items.Count.ShouldBeLessThanOrEqualTo(10);
              });
        }
    }
}
