using Shouldly;
using System.Collections.Generic;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Core.Components.Dtos;
public abstract class ComponentOnlineTranslationInputDtoTests<TStartupModule> : ComponentTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    [Fact]
    public void ComponentOnlineTranslationInputDto_Should_Throw_Exception_With_Label_Too_Short()
    {
        // Arrange
        ComponentOnlineTranslationInputDto translationDto = new("x", Faker.Lorem.Word());

        // Act
        IList<System.ComponentModel.DataAnnotations.ValidationResult> errors = ValidateModel(translationDto);

        // Assert
        errors.Count.ShouldBe(1);
        errors[0].ErrorMessage!.ShouldContain("Label");
    }
}