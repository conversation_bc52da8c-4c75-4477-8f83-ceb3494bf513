using System;
using System.Threading.Tasks;
using Shouldly;
using SpareParts.Common.Dtos;
using SpareParts.Core.Documents.Commands;
using SpareParts.Core.Documents.Dtos;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Enums;
using Volo.Abp.Data;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Core.Documents.CommandHandlers;

public abstract class CreateLinkDocumentCommandHandlerTests<TStartupModule> : DocumentTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    [Fact]
    public async Task CreateLinkDocumentCommand_Should_Succeed()
    {
        Guid tenantId = GuidGenerator.Create();
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Component assembly = await ComponentDomainService.CreateAsync("assCode", [new ComponentTranslation("en", "assLabel")], ComponentType.Assembly);
                assembly = await ComponentRepository.InsertAsync(assembly, true);
                _ = assembly.Id;
            });

            await WithUnitOfWorkAsync(async () =>
            {
                //Act
                CommonTranslationDto documentTranslationDto = CreateDocumentTranslationDtoWithFakeValues(Faker.Locale);
                DocumentResourceDto<string> documentResourceDto = new() { Type = DocumentType.Link, Value = "https://fake.com/file.png" };
                CreateLinkDocumentCommand createLinkDocumentCommand = new(documentResourceDto, [documentTranslationDto], []);

                DocumentDto documentDto = await CommandSender.Send(createLinkDocumentCommand);

                // Assert
                documentDto.Resource.ShouldNotBeNull();
                documentDto.Resource.Type.ShouldBe(documentResourceDto.Type);
                documentDto.Resource.Value.ShouldBe(documentResourceDto.Value);
                documentDto.Translations.ShouldNotBeNull();
                documentDto.Translations.Count.ShouldBe(1);
                documentDto.Translations[0].Language.ShouldBe(documentTranslationDto.Language);
                documentDto.Translations[0].Label.ShouldBe(documentTranslationDto.Label);
                documentDto.Translations[0].Description.ShouldBe(documentTranslationDto.Description);
            });
        }
    }
}