using System;
using System.Threading.Tasks;
using Bogus.Extensions;
using Microsoft.Extensions.DependencyInjection;
using Shouldly;
using SpareParts.Core.Documents.Commands;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Resources;
using SpareParts.Core.Enums;
using Volo.Abp.Data;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;
using Volo.Abp.Validation;
using Xunit;

namespace SpareParts.Core.Documents.CommandHandlers;
public abstract class DeleteDocumentTranslationCommandHandlerTests<TStartupModule> : DocumentTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    private readonly IRepository<DocumentTranslation> _documentTranslationRepository;

    protected DeleteDocumentTranslationCommandHandlerTests()
    {
        _documentTranslationRepository = ServiceProvider.GetRequiredService<IRepository<DocumentTranslation>>();
    }

    [Fact]
    public async Task DeleteComponentDocumentTranslationCommand_Should_Delete_Translation()
    {
        // Arrange
        Guid tenantId = GuidGenerator.Create();
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));

        using (CurrentTenant.Change(tenantId))
        {
            Component assembly;
            Guid documentId = Guid.NewGuid();

            // Arrange
            await WithUnitOfWorkAsync(async () =>
            {
                DocumentTranslation documentTranslation = new("de", Faker.Lorem.Word().ClampLength(2), Faker.Lorem.Word());
                Document document = await DocumentDomainService.CreateAsync(Faker.Internet.Url(),
                    [new DocumentTranslation(Faker.Locale, Faker.Lorem.Word().ClampLength(2), Faker.Lorem.Word()), documentTranslation], []);
                await DocumentRepository.InsertAsync(document, true);
                documentId = document.Id;

                assembly = await ComponentDomainService.CreateAsync(Faker.Commerce.Product(), [new ComponentTranslation(Faker.Locale, Faker.Commerce.ProductName())], ComponentType.Assembly);
                await ComponentDocumentRepository.InsertAsync(new ComponentDocument(assembly.Id, documentId));
                await ComponentRepository.InsertAsync(assembly);
            });

            // Act
            await WithUnitOfWorkAsync(async () =>
            {
                await CommandSender.Send(new DeleteDocumentTranslationCommand(documentId, "de"));
            });

            // Assert
            await WithUnitOfWorkAsync(async () =>
            {
                DocumentTranslation? deletedTranslation = await _documentTranslationRepository.FindAsync(x => x.DocumentId == documentId && x.Language == "de");
                deletedTranslation.ShouldBeNull();

                Document document = await DocumentRepository.GetAsync(documentId);
                document.Translations.ShouldNotContain(t => t.Language == "de");
            });
        }
    }

    [Fact]
    public async Task DeleteComponentDocumentTranslationCommand_Default_Translation_Should_Throw_Exception()
    {
        // Arrange
        Guid tenantId = GuidGenerator.Create();
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));

        using (CurrentTenant.Change(tenantId))
        {
            Component assembly;
            Document document = null!;
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                document = await CreateLinkDocument();

                assembly = await ComponentDomainService.CreateAsync(Faker.Commerce.Product(), [new ComponentTranslation(Faker.Locale, Faker.Commerce.ProductName())], ComponentType.Assembly);
                await ComponentDocumentRepository.InsertAsync(new ComponentDocument(assembly.Id, document.Id));
                await ComponentRepository.InsertAsync(assembly);
            });

            // Act & Assert
            await WithUnitOfWorkAsync(async () =>
            {
                await Should.ThrowAsync<AbpValidationException>(CommandSender.Send(new DeleteDocumentTranslationCommand(document.Id, Faker.Locale)));
            });
        }
    }
}
