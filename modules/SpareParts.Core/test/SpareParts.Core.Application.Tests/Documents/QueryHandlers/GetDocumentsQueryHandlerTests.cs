using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using NSubstitute;
using Shouldly;
using SpareParts.Common;
using SpareParts.Common.Companies;
using SpareParts.Common.DataFilter;
using SpareParts.Common.Dtos;
using SpareParts.Core.Components.QueryFilters;
using SpareParts.Core.DataVisibility;
using SpareParts.Core.Documents.Dtos;
using SpareParts.Core.Documents.Queries;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Resources;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Authorization;
using Volo.Abp.Data;
using Volo.Abp.Localization;
using Volo.Abp.Modularity;
using Volo.Abp.Security.Claims;
using Volo.Abp.Validation;
using Xunit;

namespace SpareParts.Core.Documents.QueryHandlers;
public abstract class GetDocumentsQueryHandlerTests<TStartupModule> : DocumentTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    private readonly IDataFilter _dataFilter;
    private readonly ICurrentPrincipalAccessor _currentPrincipalAccessor;
    private IAbpAuthorizationService _authorizationService = null!;
    private bool _authorizationResultSuccess;

    protected GetDocumentsQueryHandlerTests()
    {
        _dataFilter = ServiceProvider.GetRequiredService<IDataFilter>();
        _currentPrincipalAccessor = ServiceProvider.GetRequiredService<ICurrentPrincipalAccessor>();
    }

    protected override void AfterAddApplication(IServiceCollection services)
    {
        _authorizationService = Substitute.For<IAbpAuthorizationService>();
        _authorizationService.AuthorizeAsync(Arg.Any<ClaimsPrincipal>(), Arg.Any<object?>(), Arg.Any<string>())
            .Returns(_ => _authorizationResultSuccess ? AuthorizationResult.Success() : AuthorizationResult.Failed());

        services.Replace(ServiceDescriptor.Singleton(_authorizationService));
    }

    [Fact]
    public async Task GetComponentsDocuments_Should_Not_Return_Not_Public_Document_If_Not_Have_ShowHidden_Permission()
    {
        _authorizationResultSuccess = false;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true, GenerateDocumentsForProducts = true };
        await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                //Arrange
                List<ProductFlattenHierarchy> productFlattenHierarchies = await ProductFlattenHierarchyRepository.GetListAsync(x => !x.IsPublic);
                IEnumerable<Guid> componentIds = productFlattenHierarchies.Select(x => x.ComponentId).Distinct();
                List<ComponentDocument> componentDocuments = await ComponentDocumentRepository.GetListAsync(x => componentIds.Contains(x.ComponentId));
                IEnumerable<Guid> documentIds = componentDocuments.Select(x => x.DocumentId).Distinct();

                using (_dataFilter.Enable<IHasPublic>())
                {
                    Guid userid = Guid.NewGuid();
                    using (_currentPrincipalAccessor.Change([new Claim(AbpClaimTypes.UserId, userid.ToString()),
                           new Claim(AbpClaimTypes.TenantId, context.TenantId.ToString())]))
                    {

                        // Act
                        PagedResultDto<DocumentDto> results = await QuerySender.Send(new GetDocumentsQuery(new DocumentPaginationFilter()));

                        // Assert
                        IEnumerable<DocumentDto> documentResults = results.Items.Where(x => documentIds.Contains(x.Id));
                        documentResults.Count().ShouldBe(0);
                    }
                }
            });
        }
    }

    [Fact]
    public async Task GetComponentsDocuments_Should_Not_Return_Not_Visible_Document_If_Not_Have_ShowHidden_Permission()
    {
        _authorizationResultSuccess = false;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true, GenerateDocumentsForProducts = true };
        await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                //Arrange
                List<ProductFlattenHierarchy> productFlattenHierarchies = await ProductFlattenHierarchyRepository.GetListAsync(x => !x.IsVisible);
                IEnumerable<Guid> componentIds = productFlattenHierarchies.Select(x => x.ComponentId).Distinct();
                List<ComponentDocument> componentDocuments = await ComponentDocumentRepository.GetListAsync(x => componentIds.Contains(x.ComponentId));
                IEnumerable<Guid> documentIds = componentDocuments.Select(x => x.DocumentId).Distinct();

                using (_dataFilter.Enable<IHasVisibility>())
                {
                    // Act
                    PagedResultDto<DocumentDto> results = await QuerySender.Send(new GetDocumentsQuery(new DocumentPaginationFilter()));

                    // Assert
                    IEnumerable<DocumentDto> documentResults = results.Items.Where(x => documentIds.Contains(x.Id));
                    documentResults.Count().ShouldBe(0);
                }
            });
        }
    }

    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    public async Task GetComponentsDocuments_Should_Return_Documents(bool authorizationResultSuccess)
    {
        _authorizationResultSuccess = authorizationResultSuccess;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true, GenerateDocumentsForProducts = true };
        await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                if (authorizationResultSuccess)
                {
                    _currentPrincipalAccessor.Change(new Claim(CommonClaimTypes.CompanyType,
                        CompanyType.Internal.ToString()));
                }
                // Act
                PagedResultDto<DocumentDto> results = await QuerySender.Send(new GetDocumentsQuery(new DocumentPaginationFilter()));

                // Assert
                List<ComponentDocument> componentDocuments;
                if (authorizationResultSuccess)
                {
                    componentDocuments = await ComponentDocumentRepository.GetListAsync();
                }
                else
                {
                    List<ProductFlattenHierarchy> productFlattenHierarchies = await ProductFlattenHierarchyRepository.GetListAsync();
                    IEnumerable<Guid> componentIds = productFlattenHierarchies.Select(x => x.ComponentId).Distinct();
                    componentDocuments = await ComponentDocumentRepository.GetListAsync(x => componentIds.Contains(x.ComponentId));
                }

                results.TotalCount.ShouldBe(componentDocuments.Count);

                DocumentDto resultsDocumentDtoWithComponents = results.Items[0];
                List<ComponentDocument> resultsComponentDocuments = componentDocuments.Where(x => x.DocumentId == resultsDocumentDtoWithComponents.Id).ToList();
                resultsDocumentDtoWithComponents.ComponentIds.Count.ShouldBe(resultsComponentDocuments.Count);

                Document resultDocument = await DocumentRepository.GetAsync(resultsDocumentDtoWithComponents.Id);
                resultsDocumentDtoWithComponents.CreationTime.ShouldBe(resultDocument.CreationTime);
                resultsDocumentDtoWithComponents.CreatorId.ShouldBe(resultDocument.CreatorId);
                resultsDocumentDtoWithComponents.Filename.ShouldBe(resultDocument.Resource!.FileName);
                resultsDocumentDtoWithComponents.LastModificationTime.ShouldBe(resultDocument.LastModificationTime);
                resultsDocumentDtoWithComponents.LastModifierId.ShouldBe(resultDocument.LastModifierId);
                resultsDocumentDtoWithComponents.Resource.Type.ShouldBe(resultDocument.Type);
                resultsDocumentDtoWithComponents.Resource.Value.ShouldBe(resultDocument.Resource.Id.ToString());
                resultsDocumentDtoWithComponents.Translations.Count.ShouldBe(resultDocument.Translations.Count);
                resultsDocumentDtoWithComponents.Translations[0].Language.ShouldBe(resultDocument.Translations.First().Language);
                resultsDocumentDtoWithComponents.Translations[0].Label.ShouldBe(resultDocument.Translations.First().Label);
                resultsDocumentDtoWithComponents.Translations[0].Description.ShouldBe(resultDocument.Translations.First().Description);
            });
        }
    }

    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    public async Task GetComponentsDocuments_Should_Return_Documents_Sorted_By_CreationTime(bool ascending)
    {
        _authorizationResultSuccess = true;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true, GenerateDocumentsForProducts = true };
        await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange

                // Act
                PagedResultDto<DocumentDto> results = await QuerySender.Send(
                    new GetDocumentsQuery(new DocumentPaginationFilter
                    {
                        Sort = "creationTime",
                        SortBy = ascending ? AutoFilterer.Enums.Sorting.Ascending : AutoFilterer.Enums.Sorting.Descending
                    }));

                // Act
                List<Guid> orderedDocumentIds = ascending
                    ? results.Items.OrderBy(d => d.CreationTime).Select(x => x.Id).ToList()
                    : results.Items.OrderByDescending(d => d.CreationTime).Select(x => x.Id).ToList();

                // Assert
                results.Items.Select(d => d.Id).ToList().ShouldBeEquivalentTo(orderedDocumentIds);
            });
        }
    }

    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    public async Task GetComponentsDocuments_Should_Return_Documents_Sorted_By_LastModificationTime(bool ascending)
    {
        _authorizationResultSuccess = true;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true, GenerateDocumentsForProducts = true };
        await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange


                // Act
                PagedResultDto<DocumentDto> results = await QuerySender.Send(
                    new GetDocumentsQuery(new DocumentPaginationFilter
                    {
                        Sort = "lastModificationTime",
                        SortBy = ascending ? AutoFilterer.Enums.Sorting.Ascending : AutoFilterer.Enums.Sorting.Descending
                    }));

                // Act
                List<Guid> orderedDocumentIds = ascending
                    ? results.Items.OrderBy(d => d.CreationTime).Select(x => x.Id).ToList()
                    : results.Items.OrderByDescending(d => d.CreationTime).Select(x => x.Id).ToList();

                // Assert
                results.Items.Select(d => d.Id).ToList().ShouldBeEquivalentTo(orderedDocumentIds);
            });
        }
    }

    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    public async Task GetDocumentQuery_Should_Return_Sort_By_Translation_Label(bool ascending)
    {
        _authorizationResultSuccess = true;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true, GenerateDocumentsForProducts = true };

        await SeedDataForTenantAsync(context);

        string currentCulture = "en";
        using (CultureHelper.Use(currentCulture))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                await WithUnitOfWorkAsync(async () =>
                {
                    // Arrange
                    List<Document> documents = await DocumentRepository.GetListAsync();
                    List<string> labels = documents.SelectMany(dc => dc.Translations)
                        .Where(t => t.Language == currentCulture)
                        .Select(t => t.Label)
                        .Distinct()
                        .Order()
                        .ToList();
                    if (!ascending)
                    {
                        labels.Reverse();
                    }

                    // Act
                    PagedResultDto<DocumentDto> results = await QuerySender.Send(
                        new GetDocumentsQuery(new DocumentPaginationFilter
                        {
                            Page = 1,
                            PerPage = 50,
                            Sort = "label",
                            SortBy = ascending ? AutoFilterer.Enums.Sorting.Ascending : AutoFilterer.Enums.Sorting.Descending
                        }));

                    // Assert
                    CommonTranslationDto firstTranslation = results.Items[0].Translations.First(x => x.Language == currentCulture);
                    CommonTranslationDto lastTranslation = results.Items[^1].Translations.First(x => x.Language == currentCulture);
                    firstTranslation.Label.ShouldBe(labels[0]);
                    lastTranslation.Label.ShouldBe(labels[^1]);
                });
            }
        }
    }

    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    public async Task GetDocumentQuery_Should_Return_Sort_By_Translation_Label_With_Fallback_Language(bool ascending)
    {
        _authorizationResultSuccess = true;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true };

        CoreTestsSeedingDataStore dataStore = await SeedDataForTenantAsync(context);

        string currentCulture = "en";
        string frenchCulture = "fr";
        using (CultureHelper.Use(frenchCulture))
        {
            using (CurrentTenant.Change(context.TenantId))
            {
                await WithUnitOfWorkAsync(async () =>
                {
                    // Arrange
                    Guid productId = dataStore.ProductIdByCodes.First().Value;
                    Product product = await ProductRepository.GetAsync(productId);
                    await ComponentDocumentRepository.DeleteDirectAsync(x => x.ComponentId.Equals(product.Id));

                    DocumentTranslation documentTranslationEn = new(currentCulture, "a_EN");
                    Document document1 = await DocumentDomainService.CreateAsync(Faker.Internet.Url(), [documentTranslationEn], []);
                    product.Component.AddDocument(document1);

                    DocumentTranslation documentTranslationEn2 = new(currentCulture, "z_EN");
                    DocumentTranslation documentTranslationFr2 = new(frenchCulture, "m_FR");
                    Document document2 = await DocumentDomainService.CreateAsync(Faker.Internet.Url(), [documentTranslationEn2, documentTranslationFr2], []);
                    product.Component.AddDocument(document2);

                    DocumentTranslation documentTranslationEn3 = new(currentCulture, "b_EN");
                    DocumentTranslation documentTranslationFr3 = new(frenchCulture, "t_FR");
                    Document document3 = await DocumentDomainService.CreateAsync(Faker.Internet.Url(), [documentTranslationEn3, documentTranslationFr3], []);
                    product.Component.AddDocument(document3);

                    List<Guid> documentIds = [document1.Id, document2.Id, document3.Id];

                    await ProductRepository.UpdateAsync(product, true);

                    if (!ascending)
                    {
                        documentIds.Reverse();
                    }

                    // Act
                    PagedResultDto<DocumentDto> results = await QuerySender.Send(
                        new GetDocumentsQuery(new DocumentPaginationFilter
                        {
                            Page = 1,
                            PerPage = 50,
                            Sort = "label",
                            SortBy = ascending ? AutoFilterer.Enums.Sorting.Ascending : AutoFilterer.Enums.Sorting.Descending
                        }));

                    // Assert
                    documentIds[0].ShouldBe(results.Items[0].Id);
                    documentIds[1].ShouldBe(results.Items[1].Id);
                    documentIds[2].ShouldBe(results.Items[2].Id);
                });
            }
        }
    }

    [Fact]
    public async Task GetComponentsDocuments_Should_Throw_Exception_With_Incorrect_Parameters()
    {
        _authorizationResultSuccess = true;
        using (CurrentTenant.Change(GuidGenerator.Create()))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Act & Assert
                await QuerySender.Send(new GetDocumentsQuery(new DocumentPaginationFilter { Page = 0 })).ShouldThrowAsync<AbpValidationException>();
                await QuerySender.Send(new GetDocumentsQuery(new DocumentPaginationFilter { PerPage = 0 })).ShouldThrowAsync<AbpValidationException>();
                await QuerySender.Send(new GetDocumentsQuery(new DocumentPaginationFilter { Sort = "IncorrectField" })).ShouldThrowAsync<AbpValidationException>();
            });
        }
    }
}