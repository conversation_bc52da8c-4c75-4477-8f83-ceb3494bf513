using System;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Shouldly;
using SpareParts.Common;
using SpareParts.Common.Companies;
using SpareParts.Core.Documents.Dtos;
using SpareParts.Core.Documents.Queries;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Enums;
using Volo.Abp.Authorization;
using Volo.Abp.Data;
using Volo.Abp.Modularity;
using Volo.Abp.Security.Claims;
using Xunit;
using Document = SpareParts.Core.Entities.Resources.Document;

namespace SpareParts.Core.Documents.QueryHandlers;
public abstract class GetComponentDocumentsStatsQueryHandlerTests<TStartupModule> : DocumentTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    private int _documentsCount;
    private readonly ICurrentPrincipalAccessor _currentPrincipalAccessor;

    protected GetComponentDocumentsStatsQueryHandlerTests()
    {
        _currentPrincipalAccessor = ServiceProvider.GetRequiredService<ICurrentPrincipalAccessor>();
    }

    [Fact]
    public async Task GetComponentDocumentsStats_Should_Return_Stats()
    {
        Guid tenantId = GuidGenerator.Create();
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                await CreateDocuments();
            });

            await WithUnitOfWorkAsync(async () =>
            {
                using (_currentPrincipalAccessor.Change([new Claim(CommonClaimTypes.CompanyType, CompanyType.Internal.ToString())]))
                {
                    // Act
                    ComponentDocumentsStatsDto result = await QuerySender.Send(new GetComponentDocumentsStatsQuery());

                    // Assert
                    result.Count.ShouldBe(_documentsCount);
                }
            });
        }
    }

    [Fact]
    public async Task GetComponentDocumentsStats_With_External_User_Should_Throw_Authorization_Exception()
    {
        Guid tenantId = GuidGenerator.Create();
        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                using (_currentPrincipalAccessor.Change([new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString())]))
                {
                    // Act
                    await QuerySender.Send(new GetComponentDocumentsStatsQuery()).ShouldThrowAsync<AbpAuthorizationException>();
                }
            });
        }
    }

    private async Task CreateDocuments()
    {
        for (int i = 1; i < 3; i++)
        {
            Document doc1 = await CreateFileDocument();
            Document doc2 = await CreateLinkDocument();
            Document doc3 = await CreateFileDocument();
            _documentsCount += 3;

            Component assembly = await ComponentDomainService.CreateAsync(Guid.NewGuid().ToString(), [new ComponentTranslation(Faker.Locale, Faker.Commerce.ProductName())], ComponentType.Assembly);
            await ComponentDocumentRepository.InsertAsync(new ComponentDocument(assembly.Id, doc1.Id));
            await ComponentDocumentRepository.InsertAsync(new ComponentDocument(assembly.Id, doc2.Id));
            await ComponentDocumentRepository.InsertAsync(new ComponentDocument(assembly.Id, doc3.Id));
            await ComponentRepository.InsertAsync(assembly);
        }
    }
}
