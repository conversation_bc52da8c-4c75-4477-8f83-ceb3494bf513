using Microsoft.Extensions.FileProviders;
using Shouldly;
using SpareParts.Common.Dtos;
using SpareParts.Core.Components.Commands;
using SpareParts.Core.Drawings.Commands;
using SpareParts.Core.Drawings.Dtos;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Enums;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Volo.Abp.Content;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Core.Drawings.CommandHandlers;
public abstract class SetDrawingAsMainCommandHandlerTests<TStartupModule> : DrawingTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    [Fact]
    public async Task SetDrawingAsMainCommand_Should_Set_As_Visible_And_Main_The_Drawing()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true };
        await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            // Arrange
            CreateComponentCommand createAssemblyCommand = new("productCode",
                [new CommonTranslationDto("en", "prodLabel")], ComponentType.Assembly);
            Guid assemblyId = await WithUnitOfWorkAsync(async () => await CommandSender.Send(createAssemblyCommand));

            IFileInfo testPdf = VirtualFileProvider.GetFileInfo("/Files/Test.pdf");
            await using Stream drawingFile = testPdf.CreateReadStream();
            Dictionary<string, Guid> componentIdsByIndex = [];

            CreateDrawingCommand command = new(assemblyId, new RemoteStreamContent(drawingFile, "Test.pdf"),
                componentIdsByIndex, DrawingOrigin.SolidWorks);
            Guid drawingId = (await WithUnitOfWorkAsync(async () => await CommandSender.Send(command))).Id;

            await WithUnitOfWorkAsync(async () =>
            {
                Drawing drawing = await DrawingRepository.GetAsync(drawingId);
                drawing.IsVisible = false;
                PropertyInfo? isMainProperty = typeof(Drawing).GetProperty(nameof(Drawing.IsMain), BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                isMainProperty!.SetValue(drawing, false);
                await DrawingRepository.UpdateAsync(drawing);
            });

            // Act
            DrawingDto[] result = await WithUnitOfWorkAsync(async () => await CommandSender.Send(new SetDrawingAsMainCommand(drawingId)));

            // Assert
            await WithUnitOfWorkAsync(async () =>
            {
                Drawing drawing = await DrawingRepository.GetAsync(drawingId);
                drawing.IsMain.ShouldBeTrue();
                drawing.IsVisible.ShouldBeTrue();
                result.Length.ShouldBe(1);
                result[0].IsMain.ShouldBeTrue();
                result[0].IsVisible.ShouldBeTrue();
            });
        }
    }

    [Fact]
    public async Task SetDrawingAsMainCommand_Should_Return_Empty_Array_If_Drawing_Already_Main()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true };
        await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            // Arrange
            CreateComponentCommand createAssemblyCommand = new("productCode",
                [new CommonTranslationDto("en", "prodLabel")], ComponentType.Assembly);
            Guid assemblyId = await WithUnitOfWorkAsync(async () => await CommandSender.Send(createAssemblyCommand));

            IFileInfo testPdf = VirtualFileProvider.GetFileInfo("/Files/Test.pdf");
            await using Stream drawingFile = testPdf.CreateReadStream();
            Dictionary<string, Guid> componentIdsByIndex = [];

            CreateDrawingCommand command = new(assemblyId, new RemoteStreamContent(drawingFile, "Test.pdf"),
                componentIdsByIndex, DrawingOrigin.SolidWorks);
            Guid drawingId = (await WithUnitOfWorkAsync(async () => await CommandSender.Send(command))).Id;

            // Act & Assert
            DrawingDto[] result = await WithUnitOfWorkAsync(async () => await CommandSender.Send(new SetDrawingAsMainCommand(drawingId)));

            // Assert
            result.ShouldBeEmpty();
        }
    }

    [Fact]
    public async Task SetDrawingAsMainCommand_Should_Change_Main_Between_Two_Drawing()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true };
        await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            // Arrange
            CreateComponentCommand createAssemblyCommand = new("productCode",
                [new CommonTranslationDto("en", "prodLabel")], ComponentType.Assembly);
            Guid assemblyId = await WithUnitOfWorkAsync(async () => await CommandSender.Send(createAssemblyCommand));

            IFileInfo testPdf = VirtualFileProvider.GetFileInfo("/Files/Test.pdf");
            await using Stream drawingFile = testPdf.CreateReadStream();
            Dictionary<string, Guid> componentIdsByIndex = [];

            CreateDrawingCommand command = new(assemblyId, new RemoteStreamContent(drawingFile, "Test.pdf"),
                componentIdsByIndex, DrawingOrigin.SolidWorks);
            Guid drawing1Id = (await WithUnitOfWorkAsync(async () => await CommandSender.Send(command))).Id;
            Guid drawing2Id = (await WithUnitOfWorkAsync(async () => await CommandSender.Send(command))).Id;

            await WithUnitOfWorkAsync(async () =>
            {
                Drawing drawing2 = await DrawingRepository.GetAsync(drawing2Id);
                drawing2.IsVisible = false;
                await DrawingRepository.UpdateAsync(drawing2);
            });

            // Act
            DrawingDto[] result = await WithUnitOfWorkAsync(async () => await CommandSender.Send(new SetDrawingAsMainCommand(drawing2Id)));

            // Assert
            await WithUnitOfWorkAsync(async () =>
            {
                Drawing drawing1 = await DrawingRepository.GetAsync(drawing1Id);
                Drawing drawing2 = await DrawingRepository.GetAsync(drawing2Id);
                drawing1.IsMain.ShouldBeFalse();
                drawing2.IsMain.ShouldBeTrue();
                drawing2.IsVisible.ShouldBeTrue();
                result.Length.ShouldBe(2);
                DrawingDto drawingDto1 = result.First(x => x.Id == drawing1Id);
                drawingDto1.IsMain.ShouldBeFalse();
                DrawingDto drawingDto2 = result.First(x => x.Id == drawing2Id);
                drawingDto2.IsMain.ShouldBeTrue();
                drawingDto2.IsVisible.ShouldBeTrue();
            });
        }
    }

    [Fact]
    public async Task SetDrawingAsMainCommand_When_Id_Does_Not_Exist_Should_Throw_EntityNotFoundException()
    {
        using (CurrentTenant.Change(GuidGenerator.Create()))
        {
            // Arrange
            Guid drawingId = GuidGenerator.Create();

            await WithUnitOfWorkAsync(async () =>
            {
                SetDrawingAsMainCommand setDrawingAsMainCommand = new(drawingId);

                // Act & Assert
                await Should.ThrowAsync<EntityNotFoundException>(CommandSender.Send(setDrawingAsMainCommand));
            });
        }
    }
}