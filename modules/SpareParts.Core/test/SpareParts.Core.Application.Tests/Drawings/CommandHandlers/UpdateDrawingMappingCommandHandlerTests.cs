using Microsoft.Extensions.FileProviders;
using Shouldly;
using SpareParts.Common.Dtos;
using SpareParts.Core.Components.Commands;
using SpareParts.Core.Drawings.Commands;
using SpareParts.Core.Drawings.Dtos;
using SpareParts.Core.Drawings.Dtos.Inputs;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Enums;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Content;
using Volo.Abp.Data;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Core.Drawings.CommandHandlers;

public abstract class UpdateDrawingMappingCommandHandlerTests<TStartupModule> : DrawingTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    [Fact]
    public async Task UpdateDrawingMappingCommand_Should_Change_Index_And_Component()
    {
        Guid tenantId = GuidGenerator.Create();
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        using (CurrentTenant.Change(tenantId))
        {
            // Arrange
            string newIndex = "newIndex";

            CreateComponentCommand createAssemblyCommand = new("productCode",
                [new CommonTranslationDto("en", "prodLabel")], ComponentType.Assembly);
            Guid assemblyId = await WithUnitOfWorkAsync(async () => await CommandSender.Send(createAssemblyCommand));

            CreateComponentCommand createPartCommand1 = new("partCode1",
                [new CommonTranslationDto("en", "partLabel1")], ComponentType.Part);
            Guid part1Id = await WithUnitOfWorkAsync(async () => await CommandSender.Send(createPartCommand1));
            CreateBomLineCommand createBomLineCommand1 = new(assemblyId, part1Id, 1);
            await WithUnitOfWorkAsync(async () => await CommandSender.Send(createBomLineCommand1));

            CreateComponentCommand createPartCommand2 = new("partCode2",
                [new CommonTranslationDto("en", "partLabel2")], ComponentType.Part);
            Guid part2Id = await WithUnitOfWorkAsync(async () => await CommandSender.Send(createPartCommand2));
            CreateBomLineCommand createBomLineCommand2 = new(assemblyId, part2Id, 1);
            await WithUnitOfWorkAsync(async () => await CommandSender.Send(createBomLineCommand2));

            IFileInfo testPdf = VirtualFileProvider.GetFileInfo("/Files/Test.pdf");
            await using Stream content = testPdf.CreateReadStream();
            Dictionary<string, Guid> componentIdsByIndex = new() { { "1", part1Id } };
            CreateDrawingCommand createDrawingCommand = new(assemblyId, new RemoteStreamContent(content, "Test.pdf"), componentIdsByIndex, DrawingOrigin.Online);
            DrawingDto drawing = await WithUnitOfWorkAsync(async () => await CommandSender.Send(createDrawingCommand));

            Guid drawingMappingId = await WithUnitOfWorkAsync(async () =>
                {
                    DrawingMapping drawingMapping = await DrawingMappingRepository.FirstAsync(x => x.DrawingId == drawing.Id);
                    return drawingMapping.Id;
                }
            );

            // Act
            CreateUpdateDrawingMappingDto drawingMappingDto = new() { Index = newIndex, ComponentId = part2Id };
            UpdateDrawingMappingCommand updateDrawingMappingCommand = new(drawing.Id, drawingMappingId, CallerOrigin.Api, drawingMappingDto);
            DrawingMappingDto result = await WithUnitOfWorkAsync(async () => await CommandSender.Send(updateDrawingMappingCommand));

            // Assert
            result.Index.ShouldBe(newIndex);
            result.ComponentId.ShouldBe(part2Id);
        }
    }

    [Fact]
    public async Task UpdateDrawingMappingCommand_Should_Throw_Exception_If_Origin_Is_SolidWorks()
    {
        Guid tenantId = GuidGenerator.Create();
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        using (CurrentTenant.Change(tenantId))
        {
            // Arrange
            CreateComponentCommand createAssemblyCommand = new("productCode",
                [new CommonTranslationDto("en", "prodLabel")], ComponentType.Assembly);
            Guid assemblyId = await WithUnitOfWorkAsync(async () => await CommandSender.Send(createAssemblyCommand));

            CreateComponentCommand createPartCommand = new("partCode",
                [new CommonTranslationDto("en", "partLabel")], ComponentType.Part);
            Guid partId = await WithUnitOfWorkAsync(async () => await CommandSender.Send(createPartCommand));
            CreateBomLineCommand createBomLineCommand = new(assemblyId, partId, 1);
            await WithUnitOfWorkAsync(async () => await CommandSender.Send(createBomLineCommand));

            IFileInfo testPdf = VirtualFileProvider.GetFileInfo("/Files/Test.pdf");
            await using Stream drawingFile = testPdf.CreateReadStream();

            Dictionary<string, Guid> componentIdsByIndex = new() { { "1", partId } };
            CreateDrawingCommand command = new(assemblyId, new RemoteStreamContent(drawingFile, "Test.pdf"), componentIdsByIndex, DrawingOrigin.SolidWorks);
            DrawingDto drawing = await WithUnitOfWorkAsync(async () => await CommandSender.Send(command));

            Guid drawingMappingId = await WithUnitOfWorkAsync(async () =>
                {
                    DrawingMapping drawingMapping = await DrawingMappingRepository.FirstAsync(x => x.DrawingId == drawing.Id);
                    return drawingMapping.Id;
                }
            );

            // Act & Assert
            CreateUpdateDrawingMappingDto drawingMappingDto = new() { Index = "newIndex", ComponentId = partId };
            UpdateDrawingMappingCommand updateDrawingMappingCommand = new(drawing.Id, drawingMappingId, CallerOrigin.Api, drawingMappingDto);
            await Should.ThrowAsync<BusinessException>(CommandSender.Send(updateDrawingMappingCommand));
        }
    }
}