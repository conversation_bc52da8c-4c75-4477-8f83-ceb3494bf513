using Microsoft.Extensions.FileProviders;
using Shouldly;
using SpareParts.Common.Dtos;
using SpareParts.Common.Resources;
using SpareParts.Core.Components.Commands;
using SpareParts.Core.Containers;
using SpareParts.Core.Drawings.Commands;
using SpareParts.Core.Drawings.Dtos;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Resources;
using SpareParts.Core.Enums;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Volo.Abp.Content;
using Volo.Abp.Data;
using Volo.Abp.Modularity;
using Volo.Abp.Validation;
using Xunit;

namespace SpareParts.Core.Drawings.CommandHandlers;

public abstract class CreateDrawingCommandHandlerTests<TStartupModule> : DrawingTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    [Fact]
    public async Task Handle_ShouldThrowException_WhenFileIsNotPdf()
    {
        using (CurrentTenant.Change(GuidGenerator.Create()))
        {
            CreateDrawingCommand command = new(GuidGenerator.Create(), new RemoteStreamContent(Stream.Null, "drawing.svg"), [], DrawingOrigin.SolidWorks);
            await CommandSender.Send(command).ShouldThrowAsync<AbpValidationException>();
        }
    }

    [Fact]
    public async Task Handle_ShouldCreateDrawing_WhenFileIsPdf()
    {
        Guid tenantId = GuidGenerator.Create();
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        using (CurrentTenant.Change(tenantId))
        {
            CreateComponentCommand createAssemblyCommand = new("productCode",
                [new CommonTranslationDto("en", "prodLabel")], ComponentType.Assembly);
            Guid assemblyId = await WithUnitOfWorkAsync(async () => await CommandSender.Send(createAssemblyCommand));

            CreateComponentCommand createPartCommand = new("partCode",
                [new CommonTranslationDto("en", "partLabel")], ComponentType.Part);
            Guid partId = await WithUnitOfWorkAsync(async () => await CommandSender.Send(createPartCommand));

            IFileInfo testPdf = VirtualFileProvider.GetFileInfo("/Files/Test.pdf");
            await using Stream content = testPdf.CreateReadStream();
            Dictionary<string, Guid> componentIdsByIndex = new() { { "1", partId } };
            CreateDrawingCommand command = new(assemblyId, new RemoteStreamContent(content, "Test.pdf"), componentIdsByIndex, DrawingOrigin.SolidWorks);
            DrawingDto drawingDto = await WithUnitOfWorkAsync(async () => await CommandSender.Send(command));

            Drawing drawing = await DrawingRepository.GetAsync(drawingDto.Id);
            drawing.Rank.ShouldBe(1.0);
            drawing.ShouldNotBeNull();
            drawing.SourceFile.FileName.ShouldBe("Test.pdf");
            drawing.DrawingMappings[0].Index.ShouldBe("1");
            drawing.DrawingMappings[0].ComponentId.ShouldBe(partId);

            bool isImageBlobExist = await ImageManager.BlobExistsAsync(drawing.Image.Id);
            isImageBlobExist.ShouldBeTrue();
            bool isFileBlobExist = await FileManager.BlobExistsAsync(drawing.SourceFileId);
            isFileBlobExist.ShouldBeTrue();
            string svgFileName = AbstractResourceManager<FilesContainer, Resource>.GetBlobName(drawing.SourceFileId) + ".svg";
            bool isSvgBlobExist = await FileManager.BlobExistsAsync(svgFileName);
            isSvgBlobExist.ShouldBeTrue();
        }
    }

    [Fact]
    public async Task Handle_ShouldIncrementRankWhenCreateDrawing()
    {
        Guid tenantId = GuidGenerator.Create();
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        using (CurrentTenant.Change(tenantId))
        {
            CreateComponentCommand createAssemblyCommand = new("productCode",
                [new CommonTranslationDto("en", "prodLabel")], ComponentType.Assembly);
            Guid assemblyId = await WithUnitOfWorkAsync(async () => await CommandSender.Send(createAssemblyCommand));

            CreateComponentCommand createPartCommand = new("partCode",
                [new CommonTranslationDto("en", "partLabel")], ComponentType.Part);
            Guid partId = await WithUnitOfWorkAsync(async () => await CommandSender.Send(createPartCommand));

            IFileInfo testPdf = VirtualFileProvider.GetFileInfo("/Files/Test.pdf");
            await using Stream content = testPdf.CreateReadStream();
            Dictionary<string, Guid> componentIdsByIndex = new() { { "1", partId } };
            CreateDrawingCommand command = new(assemblyId, new RemoteStreamContent(content, "Test.pdf"), componentIdsByIndex, DrawingOrigin.SolidWorks);
            DrawingDto drawingDto = await WithUnitOfWorkAsync(async () => await CommandSender.Send(command));
            drawingDto.Rank.ShouldBe(1.0);

            drawingDto = await WithUnitOfWorkAsync(async () => await CommandSender.Send(command));
            drawingDto.Rank.ShouldBe(2.0);
        }
    }
}