using Microsoft.Extensions.FileProviders;
using Shouldly;
using SpareParts.Common.Dtos;
using SpareParts.Common.Resources;
using SpareParts.Core.Components.Commands;
using SpareParts.Core.Containers;
using SpareParts.Core.Drawings.Commands;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Resources;
using SpareParts.Core.Enums;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using SpareParts.Core.Drawings.Dtos;
using Volo.Abp.Content;
using Volo.Abp.Data;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Core.Drawings.CommandHandlers;

public abstract class DeleteDrawingsCommandHandlerTests<TStartupModule> : DrawingTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    [Fact]
    public async Task Should_Delete_Drawings_When_Command_Is_Handled()
    {
        Guid tenantId = GuidGenerator.Create();
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        using (CurrentTenant.Change(tenantId))
        {
            // Arrange
            CreateComponentCommand createAssemblyCommand = new("productCode",
                [new CommonTranslationDto("en", "prodLabel")], ComponentType.Assembly);
            Guid assemblyId = await WithUnitOfWorkAsync(async () => await CommandSender.Send(createAssemblyCommand));

            CreateComponentCommand createPartCommand = new("partCode",
                [new CommonTranslationDto("en", "partLabel")], ComponentType.Part);
            Guid partId = await WithUnitOfWorkAsync(async () => await CommandSender.Send(createPartCommand));

            IFileInfo testPdf = VirtualFileProvider.GetFileInfo("/Files/Test.pdf");
            await using Stream drawingFile = testPdf.CreateReadStream();

            Dictionary<string, Guid> componentIdsByIndex = new() { { "1", partId } };
            CreateDrawingCommand command = new(assemblyId, new RemoteStreamContent(drawingFile, "Test.pdf"), componentIdsByIndex, DrawingOrigin.SolidWorks);
            DrawingDto drawingDto1 = await WithUnitOfWorkAsync(async () => await CommandSender.Send(command));
            DrawingDto drawingDto2 = await WithUnitOfWorkAsync(async () => await CommandSender.Send(command));

            Guid drawing1Id = drawingDto1.Id;
            Guid drawing2Id = drawingDto2.Id;

            List <Drawing> drawings = await DrawingRepository.GetListAsync(x => x.Id == drawing1Id || x.Id == drawing2Id);

            // Act
            await WithUnitOfWorkAsync(async () =>
            {
                await CommandSender.Send(new DeleteDrawingsCommand(assemblyId));
            });

            // Assert
            List<Drawing> drawingResults = await DrawingRepository.GetListAsync(x => x.Id == drawing1Id || x.Id == drawing2Id);
            drawingResults.ShouldBeEmpty();

            var resourceIds = drawings.Select(d => new
            {
                d.SourceFileId,
                ImageId = d.Image.Id
            }).ToList();
            List<Guid> allResourceIds = resourceIds.SelectMany(x => new[] { x.SourceFileId, x.ImageId }).ToList();

            List<Resource> resources = await ResourceRepository.GetListAsync(x => allResourceIds.Contains(x.Id));
            resources.ShouldBeEmpty();

            foreach (var resourceId in resourceIds)
            {
                bool isImageBlobExist = await ImageManager.BlobExistsAsync(resourceId.ImageId);
                isImageBlobExist.ShouldBeFalse();
                bool isFileBlobExist = await FileManager.BlobExistsAsync(resourceId.SourceFileId);
                isFileBlobExist.ShouldBeFalse();
                string svgFileName = AbstractResourceManager<FilesContainer, Resource>.GetBlobName(resourceId.SourceFileId) + ".svg";
                bool isSvgBlobExist = await FileManager.BlobExistsAsync(svgFileName);
                isSvgBlobExist.ShouldBeFalse();
            }
        }
    }
}