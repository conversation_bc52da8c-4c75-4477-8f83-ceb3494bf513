using Microsoft.Extensions.FileProviders;
using Shouldly;
using SpareParts.Common.Dtos;
using SpareParts.Core.Components.Commands;
using SpareParts.Core.Drawings.Commands;
using SpareParts.Core.Drawings.Dtos;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Enums;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Volo.Abp.Content;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Core.Drawings.CommandHandlers;
public abstract class ChangeDrawingRankCommandHandlerTests<TStartupModule> : DrawingTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    [Fact]
    public async Task ChangeDrawingRankCommand_Should_Update_Drawing_Rank()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true };
        await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            // Arrange
            CreateComponentCommand createAssemblyCommand = new("productCode",
                [new CommonTranslationDto("en", "prodLabel")], ComponentType.Assembly);
            Guid assemblyId = await WithUnitOfWorkAsync(async () => await CommandSender.Send(createAssemblyCommand));

            IFileInfo testPdf = VirtualFileProvider.GetFileInfo("/Files/Test.pdf");
            await using Stream drawingFile = testPdf.CreateReadStream();
            Dictionary<string, Guid> componentIdsByIndex = [];

            CreateDrawingCommand command = new(assemblyId, new RemoteStreamContent(drawingFile, "Test.pdf"),
                componentIdsByIndex, DrawingOrigin.SolidWorks);
            DrawingDto drawing = await WithUnitOfWorkAsync(async () => await CommandSender.Send(command));
            Guid drawingId = drawing.Id;

            // Act
            DrawingDto drawingRankDto = await WithUnitOfWorkAsync(async () =>
            {
                ChangeDrawingRankCommand changeDrawingRankCommand = new(drawingId, 1.5);
                return await CommandSender.Send(changeDrawingRankCommand);
            });

            // Assert
            await WithUnitOfWorkAsync(async () =>
                {
                    Drawing drawing = await DrawingRepository.GetAsync(drawingId);
                    drawing.Rank.ShouldBe(1.5);
                    drawingRankDto.Id.ShouldBe(drawingId);
                    drawingRankDto.Rank.ShouldBe(1.5);
                });
        }
    }

    [Fact]
    public async Task ChangeDrawingRankCommand_When_Id_Does_Not_Exist_Should_Throw_EntityNotFoundException()
    {
        using (CurrentTenant.Change(GuidGenerator.Create()))
        {
            // Arrange
            Guid drawingId = GuidGenerator.Create();

            await WithUnitOfWorkAsync(async () =>
            {
                ChangeDrawingRankCommand changeDrawingRankCommand = new(drawingId, 1);

                // Act & Assert
                await Should.ThrowAsync<EntityNotFoundException>(CommandSender.Send(changeDrawingRankCommand));
            });
        }
    }

    [Fact]
    public async Task ChangeDrawingRankCommand_When_Rank_Is_Negative_Should_Throw_ArgumentException()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true };
        await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            // Arrange
            CreateComponentCommand createAssemblyCommand = new("productCode",
                [new CommonTranslationDto("en", "prodLabel")], ComponentType.Assembly);
            Guid assemblyId = await WithUnitOfWorkAsync(async () => await CommandSender.Send(createAssemblyCommand));

            IFileInfo testPdf = VirtualFileProvider.GetFileInfo("/Files/Test.pdf");
            await using Stream drawingFile = testPdf.CreateReadStream();
            Dictionary<string, Guid> componentIdsByIndex = [];

            CreateDrawingCommand command = new(assemblyId, new RemoteStreamContent(drawingFile, "Test.pdf"),
                componentIdsByIndex, DrawingOrigin.SolidWorks);
            DrawingDto drawingDto = await WithUnitOfWorkAsync(async () => await CommandSender.Send(command));
            Guid drawingId = drawingDto.Id;

            await WithUnitOfWorkAsync(async () =>
            {
                ChangeDrawingRankCommand changeDrawingRankCommand = new(drawingId, -1);

                // Act & Assert
                await Should.ThrowAsync<ArgumentException>(CommandSender.Send(changeDrawingRankCommand));
            });
        }
    }
}