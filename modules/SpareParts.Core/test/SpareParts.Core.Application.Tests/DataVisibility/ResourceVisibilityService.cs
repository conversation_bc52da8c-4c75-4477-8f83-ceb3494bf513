using System;
using SpareParts.Core.Attributes;
using Volo.Abp.Application.Services;

namespace SpareParts.Core.DataVisibility;

public interface IResourceVisibilityWithProperty;

public class ResourceVisibilityService : ApplicationService, IResourceVisibilityCheckingEnabled
{
    public virtual bool GetTrue(IResourceVisibilityWithProperty resourceVisibilityWithProperty)
    {
        return true;
    }
}

public record ResourceVisibilityWithoutAnyMatching(Guid AnotherId) : IResourceVisibilityWithProperty
{
    public Guid AnotherId2 { get; set; }
};

public record ResourceVisibilityWithMatchingProperty : IResourceVisibilityWithProperty
{
    public Guid Id { get; set; }
};

public record ResourceVisibilityWithMatchingPropertyAttribute(Guid AnotherId) : IResourceVisibilityWithProperty
{
    [ObjectId]
    public Guid ResourceId { get; set; }
};

public record ResourceVisibilityWithId(Guid Id) : IResourceVisibilityWithProperty;

public record ResourceVisibilityWithAttribute([ObjectId] Guid ResourceId) : IResourceVisibilityWithProperty;

public record ResourceVisibilityWithIdNull(Guid? Id) : IResourceVisibilityWithProperty;
