using System;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using NSubstitute;
using Shouldly;
using SpareParts.Core.Entities.Components;
using Volo.Abp.Authorization;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Core.DataVisibility;

public abstract class ResourceVisibilityInterceptorTests<TStartupModule> : CoreApplicationTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    private readonly ResourceVisibilityService _resourceVisibilityWithIdService;
    private IAbpAuthorizationService _authorizationService = default!;
    private bool _authorizationResultSuccess;

    protected ResourceVisibilityInterceptorTests()
    {
        _resourceVisibilityWithIdService = ServiceProvider.GetRequiredService<ResourceVisibilityService>();
    }

    protected override void AfterAddApplication(IServiceCollection services)
    {
        _authorizationService = Substitute.For<IAbpAuthorizationService>();
        _authorizationService.AuthorizeAsync(Arg.Any<ClaimsPrincipal>(), Arg.Any<object?>(), Arg.Any<string>())
            .Returns(_ => _authorizationResultSuccess ? AuthorizationResult.Success() : AuthorizationResult.Failed());

        services.Replace(ServiceDescriptor.Singleton(_authorizationService));
    }

    [Fact]
    public void ResourceVisibilityWithoutAnyMatching_Should_Throw_ArgumentException()
    {
        _authorizationResultSuccess = true;
        Should.Throw<ArgumentException>(() => _resourceVisibilityWithIdService.GetTrue(new ResourceVisibilityWithoutAnyMatching(GuidGenerator.Create()) { AnotherId2 = GuidGenerator.Create() }));
    }

    [Fact]
    public void ResourceVisibilityWithMatchingProperty_Should_Throw_AbpAuthorizationException_When_No_Matching_Id()
    {
        _authorizationResultSuccess = false;
        Should.Throw<AbpAuthorizationException>(() => _resourceVisibilityWithIdService.GetTrue(new ResourceVisibilityWithMatchingProperty { Id = GuidGenerator.Create() }));
    }

    [Fact]
    public void ResourceVisibilityWithMatchingPropertyAttribute_Should_Throw_AbpAuthorizationException_When_No_Matching_Id()
    {
        _authorizationResultSuccess = false;
        Should.Throw<AbpAuthorizationException>(() => _resourceVisibilityWithIdService.GetTrue(new ResourceVisibilityWithMatchingPropertyAttribute(GuidGenerator.Create()) { ResourceId = GuidGenerator.Create() }));
    }

    [Fact]
    public void ResourceVisibilityWithId_Should_Throw_AbpAuthorizationException_When_No_Matching_Id()
    {
        _authorizationResultSuccess = false;
        Should.Throw<AbpAuthorizationException>(() => _resourceVisibilityWithIdService.GetTrue(new ResourceVisibilityWithId(GuidGenerator.Create())));
    }

    [Fact]
    public async Task ResourceVisibilityWithAttribute_Should_Return_True()
    {
        _authorizationResultSuccess = true;
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true };
        await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Product product = await ProductRepository.FirstAsync();

                // Act & Assert
                _resourceVisibilityWithIdService.GetTrue(new ResourceVisibilityWithAttribute(product.Component.ImageId)).ShouldBeTrue();
            });
        }
    }

    [Fact]
    public void ResourceVisibilityWithId_Should_Throw_ArgumentException_When_Id_Is_Null()
    {
        _authorizationResultSuccess = true;
        Should.Throw<ArgumentException>(() => _resourceVisibilityWithIdService.GetTrue(new ResourceVisibilityWithIdNull(default!)));
    }
}