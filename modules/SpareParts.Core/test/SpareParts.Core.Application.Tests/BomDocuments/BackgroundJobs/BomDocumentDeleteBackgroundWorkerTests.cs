using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Shouldly;
using SpareParts.Common;
using SpareParts.Common.Companies;
using SpareParts.Core.Containers;
using SpareParts.Core.Entities.Boms;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Enums;
using System;
using System.IO;
using System.Reflection;
using System.Security.Claims;
using System.Threading.Tasks;
using Volo.Abp.BlobStoring;
using Volo.Abp.Data;
using Volo.Abp.Modularity;
using Volo.Abp.Security.Claims;
using Volo.Abp.VirtualFileSystem;
using Xunit;

namespace SpareParts.Core.BomDocuments.BackgroundJobs;

public abstract class BomDocumentDeleteBackgroundWorkerTests<TStartupModule> : BomDocumentTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    private readonly IVirtualFileProvider _virtualFileProvider;
    private readonly IBlobContainer<BomDocumentsContainer> _blobContainer;
    private readonly BomDocumentDeleteBackgroundWorker _bomDocumentDeleteBackgroundWorker;

    protected BomDocumentDeleteBackgroundWorkerTests()
    {
        _bomDocumentDeleteBackgroundWorker = ServiceProvider.GetRequiredService<BomDocumentDeleteBackgroundWorker>();
        _virtualFileProvider = ServiceProvider.GetRequiredService<IVirtualFileProvider>();
        _blobContainer = ServiceProvider.GetRequiredService<IBlobContainer<BomDocumentsContainer>>();
    }

    [Fact]
    public async Task DoWorkAsync_Should_Delete_BomDocument()
    {
        Guid tenantId = Guid.NewGuid();
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        using (CurrentTenant.Change(tenantId))
        {
            Product product = null!;
            Component assembly1 = null!;

            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                DataSeeded dataSeeded = await InsertData();
                product = dataSeeded.Product;
                assembly1 = dataSeeded.Assembly1;
            });

            Guid userId = Guid.NewGuid();
            using (CurrentPrincipalAccessor.Change([new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString()),
                       new Claim(AbpClaimTypes.UserId, userId.ToString())]))
            {
                Guid bomDocumentId = Guid.Empty;

                // Arrange
                await WithUnitOfWorkAsync(async () =>
                {
                    BomDocument bomDocument = new(GuidGenerator.Create(), BomDocumentContext.Product, product.Id, assembly1.Id, "en", Guid.NewGuid());
                    bomDocument.SetFilename("test.pdf");
                    bomDocument.ChangeStatus(BomDocumentationStatus.Completed);
                    PropertyInfo? creatorIdProperty = typeof(BomDocument).GetProperty(nameof(BomDocument.CreatorId), BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    creatorIdProperty!.SetValue(bomDocument, userId);
                    PropertyInfo? lastModificationTimeProperty = typeof(BomDocument).GetProperty(nameof(BomDocument.LastModificationTime), BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    lastModificationTimeProperty!.SetValue(bomDocument, DateTime.Now.AddHours((CoreApplicationConsts.BomDocumentation.ExpirationHours + 1) * -1));
                    await BomDocumentRepository.InsertAsync(bomDocument, true);

                    IFileInfo testPdf = _virtualFileProvider.GetFileInfo("/Files/Test.pdf");
                    await using Stream pdfStream = testPdf.CreateReadStream();
                    await _blobContainer.SaveAsync($"{bomDocument.Id:N}", pdfStream, true);
                    bomDocumentId = bomDocument.Id;
                });

                // Act
                await WithUnitOfWorkAsync(async () =>
                {
                    await _bomDocumentDeleteBackgroundWorker.DoWorkAsync();
                });

                // Assert
                await WithUnitOfWorkAsync(async () =>
                {
                    bool isBlobExist = await _blobContainer.ExistsAsync($"{bomDocumentId:N}");
                    isBlobExist.ShouldBeFalse();
                    BomDocument? bomDocument = await BomDocumentRepository.FindAsync(bomDocumentId);
                    bomDocument.ShouldNotBeNull();
                    bomDocument.Status.ShouldBe(BomDocumentationStatus.Expired);
                });
            }
        }
    }
}