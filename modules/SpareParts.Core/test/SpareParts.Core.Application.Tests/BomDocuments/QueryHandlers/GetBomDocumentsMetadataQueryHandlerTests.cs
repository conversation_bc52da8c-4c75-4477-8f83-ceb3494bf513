using System;
using System.Linq;
using System.Reflection;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using NSubstitute;
using Shouldly;
using SpareParts.Common;
using SpareParts.Common.Companies;
using SpareParts.Core.BomDocuments.Dtos;
using SpareParts.Core.BomDocuments.Queries;
using SpareParts.Core.BomDocuments.QueryFilters;
using SpareParts.Core.Entities.Boms;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Equipments;
using SpareParts.Core.Enums;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Authorization;
using Volo.Abp.Data;
using Volo.Abp.Modularity;
using Volo.Abp.Security.Claims;
using Xunit;

namespace SpareParts.Core.BomDocuments.QueryHandlers;
public abstract class GetBomDocumentsMetadataQueryHandlerTests<TStartupModule> : BomDocumentTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    private bool _authorizationResultSuccess;
    private IAbpAuthorizationService _authorizationService = null!;

    protected override void AfterAddApplication(IServiceCollection services)
    {
        _authorizationService = Substitute.For<IAbpAuthorizationService>();
        _authorizationService.AuthorizeAsync(Arg.Any<ClaimsPrincipal>(), Arg.Any<object?>(), Arg.Any<string>())
            .Returns(_ => _authorizationResultSuccess ? AuthorizationResult.Success() : AuthorizationResult.Failed());

        services.Replace(ServiceDescriptor.Singleton(_authorizationService));
    }

    [Fact]
    public async Task GetBomDocumentsMetadataQuery_Should_Return_Bom_Documentations_Metadata()
    {
        _authorizationResultSuccess = false;
        Guid tenantId = Guid.NewGuid();
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        using (CurrentTenant.Change(tenantId))
        {
            Equipment equipment = null!;
            Product productNotInEquipment = null!;
            Product product = null!;
            Component assembly1 = null!;
            Component assembly11 = null!;

            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                DataSeeded dataSeeded = await InsertData();
                equipment = dataSeeded.Equipment;
                productNotInEquipment = dataSeeded.ProductNotInEquipment;
                product = dataSeeded.Product;
                assembly1 = dataSeeded.Assembly1;
                assembly11 = dataSeeded.Assembly11;
            });

            Guid userId = Guid.NewGuid();
            using (CurrentPrincipalAccessor.Change([new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString()),
                       new Claim(AbpClaimTypes.UserId, userId.ToString())]))
            {
                BomDocument equipmentBomDocument = null!;
                BomDocument noModificationDateBomDocument = null!;
                BomDocument productBomDocument = null!;
                BomDocument productExpiredBomDocument = null!;
                BomDocument productExpiredBomDocumentByService = null!;
                BomDocument productOtherUserBomDocument = null!;

                await WithUnitOfWorkAsync(async () =>
                {
                    equipmentBomDocument = new(GuidGenerator.Create(), BomDocumentContext.Equipment, equipment.Id, null, "en", Guid.NewGuid());
                    equipmentBomDocument.SetFilename("equipment.pdf");
                    equipmentBomDocument.ChangeStatus(BomDocumentationStatus.Completed);
                    equipmentBomDocument.SetSize(1);
                    equipmentBomDocument.SetProcessingTime(10);
                    PropertyInfo? equipmentProperty = typeof(BomDocument).GetProperty(nameof(BomDocument.CreatorId), BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    equipmentProperty!.SetValue(equipmentBomDocument, userId);
                    await BomDocumentRepository.InsertAsync(equipmentBomDocument, true);

                    noModificationDateBomDocument = new(GuidGenerator.Create(), BomDocumentContext.Product, productNotInEquipment.Id, null, "en", Guid.NewGuid());
                    noModificationDateBomDocument.SetFilename("noModificationDate.pdf");
                    noModificationDateBomDocument.ChangeStatus(BomDocumentationStatus.Pending);
                    noModificationDateBomDocument.SetSize(2);
                    noModificationDateBomDocument.SetProcessingTime(20);
                    PropertyInfo? noModificationDateProperty = typeof(BomDocument).GetProperty(nameof(BomDocument.CreatorId), BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    noModificationDateProperty!.SetValue(noModificationDateBomDocument, userId);
                    await BomDocumentRepository.InsertAsync(noModificationDateBomDocument, true);

                    productBomDocument = new(GuidGenerator.Create(), BomDocumentContext.Product, product.Id, null, "en", Guid.NewGuid());
                    productBomDocument.SetFilename("product.pdf");
                    productBomDocument.ChangeStatus(BomDocumentationStatus.Pending);
                    productBomDocument.SetSize(3);
                    productBomDocument.SetProcessingTime(30);
                    PropertyInfo? creatorIdProperty = typeof(BomDocument).GetProperty(nameof(BomDocument.CreatorId), BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    creatorIdProperty!.SetValue(productBomDocument, userId);
                    await BomDocumentRepository.InsertAsync(productBomDocument, true);

                    productExpiredBomDocument = new(GuidGenerator.Create(), BomDocumentContext.Product, product.Id, assembly1.Id, "en", Guid.NewGuid());
                    productExpiredBomDocument.SetFilename("expired.pdf");
                    productExpiredBomDocument.ChangeStatus(BomDocumentationStatus.Completed);
                    productExpiredBomDocument.SetSize(4);
                    productExpiredBomDocument.SetProcessingTime(40);
                    PropertyInfo? expiredProperty = typeof(BomDocument).GetProperty(nameof(BomDocument.CreatorId), BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    expiredProperty!.SetValue(productExpiredBomDocument, userId);
                    PropertyInfo? lastModificationTimeProperty = typeof(BomDocument).GetProperty(nameof(BomDocument.LastModificationTime), BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    lastModificationTimeProperty!.SetValue(productExpiredBomDocument, DateTime.Now.AddHours((CoreApplicationConsts.BomDocumentation.ExpirationHours + 1) * -1));
                    await BomDocumentRepository.InsertAsync(productExpiredBomDocument, true);

                    productExpiredBomDocumentByService = new(GuidGenerator.Create(), BomDocumentContext.Product, product.Id, assembly1.Id, "en", Guid.NewGuid());
                    productExpiredBomDocumentByService.SetFilename("expired.pdf");
                    productExpiredBomDocumentByService.ChangeStatus(BomDocumentationStatus.Expired);
                    productExpiredBomDocumentByService.SetSize(5);
                    productExpiredBomDocumentByService.SetProcessingTime(50);
                    PropertyInfo? expiredByServiceProperty = typeof(BomDocument).GetProperty(nameof(BomDocument.CreatorId), BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    expiredByServiceProperty!.SetValue(productExpiredBomDocumentByService, userId);
                    PropertyInfo? lastModificationTimeByServiceProperty = typeof(BomDocument).GetProperty(nameof(BomDocument.LastModificationTime), BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    lastModificationTimeByServiceProperty!.SetValue(productExpiredBomDocumentByService, DateTime.Now.AddHours((CoreApplicationConsts.BomDocumentation.ExpirationHours + 1) * -1));
                    await BomDocumentRepository.InsertAsync(productExpiredBomDocumentByService, true);

                    productOtherUserBomDocument = new(GuidGenerator.Create(), BomDocumentContext.Product, product.Id, assembly11.Id, "en", Guid.NewGuid());
                    productOtherUserBomDocument.SetFilename("otherUser.pdf");
                    productOtherUserBomDocument.ChangeStatus(BomDocumentationStatus.Completed);
                    productOtherUserBomDocument.SetSize(6);
                    productOtherUserBomDocument.SetProcessingTime(60);
                    PropertyInfo? otherUserProperty = typeof(BomDocument).GetProperty(nameof(BomDocument.CreatorId), BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    otherUserProperty!.SetValue(productOtherUserBomDocument, Guid.NewGuid());
                    await BomDocumentRepository.InsertAsync(productOtherUserBomDocument, true);
                });

                await WithUnitOfWorkAsync(async () =>
                {
                    // Arrange
                    GetBomDocumentsMetadataQuery bomDocumentsMetadataQuery = new(new BomDocumentPaginationFilter());

                    // Act
                    PagedResultDto<BomDocumentDto> results = await QuerySender.Send(bomDocumentsMetadataQuery);

                    // Assert
                    results.TotalCount.ShouldBe(5);

                    BomDocumentDto? equipmentResult = results.Items.FirstOrDefault(x => x.Id == equipmentBomDocument.Id);
                    equipmentResult.ShouldNotBeNull();
                    equipmentResult.Status.ShouldBe(equipmentBomDocument.Status);
                    equipmentResult.Filename.ShouldBe(equipmentBomDocument.FileName);
                    equipmentResult.MainEntityId.ShouldBe(equipmentBomDocument.MainEntityId);
                    equipmentResult.AssemblyId.ShouldBe(equipmentBomDocument.AssemblyId);
                    equipmentResult.Context.ShouldBe(equipmentBomDocument.Context);
                    equipmentResult.Size.ShouldBe(equipmentBomDocument.Size);
                    equipmentResult.ProcessingTime.ShouldBe(equipmentBomDocument.ProcessingTime);

                    BomDocumentDto? noModificationDateResult = results.Items.FirstOrDefault(x => x.Id == noModificationDateBomDocument.Id);
                    noModificationDateResult.ShouldNotBeNull();
                    noModificationDateResult.Status.ShouldBe(noModificationDateBomDocument.Status);
                    noModificationDateResult.Filename.ShouldBe(noModificationDateBomDocument.FileName);
                    noModificationDateResult.MainEntityId.ShouldBe(noModificationDateBomDocument.MainEntityId);
                    noModificationDateResult.AssemblyId.ShouldBe(noModificationDateBomDocument.AssemblyId);
                    noModificationDateResult.Context.ShouldBe(noModificationDateBomDocument.Context);
                    noModificationDateResult.Size.ShouldBe(noModificationDateBomDocument.Size);
                    noModificationDateResult.ProcessingTime.ShouldBe(noModificationDateBomDocument.ProcessingTime);

                    BomDocumentDto? productResult = results.Items.FirstOrDefault(x => x.Id == productBomDocument.Id);
                    productResult.ShouldNotBeNull();
                    productResult.Status.ShouldBe(productBomDocument.Status);
                    productResult.Filename.ShouldBe(productBomDocument.FileName);
                    productResult.MainEntityId.ShouldBe(productBomDocument.MainEntityId);
                    productResult.AssemblyId.ShouldBe(productBomDocument.AssemblyId);
                    productResult.Context.ShouldBe(productBomDocument.Context);
                    productResult.Size.ShouldBe(productBomDocument.Size);
                    productResult.ProcessingTime.ShouldBe(productBomDocument.ProcessingTime);

                    BomDocumentDto? expiredResult = results.Items.FirstOrDefault(x => x.Id == productExpiredBomDocument.Id);
                    expiredResult.ShouldNotBeNull();
                    expiredResult.Status.ShouldBe(BomDocumentationStatus.Expired);
                    expiredResult.Filename.ShouldBe(productExpiredBomDocument.FileName);
                    expiredResult.MainEntityId.ShouldBe(productExpiredBomDocument.MainEntityId);
                    expiredResult.AssemblyId.ShouldBe(productExpiredBomDocument.AssemblyId);
                    expiredResult.Context.ShouldBe(productExpiredBomDocument.Context);
                    expiredResult.Size.ShouldBe(productExpiredBomDocument.Size);
                    expiredResult.ProcessingTime.ShouldBe(productExpiredBomDocument.ProcessingTime);

                    BomDocumentDto? expiredByServiceResult = results.Items.FirstOrDefault(x => x.Id == productExpiredBomDocumentByService.Id);
                    expiredByServiceResult.ShouldNotBeNull();
                    expiredByServiceResult.Status.ShouldBe(productExpiredBomDocumentByService.Status);
                    expiredByServiceResult.Filename.ShouldBe(productExpiredBomDocumentByService.FileName);
                    expiredByServiceResult.MainEntityId.ShouldBe(productExpiredBomDocumentByService.MainEntityId);
                    expiredByServiceResult.AssemblyId.ShouldBe(productExpiredBomDocumentByService.AssemblyId);
                    expiredByServiceResult.Context.ShouldBe(productExpiredBomDocumentByService.Context);
                    expiredByServiceResult.Size.ShouldBe(productExpiredBomDocumentByService.Size);
                    expiredByServiceResult.ProcessingTime.ShouldBe(productExpiredBomDocumentByService.ProcessingTime);

                    BomDocumentDto? otherUserResult = results.Items.FirstOrDefault(x => x.Id == productOtherUserBomDocument.Id);
                    otherUserResult.ShouldBeNull();
                });
            }
        }
    }

    [Fact]
    public async Task GetBomDocumentsMetadataQuery_Should_Return_Bom_Documentations_Metadata_For_BomDocument_Expired_By_Date_Or_Service()
    {
        _authorizationResultSuccess = false;
        Guid tenantId = Guid.NewGuid();
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        using (CurrentTenant.Change(tenantId))
        {
            Equipment equipment = null!;
            Product productNotInEquipment = null!;
            Product product = null!;
            Component assembly1 = null!;
            Component assembly11 = null!;

            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                DataSeeded dataSeeded = await InsertData();
                equipment = dataSeeded.Equipment;
                productNotInEquipment = dataSeeded.ProductNotInEquipment;
                product = dataSeeded.Product;
                assembly1 = dataSeeded.Assembly1;
                assembly11 = dataSeeded.Assembly11;
            });

            Guid userId = Guid.NewGuid();
            using (CurrentPrincipalAccessor.Change([new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString()),
                       new Claim(AbpClaimTypes.UserId, userId.ToString())]))
            {
                BomDocument productExpiredBomDocument = null!;
                BomDocument productExpiredBomDocumentByService = null!;

                await WithUnitOfWorkAsync(async () =>
                {
                    BomDocument equipmentBomDocument = new(GuidGenerator.Create(), BomDocumentContext.Equipment, equipment.Id, null, "en", Guid.NewGuid());
                    equipmentBomDocument.SetFilename("equipment.pdf");
                    equipmentBomDocument.ChangeStatus(BomDocumentationStatus.Completed);
                    PropertyInfo? equipmentProperty = typeof(BomDocument).GetProperty(nameof(BomDocument.CreatorId), BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    equipmentProperty!.SetValue(equipmentBomDocument, userId);
                    await BomDocumentRepository.InsertAsync(equipmentBomDocument, true);

                    BomDocument noModificationDateBomDocument = new(GuidGenerator.Create(), BomDocumentContext.Product, productNotInEquipment.Id, null, "en", Guid.NewGuid());
                    noModificationDateBomDocument.SetFilename("noModificationDate.pdf");
                    noModificationDateBomDocument.ChangeStatus(BomDocumentationStatus.Pending);
                    PropertyInfo? noModificationDateProperty = typeof(BomDocument).GetProperty(nameof(BomDocument.CreatorId), BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    noModificationDateProperty!.SetValue(noModificationDateBomDocument, userId);
                    await BomDocumentRepository.InsertAsync(noModificationDateBomDocument, true);

                    BomDocument productBomDocument = new(GuidGenerator.Create(), BomDocumentContext.Product, product.Id, null, "en", Guid.NewGuid());
                    productBomDocument.SetFilename("product.pdf");
                    productBomDocument.ChangeStatus(BomDocumentationStatus.Pending);
                    PropertyInfo? creatorIdProperty = typeof(BomDocument).GetProperty(nameof(BomDocument.CreatorId), BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    creatorIdProperty!.SetValue(productBomDocument, userId);
                    await BomDocumentRepository.InsertAsync(productBomDocument, true);

                    productExpiredBomDocument = new(GuidGenerator.Create(), BomDocumentContext.Product, product.Id, assembly1.Id, "en", Guid.NewGuid());
                    productExpiredBomDocument.SetFilename("expired.pdf");
                    productExpiredBomDocument.ChangeStatus(BomDocumentationStatus.Completed);
                    PropertyInfo? expiredProperty = typeof(BomDocument).GetProperty(nameof(BomDocument.CreatorId), BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    expiredProperty!.SetValue(productExpiredBomDocument, userId);
                    PropertyInfo? lastModificationTimeProperty = typeof(BomDocument).GetProperty(nameof(BomDocument.LastModificationTime), BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    lastModificationTimeProperty!.SetValue(productExpiredBomDocument, DateTime.Now.AddHours((CoreApplicationConsts.BomDocumentation.ExpirationHours + 1) * -1));
                    await BomDocumentRepository.InsertAsync(productExpiredBomDocument, true);

                    productExpiredBomDocumentByService = new(GuidGenerator.Create(), BomDocumentContext.Product, product.Id, assembly1.Id, "en", Guid.NewGuid());
                    productExpiredBomDocumentByService.SetFilename("expired.pdf");
                    productExpiredBomDocumentByService.ChangeStatus(BomDocumentationStatus.Expired);
                    PropertyInfo? expiredByServiceProperty = typeof(BomDocument).GetProperty(nameof(BomDocument.CreatorId), BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    expiredByServiceProperty!.SetValue(productExpiredBomDocumentByService, userId);
                    PropertyInfo? lastModificationTimeByServiceProperty = typeof(BomDocument).GetProperty(nameof(BomDocument.LastModificationTime), BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    lastModificationTimeByServiceProperty!.SetValue(productExpiredBomDocumentByService, DateTime.Now.AddHours((CoreApplicationConsts.BomDocumentation.ExpirationHours + 1) * -1));
                    await BomDocumentRepository.InsertAsync(productExpiredBomDocumentByService, true);

                    BomDocument productOtherUserBomDocument = new(GuidGenerator.Create(), BomDocumentContext.Product, product.Id, assembly11.Id, "en", Guid.NewGuid());
                    productOtherUserBomDocument.SetFilename("otherUser.pdf");
                    productOtherUserBomDocument.ChangeStatus(BomDocumentationStatus.Completed);
                    PropertyInfo? otherUserProperty = typeof(BomDocument).GetProperty(nameof(BomDocument.CreatorId), BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    otherUserProperty!.SetValue(productOtherUserBomDocument, Guid.NewGuid());
                    await BomDocumentRepository.InsertAsync(productOtherUserBomDocument, true);
                });

                await WithUnitOfWorkAsync(async () =>
                {
                    // Arrange
                    GetBomDocumentsMetadataQuery bomDocumentsMetadataQuery = new(new BomDocumentPaginationFilter() { Status = BomDocumentationStatus.Expired.ToString() });

                    // Act
                    PagedResultDto<BomDocumentDto> results = await QuerySender.Send(bomDocumentsMetadataQuery);

                    // Assert
                    results.TotalCount.ShouldBe(2);

                    BomDocumentDto? expiredResult = results.Items.FirstOrDefault(x => x.Id == productExpiredBomDocument.Id);
                    expiredResult.ShouldNotBeNull();
                    expiredResult.Status.ShouldBe(BomDocumentationStatus.Expired);
                    expiredResult.Filename.ShouldBe(productExpiredBomDocument.FileName);
                    expiredResult.MainEntityId.ShouldBe(productExpiredBomDocument.MainEntityId);
                    expiredResult.AssemblyId.ShouldBe(productExpiredBomDocument.AssemblyId);
                    expiredResult.Context.ShouldBe(productExpiredBomDocument.Context);

                    BomDocumentDto? expiredByServiceResult = results.Items.FirstOrDefault(x => x.Id == productExpiredBomDocumentByService.Id);
                    expiredByServiceResult.ShouldNotBeNull();
                    expiredByServiceResult.Status.ShouldBe(BomDocumentationStatus.Expired);
                    expiredByServiceResult.Filename.ShouldBe(productExpiredBomDocumentByService.FileName);
                    expiredByServiceResult.MainEntityId.ShouldBe(productExpiredBomDocumentByService.MainEntityId);
                    expiredByServiceResult.AssemblyId.ShouldBe(productExpiredBomDocumentByService.AssemblyId);
                    expiredByServiceResult.Context.ShouldBe(productExpiredBomDocumentByService.Context);
                });
            }
        }
    }

    [Fact]
    public async Task GetBomDocumentsMetadataQuery_Should_Return_Bom_Documentations_Metadata_Sorted_And_Filtered()
    {
        _authorizationResultSuccess = false;
        Guid tenantId = Guid.NewGuid();
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        using (CurrentTenant.Change(tenantId))
        {
            Equipment equipment = null!;
            Product productNotInEquipment = null!;
            Product product = null!;
            Component assembly1 = null!;

            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                DataSeeded dataSeeded = await InsertData();
                equipment = dataSeeded.Equipment;
                productNotInEquipment = dataSeeded.ProductNotInEquipment;
                product = dataSeeded.Product;
                assembly1 = dataSeeded.Assembly1;
            });

            Guid userId = Guid.NewGuid();
            using (CurrentPrincipalAccessor.Change([new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString()),
                       new Claim(AbpClaimTypes.UserId, userId.ToString())]))
            {
                BomDocument product1BomDocument = null!;
                BomDocument product2BomDocument = null!;

                await WithUnitOfWorkAsync(async () =>
                {
                    BomDocument equipmentBomDocument = new(GuidGenerator.Create(), BomDocumentContext.Equipment, equipment.Id, null, "en", Guid.NewGuid());
                    equipmentBomDocument.SetFilename("equipment.pdf");
                    equipmentBomDocument.ChangeStatus(BomDocumentationStatus.Completed);
                    PropertyInfo? equipmentProperty = typeof(BomDocument).GetProperty(nameof(BomDocument.CreatorId), BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    equipmentProperty!.SetValue(equipmentBomDocument, userId);
                    await BomDocumentRepository.InsertAsync(equipmentBomDocument, true);

                    product1BomDocument = new(GuidGenerator.Create(), BomDocumentContext.Product, productNotInEquipment.Id, null, "en", Guid.NewGuid());
                    product1BomDocument.SetFilename("abc.pdf");
                    product1BomDocument.ChangeStatus(BomDocumentationStatus.Pending);
                    PropertyInfo? product1Property = typeof(BomDocument).GetProperty(nameof(BomDocument.CreatorId), BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    product1Property!.SetValue(product1BomDocument, userId);
                    await BomDocumentRepository.InsertAsync(product1BomDocument, true);

                    product2BomDocument = new(GuidGenerator.Create(), BomDocumentContext.Product, product.Id, null, "en", Guid.NewGuid());
                    product2BomDocument.SetFilename("bcd.pdf");
                    product2BomDocument.ChangeStatus(BomDocumentationStatus.Pending);
                    PropertyInfo? product2Property = typeof(BomDocument).GetProperty(nameof(BomDocument.CreatorId), BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    product2Property!.SetValue(product2BomDocument, userId);
                    await BomDocumentRepository.InsertAsync(product2BomDocument, true);

                    BomDocument product3BomDocument = new(GuidGenerator.Create(), BomDocumentContext.Product, product.Id, assembly1.Id, "en", Guid.NewGuid());
                    product3BomDocument.SetFilename("def.pdf");
                    product3BomDocument.ChangeStatus(BomDocumentationStatus.Pending);
                    PropertyInfo? product3Property = typeof(BomDocument).GetProperty(nameof(BomDocument.CreatorId), BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    product3Property!.SetValue(product3BomDocument, userId);
                    await BomDocumentRepository.InsertAsync(product3BomDocument, true);
                });

                await WithUnitOfWorkAsync(async () =>
                {
                    // Arrange
                    GetBomDocumentsMetadataQuery bomDocumentsMetadataQuery = new(new BomDocumentPaginationFilter
                    {
                        FileName = "bc",
                        Status = "Pending",
                        Sort = nameof(BomDocument.FileName).ToLower()
                    });

                    // Act
                    PagedResultDto<BomDocumentDto> results = await QuerySender.Send(bomDocumentsMetadataQuery);

                    // Assert
                    results.TotalCount.ShouldBe(2);

                    BomDocumentDto firstResult = results.Items[0];
                    firstResult.Id.ShouldBe(product1BomDocument.Id);

                    BomDocumentDto secondResult = results.Items[1];
                    secondResult.Id.ShouldBe(product2BomDocument.Id);
                });
            }
        }
    }

    [Fact]
    public async Task GetBomDocumentsMetadataQuery_Should_Return_Bom_Documentations_Metadata_Filtered_By_MainEntityId()
    {
        _authorizationResultSuccess = false;
        Guid tenantId = Guid.NewGuid();
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        using (CurrentTenant.Change(tenantId))
        {
            Equipment equipment = null!;
            Product product = null!;
            Component assembly1 = null!;

            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                DataSeeded dataSeeded = await InsertData();
                equipment = dataSeeded.Equipment;
                product = dataSeeded.Product;
                assembly1 = dataSeeded.Assembly1;
            });

            Guid userId = Guid.NewGuid();
            using (CurrentPrincipalAccessor.Change([new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString()),
                       new Claim(AbpClaimTypes.UserId, userId.ToString())]))
            {
                BomDocument product1BomDocument = null!;
                BomDocument product2BomDocument = null!;

                await WithUnitOfWorkAsync(async () =>
                {
                    BomDocument equipmentBomDocument = new(GuidGenerator.Create(), BomDocumentContext.Equipment, equipment.Id, null, "en", Guid.NewGuid());
                    equipmentBomDocument.SetFilename("equipment.pdf");
                    equipmentBomDocument.ChangeStatus(BomDocumentationStatus.Completed);
                    PropertyInfo? equipmentProperty = typeof(BomDocument).GetProperty(nameof(BomDocument.CreatorId), BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    equipmentProperty!.SetValue(equipmentBomDocument, userId);
                    await BomDocumentRepository.InsertAsync(equipmentBomDocument, true);

                    product1BomDocument = new(GuidGenerator.Create(), BomDocumentContext.Product, product.Id, null, "en", Guid.NewGuid());
                    product1BomDocument.SetFilename("bcd.pdf");
                    product1BomDocument.ChangeStatus(BomDocumentationStatus.Pending);
                    PropertyInfo? product2Property = typeof(BomDocument).GetProperty(nameof(BomDocument.CreatorId), BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    product2Property!.SetValue(product1BomDocument, userId);
                    await BomDocumentRepository.InsertAsync(product1BomDocument, true);

                    product2BomDocument = new(GuidGenerator.Create(), BomDocumentContext.Product, product.Id, assembly1.Id, "en", Guid.NewGuid());
                    product2BomDocument.SetFilename("def.pdf");
                    product2BomDocument.ChangeStatus(BomDocumentationStatus.Pending);
                    PropertyInfo? product3Property = typeof(BomDocument).GetProperty(nameof(BomDocument.CreatorId), BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    product3Property!.SetValue(product2BomDocument, userId);
                    await BomDocumentRepository.InsertAsync(product2BomDocument, true);
                });

                await WithUnitOfWorkAsync(async () =>
                {
                    // Arrange
                    GetBomDocumentsMetadataQuery bomDocumentsMetadataQuery = new(new BomDocumentPaginationFilter
                    {
                        MainEntityId = product.Id
                    });

                    // Act
                    PagedResultDto<BomDocumentDto> results = await QuerySender.Send(bomDocumentsMetadataQuery);

                    // Assert
                    results.TotalCount.ShouldBe(2);

                    BomDocumentDto? product1Result = results.Items.FirstOrDefault(x => x.Id == product1BomDocument.Id);
                    product1Result.ShouldNotBeNull();

                    BomDocumentDto? product2Result = results.Items.FirstOrDefault(x => x.Id == product2BomDocument.Id);
                    product2Result.ShouldNotBeNull();
                });
            }
        }
    }

    [Fact]
    public async Task GetBomDocumentsMetadataQuery_Should_Return_Bom_Documentations_Metadata_Filtered_By_AssemblyId()
    {
        _authorizationResultSuccess = false;
        Guid tenantId = Guid.NewGuid();
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        using (CurrentTenant.Change(tenantId))
        {
            Equipment equipment = null!;
            Product product = null!;
            Component assembly1 = null!;

            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                DataSeeded dataSeeded = await InsertData();
                equipment = dataSeeded.Equipment;
                product = dataSeeded.Product;
                assembly1 = dataSeeded.Assembly1;
            });

            Guid userId = Guid.NewGuid();
            using (CurrentPrincipalAccessor.Change([new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString()),
                       new Claim(AbpClaimTypes.UserId, userId.ToString())]))
            {
                BomDocument equipmentBomDocument = null!;
                BomDocument product2BomDocument = null!;

                await WithUnitOfWorkAsync(async () =>
                {
                    equipmentBomDocument = new(GuidGenerator.Create(), BomDocumentContext.Equipment, equipment.Id, assembly1.Id, "en", Guid.NewGuid());
                    equipmentBomDocument.SetFilename("equipment.pdf");
                    equipmentBomDocument.ChangeStatus(BomDocumentationStatus.Completed);
                    PropertyInfo? equipmentProperty = typeof(BomDocument).GetProperty(nameof(BomDocument.CreatorId), BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    equipmentProperty!.SetValue(equipmentBomDocument, userId);
                    await BomDocumentRepository.InsertAsync(equipmentBomDocument, true);

                    BomDocument product1BomDocument = new(GuidGenerator.Create(), BomDocumentContext.Product, product.Id, null, "en", Guid.NewGuid());
                    product1BomDocument.SetFilename("bcd.pdf");
                    product1BomDocument.ChangeStatus(BomDocumentationStatus.Pending);
                    PropertyInfo? product2Property = typeof(BomDocument).GetProperty(nameof(BomDocument.CreatorId), BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    product2Property!.SetValue(product1BomDocument, userId);
                    await BomDocumentRepository.InsertAsync(product1BomDocument, true);

                    product2BomDocument = new(GuidGenerator.Create(), BomDocumentContext.Product, product.Id, assembly1.Id, "en", Guid.NewGuid());
                    product2BomDocument.SetFilename("def.pdf");
                    product2BomDocument.ChangeStatus(BomDocumentationStatus.Pending);
                    PropertyInfo? product3Property = typeof(BomDocument).GetProperty(nameof(BomDocument.CreatorId), BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    product3Property!.SetValue(product2BomDocument, userId);
                    await BomDocumentRepository.InsertAsync(product2BomDocument, true);
                });

                await WithUnitOfWorkAsync(async () =>
                {
                    // Arrange
                    GetBomDocumentsMetadataQuery bomDocumentsMetadataQuery = new(new BomDocumentPaginationFilter
                    {
                        AssemblyId = assembly1.Id
                    });

                    // Act
                    PagedResultDto<BomDocumentDto> results = await QuerySender.Send(bomDocumentsMetadataQuery);

                    // Assert
                    results.TotalCount.ShouldBe(2);

                    BomDocumentDto? product1Result = results.Items.FirstOrDefault(x => x.Id == equipmentBomDocument.Id);
                    product1Result.ShouldNotBeNull();

                    BomDocumentDto? product2Result = results.Items.FirstOrDefault(x => x.Id == product2BomDocument.Id);
                    product2Result.ShouldNotBeNull();
                });
            }
        }
    }

    [Fact]
    public async Task GetBomDocumentsMetadataQuery_Should_Throw_Exception_If_Query_All_Documents_Without_Permission_All()
    {
        _authorizationResultSuccess = false;
        Guid tenantId = Guid.NewGuid();
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        using (CurrentTenant.Change(tenantId))
        {

            await WithUnitOfWorkAsync(async () =>
            {
                Guid userId = Guid.NewGuid();
                using (CurrentPrincipalAccessor.Change([new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString()),
                       new Claim(AbpClaimTypes.UserId, userId.ToString())]))
                {
                    await WithUnitOfWorkAsync(async () =>
                    {
                        // Arrange
                        GetBomDocumentsMetadataQuery bomDocumentsMetadataQuery = new(new BomDocumentPaginationFilter { All = true });

                        // Act
                        await QuerySender.Send(bomDocumentsMetadataQuery).ShouldThrowAsync<AbpAuthorizationException>();

                        // Assert
                    });
                }
            });
        }
    }

    [Fact]
    public async Task GetBomDocumentsMetadataQuery_Should_Return_Bom_Documentations_Metadata_Of_Other_Users()
    {
        _authorizationResultSuccess = true;
        Guid tenantId = Guid.NewGuid();
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        using (CurrentTenant.Change(tenantId))
        {
            Equipment equipment = null!;
            Product product = null!;
            Component assembly11 = null!;

            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                DataSeeded dataSeeded = await InsertData();
                equipment = dataSeeded.Equipment;
                product = dataSeeded.Product;
                assembly11 = dataSeeded.Assembly11;
            });

            Guid userId = Guid.NewGuid();
            using (CurrentPrincipalAccessor.Change([new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString()),
                       new Claim(AbpClaimTypes.UserId, userId.ToString())]))
            {
                BomDocument equipmentBomDocument = null!;
                BomDocument productOtherUserBomDocument = null!;

                await WithUnitOfWorkAsync(async () =>
                {
                    equipmentBomDocument = new(GuidGenerator.Create(), BomDocumentContext.Equipment, equipment.Id, null, "en", Guid.NewGuid());
                    equipmentBomDocument.SetFilename("equipment.pdf");
                    equipmentBomDocument.ChangeStatus(BomDocumentationStatus.Completed);
                    PropertyInfo? equipmentProperty = typeof(BomDocument).GetProperty(nameof(BomDocument.CreatorId), BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    equipmentProperty!.SetValue(equipmentBomDocument, userId);
                    await BomDocumentRepository.InsertAsync(equipmentBomDocument, true);

                    productOtherUserBomDocument = new(GuidGenerator.Create(), BomDocumentContext.Product, product.Id, assembly11.Id, "en", Guid.NewGuid());
                    productOtherUserBomDocument.SetFilename("otherUser.pdf");
                    productOtherUserBomDocument.ChangeStatus(BomDocumentationStatus.Completed);
                    PropertyInfo? otherUserProperty = typeof(BomDocument).GetProperty(nameof(BomDocument.CreatorId), BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    otherUserProperty!.SetValue(productOtherUserBomDocument, Guid.NewGuid());
                    await BomDocumentRepository.InsertAsync(productOtherUserBomDocument, true);
                });

                await WithUnitOfWorkAsync(async () =>
                {
                    // Arrange
                    GetBomDocumentsMetadataQuery bomDocumentsMetadataQuery = new(new BomDocumentPaginationFilter { All = true });

                    // Act
                    PagedResultDto<BomDocumentDto> results = await QuerySender.Send(bomDocumentsMetadataQuery);

                    // Assert
                    results.TotalCount.ShouldBe(2);

                    BomDocumentDto? equipmentResult = results.Items.FirstOrDefault(x => x.Id == equipmentBomDocument.Id);
                    equipmentResult.ShouldNotBeNull();

                    BomDocumentDto? otherUserResult = results.Items.FirstOrDefault(x => x.Id == productOtherUserBomDocument.Id);
                    otherUserResult.ShouldNotBeNull();
                });
            }
        }
    }
}