using System;
using System.Reflection;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using NSubstitute;
using Shouldly;
using SpareParts.Common;
using SpareParts.Common.Companies;
using SpareParts.Core.BomDocuments.Dtos;
using SpareParts.Core.BomDocuments.Queries;
using SpareParts.Core.Entities.Boms;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Enums;
using Volo.Abp.Authorization;
using Volo.Abp.Data;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Modularity;
using Volo.Abp.Security.Claims;
using Xunit;

namespace SpareParts.Core.BomDocuments.QueryHandlers;
public abstract class GetBomDocumentMetadataByIdQueryHandlerTests<TStartupModule> : BomDocumentTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    private bool _authorizationResultSuccess;
    private IAbpAuthorizationService _authorizationService = null!;

    protected override void AfterAddApplication(IServiceCollection services)
    {
        _authorizationService = Substitute.For<IAbpAuthorizationService>();
        _authorizationService.AuthorizeAsync(Arg.Any<ClaimsPrincipal>(), Arg.Any<object?>(), Arg.Any<string>())
            .Returns(_ => _authorizationResultSuccess ? AuthorizationResult.Success() : AuthorizationResult.Failed());

        services.Replace(ServiceDescriptor.Singleton(_authorizationService));
    }

    [Fact]
    public async Task GetBomDocumentMetadataByIdQuery_Should_Return_Bom_Documentation_Metadata()
    {
        _authorizationResultSuccess = false;
        Guid tenantId = Guid.NewGuid();
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        using (CurrentTenant.Change(tenantId))
        {
            Product product = null!;
            Component assembly1 = null!;

            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                DataSeeded dataSeeded = await InsertData();
                product = dataSeeded.Product;
                assembly1 = dataSeeded.Assembly1;
            });

            await WithUnitOfWorkAsync(async () =>
            {
                Guid userId = Guid.NewGuid();
                using (CurrentPrincipalAccessor.Change([new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString()),
                       new Claim(AbpClaimTypes.UserId, userId.ToString())]))
                {

                    BomDocument bomDocument = null!;
                    await WithUnitOfWorkAsync(async () =>
                    {
                        bomDocument = new(GuidGenerator.Create(), BomDocumentContext.Product, product.Id, assembly1.Id, "en", Guid.NewGuid());
                        bomDocument.SetFilename("test.pdf");
                        bomDocument.ChangeStatus(BomDocumentationStatus.Completed);

                        PropertyInfo? creatorIdProperty = typeof(BomDocument).GetProperty(nameof(BomDocument.CreatorId), BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                        creatorIdProperty!.SetValue(bomDocument, userId);
                        await BomDocumentRepository.InsertAsync(bomDocument, true);
                    });

                    await WithUnitOfWorkAsync(async () =>
                    {
                        // Arrange
                        GetBomDocumentMetadataByIdQuery bomDocumentMetadataByIdQuery = new(bomDocument.Id);

                        // Act
                        BomDocumentDto result = await QuerySender.Send(bomDocumentMetadataByIdQuery);

                        // Assert
                        result.Id.ShouldBe(bomDocument.Id);
                        result.Filename.ShouldBe(bomDocument.FileName);
                        result.Status.ShouldBe(bomDocument.Status);
                        result.MainEntityId.ShouldBe(bomDocument.MainEntityId);
                        result.AssemblyId.ShouldBe(bomDocument.AssemblyId);
                        result.Context.ShouldBe(bomDocument.Context);
                        result.Size.ShouldBeNull();
                        result.ProcessingTime.ShouldBeNull();
                    });
                }
            });
        }
    }

    [Fact]
    public async Task GetBomDocumentMetadataByIdQuery_Should_Throw_Exception_If_BomDocument_Not_Exist()
    {
        _authorizationResultSuccess = true;
        using (CurrentTenant.Change(Guid.NewGuid()))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                GetBomDocumentMetadataByIdQuery bomDocumentMetadataByIdQuery = new(Guid.NewGuid());

                // Act & Assert
                await QuerySender.Send(bomDocumentMetadataByIdQuery).ShouldThrowAsync<EntityNotFoundException>();
            });
        }
    }

    [Fact]
    public async Task GetBomDocumentMetadataByIdQuery_Should_Throw_Exception_If_Query_Bom_Document_Of_Other_User()
    {
        _authorizationResultSuccess = false;
        Guid tenantId = Guid.NewGuid();
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        using (CurrentTenant.Change(tenantId))
        {
            Product product = null!;

            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                DataSeeded dataSeeded = await InsertData();
                product = dataSeeded.Product;
            });

            Guid bomDocumentId = Guid.Empty;
            await WithUnitOfWorkAsync(async () =>
            {
                BomDocument bomDocument = new(GuidGenerator.Create(), BomDocumentContext.Product, product.Id, null, "en", Guid.NewGuid());
                PropertyInfo? creatorIdProperty = typeof(BomDocument).GetProperty(nameof(BomDocument.CreatorId), BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                creatorIdProperty!.SetValue(bomDocument, Guid.NewGuid());
                await BomDocumentRepository.InsertAsync(bomDocument, true);
                bomDocumentId = bomDocument.Id;
            });

            Guid userId = Guid.NewGuid();
            using (CurrentPrincipalAccessor.Change([new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString()),
                       new Claim(AbpClaimTypes.UserId, userId.ToString())]))
            {

                await WithUnitOfWorkAsync(async () =>
                {
                    // Arrange
                    GetBomDocumentMetadataByIdQuery bomDocumentMetadataByIdQuery = new(bomDocumentId);

                    // Act & Assert
                    await QuerySender.Send(bomDocumentMetadataByIdQuery).ShouldThrowAsync<EntityNotFoundException>();
                });
            }
        }
    }
}