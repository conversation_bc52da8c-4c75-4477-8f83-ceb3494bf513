using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Shouldly;
using SpareParts.Core.BomDocuments.DataModels;
using SpareParts.Core.Entities.Components;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Core.BomDocuments.QuestPdf;

public abstract class MainEntityDataModelBuilderTests<TStartupModule> : BomDocumentTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    private MainEntityDataModelBuilder MainEntityDataModelBuilder => ServiceProvider.GetRequiredService<MainEntityDataModelBuilder>();

    [Fact]
    public async Task BuildInitialProductDataModel_Should_Build_ProductDataModel_For_Product()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true, GenerateAssemblies = true, GenerateDrawingsForProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid productId = storeContext.ProductIdByCodes.First().Value;
                Product product = await ProductRepository.GetAsync(productId);

                BomDocumentGenerationContext generationContext = new()
                {
                    TenantName = "tenant name",
                    OperationId = Guid.NewGuid(),
                    CreatorId = Guid.NewGuid(),
                    Context = Enums.BomDocumentContext.Product,
                    CodeOrSn = "code",
                    Language = "en"
                };

                // Act
                BomDocumentMainPageDataModel bomDocumentMainPageDataModel = await MainEntityDataModelBuilder.BuildInitialProductDataModelAsync(product, generationContext);

                // Assert
                bomDocumentMainPageDataModel.Code.ShouldBe(product.Component.Code);
                bomDocumentMainPageDataModel.PublicationDate.Day.ShouldBe(DateTime.Now.Day);
                bomDocumentMainPageDataModel.Label.ShouldBe(product.Component.OnlineTranslations.First().Label);
                bomDocumentMainPageDataModel.Context.ShouldBe(Enums.BomDocumentContext.Product);
                bomDocumentMainPageDataModel.ThumbnailId.ShouldBe(product.Component.ImageId);
                bomDocumentMainPageDataModel.MainComponentDataModel.ShouldBeNull();
            });
        }
    }

    [Fact]
    public async Task BuildInitialProductDataModel_Should_Build_ProductDataModel_For_Equipment()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true, GenerateAssemblies = true, GenerateDrawingsForProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid productId = storeContext.ProductIdByCodes.First().Value;
                Product product = await ProductRepository.GetAsync(productId);
                string serialNumber = "a-b";

                BomDocumentGenerationContext generationContext = new()
                {
                    TenantName = "tenant name",
                    OperationId = Guid.NewGuid(),
                    CreatorId = Guid.NewGuid(),
                    Context = Enums.BomDocumentContext.Equipment,
                    CodeOrSn = "code",
                    Language = "en"
                };

                // Act
                BomDocumentMainPageDataModel bomDocumentMainPageDataModel = await MainEntityDataModelBuilder.BuildInitialEquipmentDataModelAsync(product, serialNumber, generationContext);

                // Assert
                bomDocumentMainPageDataModel.Code.ShouldBe(product.Component.Code);
                bomDocumentMainPageDataModel.PublicationDate.Day.ShouldBe(DateTime.Now.Day);
                bomDocumentMainPageDataModel.Label.ShouldBe(product.Component.OnlineTranslations.First().Label);
                bomDocumentMainPageDataModel.SerialNumber.ShouldBe(serialNumber);
                bomDocumentMainPageDataModel.Context.ShouldBe(Enums.BomDocumentContext.Equipment);
                bomDocumentMainPageDataModel.ThumbnailId.ShouldBe(product.Component.ImageId);
                bomDocumentMainPageDataModel.MainComponentDataModel.ShouldBeNull();
            });
        }
    }
}