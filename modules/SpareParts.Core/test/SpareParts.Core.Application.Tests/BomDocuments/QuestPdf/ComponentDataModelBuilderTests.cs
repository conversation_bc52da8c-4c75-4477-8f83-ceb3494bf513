using Shouldly;
using SpareParts.Core.BomDocuments.DataModels;
using SpareParts.Core.Entities.Boms;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Core.BomDocuments.QuestPdf;

public abstract class ComponentDataModelBuilderTests<TStartupModule> : BomDocumentTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    [Fact]
    public void Create_Should_Create_ComponentDataModel()
    {
        // Arrange
        Guid thumbnailId = Guid.NewGuid();

        // Act
        ComponentDataModel componentDataModel = ComponentDataModelBuilder.Create("synchroKey", "label", thumbnailId);

        // Assert
        componentDataModel.Code.ShouldBe("synchroKey");
        componentDataModel.Label.ShouldBe("label");
        componentDataModel.ThumbnailId.ShouldBe(thumbnailId);
        componentDataModel.Anchor.ShouldBe("M");

    }

    [Fact]
    public async Task BuildAssemblyBomAsync_Should_Build_AssemblyDataModel()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateAssemblies = true, GenerateDrawingsForAssemblies = true, GeneratePartsForAssemblies = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid assemblyId = storeContext.AssemblyIdByCodes.First().Value;
                Component assembly = await ComponentRepository.GetAsync(assemblyId);

                BomDocument bomDocument = new(
                    Guid.NewGuid(),
                    Enums.BomDocumentContext.Product,
                    assembly.Id,
                    null,
                    "en",
                    Guid.NewGuid());
                BomDocumentGenerationContext generationContext = new()
                {
                    TenantName = "Tenant name",
                    OperationId = bomDocument.OperationId,
                    CreatorId = bomDocument.CreatorId,
                    Context = bomDocument.Context,
                    Language = bomDocument.Language
                };

                // Act
                ComponentDataModel componentDataModel = await ComponentDataModelBuilder.BuildAssemblyBomAsync(assembly, "A", bomDocument, generationContext);

                // Assert
                List<Drawing> drawings = await DrawingRepository.GetListAsync(x => x.AssemblyId == assemblyId);
                List<BomLine> bomLines = await BomLineRepository.GetListAsync(x => x.ParentAssemblyId == assemblyId);

                componentDataModel.Code.ShouldBe(assembly.Code);
                componentDataModel.Label.ShouldBe(assembly.OnlineTranslations.First().Label);
                componentDataModel.ThumbnailId.ShouldBe(assembly.ImageId);
                componentDataModel.Anchor.ShouldBe("A");
                componentDataModel.Drawings.Count.ShouldBe(drawings.Count);
                componentDataModel.Boms.Count.ShouldBe(bomLines.Count);
            });
        }
    }

    [Fact]
    public async Task BuildDrawingsDataModelAsync_Should_Build_DrawingDataModels()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateProducts = true, GenerateAssemblies = true, GenerateDrawingsForProducts = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid productId = storeContext.ProductIdByCodes.First().Value;
                Component assembly = await ComponentRepository.GetAsync(productId);
                List<BomLine> bomLines = await BomLineRepository.GetListAsync(x => x.ParentAssemblyId == productId);
                IEnumerable<Guid> bomLinesIds = bomLines.Select(x => x.ChildComponentId);
                List<Component> bomAssemblies = await ComponentRepository.GetListAsync(x => bomLinesIds.Contains(x.Id) && x.Type == ComponentType.Assembly);
                List<Component> bomParts = await ComponentRepository.GetListAsync(x => bomLinesIds.Contains(x.Id) && x.Type == ComponentType.Part);
                List<Drawing> drawings = await DrawingRepository.GetListAsync(x => x.AssemblyId == productId);

                BomDocument bomDocument = new(
                    Guid.NewGuid(),
                    Enums.BomDocumentContext.Product,
                    assembly.Id,
                    null,
                    "en",
                    Guid.NewGuid());
                BomDocumentGenerationContext generationContext = new()
                {
                    TenantName = "Tenant name",
                    OperationId = bomDocument.OperationId,
                    CreatorId = bomDocument.CreatorId,
                    Context = bomDocument.Context,
                    Language = bomDocument.Language
                };

                ComponentDataModel componentDataModel = await ComponentDataModelBuilder.BuildAssemblyBomAsync(assembly, "A", bomDocument, generationContext);

                // Act
                componentDataModel.Drawings = await ComponentDataModelBuilder.BuildDrawingsDataModelAsync(assembly,
                     bomAssemblies, bomParts, bomLines, bomDocument, generationContext);

                // Assert
                componentDataModel.Drawings.Count.ShouldBe(drawings.Count);
                DrawingDataModel drawingDataModel = componentDataModel.Drawings.First();
                drawingDataModel.ThumbnailId.ShouldBe(drawingDataModel.ThumbnailId);
            });
        }
    }

    [Fact]
    public void Should_Get_TocEntries()
    {
        // Arrange
        ComponentDataModel grandParent = ComponentDataModelBuilder.Create("grandParent", "grandParent", Guid.NewGuid());
        ComponentDataModel parent = ComponentDataModelBuilder.Create("parent", "parent", Guid.NewGuid());
        ComponentDataModel child = ComponentDataModelBuilder.Create("child", "child", Guid.NewGuid());

        grandParent.Children.Add(parent);
        grandParent.Boms.Add(new ComponentBomDataModel(parent.Code, parent.Label, 1));
        parent.Children.Add(child);
        parent.Boms.Add(new ComponentBomDataModel(child.Code, child.Label, 2));

        // Act
        List<TocEntryDataModel> tocEntries = ComponentDataModelBuilder.BuildTocEntries(grandParent);

        // Assert
        tocEntries.Count.ShouldBe(2);
        TocEntryDataModel tocEntry = tocEntries.First();
        tocEntry.Code.ShouldBe(grandParent.Code);
        tocEntry.Label.ShouldBe(grandParent.Label);
        tocEntry.Depth.ShouldBe(1);
        tocEntry.Anchor.ShouldBe(CoreApplicationConsts.BomPdfDocumentDefaultValues.BomDocumentAnchorPrefix);

        TocEntryDataModel childTocEntry = tocEntries[1];
        childTocEntry.Code.ShouldBe(parent.Code);
        childTocEntry.Label.ShouldBe(parent.Label);
        childTocEntry.Depth.ShouldBe(2);
        childTocEntry.Anchor.ShouldBe(CoreApplicationConsts.BomPdfDocumentDefaultValues.BomDocumentAnchorPrefix);
    }
}