using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using NSubstitute;
using Shouldly;
using SpareParts.Common.DataFilter;
using SpareParts.Core.BomDocuments.DataModels;
using SpareParts.Core.Entities.Boms;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Equipments;
using SpareParts.Core.Exceptions;
using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Volo.Abp.Authorization;
using Volo.Abp.Data;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Core.BomDocuments.QuestPdf;

public abstract class BomDocumentDataModelProviderTests<TStartupModule> : BomDocumentTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    private IAbpAuthorizationService _authorizationService = null!;

    protected override void AfterAddApplication(IServiceCollection services)
    {
        _authorizationService = Substitute.For<IAbpAuthorizationService>();
        _authorizationService.AuthorizeAsync(Arg.Any<ClaimsPrincipal>(), Arg.Any<object?>(), Arg.Any<string>())
            .Returns(_ => AuthorizationResult.Failed());

        services.Replace(ServiceDescriptor.Singleton(_authorizationService));
    }

    [Fact]
    public async Task BuildAsync_Should_Generate_Catalog_For_Equipment()
    {
        Guid tenantId = Guid.NewGuid();
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        using (CurrentTenant.Change(tenantId))
        {
            Equipment equipment = null!;
            Product product = null!;
            Drawing drawingProduct = null!;
            Component assembly1 = null!;
            Component assembly11 = null!;
            Drawing drawingAssembly1 = null!;

            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                DataSeeded dataSeeded = await InsertData();
                equipment = dataSeeded.Equipment;
                product = dataSeeded.Product;
                drawingProduct = dataSeeded.DrawingProduct;
                assembly1 = dataSeeded.Assembly1;
                assembly11 = dataSeeded.Assembly11;
                drawingAssembly1 = dataSeeded.DrawingAssembly1;
            });


            using (DataFilter.Enable<IHasPublic>())
            {
                await WithUnitOfWorkAsync(async () =>
                {
                    // Act
                    BomDocument bomDocument = new(
                        Guid.NewGuid(),
                        Enums.BomDocumentContext.Equipment,
                        equipment.Id,
                        null,
                        "en",
                        Guid.NewGuid());
                    BomDocumentGenerationContext generationContext = new()
                    {
                        TenantName = "Tenant name",
                        OperationId = bomDocument.OperationId,
                        CreatorId = bomDocument.CreatorId,
                        Context = bomDocument.Context,
                        Language = bomDocument.Language
                    };

                    BomDocumentMainPageDataModel bomDocumentMainPageDataModel = await BomDocumentDataModelProvider.BuildAsync(bomDocument, generationContext);

                    // Assert
                    bomDocumentMainPageDataModel.Code.ShouldBe(product.Component.Code);
                    bomDocumentMainPageDataModel.Label.ShouldBe(product.Component.OnlineTranslations.First().Label);
                    bomDocumentMainPageDataModel.Context.ShouldBe(Enums.BomDocumentContext.Equipment);
                    bomDocumentMainPageDataModel.PublicationDate.Day.ShouldBe(DateTime.Now.Day);
                    bomDocumentMainPageDataModel.ThumbnailId.ShouldBe(product.Component.ImageId);

                    bomDocumentMainPageDataModel.MainComponentDataModel.ShouldNotBeNull();
                    ComponentDataModel productDataModel = bomDocumentMainPageDataModel.MainComponentDataModel;
                    productDataModel.Anchor.ShouldBe(CoreApplicationConsts.BomPdfDocumentDefaultValues.BomDocumentAnchorPrefix);
                    productDataModel.Code.ShouldBe(equipment.SerialNumber);
                    productDataModel.Label.ShouldBe(product.Component.OnlineTranslations.First().Label);
                    productDataModel.ThumbnailId.ShouldBe(product.Component.ImageId);

                    bomDocumentMainPageDataModel.MainComponentDataModel.Drawings.Count.ShouldBe(1);
                    DrawingDataModel drawingProductDataModel = bomDocumentMainPageDataModel.MainComponentDataModel.Drawings.First();
                    drawingProductDataModel.ThumbnailId.ShouldBe(drawingProduct.Image.Id);
                    drawingProductDataModel.Boms.Count.ShouldBe(1);
                    DrawingBomDataModel drawingBomProductDataModel = drawingProductDataModel.Boms.First();
                    drawingBomProductDataModel.Index.ShouldBe("P1-A1");
                    drawingBomProductDataModel.Quantity.ShouldBe(1);

                    bomDocumentMainPageDataModel.MainComponentDataModel.Children.Count.ShouldBe(3);
                    ComponentDataModel assembly1DataModel = bomDocumentMainPageDataModel.MainComponentDataModel.Children.First(x => x.Code == assembly1.Code);
                    string anchor = bomDocumentMainPageDataModel.MainComponentDataModel.Children.First() == assembly1DataModel ? "1" : "2";
                    assembly1DataModel.Anchor.ShouldBe(anchor);
                    assembly1DataModel.Children.Count.ShouldBe(3);
                    assembly1DataModel.Code.ShouldBe(assembly1.Code);
                    assembly1DataModel.Label.ShouldBe(assembly1.Translations.First().Label);
                    assembly1DataModel.ThumbnailId.ShouldBe(assembly1.ImageId);
                    assembly1DataModel.Boms.Count.ShouldBe(3);
                    assembly1DataModel.Boms.First().Code.ShouldBe(assembly11.Code);

                    assembly1DataModel.Drawings.Count.ShouldBe(1);
                    DrawingDataModel drawingAssembly1DataModel = assembly1DataModel.Drawings.First();
                    drawingAssembly1DataModel.ThumbnailId.ShouldBe(drawingAssembly1.Image.Id);
                    drawingAssembly1DataModel.Boms.Count.ShouldBe(2);
                    DrawingBomDataModel drawingBomAssembly1DataModel = drawingAssembly1DataModel.Boms.First();
                    drawingBomAssembly1DataModel.Index.ShouldBe("A1-A11");
                    drawingBomAssembly1DataModel.Label.ShouldBe(assembly11.Translations.First().Label);
                    drawingBomAssembly1DataModel.Quantity.ShouldBe(11);

                    bomDocumentMainPageDataModel.MainComponentDataModel.Boms.Count.ShouldBe(3);
                });
            }
        }
    }

    [Fact]
    public async Task BuildAsync_Should_Generate_Catalog_For_Product()
    {
        Guid tenantId = Guid.NewGuid();
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        using (CurrentTenant.Change(tenantId))
        {
            Product product = null!;
            Drawing drawingProduct = null!;
            Component assembly1 = null!;
            Component assembly11 = null!;
            Drawing drawingAssembly1 = null!;
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                DataSeeded dataSeeded = await InsertData();
                product = dataSeeded.Product;
                drawingProduct = dataSeeded.DrawingProduct;
                assembly1 = dataSeeded.Assembly1;
                assembly11 = dataSeeded.Assembly11;
                drawingAssembly1 = dataSeeded.DrawingAssembly1;
            });


            await WithUnitOfWorkAsync(async () =>
            {
                BomDocument bomDocument = new(
                    Guid.NewGuid(),
                    Enums.BomDocumentContext.Product,
                    product.Id,
                    null,
                    "en",
                    Guid.NewGuid());
                BomDocumentGenerationContext generationContext = new()
                {
                    TenantName = "Tenant name",
                    OperationId = bomDocument.OperationId,
                    CreatorId = bomDocument.CreatorId,
                    Context = bomDocument.Context,
                    Language = bomDocument.Language
                };

                // Act
                BomDocumentMainPageDataModel bomDocumentMainPageDataModel = await BomDocumentDataModelProvider.BuildAsync(bomDocument, generationContext);

                // Assert
                bomDocumentMainPageDataModel.Code.ShouldBe(product.Component.Code);
                bomDocumentMainPageDataModel.Label.ShouldBe(product.Component.OnlineTranslations.First().Label);
                bomDocumentMainPageDataModel.Context.ShouldBe(Enums.BomDocumentContext.Product);
                bomDocumentMainPageDataModel.PublicationDate.Day.ShouldBe(DateTime.Now.Day);
                bomDocumentMainPageDataModel.ThumbnailId.ShouldBe(product.Component.ImageId);

                bomDocumentMainPageDataModel.MainComponentDataModel.ShouldNotBeNull();
                ComponentDataModel productDataModel = bomDocumentMainPageDataModel.MainComponentDataModel;
                productDataModel.Anchor.ShouldBe(CoreApplicationConsts.BomPdfDocumentDefaultValues.BomDocumentAnchorPrefix);
                productDataModel.Code.ShouldBe(product.Component.Code);
                productDataModel.Label.ShouldBe(product.Component.OnlineTranslations.First().Label);
                productDataModel.ThumbnailId.ShouldBe(product.Component.ImageId);

                bomDocumentMainPageDataModel.MainComponentDataModel.Drawings.Count.ShouldBe(1);
                DrawingDataModel drawingProductDataModel = bomDocumentMainPageDataModel.MainComponentDataModel.Drawings.First();
                drawingProductDataModel.ThumbnailId.ShouldBe(drawingProduct.Image.Id);
                drawingProductDataModel.Boms.Count.ShouldBe(1);
                DrawingBomDataModel drawingBomProductDataModel = drawingProductDataModel.Boms.First();
                drawingBomProductDataModel.Index.ShouldBe("P1-A1");
                drawingBomProductDataModel.Quantity.ShouldBe(1);

                bomDocumentMainPageDataModel.MainComponentDataModel.Children.Count.ShouldBe(3);
                ComponentDataModel mainComponentDataModel = bomDocumentMainPageDataModel.MainComponentDataModel.Children.First(x => x.Code == assembly1.Code);
                string anchor = bomDocumentMainPageDataModel.MainComponentDataModel.Children.First() == mainComponentDataModel ? "1" : "2";
                mainComponentDataModel.Anchor.ShouldBe(anchor);
                mainComponentDataModel.Children.Count.ShouldBe(3);
                mainComponentDataModel.Code.ShouldBe(assembly1.Code);
                mainComponentDataModel.Label.ShouldBe(assembly1.Translations.First().Label);
                mainComponentDataModel.ThumbnailId.ShouldBe(assembly1.ImageId);
                mainComponentDataModel.Boms.Count.ShouldBe(3);
                mainComponentDataModel.Boms.First().Code.ShouldBe(assembly11.Code);

                mainComponentDataModel.Drawings.Count.ShouldBe(1);
                DrawingDataModel drawingAssembly1DataModel = mainComponentDataModel.Drawings.First();
                drawingAssembly1DataModel.ThumbnailId.ShouldBe(drawingAssembly1.Image.Id);
                drawingAssembly1DataModel.Boms.Count.ShouldBe(2);
                DrawingBomDataModel drawingBomAssembly1DataModel = drawingAssembly1DataModel.Boms.First();
                drawingBomAssembly1DataModel.Index.ShouldBe("A1-A11");
                drawingBomAssembly1DataModel.Label.ShouldBe(assembly11.Translations.First().Label);
                drawingBomAssembly1DataModel.Quantity.ShouldBe(11);

                bomDocumentMainPageDataModel.MainComponentDataModel.Boms.Count.ShouldBe(3);
            });
        }
    }

    [Fact]
    public async Task BuildAsync_Should_Generate_Catalog_For_Assembly_From_Equipment()
    {
        Guid tenantId = Guid.NewGuid();
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        using (CurrentTenant.Change(tenantId))
        {
            Equipment equipment = null!;
            Product product = null!;
            Component assembly1 = null!;
            Component assembly11 = null!;
            Drawing drawingAssembly1 = null!;

            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                DataSeeded dataSeeded = await InsertData();
                equipment = dataSeeded.Equipment;
                product = dataSeeded.Product;
                assembly1 = dataSeeded.Assembly1;
                assembly11 = dataSeeded.Assembly11;
                drawingAssembly1 = dataSeeded.DrawingAssembly1;
            });

            using (DataFilter.Enable<IHasPublic>())
            {
                await WithUnitOfWorkAsync(async () =>
                {
                    // Act
                    BomDocument bomDocument = new(
                        Guid.NewGuid(),
                        Enums.BomDocumentContext.Equipment,
                        equipment.Id,
                        assembly1.Id,
                        "en",
                        Guid.NewGuid());
                    BomDocumentGenerationContext generationContext = new()
                    {
                        TenantName = "Tenant name",
                        OperationId = bomDocument.OperationId,
                        CreatorId = bomDocument.CreatorId,
                        Context = bomDocument.Context,
                        Language = bomDocument.Language
                    };

                    BomDocumentMainPageDataModel bomDocumentMainPageDataModel = await BomDocumentDataModelProvider.BuildAsync(bomDocument, generationContext);

                    // Assert
                    bomDocumentMainPageDataModel.Code.ShouldBe(product.Component.Code);
                    bomDocumentMainPageDataModel.Label.ShouldBe(product.Component.OnlineTranslations.First().Label);
                    bomDocumentMainPageDataModel.Context.ShouldBe(Enums.BomDocumentContext.Equipment);
                    bomDocumentMainPageDataModel.PublicationDate.Day.ShouldBe(DateTime.Now.Day);
                    bomDocumentMainPageDataModel.ThumbnailId.ShouldBe(product.Component.ImageId);

                    bomDocumentMainPageDataModel.MainComponentDataModel.ShouldNotBeNull();
                    ComponentDataModel mainComponentDataModel = bomDocumentMainPageDataModel.MainComponentDataModel;
                    mainComponentDataModel.Anchor.ShouldBe(CoreApplicationConsts.BomPdfDocumentDefaultValues.BomDocumentAnchorPrefix);
                    mainComponentDataModel.Code.ShouldBe(assembly1.Code);
                    mainComponentDataModel.Label.ShouldBe(assembly1.Translations.First().Label);
                    mainComponentDataModel.ThumbnailId.ShouldBe(assembly1.ImageId);

                    mainComponentDataModel.Children.Count.ShouldBe(3);
                    ComponentDataModel assembly11DataModel = mainComponentDataModel.Children.First(x => x.Code == assembly11.Code);
                    assembly11DataModel.Anchor.ShouldBe("1");
                    assembly11DataModel.Children.Count.ShouldBe(2);
                    assembly11DataModel.Code.ShouldBe(assembly11.Code);
                    assembly11DataModel.Drawings.Count.ShouldBe(0);
                    assembly11DataModel.Label.ShouldBe(assembly11.Translations.First().Label);
                    assembly11DataModel.ThumbnailId.ShouldBe(assembly11.ImageId);
                    assembly11DataModel.Boms.Count.ShouldBe(2);

                    mainComponentDataModel.Drawings.Count.ShouldBe(1);
                    DrawingDataModel drawingDataModel = mainComponentDataModel.Drawings.First();
                    drawingDataModel.ThumbnailId.ShouldBe(drawingAssembly1.Image.Id);
                    drawingDataModel.Boms.Count.ShouldBe(2);

                    DrawingBomDataModel drawingBomAssembly1DataModel = drawingDataModel.Boms.First(x => x.Code == assembly11.Code);
                    drawingBomAssembly1DataModel.Index.ShouldBe("A1-A11");
                    drawingBomAssembly1DataModel.Label.ShouldBe(assembly11.Translations.First().Label);
                    drawingBomAssembly1DataModel.Quantity.ShouldBe(11);

                    mainComponentDataModel.Boms.Count.ShouldBe(3);
                });
            }
        }
    }

    [Fact]
    public async Task BuildAsync_Should_Generate_Catalog_For_Assembly_From_Product()
    {
        Guid tenantId = Guid.NewGuid();
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        using (CurrentTenant.Change(tenantId))
        {
            Product product = null!;
            Component assembly1 = null!;
            Component assembly11 = null!;
            Drawing drawingAssembly1 = null!;

            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                DataSeeded dataSeeded = await InsertData();
                product = dataSeeded.Product;
                assembly1 = dataSeeded.Assembly1;
                assembly11 = dataSeeded.Assembly11;
                drawingAssembly1 = dataSeeded.DrawingAssembly1;
            });

            await WithUnitOfWorkAsync(async () =>
            {
                // Act
                BomDocument bomDocument = new(
                    Guid.NewGuid(),
                    Enums.BomDocumentContext.Product,
                    product.Id,
                    assembly1.Id,
                    "en",
                    Guid.NewGuid());
                BomDocumentGenerationContext generationContext = new()
                {
                    TenantName = "Tenant name",
                    OperationId = bomDocument.OperationId,
                    CreatorId = bomDocument.CreatorId,
                    Context = bomDocument.Context,
                    Language = bomDocument.Language
                };

                BomDocumentMainPageDataModel bomDocumentMainPageDataModel = await BomDocumentDataModelProvider.BuildAsync(bomDocument, generationContext);

                // Assert
                bomDocumentMainPageDataModel.Code.ShouldBe(product.Component.Code);
                bomDocumentMainPageDataModel.Label.ShouldBe(product.Component.OnlineTranslations.First().Label);
                bomDocumentMainPageDataModel.Context.ShouldBe(Enums.BomDocumentContext.Product);
                bomDocumentMainPageDataModel.PublicationDate.Day.ShouldBe(DateTime.Now.Day);
                bomDocumentMainPageDataModel.ThumbnailId.ShouldBe(product.Component.ImageId);

                bomDocumentMainPageDataModel.MainComponentDataModel.ShouldNotBeNull();
                ComponentDataModel mainComponentDataModel = bomDocumentMainPageDataModel.MainComponentDataModel;
                mainComponentDataModel.Anchor.ShouldBe(CoreApplicationConsts.BomPdfDocumentDefaultValues.BomDocumentAnchorPrefix);
                mainComponentDataModel.Code.ShouldBe(assembly1.Code);
                mainComponentDataModel.Label.ShouldBe(assembly1.Translations.First().Label);
                mainComponentDataModel.ThumbnailId.ShouldBe(assembly1.ImageId);

                mainComponentDataModel.Children.Count.ShouldBe(3);
                ComponentDataModel assembly11DataModel = mainComponentDataModel.Children.First(x => x.Code == assembly11.Code);
                assembly11DataModel.Anchor.ShouldBe("1");
                assembly11DataModel.Children.Count.ShouldBe(2);
                assembly11DataModel.Code.ShouldBe(assembly11.Code);
                assembly11DataModel.Drawings.Count.ShouldBe(0);
                assembly11DataModel.Label.ShouldBe(assembly11.Translations.First().Label);
                assembly11DataModel.ThumbnailId.ShouldBe(assembly11.ImageId);
                assembly11DataModel.Boms.Count.ShouldBe(2);

                mainComponentDataModel.Drawings.Count.ShouldBe(1);
                DrawingDataModel drawingDataModel = mainComponentDataModel.Drawings.First();
                drawingDataModel.ThumbnailId.ShouldBe(drawingAssembly1.Image.Id);
                drawingDataModel.Boms.Count.ShouldBe(2);

                DrawingBomDataModel drawingBomAssembly1DataModel = drawingDataModel.Boms.First(x => x.Code == assembly11.Code);
                drawingBomAssembly1DataModel.Index.ShouldBe("A1-A11");
                drawingBomAssembly1DataModel.Label.ShouldBe(assembly11.Translations.First().Label);

                mainComponentDataModel.Boms.Count.ShouldBe(3);
            });
        }
    }

    [Fact]
    public async Task BuildAsync_Should_Throw_Exception_If_Equipment_Not_Exist()
    {
        using (CurrentTenant.Change(Guid.NewGuid()))
        {
            BomDocument bomDocument = new(
                Guid.NewGuid(),
                Enums.BomDocumentContext.Equipment,
                Guid.NewGuid(),
                null,
                "en",
                Guid.NewGuid());
            BomDocumentGenerationContext generationContext = new()
            {
                TenantName = "Tenant name",
                OperationId = bomDocument.OperationId,
                CreatorId = bomDocument.CreatorId,
                Context = bomDocument.Context,
                Language = bomDocument.Language
            };

            // Act & Assert
            await WithUnitOfWorkAsync(async () =>
            {
                await BomDocumentDataModelProvider.BuildAsync(bomDocument, generationContext).ShouldThrowAsync<EntityNotFoundException>();
            });
        }
    }

    [Fact]
    public async Task BuildAsync_Should_Throw_Exception_If_Product_Not_Exist()
    {
        using (CurrentTenant.Change(Guid.NewGuid()))
        {
            BomDocument bomDocument = new(
                Guid.NewGuid(),
                Enums.BomDocumentContext.Product,
                Guid.NewGuid(),
                null,
                "en",
                Guid.NewGuid());
            BomDocumentGenerationContext generationContext = new()
            {
                TenantName = "Tenant name",
                OperationId = bomDocument.OperationId,
                CreatorId = bomDocument.CreatorId,
                Context = bomDocument.Context,
                Language = bomDocument.Language
            };

            // Act & Assert
            await WithUnitOfWorkAsync(async () =>
            {
                await BomDocumentDataModelProvider.BuildAsync(bomDocument, generationContext).ShouldThrowAsync<AbpAuthorizationException>();
            });
        }
    }

    [Fact]
    public async Task BuildAsync_Should_Throw_Exception_If_Assembly_Not_Exist()
    {
        Guid tenantId = Guid.NewGuid();
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        using (CurrentTenant.Change(tenantId))
        {
            Product product = null!;

            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                DataSeeded dataSeeded = await InsertData();
                product = dataSeeded.Product;
            });

            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                BomDocument bomDocument = new(
                    Guid.NewGuid(),
                    Enums.BomDocumentContext.Product,
                    product.Id,
                    Guid.NewGuid(),
                    "en",
                    Guid.NewGuid());
                BomDocumentGenerationContext generationContext = new()
                {
                    TenantName = "Tenant name",
                    OperationId = bomDocument.OperationId,
                    CreatorId = bomDocument.CreatorId,
                    Context = bomDocument.Context,
                    Language = bomDocument.Language
                };

                // Act & Assert
                await BomDocumentDataModelProvider.BuildAsync(bomDocument, generationContext).ShouldThrowAsync<UnreferencedBomInProductException>();
            });
        }
    }

    [Fact]
    public async Task BuildAsync_Should_Throw_ArgumentException_Assembly_Not_In_Product()
    {
        Guid tenantId = Guid.NewGuid();
        await DataSeeder.SeedAsync(new DataSeedContext(tenantId));
        using (CurrentTenant.Change(tenantId))
        {
            Product product = null!;
            Component notInProductAssembly = null!;

            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                DataSeeded dataSeeded = await InsertData();
                product = dataSeeded.Product;
                notInProductAssembly = dataSeeded.Assembly3NotInProduct;
            });

            await WithUnitOfWorkAsync(async () =>
            {
                BomDocument bomDocument = new(
                    Guid.NewGuid(),
                    Enums.BomDocumentContext.Product,
                    product.Id,
                    notInProductAssembly.Id,
                    "en",
                    Guid.NewGuid());
                BomDocumentGenerationContext generationContext = new()
                {
                    TenantName = "Tenant name",
                    OperationId = bomDocument.OperationId,
                    CreatorId = bomDocument.CreatorId,
                    Context = bomDocument.Context,
                    Language = bomDocument.Language
                };

                // Act & Assert
                await BomDocumentDataModelProvider.BuildAsync(bomDocument, generationContext).ShouldThrowAsync<UnreferencedBomInProductException>();
            });
        }
    }

}