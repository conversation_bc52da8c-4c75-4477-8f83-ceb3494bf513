<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>SpareParts.Core</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Files\*.*" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Files\*.*" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\SpareParts.Core.Application\SpareParts.Core.Application.csproj" />
    <ProjectReference Include="..\SpareParts.Core.Domain.Tests\SpareParts.Core.Domain.Tests.csproj" />
    <PackageReference Include="Bogus" />
    <PackageReference Include="HarfBuzzSharp.NativeAssets.Linux" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Update="coverlet.collector">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
    </PackageReference>
    <PackageReference Update="Fody">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
    </PackageReference>
  </ItemGroup>

</Project>
