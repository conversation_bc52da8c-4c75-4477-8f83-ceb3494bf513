using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.FileProviders;
using Shouldly;
using SpareParts.Core.DocumentCategories.Dtos;
using SpareParts.Core.DocumentCategories.Queries;
using SpareParts.Core.Entities.Resources;
using Volo.Abp.Content;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Core.DocumentCategories.QueryHandlers;
public abstract class GetDocumentCategoryByIdQueryHandlerTests<TStartupModule> : GetDocumentCategoryQueryTestsBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    [Fact]
    public async Task GetDocumentCategoryByIdQuery_Should_Return_DocumentCategory()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateDocumentCategories = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            Guid documentId = Guid.Empty;
            Guid documentCategoryId = storeContext.DocumentCategories.First();

            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                IFileInfo pdfFile = VirtualFileProvider.GetFileInfo("/Files/21841215.pdf");
                await using Stream pdfStream = pdfFile.CreateReadStream();
                Resource resource = await FileManager.CreateAsync(new RemoteStreamContent(pdfStream, Faker.System.CommonFileName(CoreTestConsts.PdfExtension)));

                DocumentTranslation documentTranslation = new(Faker.Locale, Faker.Lorem.Word() + Guid.NewGuid().ToString("N"), Faker.Lorem.Word());
                Document document = await DocumentDomainService.CreateAsync(resource, [documentTranslation], []);
                await DocumentRepository.InsertAsync(document);
                documentId = document.Id;
            });

            await WithUnitOfWorkAsync(async () =>
                {
                    DocumentInDocumentCategory documentInDocumentCategory = new(documentId, documentCategoryId);
                    await DocumentInDocumentCategoryRepository.InsertAsync(documentInDocumentCategory);
                });

            await WithUnitOfWorkAsync(async () =>
                {
                    DocumentCategory documentCategory = await DocumentCategoryRepository.FirstAsync(x => x.Id == documentCategoryId);

                    // Act
                    DocumentCategoryDto result = await QuerySender.Send(new GetDocumentCategoryByIdQuery(documentCategory.Id));

                    // Assert
                    result.ShouldNotBeNull();
                    result.Color.ShouldBe(documentCategory.Color);
                    result.DocumentCount.ShouldBe(1);
                    result.Translations.Count.ShouldBe(documentCategory.Translations.Count);
                    result.Translations[0].Language.ShouldBe(documentCategory.Translations.First().Language);
                    result.Translations[0].Label.ShouldBe(documentCategory.Translations.First().Label);
                    result.Translations[0].Description.ShouldBe(documentCategory.Translations.First().Description);
                });
        }
    }

    [Fact]
    public async Task GetDocumentCategoryByIdQuery_Should_Throw_Exception_If_Not_Exist()
    {
        Guid tenantId = GuidGenerator.Create();
        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Assert
                await QuerySender.Send(new GetDocumentCategoryByIdQuery(GuidGenerator.Create()))
                    .ShouldThrowAsync<InvalidOperationException>();
            });
        }
    }
}
