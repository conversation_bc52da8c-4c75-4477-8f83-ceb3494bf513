using Microsoft.Extensions.DependencyInjection;
using SpareParts.Core.Entities.Resources;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;

namespace SpareParts.Core.DocumentCategories.QueryHandlers;
public abstract class GetDocumentCategoryQueryTestsBase<TStartupModule> : DocumentCategoryTestsBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    protected readonly IRepository<DocumentInDocumentCategory> DocumentInDocumentCategoryRepository;

    protected GetDocumentCategoryQueryTestsBase()
    {
        DocumentInDocumentCategoryRepository = ServiceProvider.GetRequiredService<IRepository<DocumentInDocumentCategory>>();
    }
}