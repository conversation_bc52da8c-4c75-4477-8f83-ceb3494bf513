using System;
using System.Linq;
using System.Threading.Tasks;
using Shouldly;
using SpareParts.Core.DocumentCategories.Commands;
using SpareParts.Core.Entities.Resources;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Core.DocumentCategories.CommandHandlers;
public abstract class DeleteDocumentCategoryCommandHandlerTests<TStartupModule> : DocumentCategoryTestsBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    [Fact]
    public async Task DeleteDocumentCategoryCommandHandler_Should_Delete_Document_Category()
    {
        TestDataContext context = new(GuidGenerator.Create()) { GenerateDocumentCategories = true };
        CoreTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(context);
        using (CurrentTenant.Change(context.TenantId))
        {
            // Arrange
            Guid id = storeContext.DocumentCategories.First();
            await WithUnitOfWorkAsync(async () =>
            {
                // Act
                await CommandSender.Send(new DeleteDocumentCategoryCommand(id));
            });

            // Assert
            await WithUnitOfWorkAsync(async () =>
            {
                DocumentCategory? documentCategoryResult = await DocumentCategoryRepository.FindAsync(id);
                documentCategoryResult.ShouldBeNull();
            });
        }
    }
}
