using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Bogus.Extensions;
using Shouldly;
using SpareParts.Common.Dtos;
using SpareParts.Common.Exceptions;
using SpareParts.Core.DocumentCategories.Commands;
using SpareParts.Core.DocumentCategories.Dtos;
using SpareParts.Core.DocumentCategories.Dtos.Inputs;
using Volo.Abp.Localization;
using Volo.Abp.Modularity;
using Volo.Abp.Validation;
using Xunit;

namespace SpareParts.Core.DocumentCategories.CommandHandlers;
public abstract class CreateDocumentCategoryCommandHandlerTests<TStartupModule> : DocumentCategoryTestsBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    [Fact]
    public async Task CreateDocumentCategoryCommandHandler_Should_Throw_Exception_If_No_Translation()
    {
        using (CurrentTenant.Change(GuidGenerator.Create()))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                const string color = "#FFFFFF";
                List<CommonTranslationDto> translations = [];
                CreateDocumentCategoryCommand createDocumentCategoryCommand = new(color, translations);

                // Assert
                await CommandSender.Send(createDocumentCategoryCommand).ShouldThrowAsync<ArgumentException>();
            });
        }
    }

    [Fact]
    public async Task CreateDocumentCategoryCommandHandler_Without_Default_Language_Lang_Should_Throw_Exception()
    {
        using (CultureHelper.Use("en"))
        {
            using (CurrentTenant.Change(GuidGenerator.Create()))
            {
                await WithUnitOfWorkAsync(async () =>
                {
                    // Arrange
                    const string color = "#FFFFFF";
                    List<CommonTranslationDto> translations = [new CommonTranslationDto("de", Faker.Lorem.Word().ClampLength(2), Faker.Lorem.Word())];
                    CreateDocumentCategoryCommand createDocumentCategoryCommand = new(color, translations);

                    // Assert
                    await CommandSender.Send(createDocumentCategoryCommand).ShouldThrowAsync<MissingDefaultLanguageException>();
                });
            }
        }
    }

    [Fact]
    public async Task CreateDocumentCategoryCommandHandler_Should_Throw_Exception_If_Color_Is_Invalid()
    {
        using (CurrentTenant.Change(GuidGenerator.Create()))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                const string color = "Invalid";
                List<CommonTranslationDto> translations = [
                            new(Faker.Locale, Faker.Lorem.Word().ClampLength(2), Faker.Lorem.Word()) ];
                CreateDocumentCategoryCommand createDocumentCategoryCommand = new(color, translations);

                // Assert
                await CommandSender.Send(createDocumentCategoryCommand).ShouldThrowAsync<AbpValidationException>();
            });
        }
    }

    [Fact]
    public async Task CreateDocumentCategoryCommandHandler_Should_Create_A_Document_Category()
    {
        using (CurrentTenant.Change(GuidGenerator.Create()))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                const string color = "#FFFFFF";

                CreateDocumentCategoryDto createDocumentCategoryDto = new(color,
                [
                    new CommonTranslationDto(Faker.Locale, Faker.Lorem.Word().ClampLength(2), Faker.Lorem.Word()),
                    new CommonTranslationDto("de", Faker.Lorem.Word().ClampLength(2), Faker.Lorem.Word()),
                    new CommonTranslationDto("fr", Faker.Lorem.Word().ClampLength(2), Faker.Lorem.Word())
                ]);

                CreateDocumentCategoryCommand createDocumentCategoryCommand = ObjectMapper.Map<CreateDocumentCategoryDto, CreateDocumentCategoryCommand>(createDocumentCategoryDto);

                // Act
                DocumentCategoryDto documentCategory = await CommandSender.Send(createDocumentCategoryCommand);

                // Assert
                documentCategory.Id.ShouldNotBe(Guid.Empty);
                documentCategory.Color.ShouldBe(color);
                documentCategory.Translations.Count.ShouldBe(createDocumentCategoryCommand.Translations.Count);
                CommonTranslationDto? documentCategoryTranslationDto = documentCategory.Translations.Find(x => x.Language == createDocumentCategoryCommand.Translations[0].Language);
                documentCategoryTranslationDto.ShouldNotBeNull();
                documentCategoryTranslationDto.Label.ShouldBe(createDocumentCategoryCommand.Translations[0].Label);
                documentCategoryTranslationDto.Description.ShouldBe(createDocumentCategoryCommand.Translations[0].Description);
                documentCategoryTranslationDto.Language.ShouldBe(createDocumentCategoryCommand.Translations[0].Language);
            });
        }
    }
}