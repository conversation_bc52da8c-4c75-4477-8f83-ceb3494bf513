using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.Auditing;
using Volo.Abp.BlobStoring;
using Volo.Abp.BlobStoring.Azure;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore.SqlServer;
using Volo.Abp.Modularity;
using Volo.Abp.SettingManagement;
using Volo.Abp.VirtualFileSystem;

namespace SpareParts.Common.EntityFrameworkCore;

[DependsOn(
    typeof(CommonApplicationTestModule),
    typeof(CommonEntityFrameworkCoreModule),
    typeof(AbpEntityFrameworkCoreSqlServerModule),
    typeof(AbpBlobStoringAzureModule)
)]
public class CommonEntityFrameworkCoreTestModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        Configure<SettingManagementOptions>(options =>
        {
            options.IsDynamicSettingStoreEnabled = false;
            options.SaveStaticSettingsToDatabase = false;
        });
        Configure<AbpAuditingOptions>(opts =>
        {
            opts.IsEnabled = false;
        });

        Configure<AbpBlobStoringOptions>(options =>
        {
            options.Containers.ConfigureDefault(container =>
            {
                container.UseAzure(azure =>
                {
                    azure.ConnectionString = "DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://127.0.0.1:10000/devstoreaccount1;";
                    azure.CreateContainerIfNotExists = true;
                });
            });
        });

        ConfigureMsSqlDatabase(context.Services);

        Configure<AbpVirtualFileSystemOptions>(options =>
        {
            options.FileSets.AddEmbedded<CommonEntityFrameworkCoreTestModule>("SpareParts.Common");
        });
    }

    private static void ConfigureMsSqlDatabase(IServiceCollection services)
    {
        string connectionString = DataBaseFixture.ConnectionString;
        using (CommonDbContext context = new(new DbContextOptionsBuilder<CommonDbContext>()
                   .UseSqlServer(connectionString)
                   .Options))
        {
            IRelationalDatabaseCreator relationalDatabaseCreator = context.GetService<IRelationalDatabaseCreator>();
            relationalDatabaseCreator.EnsureCreated();
        }

        services.Configure<AbpDbContextOptions>(options =>
        {
            options.Configure(context =>
            {
                context.DbContextOptions.UseSqlServer(connectionString);
            });
        });
    }
}
