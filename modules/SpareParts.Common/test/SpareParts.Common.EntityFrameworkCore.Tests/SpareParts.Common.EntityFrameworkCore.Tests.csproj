<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>SpareParts.Common</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <EmbeddedResource Include="Files\*.*" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Files\*.*" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Proxies" />
    <ProjectReference Include="..\..\src\SpareParts.Common.EntityFrameworkCore\SpareParts.Common.EntityFrameworkCore.csproj" />
    <ProjectReference Include="..\SpareParts.Common.Application.Tests\SpareParts.Common.Application.Tests.csproj" />
    <PackageReference Include="Volo.Abp.EntityFrameworkCore.SqlServer" />
    <PackageReference Include="Testcontainers.MsSql" />
    <PackageReference Include="Volo.Abp.BlobStoring.Azure" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Update="coverlet.collector">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
    </PackageReference>
    <PackageReference Update="Fody">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
    </PackageReference>
  </ItemGroup>

</Project>
