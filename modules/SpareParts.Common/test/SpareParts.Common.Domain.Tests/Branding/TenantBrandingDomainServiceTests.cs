using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Shouldly;
using SpareParts.Common.Resources;
using System;
using System.IO;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Content;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;
using Volo.Abp.VirtualFileSystem;
using Xunit;

namespace SpareParts.Common.Branding;
public abstract class TenantBrandingDomainServiceTests<TStartupModule> : CommonDomainTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    private readonly TenantBrandingDomainService _tenantBrandingDomainService;
    private readonly IRepository<TenantBranding, Guid> _tenantBrandingRepository;
    private readonly IRepository<PublicResource, Guid> _publicResourceRepository;
    private readonly IVirtualFileProvider _virtualFileProvider;
    private readonly PublicImageManager _publicImageManager;

    protected TenantBrandingDomainServiceTests()
    {
        _tenantBrandingDomainService = ServiceProvider.GetRequiredService<TenantBrandingDomainService>();
        _tenantBrandingRepository = ServiceProvider.GetRequiredService<IRepository<TenantBranding, Guid>>();
        _virtualFileProvider = ServiceProvider.GetRequiredService<IVirtualFileProvider>();
        _publicResourceRepository = ServiceProvider.GetRequiredService<IRepository<PublicResource, Guid>>();
        _publicImageManager = ServiceProvider.GetRequiredService<PublicImageManager>();
    }

    [Fact]
    public async Task CreateAsync_Should_Create_Branding_With_Correct_Values()
    {
        Guid tenantId = Guid.NewGuid();
        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid imageId1 = await GeneratePublicResource();
                Guid imageId2 = await GeneratePublicResource();
                Guid imageId3 = await GeneratePublicResource();

                // Act
                TenantBranding branding = await _tenantBrandingDomainService.CreateAsync(
                Guid.NewGuid(),
                CommonConsts.Branding.DefaultApplicationName,
                CommonConsts.Branding.DefaultPrimaryColor, CommonConsts.Branding.DefaultAccentColor,
                imageId1, imageId2, imageId3);

                // Assert
                branding.ShouldNotBeNull();
                branding.Id.ShouldNotBe(Guid.Empty);
                branding.TenantId.ShouldBe(tenantId);

                branding.ApplicationName.Default.ShouldBe(CommonConsts.Branding.DefaultApplicationName);
                branding.ApplicationName.Override.ShouldBeNull();

                branding.PrimaryColor.Default.ShouldBe(CommonConsts.Branding.DefaultPrimaryColor);
                branding.PrimaryColor.Override.ShouldBeNull();

                branding.AccentColor.Default.ShouldBe(CommonConsts.Branding.DefaultAccentColor);
                branding.AccentColor.Override.ShouldBeNull();

                branding.Logo.Default.ShouldBe(imageId1);
                branding.Logo.Override.ShouldBeNull();

                branding.SmallLogo.Default.ShouldBe(imageId2);
                branding.SmallLogo.Override.ShouldBeNull();

                branding.Favicon.Default.ShouldBe(imageId3);
                branding.Favicon.Override.ShouldBeNull();

                branding.WelcomePageImageId.ShouldBeNull();
                branding.LoginPageImageId.ShouldBeNull();
                branding.WelcomePageTranslations.ShouldBeNull();
                branding.LoginPageTranslations.ShouldBeNull();
                branding.WelcomePageHideTenantName.ShouldBeFalse();
                branding.LoginPageHideTenantName.ShouldBeFalse();
            });
        }
    }

    [Fact]
    public async Task CreateAsync_Should_Throw_Exception_If_Branding_Already_Exist_For_Current_Tenant()
    {
        using (CurrentTenant.Change(Guid.NewGuid()))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                TenantBranding branding = await CreateBrandingAsync();
                await _tenantBrandingRepository.InsertAsync(branding);
            });

            await WithUnitOfWorkAsync(async () =>
            {
                // Act & Assert
                await _tenantBrandingDomainService.CreateAsync(Guid.NewGuid(), "", "", "", Guid.NewGuid(), Guid.NewGuid(), Guid.NewGuid()).ShouldThrowAsync<BusinessException>();
            });
        }
    }

    [Fact]
    public async Task ChangeLoginPageImageAsync_Should_Throw_Exception_When_Image_Does_Not_Exist()
    {
        using (CurrentTenant.Change(Guid.NewGuid()))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                TenantBranding branding = await CreateBrandingAsync();

                // Act
                await _tenantBrandingDomainService.ChangeLoginPageImageAsync(branding, Guid.NewGuid()).ShouldThrowAsync<EntityNotFoundException>();
            });
        }
    }

    [Fact]
    public async Task ChangeWelcomePageImageAsync_Should_Throw_Exception_When_Image_Does_Not_Exist()
    {
        using (CurrentTenant.Change(Guid.NewGuid()))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                TenantBranding branding = await CreateBrandingAsync();

                // Act
                await _tenantBrandingDomainService.ChangeWelcomePageImageAsync(branding, Guid.NewGuid()).ShouldThrowAsync<EntityNotFoundException>();
            });
        }
    }

    private async Task<TenantBranding> CreateBrandingAsync()
    {
        Guid imageId = await GeneratePublicResource();
        TenantBranding branding = await _tenantBrandingDomainService.CreateAsync(
           Guid.NewGuid(),
           CommonConsts.Branding.DefaultApplicationName,
           CommonConsts.Branding.DefaultPrimaryColor, CommonConsts.Branding.DefaultAccentColor,
          imageId, imageId, imageId);
        return branding;
    }

    private async Task<Guid> GeneratePublicResource()
    {
        string filename = Faker.System.CommonFileName(CommonTestConsts.ImageExtension);
        IFileInfo imageFile = _virtualFileProvider.GetFileInfo("/Files/default-image.png");
        await using Stream imageStream = imageFile.CreateReadStream();
        PublicResource publicResource = await _publicImageManager.CreateAsync(new RemoteStreamContent(imageStream, filename));
        await _publicResourceRepository.InsertAsync(publicResource, true);
        return publicResource.Id;
    }
}
