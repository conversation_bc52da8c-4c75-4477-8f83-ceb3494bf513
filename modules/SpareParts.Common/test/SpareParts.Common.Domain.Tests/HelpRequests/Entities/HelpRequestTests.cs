using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Shouldly;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Common.HelpRequests.Entities;

public abstract class HelpRequestTests<TStartupModule> : CommonDomainTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    private readonly HelpRequestDomainService _helpRequestDomainService;

    protected HelpRequestTests()
    {
        _helpRequestDomainService = ServiceProvider.GetRequiredService<HelpRequestDomainService>();
    }

    [Fact]
    public async Task Instantiation_Should_Succeed()
    {
        // Arrange
        Guid tenantId = GuidGenerator.Create();
        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                string code = "code";
                string title = "title";
                string countryCode = "FR";
                ContextType contextType = ContextType.ProductImport;
                Guid companyId = Guid.NewGuid();

                // Act 
                HelpRequest helpRequest = await _helpRequestDomainService.CreateAsync(code, title, countryCode, contextType, companyId);

                //Assert
                helpRequest.Code.ShouldBe(code);
                helpRequest.Title.ShouldBe(title);
                helpRequest.CountryCode.ShouldBe(countryCode);
                helpRequest.Context.ShouldBe(contextType);
                helpRequest.CompanyId.ShouldBe(companyId);
            });
        }
    }

    [Fact]
    public async Task Instantiation_Should_Throw_Exception_If_Incorrect_Values()
    {
        // Arrange
        Guid tenantId = GuidGenerator.Create();
        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                string code = "code";
                string title = "title";
                string countryCode = "FR";
                ContextType contextType = ContextType.ProductImport;
                Guid companyId = Guid.NewGuid();

                // Act & Assert
                await Should.ThrowAsync<ArgumentException>(async () => await _helpRequestDomainService.CreateAsync(string.Empty, title, countryCode, contextType, companyId));
                await Should.ThrowAsync<ArgumentException>(async () => await _helpRequestDomainService.CreateAsync(
                    new string('x', CommonDbProperties.HelpRequestTableProperties.CodeMaxLength + 1), title, countryCode, contextType, companyId));

                await Should.ThrowAsync<ArgumentException>(async () => await _helpRequestDomainService.CreateAsync(code, string.Empty, countryCode, contextType, companyId));
                await Should.ThrowAsync<ArgumentException>(async () => await _helpRequestDomainService.CreateAsync(code,
                    new string('x', CommonDbProperties.HelpRequestTableProperties.TitleMaxLength + 1), countryCode, contextType, companyId));

                await Should.ThrowAsync<ArgumentException>(async () => await _helpRequestDomainService.CreateAsync(code, title, string.Empty, contextType, companyId));
                await Should.ThrowAsync<ArgumentException>(async () => await _helpRequestDomainService.CreateAsync(code, title, "x", contextType, companyId));
                await Should.ThrowAsync<ArgumentException>(async () => await _helpRequestDomainService.CreateAsync(code, title,
                    new string('x', CommonDbProperties.HelpRequestTableProperties.CountryCodeLength + 1), contextType, companyId));

                await Should.ThrowAsync<ArgumentException>(async () => await _helpRequestDomainService.CreateAsync(code, title, countryCode, contextType, Guid.Empty));
            });
        }
    }
}