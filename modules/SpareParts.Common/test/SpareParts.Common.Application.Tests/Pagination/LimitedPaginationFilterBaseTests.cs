using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using NSubstitute;
using Shouldly;
using SpareParts.Common.Localization;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Common.Pagination;

public abstract class LimitedPaginationFilterBaseTests<TStartupModule> : CommonApplicationTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    [Fact]
    public void Validate_ReturnsNoErrors_WhenPerPageIsLessThanOrEqualToMaxPerPage()
    {
        // Arrange
        LimitedPaginationFilterBase filter = new();
        IServiceCollection services = new ServiceCollection();
        IStringLocalizer<CommonResource> localizer = Substitute.For<IStringLocalizer<CommonResource>>();
        services.Add(ServiceDescriptor.Singleton(localizer));
        ServiceProvider serviceProvider = services.BuildServiceProvider();

        // Act
        IEnumerable<ValidationResult> results = filter.Validate(new ValidationContext(filter, serviceProvider, null)).ToList();

        // Assert
        results.ShouldBeEmpty();
    }

    [Fact]
    public void Validate_ReturnsError_When_PerPage_Is_Greater_Than_MaxPerPage()
    {
        // Arrange
        LimitedPaginationFilterBase filter = new() { PerPage = LimitedPaginationFilterBase.MaxPerPage + 1 };
        IServiceCollection services = new ServiceCollection();
        IStringLocalizer<CommonResource> localizer = Substitute.For<IStringLocalizer<CommonResource>>();
        const string errorMessage = "translated";
        localizer[Arg.Any<string>(), Arg.Any<object[]>()]
            .Returns(_ => new LocalizedString("name", errorMessage));
        services.Add(ServiceDescriptor.Singleton(localizer));
        ServiceProvider serviceProvider = services.BuildServiceProvider();

        // Act
        IEnumerable<ValidationResult> results = filter.Validate(new ValidationContext(filter, serviceProvider, null)).ToList();

        // Assert
        results.Count().ShouldBe(1);
        results.First().ErrorMessage.ShouldBe(errorMessage);
    }

    [Fact]
    public void Validate_ReturnsError_When_PerPage_Is_Lower_Than_MinPerPage()
    {
        // Arrange
        LimitedPaginationFilterBase filter = new() { PerPage = LimitedPaginationFilterBase.MinPerPage - 1 };
        IServiceCollection services = new ServiceCollection();
        IStringLocalizer<CommonResource> localizer = Substitute.For<IStringLocalizer<CommonResource>>();
        const string errorMessage = "translated";
        localizer[Arg.Any<string>(), Arg.Any<object[]>()]
            .Returns(_ => new LocalizedString("name", errorMessage));
        services.Add(ServiceDescriptor.Singleton(localizer));
        ServiceProvider serviceProvider = services.BuildServiceProvider();

        // Act
        IEnumerable<ValidationResult> results = filter.Validate(new ValidationContext(filter, serviceProvider, null)).ToList();

        // Assert
        results.Count().ShouldBe(1);
        results.First().ErrorMessage.ShouldBe(errorMessage);
    }

    [Fact]
    public void Validate_ReturnsError_When_PerPage_Is_Lower_Than_MinPage()
    {
        // Arrange
        LimitedPaginationFilterBase filter = new() { PerPage = LimitedPaginationFilterBase.MinPage - 1 };
        IServiceCollection services = new ServiceCollection();
        IStringLocalizer<CommonResource> localizer = Substitute.For<IStringLocalizer<CommonResource>>();
        const string errorMessage = "translated";
        localizer[Arg.Any<string>(), Arg.Any<object[]>()]
            .Returns(_ => new LocalizedString("name", errorMessage));
        services.Add(ServiceDescriptor.Singleton(localizer));
        ServiceProvider serviceProvider = services.BuildServiceProvider();

        // Act
        IEnumerable<ValidationResult> results = filter.Validate(new ValidationContext(filter, serviceProvider, null)).ToList();

        // Assert
        results.Count().ShouldBe(1);
        results.First().ErrorMessage.ShouldBe(errorMessage);
    }
}