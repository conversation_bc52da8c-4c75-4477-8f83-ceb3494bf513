using Microsoft.Extensions.DependencyInjection;
using Shouldly;
using SpareParts.Common.Dtos;
using System;
using System.Security.Claims;
using Volo.Abp.Authorization;
using Volo.Abp.Modularity;
using Volo.Abp.Security.Claims;
using Xunit;

namespace SpareParts.Common.Company;

public abstract class CurrentCompanyTests<TStartupModule> : CommonApplicationTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    private readonly ICurrentPrincipalAccessor _currentPrincipalAccessor;
    private readonly ICurrentCompany _currentCompany;

    protected CurrentCompanyTests()
    {
        _currentPrincipalAccessor = ServiceProvider.GetRequiredService<ICurrentPrincipalAccessor>();
        _currentCompany = ServiceProvider.GetRequiredService<ICurrentCompany>();
    }

    [Fact]
    public void ApplicationConfigurationAppService_Should_Return_CurrentCompanyDto()
    {
        //Arrange
        Guid companyId = Guid.NewGuid();
        string companyType = Guid.NewGuid().ToString();
        string companyCode = Guid.NewGuid().ToString();
        string companyName = Guid.NewGuid().ToString();
        string companyLegalName = Guid.NewGuid().ToString();
        bool companyIsDefault = true;

        using (_currentPrincipalAccessor.Change([
                new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                        new Claim(CommonClaimTypes.CompanyType, companyType),
                        new Claim(CommonClaimTypes.CompanyCode, companyCode),
                        new Claim(CommonClaimTypes.CompanyName, companyName),
                        new Claim(CommonClaimTypes.CompanyLegalName, companyLegalName),
                        new Claim(CommonClaimTypes.IsDefault, companyIsDefault.ToString())
                   ]))
        {
            //Act
            CurrentCompanyDto currentCompanyDto = _currentCompany.GetCurrentCompanyDto();

            //Assert
            currentCompanyDto.Id.ShouldBe(companyId);
            currentCompanyDto.Code.ShouldBe(companyCode);
            currentCompanyDto.Name.ShouldBe(companyName);
            currentCompanyDto.LegalName.ShouldBe(companyLegalName);
            currentCompanyDto.Type.ShouldBe(companyType);
            currentCompanyDto.IsDefault.ShouldBe(companyIsDefault);
        }
    }

    [Fact]
    public void ApplicationConfigurationAppService_Should_Return_CompanyId_Null_If_Not_Provided()
    {
        //Arrange
        string companyId = "";
        string companyType = Guid.NewGuid().ToString();
        string companyCode = Guid.NewGuid().ToString();
        string companyName = Guid.NewGuid().ToString();
        string companyLegalName = Guid.NewGuid().ToString();
        bool companyIsDefault = true;

        using (_currentPrincipalAccessor.Change([
                new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                        new Claim(CommonClaimTypes.CompanyType, companyType),
                        new Claim(CommonClaimTypes.CompanyCode, companyCode),
                        new Claim(CommonClaimTypes.CompanyName, companyName),
                        new Claim(CommonClaimTypes.CompanyLegalName, companyLegalName),
                        new Claim(CommonClaimTypes.IsDefault, companyIsDefault.ToString())
                   ]))
        {
            //Act
            CurrentCompanyDto currentCompanyDto = _currentCompany.GetCurrentCompanyDto();

            //Assert
            currentCompanyDto.Id.ShouldBeNull();
        }
    }

    [Fact]
    public void ApplicationConfigurationAppService_Should_Return_CompanyId_Null_If_Not_Guid()
    {
        //Arrange
        string companyId = "incorrectId";
        string companyType = Guid.NewGuid().ToString();
        string companyCode = Guid.NewGuid().ToString();
        string companyName = Guid.NewGuid().ToString();
        string companyLegalName = Guid.NewGuid().ToString();
        bool companyIsDefault = true;

        using (_currentPrincipalAccessor.Change([
                new Claim(CommonClaimTypes.CompanyId, companyId.ToString()),
                        new Claim(CommonClaimTypes.CompanyType, companyType),
                        new Claim(CommonClaimTypes.CompanyCode, companyCode),
                        new Claim(CommonClaimTypes.CompanyName, companyName),
                        new Claim(CommonClaimTypes.CompanyLegalName, companyLegalName),
                        new Claim(CommonClaimTypes.IsDefault, companyIsDefault.ToString())
                   ]))
        {
            //Act
            CurrentCompanyDto currentCompanyDto = _currentCompany.GetCurrentCompanyDto();

            //Assert
            currentCompanyDto.Id.ShouldBeNull();
        }
    }

    [Fact]
    public void CurrentCompany_Should_Throw_If_External()
    {
        using (_currentPrincipalAccessor.Change([new Claim(CommonClaimTypes.CompanyType, "External")]))
        {
            Action action = () => { _currentCompany.ThrowIfExternal(); };
            action.ShouldThrow<AbpAuthorizationException>();
        }
    }

    [Fact]
    public void CurrentCompany_Should_Not_Throw_If_Internal()
    {
        using (_currentPrincipalAccessor.Change([new Claim(CommonClaimTypes.CompanyType, "Internal")]))
        {
            Action action = () => { _currentCompany.ThrowIfExternal(); };
            action.ShouldNotThrow();
        }
    }
}