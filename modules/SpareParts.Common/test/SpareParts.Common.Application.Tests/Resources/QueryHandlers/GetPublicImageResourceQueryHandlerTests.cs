using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Extensions.FileProviders;
using Shouldly;
using SpareParts.Common.PublicResources.Queries;
using Volo.Abp.Content;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Common.Resources.QueryHandlers;
public abstract class GetPublicImageResourceQueryHandlerTests<TStartupModule> : CommonApplicationTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    [Fact]
    public async Task GetPublicImageResource_Should_Throw_Exception_If_Id_Is_Not_Known()
    {
        using (CurrentTenant.Change(GuidGenerator.Create()))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                GetPublicImageResourceQuery getPublicImageResourceQuery = new(GuidGenerator.Create());

                // Act
                await QuerySender.Send(getPublicImageResourceQuery).ShouldThrowAsync<EntityNotFoundException>();
            });
        }
    }

    [Fact]
    public async Task GetPublicImageResource_Should_Get_Resource()
    {
        using (CurrentTenant.Change(GuidGenerator.Create()))
        {
            Guid imageId = Guid.Empty;
            string filename = string.Empty;
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                IFileInfo imageFile = VirtualFileProvider.GetFileInfo("/Files/default-image.png");
                await using Stream imageStream = imageFile.CreateReadStream();
                filename = Faker.System.CommonFileName(CommonTestConsts.ImageExtension);
                PublicResource image = await PublicImageManager.CreateAsync(new RemoteStreamContent(imageStream, filename));
                await PublicResourceRepository.InsertAsync(image);
                imageId = image.Id;
            });

            await WithUnitOfWorkAsync(async () =>
            {
                GetPublicImageResourceQuery getPublicImageResourceQuery = new(imageId);

                // Act
                RemoteStreamContent blobDto = await QuerySender.Send(getPublicImageResourceQuery);

                // Assert
                blobDto.ShouldNotBeNull();
                blobDto.FileName.ShouldBe(filename);
                blobDto.GetStream().ShouldNotBeNull();
            });
        }
    }
}