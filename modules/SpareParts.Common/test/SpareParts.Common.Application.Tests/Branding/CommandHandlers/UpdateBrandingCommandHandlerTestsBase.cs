using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.Modularity;
using Volo.Abp.SettingManagement;

namespace SpareParts.Common.Branding.CommandHandlers;

public abstract class UpdateBrandingCommandHandlerTestsBase<TStartupModule> : BrandingApplicationTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    protected readonly ISettingManager SettingManager;

    protected UpdateBrandingCommandHandlerTestsBase()
    {
        SettingManager = ServiceProvider.GetRequiredService<ISettingManager>();
    }
}