using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.FileProviders;
using NSubstitute;
using Shouldly;
using SpareParts.Common.Generated.PublicIdentity;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Volo.Abp.Modularity;
using Volo.Abp.Settings;
using Volo.Abp.TenantManagement;
using Xunit;

namespace SpareParts.Common.Branding.Providers;

public abstract class BrandingProviderTests<TStartupModule> : BrandingApplicationTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    private TenantBrandingColorsDto? _tenantBrandingColorsDto;
    private readonly IBrandingProvider _brandingProvider;
    private Stream? _logo;
    private Stream? _smallLogo;
    private Stream? _favicon;

    protected BrandingProviderTests()
    {
        _brandingProvider = ServiceProvider.GetRequiredService<IBrandingProvider>();
        _tenantBrandingColorsDto = new TenantBrandingColorsDto
        {
            PrimaryColor = "#aaaaaa",
            SecondaryColor = "#bbbbbb"
        };
    }

    protected override void AfterAddApplication(IServiceCollection services)
    {
        IIdentity identityApiClient = Substitute.For<IIdentity>();
        IConfiguration configuration = Substitute.For<IConfiguration>();
        configuration["Identity:Api:BaseAddress"].Returns("https://api.example.com");
        identityApiClient.GetBrandingColorsAsync(Arg.Any<string>())
            .Returns(_ => _tenantBrandingColorsDto!);
        identityApiClient.DownloadTenantLogoAsync(Arg.Any<string>())
            .Returns(_ => _logo!);
        identityApiClient.DownloadTenantSmallLogoAsync(Arg.Any<string>())
            .Returns(_ => _smallLogo!);
        identityApiClient.DownloadTenantFaviconAsync(Arg.Any<string>())
            .Returns(_ => _favicon!);
        services.Replace(ServiceDescriptor.Singleton(identityApiClient));
        services.Replace(ServiceDescriptor.Singleton(configuration));

        ISettingProvider? settingProvider = Substitute.For<ISettingProvider>();
        settingProvider.GetOrNullAsync(Arg.Any<string>())
            .Returns((string?)null);
        services.Replace(ServiceDescriptor.Singleton(settingProvider));
    }

    [Fact]
    public async Task CreateFromIdentityAsync_Should_Create_Branding_With_Correct_Default_Values()
    {
        Tenant tenant = await CreateTenantAsync(Guid.NewGuid().ToString("N"), Guid.NewGuid().ToString("N"));
        using (CurrentTenant.Change(tenant.Id))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                IFileInfo imageFile = VirtualFileProvider.GetFileInfo("/Files/default-image.png");
                _logo = imageFile.CreateReadStream();
                _smallLogo = imageFile.CreateReadStream();
                _favicon = imageFile.CreateReadStream();

                // Act
                TenantBranding branding = await _brandingProvider.CreateBrandingFromIdentityAsync(tenant);

                // Assert
                branding.ShouldNotBeNull();
                branding.Id.ShouldNotBe(Guid.Empty);
                branding.TenantId.ShouldBe(tenant.Id);

                branding.ApplicationName.Default.ShouldBe(CommonConsts.Branding.DefaultApplicationName);
                branding.ApplicationName.Override.ShouldBeNull();

                branding.PrimaryColor.Default.ShouldBe("#aaaaaa");
                branding.PrimaryColor.Override.ShouldBeNull();

                branding.AccentColor.Default.ShouldBe("#bbbbbb");
                branding.AccentColor.Override.ShouldBeNull();

                branding.Logo.Default.ShouldNotBeNull();
                branding.Logo.Override.ShouldBeNull();

                branding.SmallLogo.Default.ShouldNotBeNull();
                branding.SmallLogo.Override.ShouldBeNull();

                branding.Favicon.Default.ShouldNotBeNull();
                branding.Favicon.Override.ShouldBeNull();

                branding.WelcomePageImageId.ShouldBeNull();
                branding.LoginPageImageId.ShouldBeNull();
                branding.WelcomePageTranslations.ShouldBeNull();
                branding.LoginPageTranslations.ShouldBeNull();
                branding.WelcomePageHideTenantName.ShouldBeFalse();
                branding.LoginPageHideTenantName.ShouldBeFalse();
            });
        }
    }

    [Fact]
    public async Task UpdateBrandingFromIdentityAsync_Should_Update_Branding_With_Correct_Default_Values()
    {
        Tenant tenant = await CreateTenantAsync(Guid.NewGuid().ToString("N"), Guid.NewGuid().ToString("N"));
        using (CurrentTenant.Change(tenant.Id))
        {
            TenantBranding? oldBranding = null;

            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                _tenantBrandingColorsDto = new TenantBrandingColorsDto
                {
                    PrimaryColor = "#cccccc",
                    SecondaryColor = "#dddddd"
                };

                IFileInfo imageFile = VirtualFileProvider.GetFileInfo("/Files/default-image.png");
                _logo = imageFile.CreateReadStream();
                _smallLogo = imageFile.CreateReadStream();
                _favicon = imageFile.CreateReadStream();

                oldBranding = await _brandingProvider.CreateBrandingFromIdentityAsync(tenant);
                await _tenantBrandingRepository.InsertAsync(oldBranding);
            });

            await WithUnitOfWorkAsync(async () =>
            {
                IFileInfo imageFile = VirtualFileProvider.GetFileInfo("/Files/small-logo.png");
                _logo = imageFile.CreateReadStream();

                // Act
                TenantBranding updatedBranding = await _tenantBrandingRepository.GetAsync(oldBranding!.Id);
                updatedBranding = await _brandingProvider.UpdateBrandingFromIdentityAsync(tenant, updatedBranding);

                // Assert
                updatedBranding.Id.ShouldBe(oldBranding.Id);

                updatedBranding.PrimaryColor.Default.ShouldBe("#cccccc");
                updatedBranding.PrimaryColor.Override.ShouldBeNull();

                updatedBranding.AccentColor.Default.ShouldBe("#dddddd");
                updatedBranding.AccentColor.Override.ShouldBeNull();

                updatedBranding.Logo.Default.ShouldBe(oldBranding.Logo.Default);
                updatedBranding.Logo.Override.ShouldBeNull();

                updatedBranding.SmallLogo.Default.ShouldBe(oldBranding.SmallLogo.Default);
                updatedBranding.SmallLogo.Override.ShouldBeNull();

                updatedBranding.Favicon.Default.ShouldBe(oldBranding.Favicon.Default);
                updatedBranding.Favicon.Override.ShouldBeNull();
            });
        }
    }

    [Fact]
    public async Task GetLogo_Should_Return_Correct_Stream()
    {
        Tenant tenant = await CreateTenantAsync(Guid.NewGuid().ToString("N"), Guid.NewGuid().ToString("N"));
        using (CurrentTenant.Change(tenant.Id))
        {
            TenantBranding? branding = null;
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                IFileInfo imageFile = VirtualFileProvider.GetFileInfo("/Files/default-image.png");
                _logo = imageFile.CreateReadStream();
                _smallLogo = imageFile.CreateReadStream();
                _favicon = imageFile.CreateReadStream();

                branding = await _brandingProvider.CreateBrandingFromIdentityAsync(tenant);
                await _tenantBrandingRepository.InsertAsync(branding);
            });

            await WithUnitOfWorkAsync(async () =>
            {
                // Act
                Stream logoStream = await _brandingProvider.GetLogoContent(branding!);

                // Assert
                byte[] bytes = await logoStream.GetAllBytesAsync();
                byte[] expectedBytes = await _logo!.GetAllBytesAsync();
                bytes.ShouldBe(expectedBytes);
            });
        }
    }

    [Fact]
    public async Task GetColorsAsync_Should_Return_Colors_From_Identity()
    {
        // Act
        Dictionary<string, string> colors = await _brandingProvider.GetColorsFromIdentityAsync("tenantName");

        // Assert
        colors["primaryColor"].ShouldBe(_tenantBrandingColorsDto!.PrimaryColor);
        colors["accentColor"].ShouldBe(_tenantBrandingColorsDto!.SecondaryColor);
    }

    [Fact]
    public async Task GetColorsAsync_Should_Return_Colors_From_DefaultValue()
    {
        // Arrange
        _tenantBrandingColorsDto = null;

        // Act
        Dictionary<string, string> colors = await _brandingProvider.GetColorsFromIdentityAsync("tenantName");

        // Assert
        colors["primaryColor"].ShouldBe(CommonConsts.Branding.DefaultPrimaryColor);
        colors["accentColor"].ShouldBe(CommonConsts.Branding.DefaultAccentColor);
    }
}