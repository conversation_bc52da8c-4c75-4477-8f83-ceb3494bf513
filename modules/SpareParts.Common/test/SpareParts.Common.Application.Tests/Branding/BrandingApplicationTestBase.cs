using Microsoft.Extensions.DependencyInjection;
using System;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;

namespace SpareParts.Common.Branding;

public abstract class BrandingApplicationTestBase<TStartupModule> : CommonApplicationTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    protected readonly IRepository<TenantBranding, Guid> _tenantBrandingRepository;
    protected TenantBrandingDomainService _tenantBrandingDomainService;

    protected BrandingApplicationTestBase()
    {
        _tenantBrandingRepository = ServiceProvider.GetRequiredService<IRepository<TenantBranding, Guid>>();
        _tenantBrandingDomainService = ServiceProvider.GetRequiredService<TenantBrandingDomainService>();
    }
}
