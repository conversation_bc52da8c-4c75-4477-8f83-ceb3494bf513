using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Shouldly;
using SpareParts.Common.Branding.Dtos;
using SpareParts.Common.Branding.Dtos.Inputs;
using SpareParts.Common.Branding.Queries;
using SpareParts.Common.Settings;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Common.Branding.QueryHandlers;
public abstract class GetLoginPageBrandingQueryHandlerTests<TStartupModule> : GetBrandingQueryHandlerTestsBase<TStartupModule> where TStartupModule : IAbpModule
{
    [Fact]
    public async Task GetLoginPageBranding_Should_Return_Branding()
    {
        using (CurrentTenant.Change(GuidGenerator.Create()))
        {
            Guid imageId = Guid.NewGuid();
            bool hideTenantName = true;
            List<UpdateBrandingTranslationDto> translations = [new() { Language = "fr",Title="title fr", ApplicationName = "applicationName fr",Text = "text fr" },
                new() { Language = "en",Title="title en", ApplicationName = "applicationName en",Text = "text en" }];

            // Arrange
            await WithUnitOfWorkAsync(async () =>
            {
                await UpdateImageBrandingSetting(CommonSettings.LoginPageImageId, imageId);
                await UpdateTranslationBrandingSetting(CommonSettings.LoginPageTranslations, translations);
                await UpdateHideTenantNameBrandingSetting(CommonSettings.LoginPageHideTenantName, hideTenantName);
            });

            // Act
            LoginPageBrandingDto loginPageBrandingDto = default!;
            await WithUnitOfWorkAsync(async () =>
            {
                GetLoginPageBrandingQuery getLoginPageBrandingQuery = new();
                loginPageBrandingDto = await QuerySender.Send(getLoginPageBrandingQuery);
            });

            // Assert
            loginPageBrandingDto.ImageId.ShouldBe(imageId);
            loginPageBrandingDto.HideTenantName.ShouldBe(hideTenantName);
            loginPageBrandingDto.Translations.Count.ShouldBe(2);
            BrandingTranslationDto brandingTranslationDto = loginPageBrandingDto.Translations.First(x => x.Language == "fr");
            brandingTranslationDto.Text.ShouldBe("text fr");
            brandingTranslationDto.Title.ShouldBe("title fr");
            brandingTranslationDto.ApplicationName.ShouldBe("applicationName fr");
        }
    }

    [Fact]
    public async Task GetLoginPageBranding_Should_Return_Null_If_Branding_Is_String_Empty()
    {
        using (CurrentTenant.Change(GuidGenerator.Create()))
        {
            Guid? imageId = null;

            // Arrange
            await WithUnitOfWorkAsync(async () =>
            {
                await UpdateImageBrandingSetting(CommonSettings.LoginPageImageId, imageId);
            });

            // Act
            LoginPageBrandingDto loginPageBrandingDto = default!;
            await WithUnitOfWorkAsync(async () =>
            {
                GetLoginPageBrandingQuery getLoginPageBrandingQuery = new();
                loginPageBrandingDto = await QuerySender.Send(getLoginPageBrandingQuery);
                await UpdateTranslationBrandingSetting(CommonSettings.LoginPageTranslations, []);
            });

            // Assert
            loginPageBrandingDto.ImageId.ShouldBeNull();
            loginPageBrandingDto.HideTenantName.ShouldBeFalse();
            loginPageBrandingDto.Translations.Count.ShouldBe(0);
        }
    }

    [Fact]
    public async Task GetLoginPageBranding_Should_Return_Branding_From_Database()
    {
        using (CurrentTenant.Change(GuidGenerator.Create()))
        {
            Guid imageId = Guid.NewGuid();
            bool hideTenantName = true;
            List<TenantBrandingTranslation> translations = [new("fr", "title fr", "text fr"), new("en", "title en", "text en")];

            // Arrange
            await WithUnitOfWorkAsync(async () =>
            {
                imageId = await GeneratePublicResource();

                TenantBranding branding = await _tenantBrandingDomainService.CreateAsync(Guid.NewGuid(), "name", "#aaaaaa", "#bbbbbb", Guid.NewGuid(), Guid.NewGuid(), Guid.NewGuid());
                branding.LoginPageHideTenantName = hideTenantName;
                branding.LoginPageTranslations = translations;
                await _tenantBrandingDomainService.ChangeLoginPageImageAsync(branding, imageId);
                await _tenantBrandingRepository.InsertAsync(branding);
            });

            // Act
            LoginPageBrandingDto loginPageBrandingDto = default!;
            await WithUnitOfWorkAsync(async () =>
            {
                GetLoginPageBrandingQuery getLoginPageBrandingQuery = new();
                loginPageBrandingDto = await QuerySender.Send(getLoginPageBrandingQuery);
            });

            // Assert
            loginPageBrandingDto.ImageId.ShouldBe(imageId);
            loginPageBrandingDto.HideTenantName.ShouldBe(hideTenantName);
            loginPageBrandingDto.Translations.Count.ShouldBe(2);
            BrandingTranslationDto brandingTranslationDto = loginPageBrandingDto.Translations.First(x => x.Language == "fr");
            brandingTranslationDto.Text.ShouldBe("text fr");
            brandingTranslationDto.Title.ShouldBe("title fr");
        }
    }
}