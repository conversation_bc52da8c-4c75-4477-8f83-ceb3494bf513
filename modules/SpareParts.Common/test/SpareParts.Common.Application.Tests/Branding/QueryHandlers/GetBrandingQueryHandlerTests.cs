using Shouldly;
using SpareParts.Common.Branding.Dtos;
using SpareParts.Common.Branding.Queries;
using System;
using System.Threading.Tasks;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Common.Branding.QueryHandlers;
public abstract class GetBrandingQueryHandlerTests<TStartupModule> : BrandingApplicationTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    [Fact]
    public async Task GetBranding_Should_Return_Branding()
    {
        using (CurrentTenant.Change(GuidGenerator.Create()))
        {
            TenantBranding branding = default!;
            // Arrange
            await WithUnitOfWorkAsync(async () =>
            {
                branding = await _tenantBrandingDomainService.CreateAsync(Guid.NewGuid(), "name", "#aaaaaa", "#bbbbbb", Guid.NewGuid(), Guid.NewGuid(), Guid.NewGuid());
                branding.LoginPageHideTenantName = true;
                branding.LoginPageTranslations = [new TenantBrandingTranslation("en", "Login Page Title", "Login Page Description")];

                Guid imageId = await GeneratePublicResource();
                await _tenantBrandingDomainService.ChangeWelcomePageImageAsync(branding, imageId);

                await _tenantBrandingRepository.InsertAsync(branding);
            });

            // Act
            await WithUnitOfWorkAsync(async () =>
            {
                GetBrandingQuery getBrandingQuery = new();
                TenantBrandingDto brandingResult = await QuerySender.Send(getBrandingQuery);

                // Assert
                brandingResult.ApplicationName.ShouldBeEquivalentTo(branding.ApplicationName);
                brandingResult.AccentColor.ShouldBeEquivalentTo(branding.AccentColor);
                brandingResult.PrimaryColor.ShouldBeEquivalentTo(branding.PrimaryColor);
                brandingResult.Favicon.ShouldBeEquivalentTo(branding.Favicon);
                brandingResult.Logo.ShouldBeEquivalentTo(branding.Logo);
                brandingResult.SmallLogo.ShouldBeEquivalentTo(branding.SmallLogo);
                brandingResult.LoginPageHideTenantName.ShouldBeTrue();
                brandingResult.LoginPageImageId.ShouldBeNull();
                brandingResult.LoginPageTranslations.ShouldNotBeNull();
                brandingResult.LoginPageTranslations.Count.ShouldBe(branding.LoginPageTranslations!.Count);
                brandingResult.LoginPageTranslations[0].Language.ShouldBe(branding.LoginPageTranslations[0].Language);
                brandingResult.LoginPageTranslations[0].Title.ShouldBe(branding.LoginPageTranslations[0].Title);
                brandingResult.LoginPageTranslations[0].Text.ShouldBe(branding.LoginPageTranslations[0].Text);
                brandingResult.WelcomePageImageId.ShouldBe(branding.WelcomePageImageId);
                brandingResult.WelcomePageTranslations.ShouldBeNull();
                brandingResult.WelcomePageHideTenantName.ShouldBeFalse();
            });
        }
    }

    [Fact]
    public async Task GetBranding_Should_Throw_Exception_When_Branding_Does_Not_Exist()
    {
        using (CurrentTenant.Change(GuidGenerator.Create()))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                await QuerySender.Send(new GetBrandingQuery()).ShouldThrowAsync<EntityNotFoundException>();
            });
        }
    }
}