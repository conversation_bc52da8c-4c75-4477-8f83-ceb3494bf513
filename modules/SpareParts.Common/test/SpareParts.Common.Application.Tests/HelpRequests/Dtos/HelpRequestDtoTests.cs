using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Shouldly;
using SpareParts.Common.HelpRequests.Dtos.Inputs;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Common.HelpRequests.Dtos;
public abstract class HelpRequestDtoTests<TStartupModule> : CommonApplicationTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    [Fact]
    public void HelpRequestDto_Should_Be_Successfully_Instantiated()
    {
        // Arrange
        HelpRequestDto helpRequestDto = new()
        {
            Title = "Title",
            Message = "Message",
            CountryCode = "CountryCode",
            Context = ContextType.ProductImport
        };

        // Act
        IList<ValidationResult> errors = ValidateModel(helpRequestDto);

        // Assert
        errors.Count.ShouldBe(0);
    }

    [Fact]
    public void HelpRequestDto_Should_Throw_Errors_If_Title_Is_Empty()
    {
        // Arrange
        HelpRequestDto helpRequestDto = new()
        {
            Title = default!,
            Message = "Message",
            CountryCode = "CountryCode",
            Context = ContextType.ProductImport
        };

        // Act
        IList<ValidationResult> errors = ValidateModel(helpRequestDto);

        // Assert
        errors.Count.ShouldBe(1);
        errors[0].ErrorMessage!.ShouldContain("The Title field is required.");
    }

    [Fact]
    public void HelpRequestDto_Should_Throw_Errors_If_Message_Is_Empty()
    {
        // Arrange
        HelpRequestDto helpRequestDto = new()
        {
            Title = "Title",
            Message = default!,
            CountryCode = "CountryCode",
            Context = ContextType.ProductImport
        };

        // Act
        IList<ValidationResult> errors = ValidateModel(helpRequestDto);

        // Assert
        errors.Count.ShouldBe(1);
        errors[0].ErrorMessage!.ShouldContain("The Message field is required.");
    }

    [Fact]
    public void HelpRequestDto_Should_Throw_Errors_If_CountryCode_Is_Empty()
    {
        // Arrange
        HelpRequestDto helpRequestDto = new()
        {
            Title = "Title",
            Message = "Message",
            CountryCode = default!,
            Context = ContextType.ProductImport
        };

        // Act
        IList<ValidationResult> errors = ValidateModel(helpRequestDto);

        // Assert
        errors.Count.ShouldBe(1);
        errors[0].ErrorMessage!.ShouldContain("The CountryCode field is required.");
    }
}
