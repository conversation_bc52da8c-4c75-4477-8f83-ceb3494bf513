using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using Azure.AI.Translation.Text;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using NSubstitute;
using Shouldly;
using SpareParts.Common.Translator;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Common.Translators;

public abstract class TranslatorTests<TStartupModule> : CommonApplicationTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    private ITranslator Translator => ServiceProvider.GetRequiredService<ITranslator>();
    private const string DefaultLanguage = "en";
    private const string Text = "how are you?";
    private TextTranslationClient _client = null!;

    protected override void AfterAddApplication(IServiceCollection services)
    {
        _client = Substitute.For<TextTranslationClient>();
        _client.TranslateAsync(Arg.Any<IEnumerable<string>>(), Arg.Any<IEnumerable<string>>(),
                sourceLanguage: Arg.Any<string>());
        services.Replace(ServiceDescriptor.Singleton(_client));
    }

    [Fact]
    public async Task TranslateAsync_MultipleValidTargetLanguages_ReturnsTranslationsForEach()
    {
        TranslateDto dto = new(["fr", "de"], Text, DefaultLanguage);
        await Translator.TranslateAsync(dto);
        
        await _client.Received(1).TranslateAsync(
            Arg.Is<IEnumerable<string>>(x => x.SequenceEqual(new[] { "fr", "de" })),
            Arg.Is<IEnumerable<string>>(x => x.SequenceEqual(new[] { Text })),
            sourceLanguage: Arg.Is<string>(x => x == DefaultLanguage));
    }

    [Fact]
    public async Task TranslateAsync_TargetLanguagesContainSourceLanguage_ReturnsOnlyOtherTranslations()
    {
        TranslateDto dto = new([DefaultLanguage, "fr"], Text, DefaultLanguage);
        await Translator.TranslateAsync(dto);
        
        await _client.Received(1).TranslateAsync(
            Arg.Is<IEnumerable<string>>(x => x.SequenceEqual(new[] { "fr" })),
            Arg.Is<IEnumerable<string>>(x => x.SequenceEqual(new[] { Text })),
            sourceLanguage: Arg.Is<string>(x => x == DefaultLanguage));
    }

    [Fact]
    public async Task TranslateAsync_UnsupportedTargetLanguage_RemovesUnsupportedAndReturnsValidTranslations()
    {
        TranslateDto dto = new(["ar", "fr"], Text, DefaultLanguage);
        await Translator.TranslateAsync(dto);


        await _client.Received(1).TranslateAsync(
            Arg.Is<IEnumerable<string>>(x => x.SequenceEqual(new[] { "fr" })),
            Arg.Is<IEnumerable<string>>(x => x.SequenceEqual(new[] { Text })),
            sourceLanguage: Arg.Is<string>(x => x == DefaultLanguage));
    }

    [Fact]
    public async Task TranslateAsync_UnsupportedSourceLanguage_ThrowsValidationException()
    {
        TranslateDto dto = new(["fr", "de"], Text, "ar");
        await Translator.TranslateAsync(dto).ShouldThrowAsync<ValidationException>();
    }
}