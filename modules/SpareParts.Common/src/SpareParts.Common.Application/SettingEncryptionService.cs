using System.Diagnostics.CodeAnalysis;
using Microsoft.Extensions.Options;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Security.Encryption;
using Volo.Abp.Settings;

namespace SpareParts.Common;

[Dependency(ReplaceServices = true)]
[ExcludeFromCodeCoverage]
public class SettingEncryptionService(
    IStringEncryptionService stringEncryptionService,
    IOptions<AbpSettingOptions> options)
    : Volo.Abp.Settings.SettingEncryptionService(stringEncryptionService, options)
{
    public override string? Decrypt(SettingDefinition settingDefinition, string? encryptedValue)
    {
        return encryptedValue;
    }

    public override string? Encrypt(SettingDefinition settingDefinition, string? plainValue)
    {
        return plainValue;
    }
}