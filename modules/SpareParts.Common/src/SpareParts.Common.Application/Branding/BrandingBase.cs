using SpareParts.Common.Branding.Dtos;
using System;
using System.Collections.Generic;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.SettingManagement;

namespace SpareParts.Common.Branding;

public abstract class BrandingBase : CommonRequestBase
{
    protected ISettingManager SettingManager => LazyServiceProvider.LazyGetRequiredService<SettingManager>();
    protected IRepository<TenantBranding, Guid> BrandingRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<TenantBranding, Guid>>();
    protected TenantBrandingDomainService TenantBrandingDomainService => LazyServiceProvider.LazyGetRequiredService<TenantBrandingDomainService>();

    protected WelcomePageBrandingDto GetWelcomePageBrandingDto(TenantBranding branding)
    {
        return new WelcomePageBrandingDto
        {
            ImageId = branding.WelcomePageImageId,
            HideTenantName = branding.WelcomePageHideTenantName,
            Translations = branding.WelcomePageTranslations != null ? ObjectMapper.Map<List<TenantBrandingTranslation>, List<BrandingTranslationDto>>(branding.WelcomePageTranslations) : []
        };
    }

    protected LoginPageBrandingDto GetLoginPageBrandingDto(TenantBranding branding)
    {
        return new LoginPageBrandingDto
        {
            ImageId = branding.LoginPageImageId,
            HideTenantName = branding.LoginPageHideTenantName,
            Translations = branding.LoginPageTranslations != null ? ObjectMapper.Map<List<TenantBrandingTranslation>, List<BrandingTranslationDto>>(branding.LoginPageTranslations) : []
        };
    }
}