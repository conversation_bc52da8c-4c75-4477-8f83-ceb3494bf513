using SpareParts.Common.Branding.Dtos;
using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Threading.Tasks;
using Volo.Abp.SettingManagement;

namespace SpareParts.Common.Branding.QueryHandlers;

public abstract class GetBrandingQueryHandlerBase : BrandingBase
{
    protected async Task<Guid?> GetBandingImageId(string settingName)
    {
        string imageIdSetting = await SettingManager.GetOrNullForCurrentTenantAsync(settingName);
        if (Guid.TryParse(imageIdSetting, out Guid imageId))
        {
            return imageId;
        }
        return null;
    }

    protected async Task<bool> GetBandingHideTenantName(string settingName)
    {
        string hide = await SettingManager.GetOrNullForCurrentTenantAsync(settingName);
        if (bool.TryParse(hide, out bool value))
        {
            return value;
        }
        return false;
    }

    protected async Task<List<BrandingTranslationDto>> GetBandingTranslations(string settingName)
    {
        string translations = await Setting<PERSON>anager.GetOrNullForCurrentTenantAsync(settingName);
        if (string.IsNullOrWhiteSpace(translations))
        {
            return [];
        }
        return JsonSerializer.Deserialize<List<BrandingTranslationDto>>(translations) ?? [];
    }
}