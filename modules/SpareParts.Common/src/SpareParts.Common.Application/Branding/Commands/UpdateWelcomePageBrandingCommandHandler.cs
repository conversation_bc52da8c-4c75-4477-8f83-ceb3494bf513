using SpareParts.AbpMediatR.Handlers;
using SpareParts.Common.Branding.Dtos;
using SpareParts.Common.Branding.Dtos.Inputs;
using SpareParts.Common.Settings;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace SpareParts.Common.Branding.Commands;

public class UpdateWelcomePageBrandingCommandHandler : UpdateBrandingCommandHandlerBase, ICommandHandler<UpdateWelcomePageBrandingCommand, WelcomePageBrandingDto>
{
    public virtual async Task<WelcomePageBrandingDto> Handle(UpdateWelcomePageBrandingCommand request, CancellationToken cancellationToken)
    {
        TenantBranding? branding = await BrandingRepository.SingleOrDefaultAsync(cancellationToken: cancellationToken);

        if (branding == null)
        {
            return new WelcomePageBrandingDto
            {
                ImageId = await UpdateBrandingImage(CommonSettings.WelcomePageImageId, request.ImageId, cancellationToken),
                HideTenantName = await UpdateBrandingHideTenantName(CommonSettings.WelcomePageHideTenantName, request.HideTenantName, cancellationToken),
                Translations = await UpdateBrandingTranslations(CommonSettings.WelcomePageTranslations, request.Translations, cancellationToken)
            };
        }

        await UpdateWelcomePageProperties(
            request.HideTenantName,
            request.ImageId,
            ObjectMapper.Map<List<UpdateBrandingTranslationDto>, List<UpdateTenantBrandingTranslationDto>>(request.Translations),
            branding
        );

        branding = await BrandingRepository.UpdateAsync(branding, cancellationToken: cancellationToken);

        return GetWelcomePageBrandingDto(branding);
    }
}