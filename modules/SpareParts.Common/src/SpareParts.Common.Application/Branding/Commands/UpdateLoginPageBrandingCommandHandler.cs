using SpareParts.AbpMediatR.Handlers;
using SpareParts.Common.Branding.Dtos;
using SpareParts.Common.Branding.Dtos.Inputs;
using SpareParts.Common.Settings;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace SpareParts.Common.Branding.Commands;

public class UpdateLoginPageBrandingCommandHandler : UpdateBrandingCommandHandlerBase, ICommandHandler<UpdateLoginPageBrandingCommand, LoginPageBrandingDto>
{
    public virtual async Task<LoginPageBrandingDto> Handle(UpdateLoginPageBrandingCommand request, CancellationToken cancellationToken)
    {
        TenantBranding? branding = await BrandingRepository.SingleOrDefaultAsync(cancellationToken: cancellationToken);

        if (branding == null)
        {
            return new LoginPageBrandingDto
            {
                ImageId = await UpdateBrandingImage(CommonSettings.LoginPageImageId, request.ImageId, cancellationToken),
                HideTenantName = await UpdateBrandingHideTenantName(CommonSettings.LoginPageHideTenantName, request.HideTenantName, cancellationToken),
                Translations = await UpdateBrandingTranslations(CommonSettings.LoginPageTranslations, request.Translations, cancellationToken)
            };
        }

        await UpdateLoginPageProperties(
            request.HideTenantName,
            request.ImageId,
            ObjectMapper.Map<List<UpdateBrandingTranslationDto>, List<UpdateTenantBrandingTranslationDto>>(request.Translations),
            branding
        );

        branding = await BrandingRepository.UpdateAsync(branding, cancellationToken: cancellationToken);

        return GetLoginPageBrandingDto(branding);
    }
}