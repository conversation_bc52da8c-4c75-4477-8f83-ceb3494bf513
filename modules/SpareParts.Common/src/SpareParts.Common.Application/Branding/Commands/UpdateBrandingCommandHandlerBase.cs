using SpareParts.Common.Branding.Dtos;
using SpareParts.Common.Branding.Dtos.Inputs;
using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.SettingManagement;

namespace SpareParts.Common.Branding.Commands;

public abstract class UpdateBrandingCommandHandlerBase : BrandingBase
{
    protected async Task UpdateLoginPageProperties(bool hideTenantName, Guid? imageId, List<UpdateTenantBrandingTranslationDto>? translations, TenantBranding branding)
    {
        branding.LoginPageHideTenantName = hideTenantName;
        await TenantBrandingDomainService.ChangeLoginPageImageAsync(branding, imageId);
        branding.LoginPageTranslations = translations != null
             ? ObjectMapper.Map<List<UpdateTenantBrandingTranslationDto>, List<TenantBrandingTranslation>>(translations)
             : null;
    }

    protected async Task UpdateWelcomePageProperties(bool hideTenantName, Guid? imageId, List<UpdateTenantBrandingTranslationDto>? translations, TenantBranding branding)
    {
        branding.WelcomePageHideTenantName = hideTenantName;
        await TenantBrandingDomainService.ChangeWelcomePageImageAsync(branding, imageId);
        branding.WelcomePageTranslations = translations != null
             ? ObjectMapper.Map<List<UpdateTenantBrandingTranslationDto>, List<TenantBrandingTranslation>>(translations)
             : null;
    }

    protected async Task<List<BrandingTranslationDto>> UpdateBrandingTranslations(string settingName, List<UpdateBrandingTranslationDto> translationDtos, CancellationToken cancellationToken)
    {
        if (translationDtos.Count == 0)
        {
            await SettingManager.SetForCurrentTenantAsync(settingName, string.Empty);
            return [];
        }
        string translations = JsonSerializer.Serialize(translationDtos);
        await SettingManager.SetForCurrentTenantAsync(settingName, translations);

        return JsonSerializer.Deserialize<List<BrandingTranslationDto>>(translations) ?? [];
    }

    protected async Task<bool> UpdateBrandingHideTenantName(string settingName, bool hideTenantName, CancellationToken cancellationToken)
    {
        await SettingManager.SetForCurrentTenantAsync(settingName, hideTenantName.ToString());
        return hideTenantName;
    }

    protected async Task<Guid?> UpdateBrandingImage(string settingName, Guid? imageId, CancellationToken cancellationToken)
    {
        if (imageId == null)
        {
            await SettingManager.SetForCurrentTenantAsync(settingName, string.Empty);
        }
        else
        {
            await UpdateImageIdInSettings(settingName, imageId.Value, cancellationToken);
        }
        return imageId;
    }

    private async Task UpdateImageIdInSettings(string settingName, Guid imageId, CancellationToken cancellationToken)
    {
        await PublicResourceRepository.EnsureExistsAsync(imageId, cancellationToken: cancellationToken);
        await EnsureBlobImageExistAsync(imageId, cancellationToken);

        await SettingManager.SetForCurrentTenantAsync(settingName, imageId.ToString());
    }

    private async Task EnsureBlobImageExistAsync(Guid imageId, CancellationToken cancellationToken)
    {
        bool exist = await PublicImageManager.BlobExistsAsync(imageId, cancellationToken);
        if (!exist)
        {
            throw new EntityNotFoundException($"The image {imageId} was not found");
        }
    }
}