using AutoMapper;
using SpareParts.Common.Branding.Commands;
using SpareParts.Common.Branding.Dtos;
using SpareParts.Common.Branding.Dtos.Inputs;
using Volo.Abp.AutoMapper;

namespace SpareParts.Common.Branding.Mapping;
public class BrandingMappingProfile : Profile
{
    public BrandingMappingProfile()
    {
        AllowNullCollections = true;

        CreateMap<TenantBranding, TenantBrandingDto>();

        CreateMap<TenantBrandingTranslation, TenantBrandingTranslationDto>();
        CreateMap<UpdateTenantBrandingTranslationDto, TenantBrandingTranslation>();

        CreateMap<UpdateWelcomePageBrandingDto, UpdateWelcomePageBrandingCommand>();
        CreateMap<UpdateLoginPageBrandingDto, UpdateLoginPageBrandingCommand>();
        CreateMap<UpdateBrandingTranslationDto, UpdateTenantBrandingTranslationDto>();
        CreateMap<TenantBrandingTranslation, BrandingTranslationDto>()
            .Ignore(dest => dest.ApplicationName);
    }
}
