using SpareParts.Common.Companies;
using SpareParts.Common.Dtos;
using System;
using System.Linq;
using System.Security.Claims;
using Volo.Abp.Authorization;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Security.Claims;

namespace SpareParts.Common.Company;

public class CurrentCompany : ITransientDependency, ICurrentCompany
{
    private readonly ICurrentPrincipalAccessor _principalAccessor;

    public string? CompanyName => FindClaimValue(CommonClaimTypes.CompanyName);
    public string? LegalName => FindClaimValue(CommonClaimTypes.CompanyLegalName);
    public string? Code => FindClaimValue(CommonClaimTypes.CompanyCode);
    public Guid? Id => FindCompanyId();
    public string? Type => FindClaimValue(CommonClaimTypes.CompanyType);
    public bool? IsDefault => string.Equals(FindClaimValue(CommonClaimTypes.IsDefault), "true", StringComparison.InvariantCultureIgnoreCase);

    public CurrentCompany(ICurrentPrincipalAccessor principalAccessor)
    {
        _principalAccessor = principalAccessor;
    }

    public string? FindClaimValue(string claimType)
    {
        return _principalAccessor.Principal?.Claims.FirstOrDefault(c => c.Type == claimType)?.Value;
    }

    public Guid? FindCompanyId()
    {
        Claim? idOrNull = _principalAccessor.Principal?.Claims?.FirstOrDefault(c => c.Type == CommonClaimTypes.CompanyId);
        if (idOrNull == null || idOrNull.Value.IsNullOrWhiteSpace())
        {
            return null;
        }

        if (Guid.TryParse(idOrNull.Value, out Guid guid))
        {
            return guid;
        }

        return null;
    }

    public CurrentCompanyDto GetCurrentCompanyDto()
    {
        return new CurrentCompanyDto
        {
            Code = Code,
            Id = Id,
            IsDefault = IsDefault,
            LegalName = LegalName,
            Name = CompanyName,
            Type = Type
        };
    }

    public void ThrowIfExternal()
    {
        if (!Enum.TryParse(Type, true, out CompanyType companyType) || companyType == Companies.CompanyType.External)
        {
            throw new AbpAuthorizationException();
        }
    }
}