using SpareParts.Common.Localization;
using SpareParts.Common.Resources;
using System;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace SpareParts.Common;

public abstract class CommonRequestBase : ApplicationService
{
    protected PublicImageManager PublicImageManager => LazyServiceProvider.LazyGetRequiredService<PublicImageManager>();
    protected IRepository<PublicResource, Guid> PublicResourceRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<PublicResource, Guid>>();

    protected CommonRequestBase()
    {
        LocalizationResource = typeof(CommonResource);
        ObjectMapperContext = typeof(CommonApplicationModule);
    }
}