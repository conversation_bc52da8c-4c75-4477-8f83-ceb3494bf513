using AutoFilterer.Attributes;
using FluentValidation;
using System.Linq;
using System.Reflection;

namespace SpareParts.Common.Validators;

public abstract class PaginationFilterValidator<TQuery> : AbstractValidator<TQuery> where TQuery : class
{
    protected PaginationFilterValidator()
    {
        RuleFor(x => GetFilter(x)).Must(x =>
        {
            PossibleSortingsAttribute? attribute = x.GetType().GetCustomAttribute<PossibleSortingsAttribute>();
            return string.IsNullOrWhiteSpace(x.Sort) || attribute == null || attribute.PropertyNames.Contains(x.Sort);
        }).WithMessage(x =>
        {
            PossibleSortingsAttribute attribute = GetFilter(x).GetType().GetCustomAttribute<PossibleSortingsAttribute>()!;
            return $"The sorting field must be one of the following : {string.Join(',', attribute.PropertyNames)}";
        });
    }

    protected abstract LimitedPaginationFilterBase GetFilter(TQuery query);
}