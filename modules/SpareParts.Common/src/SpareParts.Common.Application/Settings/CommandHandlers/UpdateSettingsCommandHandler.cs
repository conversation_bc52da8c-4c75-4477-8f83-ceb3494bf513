using SpareParts.AbpMediatR.Handlers;
using SpareParts.Common.Helpers;
using SpareParts.Common.Settings.Commands;
using SpareParts.Common.Settings.Dtos;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Localization;
using Volo.Abp.SettingManagement;
using Volo.Abp.Validation;

namespace SpareParts.Common.Settings.CommandHandlers;

public class UpdateSettingsCommandHandler : SettingsRequestBase, ICommandHandler<UpdateSettingsCommand, SettingsDto>
{
    public EntityTranslationHelper EntityTranslationHelper { get; set; } = default!;

    public virtual async Task<SettingsDto> Handle(UpdateSettingsCommand request, CancellationToken cancellationToken)
    {
        string newLanguage = request.DefaultLanguage;
        string defaultLanguage = (await SettingProvider.GetOrNullAsync(CommonSettings.DefaultLanguage))!;

        if (newLanguage != defaultLanguage)
        {
            await ValidateNewLanguageAsync(newLanguage, cancellationToken);
            await SettingManager.SetForCurrentTenantAsync(CommonSettings.DefaultLanguage, newLanguage);
        }

        return new SettingsDto
        {
            DefaultLanguage = newLanguage
        };

    }

    private async Task ValidateNewLanguageAsync(string newLanguage, CancellationToken cancellationToken)
    {
        IReadOnlyList<LanguageInfo> languages = await LanguageProvider.GetLanguagesAsync();
        List<string> cultures = languages.Select(x => x.CultureName).ToList();
        if (!cultures.Contains(newLanguage))
        {
            throw new ArgumentException($"The language {newLanguage} is not supported");
        }

        List<string> errorMessages = [];

        IEnumerable<Type> translatedEntityTypes = EntityTranslationHelper.GetTranslatedEntityTypes();

        foreach (Type translatedEntityType in translatedEntityTypes)
        {
            int count = await EntityTranslationHelper.CountEntitiesWithoutTranslation(translatedEntityType, newLanguage, cancellationToken);
            if (count > 0)
            {
                errorMessages.Add($"{translatedEntityType.Name}:{count}");
            }
        }

        if (errorMessages.Count > 0)
        {
            throw new AbpValidationException([new ValidationResult("core:settings-translation", errorMessages)]);
        }
    }

}