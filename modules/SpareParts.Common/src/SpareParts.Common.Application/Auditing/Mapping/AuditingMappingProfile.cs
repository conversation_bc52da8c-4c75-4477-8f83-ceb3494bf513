using AutoMapper;
using SpareParts.Common.Auditing.Dtos;
using System;
using System.Collections.Generic;

namespace SpareParts.Common.Auditing.Mapping;

public class AuditingMappingProfile : Profile
{
    public AuditingMappingProfile()
    {
        CreateMap<EntityChange, EntityChangeDto>()
            .ForCtorParam(nameof(EntityChangeDto.ApplicationName), opt => opt.MapFrom(src => src.ApplicationName))
            .ForCtorParam(nameof(EntityChangeDto.ChangeTime), opt => opt.MapFrom(src => src.ChangeTime))
            .ForCtorParam(nameof(EntityChangeDto.ChangeType), opt => opt.MapFrom(src => src.ChangeType.ToString()))
            .ForCtorParam(nameof(EntityChangeDto.EntityTypeFullName), opt => opt.MapFrom(src => src.EntityTypeFullName))
            .ForCtorParam(nameof(EntityChangeDto.UserId), opt => opt.MapFrom(src => src.UserId))
            .ForCtorParam(nameof(EntityChangeDto.UserFirstName), opt => opt.MapFrom(src => src.UserFirstName))
            .ForCtorParam(nameof(EntityChangeDto.UserLastName), opt => opt.MapFrom(src => src.UserLastName))
            .ForCtorParam(nameof(EntityChangeDto.Changes), opt => opt.MapFrom(src => ToChangeDtos(src.Changes)));
    }

    private static List<ChangeDto> ToChangeDtos(string changes)
    {
        if (string.IsNullOrWhiteSpace(changes))
        {
            return [];
        }
        List<ChangeDto> changeList = [];
        string[] changePairs = changes.Split('|', StringSplitOptions.RemoveEmptyEntries);
        foreach (string pair in changePairs)
        {
            string[] parts = pair.Split("=>", StringSplitOptions.RemoveEmptyEntries);
            string propertyNameWithOriginalValue = parts[0].Trim();
            string[] parts1 = propertyNameWithOriginalValue.Split(':', StringSplitOptions.RemoveEmptyEntries);
            string propertyName = parts1[0].Trim();
            string? originalValue = parts1[1].Trim().Trim('"');
            originalValue = originalValue == "null" ? null : originalValue;
            string? newValue = parts[1].Trim().Trim('"');
            newValue = newValue == "null" ? null : newValue;
            changeList.Add(new ChangeDto(propertyName, originalValue, newValue));
        }
        return changeList;
    }
}