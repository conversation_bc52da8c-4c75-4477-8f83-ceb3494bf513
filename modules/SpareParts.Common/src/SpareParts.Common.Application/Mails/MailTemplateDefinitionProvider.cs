using SpareParts.Common.Localization;
using Volo.Abp.TextTemplating;
using Volo.Abp.TextTemplating.Scriban;

namespace SpareParts.Common.Mails;

public class MailTemplateDefinitionProvider : TemplateDefinitionProvider
{
    public override void Define(ITemplateDefinitionContext context)
    {
        context.Add(
            new TemplateDefinition(CommonMailingTemplateConsts.HelpVisiativSupport, typeof(CommonResource))
                .WithVirtualFilePath(
                    "/Mails/Templates/HelpVisiativSupport.tpl",
                    isInlineLocalized: true
                )
                .WithScribanEngine()
        );
        context.Add(
            new TemplateDefinition(CommonMailingTemplateConsts.HelpClientConfirmation, typeof(CommonResource))
                .WithVirtualFilePath(
                    "/Mails/Templates/HelpClientConfirmation.tpl",
                    isInlineLocalized: true
                )
                .WithScribanEngine()
        );
    }
}