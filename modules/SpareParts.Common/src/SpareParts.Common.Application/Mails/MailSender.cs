using Microsoft.Extensions.Logging;
using SpareParts.Common.Branding;
using SpareParts.Common.Mails.Args;
using SpareParts.Common.Resources;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Emailing;
using Volo.Abp.TextTemplating;

namespace SpareParts.Common.Mails;

[ExcludeFromCodeCoverage]
public class MailSender : ITransientDependency
{
    public IAbpLazyServiceProvider LazyServiceProvider { get; set; } = null!;
    private ITemplateRenderer TemplateRenderer => LazyServiceProvider.LazyGetRequiredService<ITemplateRenderer>();
    private IEmailSender EmailSender => LazyServiceProvider.LazyGetRequiredService<IEmailSender>();
    private ILoggerFactory LoggerFactory => LazyServiceProvider.LazyGetRequiredService<ILoggerFactory>();
    private ILogger Logger => LazyServiceProvider.LazyGetService<ILogger>(_ => LoggerFactory.CreateLogger(GetType().FullName!));
    private IRepository<TenantBranding, Guid> BrandingRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<TenantBranding, Guid>>();
    private IBrandingProvider BrandingProvider => LazyServiceProvider.LazyGetRequiredService<IBrandingProvider>();

    public async Task SendOrQueueAsync(SendMailArgs sendMailArgs, CancellationToken cancellationToken)
    {
        (_, string to, string subject, string templateName, object? model, string language, bool isBodyHtml) = sendMailArgs;
        AdditionalEmailSendingArgs? additionalEmailSendingArgs = sendMailArgs.AdditionalEmailSendingArgs;

        TenantBranding tenantBranding = await BrandingRepository.SingleAsync(cancellationToken: cancellationToken);
        Dictionary<string, object> globalContext = new()
        {
            {"LogoSrc", await GetLogoSrc(tenantBranding, cancellationToken)},
            {"PrimaryColor", tenantBranding.PrimaryColor.GetEffectiveValue()}
        };

        string body = await TemplateRenderer.RenderAsync(templateName, model, language, globalContext);

        try
        {
            await EmailSender.SendAsync(to, subject, body, isBodyHtml, additionalEmailSendingArgs);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Email failed to send. Queued for later.");
            await EmailSender.QueueAsync(to, subject, body, isBodyHtml, additionalEmailSendingArgs);
        }
    }

    private async Task<string> GetLogoSrc(TenantBranding tenantBranding, CancellationToken cancellationToken)
    {
        Stream stream = await BrandingProvider.GetLogoContent(tenantBranding, cancellationToken: cancellationToken);
        using MemoryStream ms = new();
        await stream.CopyToAsync(ms, cancellationToken);
        string base64 = Convert.ToBase64String(ms.ToArray());

        PublicResource logo = await BrandingProvider.GetLogoResource(tenantBranding, cancellationToken);
        string mimeType = $"image/{Path.GetExtension(logo.FileName)}";

        return $"data:{mimeType};base64,{base64}";
    }
}