using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Azure.AI.Translation.Text;
using SpareParts.Common.Translator;

namespace SpareParts.Common.Translators;

public class AzureAiTranslator : AbstractTranslator
{
    private TextTranslationClient Client => LazyServiceProvider.LazyGetRequiredService<TextTranslationClient>();

    private protected override async Task<IEnumerable<TranslatedText>> ProviderTranslateAsync(TranslateDto dto,
        CancellationToken cancellationToken)
    {
        IReadOnlyList<TranslatedTextItem> translatedTextItems = (await Client.TranslateAsync(dto.TargetLanguages, [dto.Text], sourceLanguage: dto.SourceLanguage, cancellationToken: cancellationToken)).Value;

        return from item in translatedTextItems from translation in item.Translations select new TranslatedText(translation.TargetLanguage, translation.Text);
    }
}