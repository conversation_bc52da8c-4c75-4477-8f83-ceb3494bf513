using System;

namespace SpareParts.Common;

public static class CommonConsts
{
    public const string ProductLineName = "spareparts";
    public const string PublicImagesContainerName = "public-images";
    public const string ColorPattern = @"^#(?:[0-9a-fA-F]{3}){1,2}$";
    public const string UrlPattern = @"[(http(s)?):\/\/(www\.)?a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)";

    public static readonly TimeSpan RegexTimeout = TimeSpan.FromSeconds(1);

    public static class AbpClockOptions
    {
        public const DateTimeKind Kind = DateTimeKind.Utc;
    }

    public static class Branding
    {
        public const string AssetsFolder = "Images/";
        public const string LogoFileName = "logo.png";
        public const string SmallLogoFileName = "small-logo.png";
        public const string AlternateLogoFileName = "alternateLogo.png";
        public const string FaviconFileName = "favicon.png";
        public const string LogoPath = AssetsFolder + LogoFileName;
        public const string SmallLogoPath = AssetsFolder + SmallLogoFileName;
        public const string AlternateLogoPath = AssetsFolder + AlternateLogoFileName;
        public const string FaviconPath = AssetsFolder + FaviconFileName;
        public const string DefaultPrimaryColor = "#951CFF";
        public const string DefaultAccentColor = "#34BEAE";
        public const string DefaultApplicationName = "Visiativ Spareparts";
        public const int ApplicationNameMaxLength = 50;
    }

    public static class SizeLimits
    {
        //5 MB
        public const int ImageSizeLimitInBytes = 5 * 1024 * 1024;
        //15 MB
        public const int FileSizeLimitInBytes = 15 * 1024 * 1024;
        //100 KB
        public const int OverheadInBytes = 100 * 1024;
    }

    public static class HelpRequests
    {
        public const string CodeTemplate = "[Spareparts #{0}]";
    }
}