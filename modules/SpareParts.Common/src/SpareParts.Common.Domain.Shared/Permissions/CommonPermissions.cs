using System.Collections.Generic;

namespace SpareParts.Common.Permissions;

public static class CommonPermissions
{
    public const string GroupName = "Common";

    public static class PublicResources
    {
        public const string Default = $"{GroupName}.{nameof(PublicResources)}";
        public const string Create = $"{Default}.{nameof(Create)}";
    }

    public static class Branding
    {
        public const string Default = $"{GroupName}.{nameof(Branding)}";
        public const string Edit = $"{Default}.{nameof(Edit)}";
    }

    public static class Auditing
    {
        public const string Default = $"{GroupName}.{nameof(Auditing)}";
    }

    public static class Settings
    {
        public const string Default = $"{GroupName}.{nameof(Settings)}";
        public const string Edit = $"{Default}.{nameof(Edit)}";
    }

    public static class Translations
    {
        public const string Default = $"{GroupName}.{nameof(Translations)}";
    }

    public static class HelpRequests
    {
        public const string Default = $"{GroupName}.{nameof(HelpRequests)}";
        public const string Create = $"{Default}.{nameof(Create)}";
    }

    public static List<string> GetConfigurationAdministratorPermissions()
    {
        return [PublicResources.Default, PublicResources.Create, Branding.Default, Branding.Edit, Settings.Default, Settings.Edit, HelpRequests.Default, HelpRequests.Create];
    }

    public static List<string> GetContentViewerPermissions()
    {
        return [HelpRequests.Default, HelpRequests.Create];
    }

    public static List<string> GetAccountManagerPermissions()
    {
        return [HelpRequests.Default, HelpRequests.Create];
    }

    public static List<string> GetContentManagerPermissions()
    {
        return [Translations.Default, Settings.Default, Settings.Edit, HelpRequests.Default, HelpRequests.Create, Auditing.Default];
    }
}