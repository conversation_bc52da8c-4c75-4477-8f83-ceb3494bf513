using System;
using System.ComponentModel.DataAnnotations;

namespace SpareParts.Common.Branding;

[AttributeUsage(AttributeTargets.Property)]
public class StringBrandingValidationAttribute : ValidationAttribute
{
    public int MaxLength { get; set; } = int.MaxValue;

    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        BrandingProperty<string> brandingProperty = (BrandingProperty<string>)value!;

        if (string.IsNullOrWhiteSpace(brandingProperty.Default))
        {
            return new ValidationResult($"Default for property '{validationContext.MemberName}' cannot be null or whitespace.");
        }

        if (brandingProperty.Default.Length > MaxLength)
        {
            return new ValidationResult($"Default for property '{validationContext.MemberName}' exceeds the maximum length of {MaxLength} characters.");
        }

        if (brandingProperty.Override != null && brandingProperty.Override.Length > MaxLength)
        {
            return new ValidationResult($"Override for property '{validationContext.MemberName}' exceeds the maximum length of {MaxLength} characters.");
        }

        return ValidationResult.Success;
    }
}

