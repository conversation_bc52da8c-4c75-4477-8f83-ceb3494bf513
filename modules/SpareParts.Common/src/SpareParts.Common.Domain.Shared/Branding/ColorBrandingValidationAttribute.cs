using System;
using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace SpareParts.Common.Branding;

[AttributeUsage(AttributeTargets.Property)]
public partial class ColorBrandingValidationAttribute : StringBrandingValidationAttribute
{
    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        ValidationResult? stringValidation = base.IsValid(value, validationContext);
        if (stringValidation != null)
        {
            return stringValidation;
        }

        BrandingProperty<string> brandingProperty = (BrandingProperty<string>)value!;

        bool isHexaColor = Regex.IsMatch(brandingProperty.Default, CommonConsts.ColorPattern, RegexOptions.None, CommonConsts.RegexTimeout);
        if (!isHexaColor)
        {
            return new ValidationResult($"The value '{brandingProperty.Default}' for Default of property '{validationContext.MemberName}' is not a valid hexadecimal color code.");
        }

        if (brandingProperty.Override != null)
        {
            isHexaColor = Regex.IsMatch(brandingProperty.Override, CommonConsts.ColorPattern, RegexOptions.None, CommonConsts.RegexTimeout);
            if (!isHexaColor)
            {
                return new ValidationResult($"The value '{brandingProperty.Override}' for Override of property '{validationContext.MemberName}' is not a valid hexadecimal color code.");
            }
        }

        return ValidationResult.Success;
    }
}

