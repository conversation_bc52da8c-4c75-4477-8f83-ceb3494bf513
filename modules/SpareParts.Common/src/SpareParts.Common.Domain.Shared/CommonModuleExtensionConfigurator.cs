using Volo.Abp.ObjectExtending;
using Volo.Abp.Threading;

namespace SpareParts.Common;

public static class CommonModuleExtensionConfigurator
{
    public const string IsEnabledPropertyName = "IsEnabled";
    public const string DisplayNamePropertyName = "DisplayName";

    private static readonly OneTimeRunner OneTimeRunner = new();

    public static void Configure()
    {
        OneTimeRunner.Run(ConfigureExtraProperties);
    }

    private static void ConfigureExtraProperties()
    {
        ObjectExtensionManager.Instance.Modules()
            .ConfigureTenantManagement(config =>
            {
                config.ConfigureTenant(tenant =>
                {
                    tenant.AddOrUpdateProperty<bool>(IsEnabledPropertyName);
                    tenant.AddOrUpdateProperty<string>(DisplayNamePropertyName);
                });
            });
    }
}