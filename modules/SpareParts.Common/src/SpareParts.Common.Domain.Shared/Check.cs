using JetBrains.Annotations;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text.RegularExpressions;

namespace SpareParts.Common;

[ExcludeFromCodeCoverage]
public static class Check
{
    public static T NotDefault<T>(
        T value,
        [InvokerParameterName] string parameterName)
        where T : struct
    {
        if (value.Equals(default(T)))
        {
            throw new ArgumentException($"{parameterName} has a default value!", parameterName);
        }

        return value;
    }

    public static void NotEqual<T>(T value1, T value2, [InvokerParameterName] string parameterName1, [InvokerParameterName] string parameterName2) where T : IEquatable<T>
    {
        if (value1.Equals(value2))
        {
            throw new ArgumentException($"{parameterName1} should be different from {parameterName2}");
        }
    }

    public static IEnumerable<T> HasAtLeastOne<T>(this IEnumerable<T> enumerable, [InvokerParameterName] string parameterName)
    {
        if (!enumerable.Any())
        {
            throw new ArgumentException($"{parameterName} should have at least one", parameterName);
        }

        return enumerable;
    }

    public static T? NotDefaultIfNotNull<T>(
        T? value,
        [InvokerParameterName] string parameterName)
        where T : struct
    {
        if (value.HasValue && value.Value.Equals(default(T)))
        {
            throw new ArgumentException($"{parameterName} has a default value!", parameterName);
        }

        return value;
    }

    public static string? NotWhiteSpaceIfNotNull(
        this string? value,
        [InvokerParameterName] string parameterName)
    {
        if (value != null && string.IsNullOrWhiteSpace(value))
        {
            throw new ArgumentException($"{parameterName} is white space!", parameterName);
        }

        return value;
    }

    public static string MatchUrl(string value, [InvokerParameterName] string parameterName)
    {
        try
        {
            if (!Regex.IsMatch(value, CommonConsts.UrlPattern, RegexOptions.None, CommonConsts.RegexTimeout))
            {
                throw new ArgumentException($"{parameterName} does not match the url pattern !", parameterName);
            }
        }
        catch (RegexMatchTimeoutException)
        {
            throw new ArgumentException($"The provided '{parameterName}' is not valid !", parameterName);
        }

        return value;
    }

    public static string MatchHexadecimalColor(string value, [InvokerParameterName] string parameterName)
    {
        try
        {
            if (!Regex.IsMatch(value, CommonConsts.ColorPattern, RegexOptions.None, CommonConsts.RegexTimeout))
            {
                throw new ArgumentException($"{parameterName} does not match the hexadecimal color pattern !", parameterName);
            }
        }
        catch (RegexMatchTimeoutException)
        {
            throw new ArgumentException($"The provided '{parameterName}' is not valid !", parameterName);
        }
        return value;
    }

    public static DateTime NotUnspecified(DateTime value, [InvokerParameterName] string parameterName)
    {
        if (value.Kind == DateTimeKind.Unspecified)
        {
            throw new ArgumentException($"{parameterName} must not be in unspecified", parameterName);
        }
        return value;
    }

    public static string MatchRegex(string value, string regex, [InvokerParameterName] string parameterName)
    {
        try
        {
            if (!Regex.IsMatch(value, regex, RegexOptions.None, CommonConsts.RegexTimeout))
            {
                throw new ArgumentException($"{parameterName} does not match the regex pattern !", parameterName);
            }
        }
        catch (RegexMatchTimeoutException)
        {
            throw new ArgumentException($"The provided '{parameterName}' is not valid !", parameterName);
        }
        return value;
    }
}