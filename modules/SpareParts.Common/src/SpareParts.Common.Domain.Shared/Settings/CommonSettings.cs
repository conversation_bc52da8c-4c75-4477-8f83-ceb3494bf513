namespace SpareParts.Common.Settings;

public static class CommonSettings
{
    private const string GroupName = "Common";

    public const string DefaultLanguage = GroupName + ".Data.DefaultLanguage";

    public const string FilesAllowedExtensions = GroupName + ".Files.AllowedExtensions";
    public const string ImagesAllowedExtensions = GroupName + ".Images.AllowedExtensions";
    public const string FilesSizeLimit = GroupName + ".Files.SizeLimit";
    public const string ImagesSizeLimit = GroupName + ".Images.SizeLimit";

    public const string Branding = GroupName + ".Branding";
    public const string DefaultLogoId = Branding + ".DefaultLogoId";
    public const string DefaultSmallLogoId = Branding + ".DefaultSmallLogoId";
    public const string DefaultFaviconId = Branding + ".DefaultFaviconId";
    public const string BrandingCommonConfig = Branding + ".BrandingCommonConfig";
    public const string WelcomePageImageId = Branding + ".WelcomePage.ImageId";
    public const string WelcomePageHideTenantName = Branding + ".WelcomePage.HideTenantName";
    public const string WelcomePageTranslations = Branding + ".WelcomePage.Translations";
    public const string LoginPageImageId = Branding + ".LoginPage.ImageId";
    public const string LoginPageTranslations = Branding + ".LoginPage.Translations";
    public const string LoginPageHideTenantName = Branding + ".LoginPage.HideTenantName";

    public const string Country = GroupName + ".Country";
    public const string CountryCode = Country + ".Code";

    public const string Support = GroupName + ".Support";
    public const string SupportEmailAddressesByCountry = Support + ".EmailAddresses";
    public const string SupportDefaultCountryCode = Support + ".DefaultCountryCode";
    public const string SupportUrl = Support + ".SupportUrl";
}