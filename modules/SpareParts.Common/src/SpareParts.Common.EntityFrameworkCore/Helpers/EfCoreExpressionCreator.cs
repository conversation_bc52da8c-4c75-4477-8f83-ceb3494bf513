using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;

namespace SpareParts.Common.Helpers;

public class EfCoreExpressionCreator : IExpressionCreator
{
    public Expression<Func<T, TV>> GetPropertyExpression<T, TV>(string propertyName)
    {
        return e => EF.Property<TV>(e!, propertyName);
    }

    public Expression<Func<T, bool>> GetEqualsExpression<T, TV>(string propertyName, TV value)
    {
        return e => EF.Property<TV>(e!, propertyName)!.Equals(value);
    }

    public Expression<Func<T, bool>> GetContainsExpression<T, TV>(string propertyName, IEnumerable<TV> values)
    {
        return e => values.Contains(EF.Property<TV>(e!, propertyName));
    }
}