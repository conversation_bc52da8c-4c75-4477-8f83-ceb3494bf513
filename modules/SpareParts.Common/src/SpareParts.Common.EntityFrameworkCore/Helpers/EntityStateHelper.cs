using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Entities;
using Volo.Abp.EntityFrameworkCore;

namespace SpareParts.Common.Helpers;

public abstract class EntityStateHelper<TDbContext> : IEntityStateHelper, ISingletonDependency where TDbContext : IEfCoreDbContext
{
    public IAbpLazyServiceProvider LazyServiceProvider { get; set; } = null!;
    private IDbContextProvider<TDbContext> DbContextProvider => LazyServiceProvider.LazyGetRequiredService<IDbContextProvider<TDbContext>>();

    public async Task ChangeStateIfModifiedAsync(IEntity entity)
    {
        IEfCoreDbContext dbContext = await DbContextProvider.GetDbContextAsync();
        EntityEntry<IEntity> entry = dbContext.Entry(entity);
        if (entry.IsModified())
        {
            entry.State = EntityState.Modified;
        }
    }

    public async Task PropagateStateIfModifiedAsync(IEntity sourceEntity, IEntity destinationEntity)
    {
        IEfCoreDbContext dbContext = await DbContextProvider.GetDbContextAsync();
        EntityEntry<IEntity> sourceEntry = dbContext.Entry(sourceEntity);

        if (sourceEntry.IsModified())
        {
            dbContext.Entry(destinationEntity).State = EntityState.Modified;
        }
    }

    public async Task DetachAsync(IEntity entity)
    {
        IEfCoreDbContext dbContext = await DbContextProvider.GetDbContextAsync();
        dbContext.Entry(entity).State = EntityState.Detached;
    }
}