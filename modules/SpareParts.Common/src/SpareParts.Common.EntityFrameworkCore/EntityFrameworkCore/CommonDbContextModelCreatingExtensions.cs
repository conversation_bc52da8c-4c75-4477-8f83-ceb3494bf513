using Microsoft.EntityFrameworkCore;
using SpareParts.Common.Auditing;
using SpareParts.Common.Branding;
using SpareParts.Common.Companies;
using SpareParts.Common.HelpRequests;
using SpareParts.Common.Resources;
using Volo.Abp.AuditLogging.EntityFrameworkCore;
using Volo.Abp.BackgroundJobs.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore.Modeling;
using Volo.Abp.PermissionManagement.EntityFrameworkCore;
using Volo.Abp.SettingManagement.EntityFrameworkCore;
using Volo.Abp.TenantManagement.EntityFrameworkCore;

namespace SpareParts.Common.EntityFrameworkCore;

public static class CommonDbContextModelCreatingExtensions
{
    public static void ConfigureCommon(this ModelBuilder modelBuilder)
    {
        Volo.Abp.Check.NotNull(modelBuilder, nameof(modelBuilder));

        modelBuilder.ConfigureAuditLogging();
        CommonEfCoreEntityExtensionMappings.Configure();
        modelBuilder.ConfigureTenantManagement();
        modelBuilder.ConfigureSettingManagement();
        modelBuilder.ConfigurePermissionManagement();
        modelBuilder.ConfigureBackgroundJobs();
        ConfigureCompany(modelBuilder);
        ConfigurePublicResource(modelBuilder);
        ConfigureHelpRequest(modelBuilder);
        ConfigureEntityChange(modelBuilder);
        ConfigureBranding(modelBuilder);
    }

    private static void ConfigureCompany(ModelBuilder builder)
    {
        builder.Entity<Company>(b =>
        {
            b.ToTable(CommonDbProperties.AdministrationDbTablePrefix + "Companies", CommonDbProperties.DbSchema);
            b.ConfigureByConvention();
            b.Property(c => c.Code)
                .HasMaxLength(CommonDbProperties.CompanyTableProperties.CodeMaxLength)
                .IsRequired();
            b.Property(c => c.Name)
                .HasMaxLength(CommonDbProperties.CompanyTableProperties.NameMaxLength)
                .IsRequired();
            b.Property(c => c.LegalName)
                .HasMaxLength(CommonDbProperties.CompanyTableProperties.LegalNameMaxLength)
                .IsRequired();
            b.Property(c => c.Type)
                .HasMaxLength(CommonDbProperties.CompanyTableProperties.TypeMaxLength)
                .HasConversion<string>()
                .IsRequired();
            b.HasIndex(p => new { p.Code, p.TenantId }).IsUnique();
            b.Property(c => c.TenantId).IsRequired();
        });
    }

    private static void ConfigurePublicResource(ModelBuilder builder)
    {
        builder.Entity<PublicResource>(b =>
        {
            b.ToTable(CommonDbProperties.DbTablePrefix + "PublicResources", CommonDbProperties.DbSchema);

            b.ConfigureByConvention();

            b.ApplyObjectExtensionMappings();

            b.Property(pr => pr.TenantId).IsRequired();
        });
    }

    private static void ConfigureHelpRequest(ModelBuilder builder)
    {
        builder.Entity<HelpRequest>(b =>
        {
            b.ToTable(CommonDbProperties.DbTablePrefix + "HelpRequests", CommonDbProperties.DbSchema);
            b.ConfigureByConvention();
            b.ApplyObjectExtensionMappings();

            b.Property(c => c.Code)
                .HasMaxLength(CommonDbProperties.HelpRequestTableProperties.CodeMaxLength)
                .IsRequired();
            b.Property(c => c.Title)
                .HasMaxLength(CommonDbProperties.HelpRequestTableProperties.TitleMaxLength)
                .IsRequired();
            b.Property(c => c.CountryCode)
                .HasMaxLength(CommonDbProperties.HelpRequestTableProperties.CountryCodeLength)
                .IsRequired();
            b.Property(c => c.Context)
                .HasMaxLength(CommonDbProperties.HelpRequestTableProperties.ContextMaxLength)
                .HasConversion<string>()
                .IsRequired();

            b.Property(helpRequest => helpRequest.CompanyId).IsRequired();
            b.HasOne<Company>()
                .WithMany()
                .HasForeignKey(i => i.CompanyId)
                .IsRequired();

            b.HasIndex(p => new { p.Code, p.TenantId }).IsUnique();
        });
    }

    private static void ConfigureEntityChange(ModelBuilder builder)
    {
        builder.Entity<EntityChange>().ToView(CommonDbProperties.DbTablePrefix + nameof(EntityChange) + 's').HasNoKey();
    }

    private static void ConfigureBranding(ModelBuilder builder)
    {
        builder.Entity<TenantBranding>(b =>
        {
            b.ToTable(CommonDbProperties.DbTablePrefix + "TenantBrandings", CommonDbProperties.DbSchema);

            b.OwnsOne(c => c.ApplicationName).ToJson();
            b.OwnsOne(c => c.PrimaryColor).ToJson();
            b.OwnsOne(c => c.AccentColor).ToJson();
            b.OwnsOne(c => c.Logo).ToJson();
            b.OwnsOne(c => c.SmallLogo).ToJson();
            b.OwnsOne(c => c.Favicon).ToJson();
            b.OwnsMany(c => c.WelcomePageTranslations).ToJson();
            b.OwnsMany(c => c.LoginPageTranslations).ToJson();

            b.ConfigureByConvention();

            b.Property(c => c.TenantId).IsRequired();
        });
    }
}