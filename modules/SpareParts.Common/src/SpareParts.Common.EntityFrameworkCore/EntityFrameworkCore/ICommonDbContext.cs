using Microsoft.EntityFrameworkCore;
using SpareParts.Common.Auditing;
using SpareParts.Common.Branding;
using SpareParts.Common.Companies;
using SpareParts.Common.HelpRequests;
using SpareParts.Common.Resources;
using Volo.Abp.EntityFrameworkCore;

namespace SpareParts.Common.EntityFrameworkCore;

public interface ICommonDbContext : IEfCoreDbContext
{
    DbSet<Company> Companies { get; set; }
    DbSet<EntityChange> EntityChanges { get; set; }
    DbSet<HelpRequest> HelpRequests { get; set; }
    DbSet<PublicResource> PublicResource { get; set; }
    DbSet<TenantBranding> Brandings { get; set; }
}