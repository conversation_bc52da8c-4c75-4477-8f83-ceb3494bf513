using Volo.Abp.ObjectExtending;
using Volo.Abp.TenantManagement;
using Volo.Abp.Threading;

namespace SpareParts.Common.EntityFrameworkCore;

public static class CommonEfCoreEntityExtensionMappings
{
    private static readonly OneTimeRunner OneTimeRunner = new();

    public static void Configure()
    {
        OneTimeRunner.Run(() =>
        {
            ObjectExtensionManager.Instance.MapEfCoreProperty<Tenant, bool>(CommonModuleExtensionConfigurator.IsEnabledPropertyName);

            ObjectExtensionManager.Instance
                .MapEfCoreProperty<Tenant, string>(
                    CommonModuleExtensionConfigurator.DisplayNamePropertyName,
                    (_, propertyBuilder) =>
                    {
                        propertyBuilder.HasMaxLength(125);
                        propertyBuilder.IsRequired();
                    }
                );
        });
    }
}