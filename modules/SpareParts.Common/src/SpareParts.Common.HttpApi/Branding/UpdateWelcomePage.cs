using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Common.Branding.Commands;
using SpareParts.Common.Branding.Dtos;
using SpareParts.Common.Branding.Dtos.Inputs;
using SpareParts.Common.Permissions;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Common.Branding;
public partial class BrandingController
{
    [HttpPut]
    [Authorize(CommonPermissions.Branding.Edit)]
    [Route("welcome-page")]
    [SwaggerOperation(Summary = "Update the welcome page branding",
        Description = "Update the welcome page branding with identified resources",
        OperationId = "Update_Welcome_Page",
        Tags = ["Branding"])]
    [Obsolete("use PUT api/common/branding/ instead")]
    public async Task<WelcomePageBrandingDto> UpdateWelcomePage([FromBody] UpdateWelcomePageBrandingDto updateWelcomePageBrandingDto)
    {
        UpdateWelcomePageBrandingCommand command = ObjectMapper.Map<UpdateWelcomePageBrandingDto, UpdateWelcomePageBrandingCommand>(updateWelcomePageBrandingDto);
        return await CommandSender.Send(command);
    }
}