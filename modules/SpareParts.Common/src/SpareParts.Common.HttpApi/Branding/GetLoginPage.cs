using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Common.Branding.Dtos;
using SpareParts.Common.Branding.Queries;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Common.Branding;
public partial class BrandingController
{
    [AllowAnonymous]
    [HttpGet]
    [Route("login-page")]
    [SwaggerOperation(Summary = "Get login page branding",
        Description = "Retrieves login page branding with its unique identifier.",
        OperationId = "Get_Login_Page",
        Tags = ["Branding"])]
    [Obsolete("use GET api/common/branding/ instead")]
    public async Task<LoginPageBrandingDto> GetLoginPage()
    {
        GetLoginPageBrandingQuery getLoginPageBrandingQuery = new();
        return await QuerySender.Send(getLoginPageBrandingQuery);
    }
}