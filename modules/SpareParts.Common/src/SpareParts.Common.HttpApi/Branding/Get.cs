using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Common.Branding.Dtos;
using SpareParts.Common.Branding.Queries;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Common.Branding;
public partial class BrandingController
{
    [AllowAnonymous]
    [HttpGet]
    [SwaggerOperation(Summary = "Get branding",
        Description = "Retrieves branding.",
        OperationId = "Get_Branding",
        Tags = ["Branding"])]
    public async Task<TenantBrandingDto> Get()
    {
        GetBrandingQuery getBrandingQuery = new();
        return await QuerySender.Send(getBrandingQuery);
    }
}