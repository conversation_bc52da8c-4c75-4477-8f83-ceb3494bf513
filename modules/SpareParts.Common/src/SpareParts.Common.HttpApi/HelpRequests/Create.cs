using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Common.HelpRequests.Commands;
using SpareParts.Common.HelpRequests.Dtos.Inputs;
using SpareParts.Common.Permissions;
using Swashbuckle.AspNetCore.Annotations;
using Volo.Abp.Content;

namespace SpareParts.Common.HelpRequests;
public partial class HelpRequestsController
{
    [HttpPost]
    [Authorize(CommonPermissions.HelpRequests.Create)]
    [Route("help-request")]
    [SwaggerOperation(Summary = "Create a new help request",
        Description = "Create a new help request",
        OperationId = "Create_Help_Request",
        Tags = ["HelpRequests"])]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [RequestSizeLimit(CommonConsts.SizeLimits.ImageSizeLimitInBytes + CommonConsts.SizeLimits.OverheadInBytes)]
    public async Task<CreatedResult> Create([FromForm] HelpRequestDto helpRequestDto, IFormFile? imageInput)
    {
        CreateHelpRequestCommand createHelpRequestCommand = ObjectMapper.Map<HelpRequestDto, CreateHelpRequestCommand>(helpRequestDto);

        if (imageInput != null)
        {
            Stream content = imageInput.OpenReadStream();
            createHelpRequestCommand.Attachment = new RemoteStreamContent(content, imageInput.FileName, imageInput.ContentType);
        }

        await CommandSender.Send(createHelpRequestCommand);
        return Created();
    }
}