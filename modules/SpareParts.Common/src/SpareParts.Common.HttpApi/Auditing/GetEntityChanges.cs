using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Common.Auditing.Dtos;
using SpareParts.Common.Auditing.Queries;
using Swashbuckle.AspNetCore.Annotations;
using Volo.Abp.Application.Dtos;

namespace SpareParts.Common.Auditing;

public partial class AuditingController
{
    [HttpGet]
    [Route("{id:guid}/changes")]
    [SwaggerOperation(Summary = "Gets the change history for a specific entity.",
        Description = "Retrieves a paged list of changes (audit trail) made to the entity identified by the given GUID. Supports pagination and filtering via query parameters.",
        OperationId = "Get_Entity_Changes",
        Tags = ["Auditing"])]
    public async Task<PagedResultDto<EntityChangeDto>> GetEntityChanges(Guid id, [FromQuery] LimitedPaginationFilterBase filter)
    {
        return await QuerySender.Send(new GetEntityChangesByIdQuery(id, filter));
    }
}