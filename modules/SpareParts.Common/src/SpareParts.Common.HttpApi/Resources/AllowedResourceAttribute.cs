using FluentValidation;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Content;
using Volo.Abp.Threading;

namespace SpareParts.Common.Resources;

[AttributeUsage(AttributeTargets.Property)]
public class AllowedResourceAttribute<TValidator> : ValidationAttribute
    where TValidator : AbstractValidator<RemoteStreamContent>
{
    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        if (value is not IFormFile file)
        {
            return new ValidationResult("Must be a file");
        }

        TValidator validator = validationContext.GetRequiredService<TValidator>();
        FluentValidation.Results.ValidationResult validationResult =
            AsyncHelper.RunSync(async () => await validator.ValidateAsync(new RemoteStreamContent(file.OpenReadStream(), file.FileName)));
        return validationResult.IsValid ?
            ValidationResult.Success :
            new ValidationResult(validationResult.Errors[0].ErrorMessage);
    }
}