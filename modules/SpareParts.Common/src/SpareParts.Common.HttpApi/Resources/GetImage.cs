using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Primitives;
using Microsoft.Net.Http.Headers;
using SpareParts.Common.PublicResources.Queries;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Common.Resources;

public partial class PublicResourceController
{
    [AllowAnonymous]
    [HttpGet]
    [Route("images/{id:guid}")]
    [SwaggerOperation(Summary = "Get a public image by id",
        Description = "Retrieves a public image by its unique identifier.",
        OperationId = "Get_Public_Image_Resource",
        Tags = ["PublicResources"])]
    [SwaggerResponse(StatusCodes.Status200OK, "Return a blob object representing the image.", typeof(FileStreamResult), "application/octet-stream")]
    public async Task<FileStreamResult> GetImage(Guid id)
    {
        GetPublicImageResourceQuery getPublicImageResourceQuery = new(id);
        VersionedRemoteStreamContent versionedRemoteStreamContent = await QuerySender.Send(getPublicImageResourceQuery);
        EntityTagHeaderValue etag = new(new StringSegment($"\"{versionedRemoteStreamContent.Hash}\""));
        return File(versionedRemoteStreamContent.GetStream(), versionedRemoteStreamContent.ContentType, versionedRemoteStreamContent.FileName, versionedRemoteStreamContent.LastModificationTime, etag);
    }
}