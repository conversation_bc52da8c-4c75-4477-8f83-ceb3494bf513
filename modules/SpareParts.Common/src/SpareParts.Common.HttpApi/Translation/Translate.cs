using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Common.Translator;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Common.Translation;

public partial class TranslationController
{
    private ITranslator Translator => LazyServiceProvider.LazyGetRequiredService<ITranslator>();

    [SwaggerOperation(
        Summary = "Translates text to one or more target languages.",
        Description = "Accepts a translation request and returns the translated text for the specified target languages. The request body should include the source text and target language codes.",
        OperationId = "Translation_Translate",
        Tags = ["Translation"]
    )]
    [HttpPost]
    [Route("translate")]
    public async Task<IEnumerable<TranslatedText>> Translate([FromBody] TranslateDto dto)
    {
        return await Translator.TranslateAsync(dto);
    }
}