using Volo.Abp.Application;
using Volo.Abp.Modularity;
using Volo.Abp.Authorization;
using Volo.Abp.SettingManagement;

namespace SpareParts.Common;

[DependsOn(
    typeof(CommonDomainSharedModule),
    typeof(AbpDddApplicationContractsModule),
    typeof(AbpAuthorizationModule),
    typeof(AbpSettingManagementApplicationContractsModule)
    )]
public class CommonApplicationContractsModule : AbpModule;
