// <auto-generated>
//     This code was generated by Refitter.
// </auto-generated>


using Refit;
using System.Collections.Generic;
using System.IO;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;

#nullable enable annotations

namespace SpareParts.Common.Generated.PublicIdentity
{
    /// <summary>Identity</summary>
    [System.CodeDom.Compiler.GeneratedCode("Refitter", "1.6.1.0")]
    public partial interface IIdentity
    {
        /// <summary>Download tenant favicon from its branding. The default asset is returned when branding is not found.</summary>
        /// <remarks>This is a public endpoint, no security rule applies</remarks>
        /// <param name="tenant">The current tenant id.</param>
        /// <returns>The requested favicon.</returns>
        /// <exception cref="ApiException">
        /// Thrown when the request returns a non-success status code:
        /// <list type="table">
        /// <listheader>
        /// <term>Status</term>
        /// <description>Description</description>
        /// </listheader>
        /// <item>
        /// <term>5XX</term>
        /// <description>An unexpected error has occurred. Error description may vary.</description>
        /// </item>
        /// </list>
        /// </exception>
        [Headers("Accept: application/octet-stream, application/json")]
        [Get("/identity/{tenant}/v2/public/branding/assets/favicon")]
        Task<Stream> DownloadTenantFaviconAsync(string tenant, CancellationToken cancellationToken = default);

        /// <summary>Download tenant logo from tenant's branding. The default asset is returned when branding is not found.</summary>
        /// <remarks>This is a public endpoint, no security rule applies</remarks>
        /// <param name="tenant">The current tenant id.</param>
        /// <returns>The requested logo.</returns>
        /// <exception cref="ApiException">
        /// Thrown when the request returns a non-success status code:
        /// <list type="table">
        /// <listheader>
        /// <term>Status</term>
        /// <description>Description</description>
        /// </listheader>
        /// <item>
        /// <term>5XX</term>
        /// <description>An unexpected error has occurred. Error description may vary.</description>
        /// </item>
        /// </list>
        /// </exception>
        [Headers("Accept: application/octet-stream, application/json")]
        [Get("/identity/{tenant}/v2/public/branding/assets/logo")]
        Task<Stream> DownloadTenantLogoAsync(string tenant, CancellationToken cancellationToken = default);

        /// <summary>Download tenant small logo from tenant's branding. The default asset is returned when branding is not found.</summary>
        /// <remarks>This is a public endpoint, no security rule applies</remarks>
        /// <param name="tenant">The current tenant id.</param>
        /// <returns>The requested small logo.</returns>
        /// <exception cref="ApiException">
        /// Thrown when the request returns a non-success status code:
        /// <list type="table">
        /// <listheader>
        /// <term>Status</term>
        /// <description>Description</description>
        /// </listheader>
        /// <item>
        /// <term>5XX</term>
        /// <description>An unexpected error has occurred. Error description may vary.</description>
        /// </item>
        /// </list>
        /// </exception>
        [Headers("Accept: application/octet-stream, application/json")]
        [Get("/identity/{tenant}/v2/public/branding/assets/small-logo")]
        Task<Stream> DownloadTenantSmallLogoAsync(string tenant, CancellationToken cancellationToken = default);

        /// <summary>Get branding color for a specific tenant.</summary>
        /// <remarks>This is a public endpoint, no security rule applies</remarks>
        /// <param name="tenant">The current tenant id.</param>
        /// <returns>The requested branding.</returns>
        /// <exception cref="ApiException">
        /// Thrown when the request returns a non-success status code:
        /// <list type="table">
        /// <listheader>
        /// <term>Status</term>
        /// <description>Description</description>
        /// </listheader>
        /// <item>
        /// <term>5XX</term>
        /// <description>An unexpected error has occurred. Error description may vary.</description>
        /// </item>
        /// </list>
        /// </exception>
        [Headers("Accept: application/json")]
        [Get("/identity/{tenant}/v2/public/branding/colors")]
        Task<TenantBrandingColorsDto> GetBrandingColorsAsync(string tenant, CancellationToken cancellationToken = default);
    }
}

//----------------------
// <auto-generated>
//     Generated using the NSwag toolchain v14.4.0.0 (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0)) (http://NSwag.org)
// </auto-generated>
//----------------------

#pragma warning disable 108 // Disable "CS0108 '{derivedDto}.ToJson()' hides inherited member '{dtoBase}.ToJson()'. Use the new keyword if hiding was intended."
#pragma warning disable 114 // Disable "CS0114 '{derivedDto}.RaisePropertyChanged(String)' hides inherited member 'dtoBase.RaisePropertyChanged(String)'. To make the current member override that implementation, add the override keyword. Otherwise add the new keyword."
#pragma warning disable 472 // Disable "CS0472 The result of the expression is always 'false' since a value of type 'Int32' is never equal to 'null' of type 'Int32?'
#pragma warning disable 612 // Disable "CS0612 '...' is obsolete"
#pragma warning disable 649 // Disable "CS0649 Field is never assigned to, and will always have its default value null"
#pragma warning disable 1573 // Disable "CS1573 Parameter '...' has no matching param tag in the XML comment for ...
#pragma warning disable 1591 // Disable "CS1591 Missing XML comment for publicly visible type or member ..."
#pragma warning disable 8073 // Disable "CS8073 The result of the expression is always 'false' since a value of type 'T' is never equal to 'null' of type 'T?'"
#pragma warning disable 3016 // Disable "CS3016 Arrays as attribute arguments is not CLS-compliant"
#pragma warning disable 8600 // Disable "CS8600 Converting null literal or possible null value to non-nullable type"
#pragma warning disable 8602 // Disable "CS8602 Dereference of a possibly null reference"
#pragma warning disable 8603 // Disable "CS8603 Possible null reference return"
#pragma warning disable 8604 // Disable "CS8604 Possible null reference argument for parameter"
#pragma warning disable 8625 // Disable "CS8625 Cannot convert null literal to non-nullable reference type"
#pragma warning disable 8765 // Disable "CS8765 Nullability of type of parameter doesn't match overridden member (possibly because of nullability attributes)."

namespace SpareParts.Common.Generated.PublicIdentity
{
    using System = global::System;

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "14.4.0.0 (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class TenantBrandingColorsDto
    {

        [JsonPropertyName("primaryColor")]
        public string PrimaryColor { get; set; }

        [JsonPropertyName("secondaryColor")]
        public string SecondaryColor { get; set; }

        private IDictionary<string, object> _additionalProperties;

        [JsonExtensionData]
        public IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }
}

#pragma warning restore  108
#pragma warning restore  114
#pragma warning restore  472
#pragma warning restore  612
#pragma warning restore 1573
#pragma warning restore 1591
#pragma warning restore 8073
#pragma warning restore 3016
#pragma warning restore 8600
#pragma warning restore 8602
#pragma warning restore 8603
#pragma warning restore 8604
#pragma warning restore 8625