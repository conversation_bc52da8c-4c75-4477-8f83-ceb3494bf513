using AutoFilterer;
using AutoFilterer.Attributes;
using System.Linq.Expressions;
using System.Reflection;

namespace SpareParts.Common.FilterAttributes;
public class ToLowerStartsWithComparisonAttribute : FilteringOptionsBaseAttribute
{
    public override Expression BuildExpression(ExpressionBuildContext context)
    {
        MethodInfo? startsWithMethod = typeof(string).GetMethod(nameof(string.StartsWith), types: [typeof(string)]);

        MethodInfo? toLowerMethod = typeof(string).GetMethod(nameof(string.ToLower), types: []);

        BinaryExpression comparison = Expression.Equal(
            Expression.Call(
                method: startsWithMethod!,
                instance: Expression.Call(method: toLowerMethod!, instance: Expression.Property(context.ExpressionBody, context.TargetProperty.Name)),
                arguments: Expression.Call(method: toLowerMethod!, instance: Expression.Constant(context.FilterObjectPropertyValue))),
            Expression.Constant(true));

        return comparison;
    }
}