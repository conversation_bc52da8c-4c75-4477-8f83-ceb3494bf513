using System.Text.Json.Serialization;

namespace SpareParts.Common.Branding.Dtos;

public record BrandingTranslationDto
{
    [JsonPropertyName("language")]
    public required string Language { get; init; }

    [JsonPropertyName("title")]
    public string? Title { get; init; }

    [JsonPropertyName("applicationName")]
    public string? ApplicationName { get; init; }

    [JsonPropertyName("text")]
    public string? Text { get; init; }
}