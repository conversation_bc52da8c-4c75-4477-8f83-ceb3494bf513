using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using Volo.Abp.GlobalFeatures;

namespace SpareParts.Common.Features;

[ExcludeFromCodeCoverage]
public static class GlobalModuleFeaturesDictionaryCommonExtensions
{
    public static GlobalCommonFeatures CommonFeatures(this GlobalModuleFeaturesDictionary modules)
    {
        GlobalCommonFeatures? globalCommonFeatures =
            modules.GetOrAdd(GlobalCommonFeatures.ModuleName, _ => new GlobalCommonFeatures(modules.FeatureManager)) as GlobalCommonFeatures;
        return globalCommonFeatures!;
    }
}