<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\..\..\common.props" />

  <PropertyGroup>
    <TargetFrameworks>net9.0</TargetFrameworks>
    <Nullable>enable</Nullable>
    <RootNamespace>SpareParts.Common</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Refit" />
    <PackageReference Include="AutoFilterer" />
    <PackageReference Include="Volo.Abp.Ddd.Application.Contracts" />
    <PackageReference Include="Volo.Abp.GlobalFeatures" />
    <PackageReference Include="Volo.Abp.Authorization" />
    <ProjectReference Include="..\..\..\..\framework\src\SpareParts.AbpMediatR\SpareParts.AbpMediatR.csproj" />
    <ProjectReference Include="..\SpareParts.Common.Domain.Shared\SpareParts.Common.Domain.Shared.csproj" />
    <PackageReference Include="Volo.Abp.SettingManagement.Application.Contracts" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Update="Fody">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
    </PackageReference>
  </ItemGroup>

</Project>
