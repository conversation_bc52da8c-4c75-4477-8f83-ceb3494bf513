using SpareParts.Common.Localization;
using SpareParts.Common.Permissions;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Localization;
using Volo.Abp.MultiTenancy;

namespace SpareParts.Common;

public class CommonPermissionDefinitionProvider : PermissionDefinitionProvider
{
    private const string PermissionCreateKey = "Permission:Create";
    private const string PermissionEditKey = "Permission:Edit";

    public override void Define(IPermissionDefinitionContext context)
    {
        PermissionGroupDefinition permissionGroup = context.AddGroup(CommonPermissions.GroupName, L("Permission:Core"));
        AddPublicResourcesPermissions(permissionGroup);
        AddBrandingPermissions(permissionGroup);
        AddAuditingPermissions(permissionGroup);
        AddSettingsPermissions(permissionGroup);
        AddHelpRequestsPermissions(permissionGroup);
        AddTranslationsPermissions(permissionGroup);
    }

    private static void AddPublicResourcesPermissions(PermissionGroupDefinition permissionGroup)
    {
        PermissionDefinition permissionDefinition = permissionGroup.AddPermission(CommonPermissions.PublicResources.Default,
            L("Permission:PublicResources"), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CommonPermissions.PublicResources.Create, L(PermissionCreateKey), MultiTenancySides.Tenant);
    }

    private static void AddBrandingPermissions(PermissionGroupDefinition permissionGroup)
    {
        PermissionDefinition permissionDefinition = permissionGroup.AddPermission(CommonPermissions.Branding.Default,
            L("Permission:Branding"), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CommonPermissions.Branding.Edit, L(PermissionCreateKey), MultiTenancySides.Tenant);
    }

    private static void AddAuditingPermissions(PermissionGroupDefinition permissionGroup)
    {
        permissionGroup.AddPermission(CommonPermissions.Auditing.Default,
            L("Permission:Auditing"), MultiTenancySides.Tenant);
    }

    private static void AddSettingsPermissions(PermissionGroupDefinition permissionGroup)
    {
        PermissionDefinition permissionDefinition = permissionGroup.AddPermission(CommonPermissions.Settings.Default, L("Permission:Settings"), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CommonPermissions.Settings.Edit, L(PermissionEditKey), MultiTenancySides.Tenant);
    }

    private static void AddHelpRequestsPermissions(PermissionGroupDefinition permissionGroup)
    {
        PermissionDefinition permissionDefinition = permissionGroup.AddPermission(CommonPermissions.HelpRequests.Default,
            L("Permission:HelpRequests"), MultiTenancySides.Tenant);
        permissionDefinition.AddChild(CommonPermissions.HelpRequests.Create, L(PermissionCreateKey), MultiTenancySides.Tenant);
    }

    private static void AddTranslationsPermissions(PermissionGroupDefinition permissionGroup)
    {
        permissionGroup.AddPermission(CommonPermissions.Translations.Default,
            L("Permission:Translations"), MultiTenancySides.Tenant);
    }

    private static LocalizableString L(string name)
    {
        return LocalizableString.Create<CommonResource>(name);
    }
}