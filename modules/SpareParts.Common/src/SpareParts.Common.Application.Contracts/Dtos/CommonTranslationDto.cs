using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;

namespace SpareParts.Common.Dtos;

public class CommonTranslationDto : CommonTranslationWithoutLanguageDto
{
    [MinLength(2)]
    public required string Language { get; init; }

    [SetsRequiredMembers]
    public CommonTranslationDto(string language, string label, string? description = null) : base(label, description)
    {
        Language = language;
    }

    public CommonTranslationDto()
    {
    }
}