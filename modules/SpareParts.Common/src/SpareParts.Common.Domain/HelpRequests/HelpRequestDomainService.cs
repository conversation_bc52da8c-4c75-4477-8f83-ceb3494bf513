using System;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;

namespace SpareParts.Common.HelpRequests;

public class HelpRequestDomainService : DomainService
{
    private IReadOnlyRepository<HelpRequest> HelpRequestRepository => LazyServiceProvider.LazyGetRequiredService<IReadOnlyRepository<HelpRequest>>();

    public virtual async Task<HelpRequest> CreateAsync(string code, string title, string countryCode, ContextType context, Guid? companyId)
    {
        await CheckCode(code);
        return new HelpRequest(GuidGenerator.Create(), code, title, countryCode, context, companyId);
    }

    private async Task CheckCode(string code)
    {
        HelpRequest? entity = await HelpRequestRepository.FirstOrDefaultAsync(c => c.Code == code);
        if (entity != null)
        {
            throw new BusinessException("com:duplicated-code", "duplicated help request code");
        }
    }
}