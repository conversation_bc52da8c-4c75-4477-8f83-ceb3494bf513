namespace SpareParts.Common;

public static class CommonDbProperties
{
    public static string DbTablePrefix { get; set; } = "Common";

    //TODO: delete when AdmCompanies will be renamed in CommonCompanies
    public static string AdministrationDbTablePrefix { get; set; } = "Adm";

    public static string? DbSchema { get; set; } = null;

    public const string ConnectionStringName = "Common";

    public static class CompanyTableProperties
    {
        public static int CodeMaxLength => 32;
        public static int NameMaxLength => 128;
        public static int LegalNameMaxLength => 128;
        public static int TypeMaxLength => 20;
    }

    public static class HelpRequestTableProperties
    {
        public static int CodeMaxLength => 10;
        public static int TitleMaxLength => 128;
        public static int CountryCodeLength => 2;
        public static int ContextMaxLength => 25;
    }
}
