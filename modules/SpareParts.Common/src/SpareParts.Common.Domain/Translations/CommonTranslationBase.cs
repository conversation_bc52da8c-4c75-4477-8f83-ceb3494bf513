using System;
using Volo.Abp.Domain.Entities;
using Volo.Abp.MultiTenancy;

namespace SpareParts.Common.Translations;

public abstract class CommonTranslationBase : Entity, ITranslation, IMultiTenant, IEquatable<CommonTranslationBase>
{
    public string Language { get; } = null!;
    public string? Description { get; protected set; }
    public Guid? TenantId { get; protected init; }

    protected CommonTranslationBase(string language, string? description = null)
    {
        Volo.Abp.Check.NotDefaultOrNull(TenantId, nameof(TenantId));
        Language = Volo.Abp.Check.NotNullOrWhiteSpace(language, nameof(language)).Trim().ToLower();
        ChangeDescription(description);
    }

    protected CommonTranslationBase()
    {
    }

    public void ChangeDescription(string? description)
    {
        Description = description.NotWhiteSpaceIfNotNull(nameof(description))?.Trim();
    }

    public virtual bool Equals(CommonTranslationBase? other)
    {
        if (other is null)
        {
            return false;
        }

        if (ReferenceEquals(this, other))
        {
            return true;
        }

        return Language == other.Language;
    }

    public override int GetHashCode()
    {
        return Language.GetHashCode();
    }

    public override bool Equals(object? obj)
    {
        return Equals(obj as CommonTranslationBase);
    }
}