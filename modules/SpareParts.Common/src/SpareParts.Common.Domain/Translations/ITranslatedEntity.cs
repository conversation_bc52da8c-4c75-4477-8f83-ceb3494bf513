using System.Collections.Generic;
using System.Linq;

namespace SpareParts.Common.Translations;

public interface ITranslatedEntity<T> where T : CommonTranslation
{
    IReadOnlySet<T> Translations { get; }
    void AddTranslation(T translation);
    void SetTranslations(HashSet<T> newTranslations)
    {
        HashSet<T> translations = (HashSet<T>)Translations;
        translations.RemoveWhere(t => !newTranslations.Contains(t));
        foreach (T newTranslation in newTranslations)
        {
            T? translation = translations.FirstOrDefault(t => t.Language == newTranslation.Language);
            if (translation != null)
            {
                translation.ChangeLabel(newTranslation.Label);
                translation.ChangeDescription(newTranslation.Description);
            }
            else
            {
                translations.Add(newTranslation);
            }
        }
    }
}