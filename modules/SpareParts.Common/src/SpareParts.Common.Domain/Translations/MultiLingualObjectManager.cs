using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Localization;
using Volo.Abp.Settings;

namespace SpareParts.Common.Translations;

public class MultiLingualObjectManager : ITransientDependency
{

    private readonly ISettingProvider _settingProvider;

    protected const int MaxCultureFallbackDepth = 5;

    public MultiLingualObjectManager(ISettingProvider settingProvider)
    {
        _settingProvider = settingProvider;
    }

    public virtual async Task<TProperty> GetTranslationAsync<TTranslation, TProperty, TOnlineTranslation>(
        IReadOnlySet<TTranslation> translations,
        Func<TTranslation, TProperty> translationPropertySelector,
        IReadOnlySet<TOnlineTranslation>? onlineTranslations = null,
        Func<TOnlineTranslation, TProperty?>? onlineTranslationPropertySelector = null,
        CultureInfo? cultureInfo = null)
        where TOnlineTranslation : class, ITranslation
        where TTranslation : class, ITranslation
    {
        cultureInfo ??= CultureInfo.CurrentUICulture;

        TProperty? translationForCurrentCulture = GetTranslationForCurrentCulture(translations, translationPropertySelector, cultureInfo,
            onlineTranslations, onlineTranslationPropertySelector);
        if (!EqualityComparer<TProperty>.Default.Equals(translationForCurrentCulture, default))
        {
            return translationForCurrentCulture!;
        }

        TProperty? translationForParentCulture = GetTranslationForParentCulture(translations, translationPropertySelector, cultureInfo,
            onlineTranslations, onlineTranslationPropertySelector);
        if (!EqualityComparer<TProperty>.Default.Equals(translationForParentCulture, default))
        {
            return translationForParentCulture!;
        }

        TProperty translationForDefaultCulture = await GetTranslationForDefaultCulture(translations, translationPropertySelector,
            onlineTranslations, onlineTranslationPropertySelector);
        return translationForDefaultCulture;
    }

    private static TProperty? GetTranslationForCurrentCulture<TTranslation, TProperty, TOnlineTranslation>(
        IReadOnlySet<TTranslation> translations,
        Func<TTranslation, TProperty> translationPropertySelector,
        CultureInfo cultureInfo,
        IReadOnlySet<TOnlineTranslation>? onlineTranslations = null,
        Func<TOnlineTranslation, TProperty?>? onlineTranslationPropertySelector = null)
        where TOnlineTranslation : class, ITranslation
        where TTranslation : class, ITranslation
    {
        string culture = cultureInfo.Name;
        TProperty? translatedValue;

        if (onlineTranslations != null && onlineTranslationPropertySelector != null)
        {
            TOnlineTranslation? onlineTranslation = onlineTranslations.FirstOrDefault(pt => pt.Language.Equals(culture, StringComparison.OrdinalIgnoreCase));

            if (onlineTranslation != null)
            {
                translatedValue = onlineTranslationPropertySelector(onlineTranslation);
                if (!EqualityComparer<TProperty>.Default.Equals(translatedValue, default))
                {
                    return translatedValue;
                }
            }
        }

        TTranslation? translation = translations.FirstOrDefault(pt => pt.Language.Equals(culture, StringComparison.OrdinalIgnoreCase));
        if (translation != null)
        {
            translatedValue = translationPropertySelector(translation);
            if (!EqualityComparer<TProperty>.Default.Equals(translatedValue, default))
            {
                return translatedValue;
            }
        }
        return default;
    }

    private static TProperty? GetTranslationForParentCulture<TTranslation, TProperty, TOnlineTranslation>(
        IReadOnlySet<TTranslation> translations,
        Func<TTranslation, TProperty> translationPropertySelector,
        CultureInfo cultureInfo,
        IReadOnlySet<TOnlineTranslation>? onlineTranslations = null,
        Func<TOnlineTranslation, TProperty?>? onlineTranslationPropertySelector = null)
        where TOnlineTranslation : class, ITranslation
        where TTranslation : class, ITranslation
    {
        TProperty? translatedValue;

        if (onlineTranslations != null && onlineTranslationPropertySelector != null)
        {
            translatedValue = GetTranslationBasedOnCulturalRecursive(cultureInfo.Parent, [.. onlineTranslations], 0, onlineTranslationPropertySelector);
            if (!EqualityComparer<TProperty>.Default.Equals(translatedValue, default))
            {
                return translatedValue;
            }
        }

        translatedValue = GetTranslationBasedOnCulturalRecursive(cultureInfo.Parent, [.. translations], 0, translationPropertySelector);
        if (!EqualityComparer<TProperty>.Default.Equals(translatedValue, default))
        {
            return translatedValue;
        }
        return default;
    }

    private async Task<TProperty> GetTranslationForDefaultCulture<TOnlineTranslation, TTranslation, TProperty>(
        IReadOnlySet<TTranslation> translations,
        Func<TTranslation, TProperty> translationPropertySelector,
        IReadOnlySet<TOnlineTranslation>? onlineTranslations = null,
        Func<TOnlineTranslation, TProperty?>? onlineTranslationPropertySelector = null)
        where TOnlineTranslation : class, ITranslation
        where TTranslation : class, ITranslation
    {
        string? defaultLanguage = await _settingProvider.GetOrNullAsync(LocalizationSettingNames.DefaultLanguage);

        TProperty? translatedValue;

        if (onlineTranslations != null && onlineTranslationPropertySelector != null)
        {
            TOnlineTranslation? onlineTranslation = onlineTranslations.FirstOrDefault(pt => pt.Language.Equals(defaultLanguage, StringComparison.OrdinalIgnoreCase));
            if (onlineTranslation != null)
            {
                translatedValue = onlineTranslationPropertySelector(onlineTranslation);
                if (!EqualityComparer<TProperty>.Default.Equals(translatedValue, default))
                {
                    return translatedValue!;
                }
            }
        }

        TTranslation? translation = translations.FirstOrDefault(pt => pt.Language.Equals(defaultLanguage, StringComparison.OrdinalIgnoreCase));
        if (translation != null)
        {
            translatedValue = translationPropertySelector(translation);
            if (!EqualityComparer<TProperty>.Default.Equals(translatedValue, default))
            {
                return translatedValue;
            }
        }

        if (onlineTranslations != null && onlineTranslationPropertySelector != null)
        {
            TOnlineTranslation? onlineTranslation = onlineTranslations.FirstOrDefault();
            if (onlineTranslation != null)
            {
                translatedValue = onlineTranslationPropertySelector(onlineTranslation);
                if (!EqualityComparer<TProperty>.Default.Equals(translatedValue, default))
                {
                    return translatedValue!;
                }
            }
        }

        translation = translations.First();
        translatedValue = translationPropertySelector(translation);
        return translatedValue;
    }

    private static TProperty? GetTranslationBasedOnCulturalRecursive<TTranslation, TProperty>(
        CultureInfo? culture,
        List<TTranslation> translations,
        int currentDepth,
        Func<TTranslation, TProperty> propertySelector)
        where TTranslation : class, ITranslation
    {
        if (culture == null || culture.Name.IsNullOrWhiteSpace() || currentDepth > MaxCultureFallbackDepth)
        {
            return default;
        }

        TTranslation? translation = translations.FirstOrDefault(pt => pt.Language.Equals(culture.Name, StringComparison.OrdinalIgnoreCase));
        TProperty? translatedValue = default;
        if (translation != null)
        {
            translatedValue = propertySelector(translation);
        }

        if (!EqualityComparer<TProperty>.Default.Equals(translatedValue, default))
        {
            return translatedValue;
        }

        return GetTranslationBasedOnCulturalRecursive(culture.Parent, translations, currentDepth + 1, propertySelector);
    }
}