namespace SpareParts.Common.Translations;

public abstract class CommonOnlineTranslation : CommonTranslationBase
{
    public string? Label { get; private set; }

    protected CommonOnlineTranslation()
    {
    }

    protected CommonOnlineTranslation(string language, string? label = null, string? description = null) : base(language, description)
    {
        ChangeLabel(label);
    }

    public void ChangeLabel(string? label)
    {
        Label = label.NotWhiteSpaceIfNotNull(nameof(label))?.Trim();
    }

    public void ResetLabel()
    {
        Label = null;
    }

    public void ResetDescription()
    {
        Description = null;
    }
}