using Volo.Abp.BackgroundJobs;
using Volo.Abp.BlobStoring;
using Volo.Abp.Domain;
using Volo.Abp.FluentValidation;
using Volo.Abp.Localization;
using Volo.Abp.Modularity;
using Volo.Abp.SettingManagement;
using Volo.Abp.TenantManagement;
using Volo.Abp.TextTemplating.Scriban;
using Volo.Abp.Timing;
using Volo.Abp.VirtualFileSystem;

namespace SpareParts.Common;

[DependsOn(
    typeof(AbpDddDomainModule),
    typeof(CommonDomainSharedModule),
    typeof(AbpTextTemplatingScribanModule),
    typeof(AbpSettingManagementDomainModule),
    typeof(AbpBackgroundJobsDomainModule),
    typeof(AbpTenantManagementDomainModule),
    typeof(AbpBlobStoringModule),
    typeof(AbpVirtualFileSystemModule),
    typeof(AbpFluentValidationModule)
)]
public class CommonDomainModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        Configure<AbpLocalizationOptions>(options =>
        {
            options.Languages.Add(new LanguageInfo("en", "en", "English"));
            options.Languages.Add(new LanguageInfo("fr", "fr", "Français"));
            options.Languages.Add(new LanguageInfo("de", "de", "Deutsch"));
        });
        Configure<AbpClockOptions>(options => { options.Kind = CommonConsts.AbpClockOptions.Kind; });
        Configure<AbpVirtualFileSystemOptions>(options =>
        {
            options.FileSets.AddEmbedded<CommonDomainModule>("SpareParts.Common");
        });
    }
}