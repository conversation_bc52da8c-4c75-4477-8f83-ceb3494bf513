using Microsoft.Extensions.DependencyInjection;
using SpareParts.Common.Translations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;

namespace SpareParts.Common.Helpers;

public class EntityTranslationHelper : ITransientDependency
{
    public IAbpLazyServiceProvider LazyServiceProvider { get; set; } = default!;

    public static IEnumerable<Type> GetTranslatedEntityTypes()
    {
        IEnumerable<Type> types = AppDomain.CurrentDomain.GetAssemblies().Where(x =>
        {
            string? name = x.GetName().Name;
            return name != null && name.StartsWith("SpareParts");
        }).SelectMany(x => x.GetTypes());

        return types
            .Where(t => t is { IsInterface: false, IsAbstract: false })
            .Where(t => t.GetInterfaces().Any(i => i.IsGenericType && i.GetGenericTypeDefinition() == typeof(ITranslatedEntity<>)));
    }

    public virtual async Task<int> CountEntitiesWithoutTranslation(Type entityType, string newLanguage, CancellationToken cancellationToken)
    {
        object repository = LazyServiceProvider.GetRequiredService(typeof(IReadOnlyRepository<>).MakeGenericType(entityType));

        bool hasOnlineTranslations = entityType.GetInterfaces().Any(i => i.IsGenericType && i.GetGenericTypeDefinition() == typeof(IOnlineTranslatedEntity<>));

        LambdaExpression lambda;
        if (hasOnlineTranslations)
        {
            lambda = GetLambdaExpressionWithOnlineTranslations(entityType, newLanguage);
        }
        else
        {
            lambda = GetLambdaExpression(entityType, newLanguage);
        }

        string countAsyncMethodName = nameof(RepositoryAsyncExtensions.CountAsync);
        MethodInfo countAsyncMethod = typeof(RepositoryAsyncExtensions)
            .GetMethods().First(m => m.Name == countAsyncMethodName && m.GetParameters().Length == 3)
            .MakeGenericMethod(entityType);

        Task<int> task = (Task<int>)countAsyncMethod.Invoke(null, [repository, lambda, cancellationToken])!;
        return await task;
    }

    private static LambdaExpression GetLambdaExpression(Type entityType, string newLanguage)
    {
        string languagePropertyName = nameof(ITranslation.Language);
        string translationPropertyName = nameof(ITranslatedEntity<CommonTranslation>.Translations);
        ParameterExpression entityParameter = Expression.Parameter(entityType, "x");

        MethodCallExpression allExpression = GetTranslationExpression(entityType, newLanguage, entityParameter, languagePropertyName, translationPropertyName);
        return Expression.Lambda(allExpression, entityParameter);
    }

    private static LambdaExpression GetLambdaExpressionWithOnlineTranslations(Type entityType, string newLanguage)
    {
        string languagePropertyName = nameof(ITranslation.Language);
        ParameterExpression entityParameter = Expression.Parameter(entityType, "x");

        string translationPropertyName = nameof(ITranslatedEntity<CommonTranslation>.Translations);
        MethodCallExpression allExpression = GetTranslationExpression(entityType, newLanguage, entityParameter, languagePropertyName, translationPropertyName);

        string onlineTranslationPropertyName = nameof(IOnlineTranslatedEntity<CommonOnlineTranslation>.OnlineTranslations);
        MethodCallExpression allOnlineExpression = GetTranslationExpression(entityType, newLanguage, entityParameter, languagePropertyName, onlineTranslationPropertyName);

        BinaryExpression combinedExpression = Expression.AndAlso(allExpression, allOnlineExpression);
        return Expression.Lambda(combinedExpression, entityParameter);
    }

    private static MethodCallExpression GetTranslationExpression(Type entityType, string newLanguage,
        ParameterExpression entityParameter, string languagePropertyName, string translationPropertyName)
    {
        PropertyInfo translationsProperty = entityType.GetProperty(translationPropertyName)!;
        Type translationType = translationsProperty.PropertyType.GenericTypeArguments[0];

        MemberExpression translationsAccess = Expression.Property(entityParameter, translationsProperty);

        ParameterExpression translationParameter = Expression.Parameter(translationType, "translation");

        PropertyInfo languageProperty = translationType.GetProperty(languagePropertyName)!;

        MemberExpression languageAccess = Expression.Property(translationParameter, languageProperty);
        BinaryExpression languageComparison = Expression.NotEqual(languageAccess, Expression.Constant(newLanguage));
        LambdaExpression translationLambda = Expression.Lambda(languageComparison, translationParameter);

        MethodInfo allMethod = typeof(Enumerable)
            .GetMethods()
            .First(m => m.Name == "All" && m.GetParameters().Length == 2)
            .MakeGenericMethod(translationType);

        MethodCallExpression allExpression = Expression.Call(null, allMethod, translationsAccess, translationLambda);
        return allExpression;
    }
}