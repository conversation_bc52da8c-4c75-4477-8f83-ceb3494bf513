using System.Linq.Expressions;
using System;
using Volo.Abp.DependencyInjection;
using System.Collections.Generic;

namespace SpareParts.Common.Helpers;

public interface IExpressionCreator : ITransientDependency
{
    Expression<Func<T, bool>> GetEqualsExpression<T, TV>(string propertyName, TV value);
    Expression<Func<T, bool>> GetContainsExpression<T, TV>(string propertyName, IEnumerable<TV> values);
    Expression<Func<T, TV>> GetPropertyExpression<T, TV>(string propertyName);
}