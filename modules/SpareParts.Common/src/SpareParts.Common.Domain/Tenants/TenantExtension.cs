using Volo.Abp.Data;
using Volo.Abp.TenantManagement;

namespace SpareParts.Common.Tenants;

public static class TenantExtension
{
    public static void Disable(this Tenant tenant)
    {
        tenant.SetProperty(CommonModuleExtensionConfigurator.IsEnabledPropertyName, false);
    }

    public static bool IsEnabled(this Tenant tenant)
    {
        return Volo.Abp.Check.NotNull(tenant.GetProperty<bool?>(CommonModuleExtensionConfigurator.IsEnabledPropertyName),
            CommonModuleExtensionConfigurator.IsEnabledPropertyName)!.Value;
    }

    public static void Enable(this Tenant tenant)
    {
        tenant.SetProperty(CommonModuleExtensionConfigurator.IsEnabledPropertyName, true);
    }

    public static void SetDisplayName(this Tenant tenant, string displayName)
    {
        tenant.SetProperty(CommonModuleExtensionConfigurator.DisplayNamePropertyName, displayName);
    }

    public static string DisplayName(this Tenant tenant)
    {
        return tenant.GetProperty<string>(CommonModuleExtensionConfigurator.DisplayNamePropertyName)!;
    }
}