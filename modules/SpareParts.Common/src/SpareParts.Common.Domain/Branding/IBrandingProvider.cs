using SpareParts.Common.Resources;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.TenantManagement;

namespace SpareParts.Common.Branding;

public interface IBrandingProvider
{
    Task<TenantBranding> CreateBrandingFromIdentityAsync(Tenant tenant);
    Task<TenantBranding> UpdateBrandingFromIdentityAsync(Tenant tenant, TenantBranding branding);
    Task<Dictionary<string, string>> GetColorsFromIdentityAsync(string tenantName, CancellationToken cancellationToken = default);

    Task<Stream> GetLogoContent(TenantBranding branding, CancellationToken cancellationToken = default);
    Task<PublicResource> GetLogoResource(TenantBranding branding, CancellationToken cancellationToken = default);
}