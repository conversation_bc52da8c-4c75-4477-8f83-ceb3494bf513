using JetBrains.Annotations;
using System;
using System.Collections.Generic;
using Volo.Abp.Auditing;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.MultiTenancy;

namespace SpareParts.Common.Branding;

[Audited]
public sealed class TenantBranding : AuditedEntity<Guid>, IMultiTenant
{
    public Guid? TenantId { get; private set; }

    [StringBrandingValidation(MaxLength = 50)]
    public BrandingProperty<string> ApplicationName { get; private set; } = new();

    [ColorBrandingValidation]
    public BrandingProperty<string> PrimaryColor { get; private set; } = new();

    [ColorBrandingValidation]
    public BrandingProperty<string> AccentColor { get; private set; } = new();

    public BrandingProperty<Guid?> Logo { get; private set; } = new();

    public BrandingProperty<Guid?> SmallLogo { get; private set; } = new();

    public BrandingProperty<Guid?> Favicon { get; private set; } = new();

    public Guid? WelcomePageImageId { get; internal set; } = null;

    public Guid? LoginPageImageId { get; internal set; } = null;

    public List<TenantBrandingTranslation>? WelcomePageTranslations { get; set; } = null;

    public List<TenantBrandingTranslation>? LoginPageTranslations { get; set; } = null;

    public bool WelcomePageHideTenantName { get; set; } = default;

    public bool LoginPageHideTenantName { get; set; } = default;

    [UsedImplicitly]
    private TenantBranding()
    {

    }

    internal TenantBranding(
        Guid id, string applicationName,
        string defaultPrimaryColor, string defaultSecondaryColor,
        Guid defaultLogo, Guid defaultSmallLogo, Guid defaultFavicon)
        : base(id)
    {
        Volo.Abp.Check.NotDefaultOrNull(TenantId, nameof(TenantId));

        ApplicationName.Default = applicationName;
        PrimaryColor.Default = defaultPrimaryColor;
        AccentColor.Default = defaultSecondaryColor;
        Logo.Default = defaultLogo;
        SmallLogo.Default = defaultSmallLogo;
        Favicon.Default = defaultFavicon;
    }
}
