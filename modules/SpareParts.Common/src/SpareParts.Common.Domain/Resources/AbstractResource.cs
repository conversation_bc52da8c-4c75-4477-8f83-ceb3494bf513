using JetBrains.Annotations;
using System;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.MultiTenancy;

namespace SpareParts.Common.Resources;

public abstract class AbstractResource : AuditedEntity<Guid>, IMultiTenant
{
    public string FileName { get; private set; } = null!;
    public Guid? TenantId { get; protected init; }
    public string? Hash { get; private set; }

    [UsedImplicitly]
    protected AbstractResource()
    {
    }

    protected AbstractResource(Guid id, string fileName, string hash) : base(id)
    {
        Volo.Abp.Check.NotDefaultOrNull(TenantId, nameof(TenantId));
        ChangeFileName(fileName);
        ChangeHash(hash);
    }

    public void ChangeHash(string hash)
    {
        Hash = Volo.Abp.Check.NotNullOrWhiteSpace(hash, nameof(hash)).Trim();
    }

    internal bool ChangeFileName(string fileName)
    {
        string newFileName = Volo.Abp.Check.NotNullOrWhiteSpace(fileName, nameof(fileName)).Trim();
        if (string.Equals(FileName, newFileName))
        {
            return false;
        }
        FileName = newFileName;
        return true;
    }
}