using Volo.Abp.Application;
using Volo.Abp.Modularity;
using Volo.Abp.Authorization;
using SpareParts.Common;

namespace SpareParts.Administration;

[DependsOn(
    typeof(AdministrationDomainSharedModule),
    typeof(AbpDddApplicationContractsModule),
    typeof(AbpAuthorizationModule),
    typeof(CommonApplicationContractsModule)
    )]
public class AdministrationApplicationContractsModule : AbpModule
{

}
