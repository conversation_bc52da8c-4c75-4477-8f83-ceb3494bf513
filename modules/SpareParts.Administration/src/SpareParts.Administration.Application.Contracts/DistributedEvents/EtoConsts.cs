namespace SpareParts.Administration.DistributedEvents;

public static class EtoConsts
{
    public const string ProductLine = "product-line";

    #region [ProductLine consts]
    public static class ProductLinePayloadBody
    {
        public const string ProductLineName = "productLineId";
        public const string AdminUser = "invitationRecipient";
    }

    public static class AdminUserPayloadBody
    {
        public const string Email = "email";
        public const string Language = "language";
    }
    #endregion

    public static class Eto
    {
        public const string Payload = "payload";
        public const string Tenant = "tenant";
        public const string TenantName = "id";
        public const string TenantDisplayName = "label";
    }

    public static class EventNames
    {
        public const string Subscribed = "subscribed";
        public const string Unsubscribed = "unsubscribed";
        public const string Updated = "updated";
    }
}