using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace SpareParts.Administration.DistributedEvents.Etos;

public record AdminUserPayload
{
    [DataMember(Name = EtoConsts.AdminUserPayloadBody.Email)]
    [JsonPropertyName(EtoConsts.AdminUserPayloadBody.Email)]
    public required string Email { get; set; }
    [DataMember(Name = EtoConsts.AdminUserPayloadBody.Language)]
    [JsonPropertyName(EtoConsts.AdminUserPayloadBody.Language)]
    public required string Language { get; set; }
}