using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace SpareParts.Administration.DistributedEvents.Etos;

public record TenantEto
{
    [DataMember(Name = EtoConsts.Eto.TenantName)]
    [JsonPropertyName(EtoConsts.Eto.TenantName)]
    public required string Name { get; set; }

    [DataMember(Name = EtoConsts.Eto.TenantDisplayName)]
    [JsonPropertyName(EtoConsts.Eto.TenantDisplayName)]
    public required string DisplayName { get; set; }
}