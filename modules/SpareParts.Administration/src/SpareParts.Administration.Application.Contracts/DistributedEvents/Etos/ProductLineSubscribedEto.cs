using System;
using System.Runtime.Serialization;
using System.Text.Json.Serialization;
using Volo.Abp.EventBus;

namespace SpareParts.Administration.DistributedEvents.Etos;

[Serializable]
[EventName($"{EtoConsts.ProductLine}_{EtoConsts.EventNames.Subscribed}")]
public record ProductLineSubscribedEto
{
    [DataMember(Name = EtoConsts.Eto.Payload)]
    [JsonPropertyName(EtoConsts.Eto.Payload)]
    public required SubscribedProductLinePayload Payload { get; set; }
};