using AutoFilterer.Attributes;
using AutoFilterer.Enums;
using SpareParts.Administration.Companies.Dtos;
using SpareParts.Common;
using SpareParts.Common.Companies;

namespace SpareParts.Administration.Companies.QueryFilters;
[PossibleSortings("code", "name", "legalName", "type", "lastModificationTime", "creationTime")]
public class CompanyPaginationFilter : LimitedPaginationFilterBase
{
    [CompareTo(nameof(CompanyDto.Code), nameof(CompanyDto.Name), nameof(CompanyDto.LegalName))]
    [StringFilterOptions(StringFilterOption.Contains)]
    public string? Text { get; set; }

    public CompanyType? Type { get; set; }
}
