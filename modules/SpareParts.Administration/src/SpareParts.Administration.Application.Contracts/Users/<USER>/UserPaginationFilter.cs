using AutoFilterer.Attributes;
using AutoFilterer.Enums;
using SpareParts.Administration.Users.Dtos;
using SpareParts.Common;
using SpareParts.Common.Companies;
using System;
using System.Collections.Generic;

namespace SpareParts.Administration.Users.QueryFilters;

[PossibleSortings("firstName", "lastName", "email")]
public class UserPaginationFilter : LimitedPaginationFilterBase
{
    [StringFilterOptions(StringFilterOption.Contains)]
    [CompareTo(nameof(UserDto.Email), nameof(UserDto.FirstName), nameof(UserDto.LastName))]
    public string? Email { get; set; }

    public Guid? CompanyId { get; set; }

    [IgnoreFilter]
    public CompanyType? Type { get; set; }

    [IgnoreFilter]
    public List<string>? Roles { get; set; }
}
