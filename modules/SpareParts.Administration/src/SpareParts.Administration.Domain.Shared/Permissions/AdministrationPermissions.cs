using System.Collections.Generic;

namespace SpareParts.Administration.Permissions;
public static class AdministrationPermissions
{
    public const string GroupName = "Administration";

    public static class Users
    {
        public const string Default = GroupName + "." + nameof(Users);
        public const string Edit = Default + "." + nameof(Edit);
        public const string Delete = Default + "." + nameof(Delete);
        public const string Stats = Default + "." + nameof(Stats);
    }

    public static class Roles
    {
        public const string Default = GroupName + "." + nameof(Roles);
    }

    public static class Invitations
    {
        public const string Default = GroupName + "." + nameof(Invitations);
        public const string Create = Default + "." + nameof(Create);
        public const string Cancel = Default + "." + nameof(Cancel);
    }

    public static class Companies
    {
        public const string Default = GroupName + "." + nameof(Companies);
        public const string Create = Default + "." + nameof(Create);
        public const string Edit = Default + "." + nameof(Edit);
        public const string Delete = Default + "." + nameof(Delete);
        public const string Stats = Default + "." + nameof(Stats);
    }

    public static List<string> GetContentViewerPermissions()
    {
        return [Companies.Stats, Users.Stats];
    }

    public static List<string> GetContentManagerPermissions()
    {
        return [Companies.Default, Companies.Stats, Users.Stats];
    }

    public static List<string> GetAccountManagerPermissions()
    {
        return
        [
            Users.Default, Users.Edit, Users.Delete, Users.Stats,
            Invitations.Default, Invitations.Create, Invitations.Cancel,
            Companies.Default, Companies.Create, Companies.Edit, Companies.Delete, Companies.Stats,
            Roles.Default
        ];
    }

    public static List<string> GetConfigurationAdministratorPermissions()
    {
        return [Companies.Stats, Users.Stats];
    }
}
