using System.Threading.Tasks;
using SpareParts.AbpMediatR.Notifications;
using SpareParts.Administration.BackgroundJobs.Args;
using SpareParts.Administration.Companies.Notifications;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.DependencyInjection;

namespace SpareParts.Administration.BackgroundJobs;
public class DeleteCompanyJob : AsyncBackgroundJob<DeleteCompanyJobArgs>, ITransientDependency
{
    private readonly INotificationPublisher _publisher;

    public DeleteCompanyJob(INotificationPublisher publisher)
    {
        _publisher = publisher;
    }

    public override async Task ExecuteAsync(DeleteCompanyJobArgs args)
    {
        await _publisher.Publish(new CompanyDeletedNotification(args.TenantId, args.TenantName, args.CompanyId));
    }
}
