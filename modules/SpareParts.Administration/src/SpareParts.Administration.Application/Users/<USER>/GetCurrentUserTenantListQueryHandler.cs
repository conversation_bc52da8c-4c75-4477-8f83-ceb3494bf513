using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using SpareParts.AbpMediatR.Handlers;
using SpareParts.Administration.Tenants.Dtos;
using SpareParts.Common.Tenants;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using Volo.Abp.MultiTenancy;
using Volo.Abp.TenantManagement;

namespace SpareParts.Administration.Users.Queries;

public class GetCurrentUserTenantListQueryHandler :UserRequestHandlerBase,  IQueryHandler<GetCurrentUserTenantListQuery,List<TenantResult>>
{
    private IRepository<Tenant,Guid> TenantRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Tenant,Guid>>();

    public async Task<List<TenantResult>> Handle(GetCurrentUserTenantListQuery request, CancellationToken cancellationToken)
    {
        using IDisposable disable = DataFilter.Disable<IMultiTenant>();
        IQueryable<Guid?> userTenantIdsQuery = (await UserRepository.GetQueryableAsync())
            .Where(ExpressionCreator.GetEqualsExpression<IdentityUser, Guid>(
                AdministrationModuleExtensionConfigurator.ExternalIdPropertyName, CurrentUser.Id!.Value))
            .Select(u => u.TenantId);

        List<Guid?> tenantIds =
            await UserRepository.AsyncExecuter.ToListAsync(userTenantIdsQuery, cancellationToken);

        IQueryable<TenantResult> userTenantsQuery = (await TenantRepository.GetQueryableAsync())
            .Where(t => tenantIds.Contains(t.Id))
            .Select(t => new TenantResult(t.Name, t.DisplayName()));

        List<TenantResult> tenantResults =
            await TenantRepository.AsyncExecuter.ToListAsync(userTenantsQuery, cancellationToken);
        return tenantResults;
    }
}