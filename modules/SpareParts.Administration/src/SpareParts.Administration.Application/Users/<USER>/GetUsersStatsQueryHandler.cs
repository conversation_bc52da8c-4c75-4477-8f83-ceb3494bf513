using SpareParts.AbpMediatR.Handlers;
using SpareParts.Administration.Users.Dtos;
using SpareParts.Common;
using System.Threading;
using System.Threading.Tasks;

namespace SpareParts.Administration.Users.Queries;

public class GetUsersStatsQueryHandler : UserRequestHandlerBase, IQueryHandler<GetUsersStatsQuery, UsersStatsDto>
{
    public async Task<UsersStatsDto> Handle(GetUsersStatsQuery request, CancellationToken cancellationToken)
    {
        CurrentUser.ThrowAuthorizationExceptionIfUserDoesNotBelongToInternalCompany();

        long usersCount = await UserReadOnlyRepository.GetCountAsync(cancellationToken:cancellationToken);
        return new UsersStatsDto { Count = usersCount };
    }
}