using SpareParts.Common.Companies;
using SpareParts.Common.Helpers;
using System;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;

namespace SpareParts.Administration.Users;
/// <summary>
/// Abstract base class for handling User-related commands and queries.
/// </summary>
/// <remarks>
/// This abstract class serves as a foundation for handling various commands and queries related to companies within the system.
/// It encapsulates common dependencies required for User operations and provides a structure for implementing specific functionality.
/// All User commands and queries should inherit from this base class to leverage common dependencies and cross-cutting concerns.
/// </remarks>
public abstract class UserRequestHandlerBase : AdministrationRequestBase
{
    protected UserDomainService UserDomainService => LazyServiceProvider.LazyGetRequiredService<UserDomainService>();
    protected IdentityUserManager UserManager => LazyServiceProvider.LazyGetRequiredService<IdentityUserManager>();
    protected IRepository<IdentityUser, Guid> UserRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<IdentityUser, Guid>>();
    protected IReadOnlyRepository<IdentityUser, Guid> UserReadOnlyRepository => LazyServiceProvider.LazyGetRequiredService<IReadOnlyRepository<IdentityUser, Guid>>();
    protected IRepository<Company, Guid> CompanyRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Company, Guid>>();
    protected IReadOnlyRepository<Company, Guid> CompanyReadOnlyRepository => LazyServiceProvider.LazyGetRequiredService<IReadOnlyRepository<Company, Guid>>();
    protected IRepository<IdentityRole> RoleRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<IdentityRole>>();
    protected IExpressionCreator ExpressionCreator => LazyServiceProvider.LazyGetRequiredService<IExpressionCreator>();
}