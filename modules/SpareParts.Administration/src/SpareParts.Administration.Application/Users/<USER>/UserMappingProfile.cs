using AutoMapper;
using SpareParts.Administration.Users.Dtos;
using Volo.Abp.Identity;

namespace SpareParts.Administration.Users.Mapping;
public class UserMappingProfile : Profile
{
    public UserMappingProfile()
    {
        CreateMap<IdentityUser, UserCreatedDto>()
            .ForMember(dest => dest.ExternalId, opt => opt.MapFrom(src => src.GetExternalId()))
            .ForMember(dest => dest.CompanyId, opt => opt.MapFrom(src => src.GetCompanyId()))
            .ForMember(dest => dest.FirstName,
            opt => opt.MapFrom(src => src.Name))
            .ForMember(dest => dest.LastName,
            opt => opt.MapFrom(src => src.Surname));
    }
}
