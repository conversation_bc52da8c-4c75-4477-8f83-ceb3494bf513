using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using SpareParts.AbpMediatR.Notifications;
using SpareParts.Administration.Companies.Notifications;
using SpareParts.Administration.Generated.Identity;
using SpareParts.Common;
using SpareParts.Common.Helpers;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using Volo.Abp.MultiTenancy;

namespace SpareParts.Administration.Users.NotificationHandlers;

public class CompanyDeleteUsersNotificationHandler : INotificationHandler<CompanyDeletedNotification>
{
    private readonly ICurrentTenant _currentTenant;
    private readonly IRepository<IdentityUser> _userRepository;
    private readonly IIdentity _identityService;
    private readonly IExpressionCreator _expressionCreator;

    public CompanyDeleteUsersNotificationHandler(ICurrentTenant currentTenant, IRepository<IdentityUser> userRepository, IIdentity identityService, IExpressionCreator expressionCreator)
    {
        _currentTenant = currentTenant;
        _userRepository = userRepository;
        _identityService = identityService;
        _expressionCreator = expressionCreator;
    }

    public virtual async Task Handle(CompanyDeletedNotification notification, CancellationToken cancellationToken)
    {
        using (_currentTenant.Change(notification.TenantId))
        {
            List<IdentityUser> users = await _userRepository.GetListAsync(_expressionCreator.GetEqualsExpression<IdentityUser, Guid>(AdministrationModuleExtensionConfigurator.CompanyIdPropertyName, notification.CompanyId), cancellationToken: cancellationToken);
            foreach (IdentityUser user in users)
            {
                await _identityService.UpdateRolesAsync(notification.TenantName,
                    CommonConsts.ProductLineName,
                    user.GetExternalId().ToString(), [], cancellationToken);
            }

            await _userRepository.DeleteManyAsync(users, cancellationToken: cancellationToken);
        }
    }
}