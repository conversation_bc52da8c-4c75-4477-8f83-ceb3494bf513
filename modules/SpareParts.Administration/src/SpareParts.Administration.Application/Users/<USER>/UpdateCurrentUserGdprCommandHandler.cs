using SpareParts.AbpMediatR.Handlers;
using SpareParts.Administration.Users.Commands;
using SpareParts.Administration.Users.Dtos;
using System;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Identity;

namespace SpareParts.Administration.Users.CommandHandlers;

public class UpdateCurrentUserGdprCommandHandler : UserRequestHandlerBase, ICommandHandler<UpdateCurrentUserGdprCommand, GdprUpdatedStatusResponseDto>
{
    public virtual async Task<GdprUpdatedStatusResponseDto> Handle(UpdateCurrentUserGdprCommand command, CancellationToken cancellationToken)
    {
        IdentityUser user = await UserRepository.GetAsync(ExpressionCreator.GetEqualsExpression<IdentityUser, Guid>(
            AdministrationModuleExtensionConfigurator.ExternalIdPropertyName, CurrentUser.Id!.Value), cancellationToken: cancellationToken);
        user.SetGdprConsentStatus(command.HasAcceptedGdpr);
        await UserRepository.UpdateAsync(user, cancellationToken: cancellationToken);

        return new GdprUpdatedStatusResponseDto(command.HasAcceptedGdpr);
    }
}