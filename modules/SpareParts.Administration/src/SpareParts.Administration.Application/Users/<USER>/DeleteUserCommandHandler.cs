using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;
using SpareParts.AbpMediatR.Handlers;
using SpareParts.Administration.Generated.Identity;
using SpareParts.Administration.Users.Commands;
using SpareParts.Common;
using System;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Identity;

namespace SpareParts.Administration.Users.CommandHandlers;
public class DeleteUserCommandHandler : UserRequestHandlerBase, ICommandHandler<DeleteUserCommand>
{
    private IIdentity Identity => LazyServiceProvider.LazyGetRequiredService<IIdentity>();

    public virtual async Task Handle(DeleteUserCommand command, CancellationToken cancellationToken)
    {
        IdentityUser user = await UserRepository.GetAsync(command.Id, cancellationToken: cancellationToken);
        try
        {
            await Identity.UpdateRolesAsync(CurrentTenant.Name!, CommonConsts.ProductLineName, user.GetExternalId().ToString(), [], cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "The roles, of the user with Id {id}, were not deleted", command.Id);
            throw new BusinessException("Can not delete user");
        }
        (await UserManager.DeleteAsync(user)).CheckErrors();
    }
}