using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;
using SpareParts.Administration.Generated.Identity;
using SpareParts.Common;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Identity;
using Check = Volo.Abp.Check;

namespace SpareParts.Administration.Users.CommandHandlers;
public abstract class UpdateUserRolesCommandHandlerBase : UserRequestHandlerBase
{
    protected IIdentity Identity => LazyServiceProvider.LazyGetRequiredService<IIdentity>();

    protected async Task UpdateRoles(IdentityUser user, List<string> roleNames, CancellationToken cancellationToken)
    {
        Check.NotNullOrEmpty(roleNames, nameof(roleNames));

        Logger.LogInformation("Start updating roles in identity for user {Id}", user.Id);
        await Identity.UpdateRolesAsync(CurrentTenant.Name!, CommonConsts.ProductLineName,
            user.GetExternalId().ToString(), roleNames, cancellationToken);
        Logger.LogInformation("Roles updated in identity for user {Id} - identity userId : {ExternalId}", user.Id, user.GetExternalId());

        (await UserManager.SetRolesAsync(user, roleNames)).CheckErrors();
    }
}