using SpareParts.AbpMediatR.Handlers;
using SpareParts.Administration.Companies.Commands;
using SpareParts.Administration.Companies.Dtos;
using SpareParts.Common.Companies;
using System.Threading;
using System.Threading.Tasks;

namespace SpareParts.Administration.Companies.CommandHandlers;

public class CreateCompanyCommandHandler : CompanyRequestHandlerBase, ICommandHandler<CreateCompanyCommand, CompanyDto>
{
    public virtual async Task<CompanyDto> Handle(CreateCompanyCommand command, CancellationToken cancellationToken)
    {
        Company company = await CompanyDomainService.CreateAsync(GuidGenerator.Create(), command.Code, command.Name, command.LegalName, command.Type, command.IsDefault);
        await CompanyRepository.InsertAsync(company, cancellationToken: cancellationToken);
        return ObjectMapper.Map<Company, CompanyDto>(company);
    }
}