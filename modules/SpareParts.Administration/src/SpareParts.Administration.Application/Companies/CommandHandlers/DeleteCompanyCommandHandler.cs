using SpareParts.AbpMediatR.Handlers;
using SpareParts.Administration.BackgroundJobs.Args;
using SpareParts.Administration.Companies.Commands;
using SpareParts.Common.Companies;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.TenantManagement;
using Volo.Abp.Validation;

namespace SpareParts.Administration.Companies.CommandHandlers;

public class DeleteCompanyCommandHandler : CompanyRequestHandlerBase, ICommandHandler<DeleteCompanyCommand, string>
{
    private IBackgroundJobManager BackgroundJobManager => LazyServiceProvider.LazyGetRequiredService<IBackgroundJobManager>();
    private ITenantRepository TenantRepository => LazyServiceProvider.LazyGetRequiredService<ITenantRepository>();

    public virtual async Task<string> Handle(DeleteCompanyCommand command, CancellationToken cancellationToken)
    {
        Company? company = await CompanyRepository.FindAsync(command.Id, cancellationToken: cancellationToken);
        if (company == null)
        {
            return string.Empty;
        }
        if (company.IsDefault)
        {
            throw new AbpValidationException("Cannot delete default company");
        }

        await CompanyRepository.DeleteAsync(company, cancellationToken: cancellationToken);

        if (!CurrentTenant.Id.HasValue)
        {
            return string.Empty;
        }

        Tenant tenant = await TenantRepository.GetAsync(CurrentTenant.Id.Value, cancellationToken: cancellationToken);
        return await BackgroundJobManager.EnqueueAsync(new DeleteCompanyJobArgs(tenant.Id, tenant.Name, command.Id), BackgroundJobPriority.High);
    }
}