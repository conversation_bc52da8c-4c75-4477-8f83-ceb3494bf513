using SpareParts.AbpMediatR.Handlers;
using SpareParts.Administration.Companies.Dtos;
using SpareParts.Administration.Companies.Queries;
using SpareParts.Common;
using SpareParts.Common.Companies;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace SpareParts.Administration.Companies.QueryHandlers;
public class GetCompaniesStatsQueryHandler : CompanyRequestHandlerBase, IQueryHandler<GetCompaniesStatsQuery, CompaniesStatsDto>
{
    public async Task<CompaniesStatsDto> Handle(GetCompaniesStatsQuery request, CancellationToken cancellationToken)
    {
        CurrentUser.ThrowAuthorizationExceptionIfUserDoesNotBelongToInternalCompany();

        int count = await ReadOnlyCompanyRepository.CountAsync(c => c.Type == CompanyType.External, cancellationToken:cancellationToken);
        return new CompaniesStatsDto() {Count = count};
    }
}
