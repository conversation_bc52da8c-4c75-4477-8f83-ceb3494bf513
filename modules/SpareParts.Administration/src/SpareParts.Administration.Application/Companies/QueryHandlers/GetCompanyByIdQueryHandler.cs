using SpareParts.AbpMediatR.Handlers;
using SpareParts.Administration.Companies.Dtos;
using SpareParts.Administration.Companies.Queries;
using SpareParts.Common.Companies;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.ChangeTracking;

namespace SpareParts.Administration.Companies.QueryHandlers;

[DisableEntityChangeTracking]
public class GetCompanyByIdQueryHandler : CompanyRequestHandlerBase, IQueryHandler<GetCompanyByIdQuery, CompanyDto>
{
    public async Task<CompanyDto> Handle(GetCompanyByIdQuery request, CancellationToken cancellationToken)
    {
        Company company = await CompanyRepository.GetAsync(request.Id, cancellationToken: cancellationToken);

        return ObjectMapper.Map<Company, CompanyDto>(company);
    }
}
