using SpareParts.AbpMediatR.Handlers;
using SpareParts.Administration.Companies.Dtos;
using SpareParts.Administration.Companies.Queries;
using SpareParts.Common.Companies;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.ChangeTracking;

namespace SpareParts.Administration.Companies.QueryHandlers;

[DisableEntityChangeTracking]
public class GetCompaniesByIdsQueryHandler : GetCompaniesQueryBase, IQueryHandler<GetCompaniesByIdsQuery, List<CompanyDto>>
{
    public async Task<List<CompanyDto>> Handle(GetCompaniesByIdsQuery request, CancellationToken cancellationToken)
    {
        IQueryable<Company> companyQueryable = await CompanyRepository.GetQueryableAsync();
        companyQueryable = companyQueryable.Where(x => request.Ids.Contains(x.Id));

        IQueryable<CompanyDto> companyDtoQueryable = await GetCompanyDtoQueryable(companyQueryable);

        List<CompanyDto> companiesDto = await CompanyRepository.AsyncExecuter.ToListAsync(companyDtoQueryable, cancellationToken);
        return companiesDto;
    }
}
