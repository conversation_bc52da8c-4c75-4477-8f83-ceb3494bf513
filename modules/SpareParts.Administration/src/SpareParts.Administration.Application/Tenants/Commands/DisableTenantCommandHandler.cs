using Microsoft.Extensions.Logging;
using SpareParts.AbpMediatR.Handlers;
using System.Threading;
using System.Threading.Tasks;
using SpareParts.Common.Tenants;
using Volo.Abp.TenantManagement;

namespace SpareParts.Administration.Tenants.Commands;
public class DisableTenantCommandHandler : AdministrationRequestBase, ICommandHandler<DisableTenantCommand>
{
    private readonly ITenantRepository _tenantRepository;

    public DisableTenantCommandHandler(ITenantRepository tenantRepository)
    {
        _tenantRepository = tenantRepository;
    }

    public virtual async Task Handle(DisableTenantCommand command, CancellationToken cancellationToken)
    {
        string tenantName = command.TenantName;
        Tenant tenant = await _tenantRepository.FindByNameAsync(command.TenantName, cancellationToken: cancellationToken);
        if (tenant != null)
        {
            tenant.Disable();
            await _tenantRepository.UpdateAsync(tenant, cancellationToken: cancellationToken);
            Logger.LogInformation("Tenant {tenantName} disabled", tenantName);
        }
        else
        {
            Logger.LogInformation("Tenant {tenantName} not found or already disabled", tenantName);
        }
    }
}
