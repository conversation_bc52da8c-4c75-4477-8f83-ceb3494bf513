using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using SpareParts.AbpMediatR.Commands;
using SpareParts.AbpMediatR.Handlers;
using SpareParts.AbpMediatR.Notifications;
using SpareParts.Administration.Companies.Commands;
using SpareParts.Administration.Companies.Dtos;
using SpareParts.Administration.Tenants.Dtos;
using SpareParts.Common;
using SpareParts.Common.Companies;
using SpareParts.Common.Tenants;
using Volo.Abp.Data;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.TenantManagement;

namespace SpareParts.Administration.Tenants.Commands;

public class CreateOrEnableTenantCommandHandler : AdministrationRequestBase, ICommandHandler<CreateOrEnableTenantCommand, TenantDto>
{
    private ITenantManager TenantManager => LazyServiceProvider.LazyGetRequiredService<ITenantManager>();
    private IDataSeeder DataSeeder => LazyServiceProvider.LazyGetRequiredService<IDataSeeder>();
    private ITenantRepository TenantRepository => LazyServiceProvider.LazyGetRequiredService<ITenantRepository>();
    private ICommandSender CommandSender => LazyServiceProvider.LazyGetRequiredService<ICommandSender>();
    private INotificationPublisher NotificationPublisher => LazyServiceProvider.LazyGetRequiredService<INotificationPublisher>();
    private IRepository<Company> CompanyRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Company>>();

    public virtual async Task<TenantDto> Handle(CreateOrEnableTenantCommand command, CancellationToken cancellationToken)
    {
        using (DataFilter.Disable<IIsEnabledTenant>())
        {
            string tenantName = command.TenantName;
            string tenantDisplayName = command.DisplayName;
            Tenant? tenant = await TenantRepository.FindByNameAsync(tenantName, cancellationToken: cancellationToken);
            Guid firstInternalCompanyId;
            if (tenant is null)
            {
                tenant = await TenantManager.CreateAsync(tenantName);
                tenant.Enable();
                tenant.SetDisplayName(tenantDisplayName);
                tenant = await TenantRepository.InsertAsync(tenant, cancellationToken: cancellationToken);
                await DataSeeder.SeedAsync(new DataSeedContext(tenant.Id));
                using (CurrentTenant.Change(tenant.Id, tenant.Name))
                {
                    CompanyDto companyDto = await CommandSender.Send(new CreateCompanyCommand(tenantName, tenantDisplayName, tenantDisplayName,
                        CompanyType.Internal, true), cancellationToken);
                    firstInternalCompanyId = companyDto.Id;
                    await CurrentUnitOfWork!.SaveChangesAsync(cancellationToken);
                    Logger.LogInformation("Tenant and internal company with name {TenantName} created", tenantName);
                    await NotificationPublisher.Publish(new NewTenantNotification(tenant.Id, tenant.Name), cancellationToken);
                }
            }
            else
            {
                if (!tenant.IsEnabled())
                {
                    tenant.Enable();
                    tenant.SetDisplayName(tenantDisplayName);
                    tenant = await TenantRepository.UpdateAsync(tenant, cancellationToken: cancellationToken);
                    Logger.LogInformation("Tenant {TenantName} enabled", tenantName);
                }
                else
                {
                    Logger.LogInformation("Tenant {TenantName} already created and enabled", tenantName);
                }

                using (CurrentTenant.Change(tenant.Id, tenant.Name))
                {
                    Company firstInternalCompany = await CompanyRepository.GetAsync(c => c.Code.Equals(tenant.Name) && c.Type.Equals(CompanyType.Internal), cancellationToken: cancellationToken);
                    firstInternalCompanyId = firstInternalCompany.Id;
                    await CommandSender.Send(new UpdateCompanyCommand(firstInternalCompanyId, tenant.Name, tenantDisplayName, tenantDisplayName), cancellationToken);
                }
            }

            return new TenantDto(tenant.Id, firstInternalCompanyId, tenantDisplayName);
        }
    }
}