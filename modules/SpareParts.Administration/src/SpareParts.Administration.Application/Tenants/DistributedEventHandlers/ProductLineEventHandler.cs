using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using SpareParts.AbpMediatR.Commands;
using SpareParts.Administration.DistributedEvents.Etos;
using SpareParts.Administration.Invitations.Commands;
using SpareParts.Administration.Tenants.Commands;
using SpareParts.Administration.Tenants.Dtos;
using SpareParts.Common;
using Volo.Abp.DependencyInjection;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.MultiTenancy;
using Volo.Abp.Uow;

namespace SpareParts.Administration.Tenants.DistributedEventHandlers;

public class ProductLineEventHandler : ITransientDependency, IDistributedEventHandler<ProductLineSubscribedEto>, IDistributedEventHandler<ProductLineUnsubscribedEto>
{
    public IAbpLazyServiceProvider LazyServiceProvider { get; set; } = null!;
    private ICommandSender Sender => LazyServiceProvider.LazyGetRequiredService<ICommandSender>();
    private CurrentTenant CurrentTenant => LazyServiceProvider.LazyGetRequiredService<CurrentTenant>();

    private ILogger<ProductLineEventHandler> Logger =>
        LazyServiceProvider.LazyGetRequiredService<ILogger<ProductLineEventHandler>>();

    [UnitOfWork]
    public virtual async Task HandleEventAsync(ProductLineSubscribedEto eventData)
    {
        if (string.Equals(eventData.Payload.ProductLineName, CommonConsts.ProductLineName, StringComparison.OrdinalIgnoreCase))
        {
            try
            {
                TenantDto tenantDto = await Sender.Send(new CreateOrEnableTenantCommand(eventData.Payload.TenantEto.Name, eventData.Payload.TenantEto.DisplayName));
                using (CurrentTenant.Change(tenantDto.TenantId, eventData.Payload.TenantEto.Name))
                {
                    await Sender.Send(new CreateInvitationCommand(eventData.Payload.AdminUser.Email,
                        [IdentityRoles.ConfigurationAdministrator, IdentityRoles.AccountManager, IdentityRoles.ContentManager, IdentityRoles.ContentViewer],
                        eventData.Payload.AdminUser.Language,
                        tenantDto.CompanyId));
                }
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
            }
        }
    }

    [UnitOfWork]
    public virtual async Task HandleEventAsync(ProductLineUnsubscribedEto eventData)
    {
        if (string.Equals(eventData.Payload.ProductLineName, CommonConsts.ProductLineName, StringComparison.OrdinalIgnoreCase))
        {
            await Sender.Send(new DisableTenantCommand(eventData.Payload.TenantEto.Name));
        }
    }
}