using System;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.TenantManagement;

namespace SpareParts.Administration.Invitations;
public abstract class InvitationRequestBase : AdministrationRequestBase
{
    protected IReadOnlyRepository<Invitation, Guid> InvitationReadOnlyRepository => LazyServiceProvider.LazyGetRequiredService<IReadOnlyRepository<Invitation, Guid>>();
    protected IRepository<Tenant, Guid> TenantRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Tenant, Guid>>();
}
