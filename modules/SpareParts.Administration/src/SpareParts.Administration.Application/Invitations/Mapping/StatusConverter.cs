using AutoMapper;
using SpareParts.Administration.Enums;
using Volo.Abp.Timing;

namespace SpareParts.Administration.Invitations.Mapping;
public class StatusConverter : IValueConverter<Invitation, string>
{
    private readonly IClock _clock;

    public StatusConverter(IClock clock)
    {
        _clock = clock;
    }

    public string Convert(Invitation sourceMember, ResolutionContext context)
    {
        return GetInvitationStatus(sourceMember).ToString();
    }

    private InvitationStatus GetInvitationStatus(Invitation sourceMember)
    {
        if (sourceMember.CompletionDate != null)
        {
            return InvitationStatus.Accepted;
        }

        if (sourceMember.CancellationDate != null)
        {
            return InvitationStatus.Canceled;
        }

        return _clock.Normalize(sourceMember.ExpirationDate) < _clock.Now ? InvitationStatus.Expired : InvitationStatus.Pending;
    }
}