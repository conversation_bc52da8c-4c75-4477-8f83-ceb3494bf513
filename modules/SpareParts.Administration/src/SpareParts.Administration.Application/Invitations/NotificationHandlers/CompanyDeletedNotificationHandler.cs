using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using SpareParts.AbpMediatR.Notifications;
using SpareParts.Administration.Companies.Notifications;
using SpareParts.Administration.Generated.Identity;
using SpareParts.Common;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.MultiTenancy;

namespace SpareParts.Administration.Invitations.NotificationHandlers;

public class CompanyDeletedNotificationHandler : INotificationHandler<CompanyDeletedNotification>
{
    private readonly ICurrentTenant _currentTenant;
    private readonly IRepository<Invitation> _invitationRepository;
    private readonly IIdentity _identityService;
    private readonly InvitationDomainService _invitationDomainService;
    private readonly ILogger<CompanyDeletedNotificationHandler> _logger;

    public CompanyDeletedNotificationHandler(ICurrentTenant currentTenant, IRepository<Invitation> invitationRepository, 
        IIdentity identityService, InvitationDomainService invitationDomainService, ILogger<CompanyDeletedNotificationHandler> logger)
    {
        _currentTenant = currentTenant;
        _invitationRepository = invitationRepository;
        _identityService = identityService;
        _invitationDomainService = invitationDomainService;
        _logger = logger;
    }

    public virtual async Task Handle(CompanyDeletedNotification notification, CancellationToken cancellationToken)
    {
        using (_currentTenant.Change(notification.TenantId))
        {
            List<Invitation> invitations = await _invitationRepository.GetListAsync(i => i.CompanyId == notification.CompanyId, cancellationToken: cancellationToken);
            foreach (string code in invitations.Where(_invitationDomainService.CanBeCancelled).Select(i => i.Code))
            {
                try
                {
                    await _identityService.CancelInvitationAsync(notification.TenantName,
                        CommonConsts.ProductLineName,
                        code,
                        cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Couldn't cancel identity invitation {InvitationCode}", code);
                }
            }

            await _invitationRepository.DeleteManyAsync(invitations, cancellationToken: cancellationToken);
        }
    }
}