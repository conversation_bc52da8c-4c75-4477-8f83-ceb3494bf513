using FluentValidation;
using SpareParts.Administration.Invitations.Commands;

namespace SpareParts.Administration.Invitations.Validators;
public class CreateInvitationCommandValidator : AbstractValidator<CreateInvitationCommand>
{
    public CreateInvitationCommandValidator()
    {
        RuleFor(x => x.Email).NotEmpty();
        RuleFor(x => x.Language).NotEmpty();
        RuleFor(x => x.CompanyId).NotEmpty();
        RuleFor(x => x.Roles).NotEmpty();

        RuleFor(x => x.Email).MaximumLength(AdministrationConsts.Invitation.EmailMaxLength);
        RuleFor(x => x.Language).MaximumLength(AdministrationConsts.Invitation.LanguageMaxLength);
        RuleFor(x => x.FirstName).MaximumLength(AdministrationConsts.Invitation.FirstNameMaxLength);
        RuleFor(x => x.Name).MaximumLength(AdministrationConsts.Invitation.NameMaxLength);
    }
}