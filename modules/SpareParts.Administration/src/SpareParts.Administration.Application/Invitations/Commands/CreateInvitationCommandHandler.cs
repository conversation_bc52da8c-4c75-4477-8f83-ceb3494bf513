using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using SpareParts.AbpMediatR.Handlers;
using SpareParts.Administration.Generated.Identity;
using SpareParts.Administration.Invitations.Dtos;
using SpareParts.Administration.Localization;
using SpareParts.Administration.Mails;
using SpareParts.Common;
using SpareParts.Common.Companies;
using SpareParts.Common.Configurations;
using SpareParts.Common.Mails;
using SpareParts.Common.Mails.Args;
using SpareParts.Common.Tenants;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Localization;
using Volo.Abp.TenantManagement;
using Volo.Abp.Validation;

namespace SpareParts.Administration.Invitations.Commands;

public class CreateInvitationCommandHandler : InvitationRequestBase, ICommandHandler<CreateInvitationCommand, InvitationCreatedDto>
{
    private InvitationDomainService InvitationDomainService => LazyServiceProvider.LazyGetRequiredService<InvitationDomainService>();
    protected IReadOnlyRepository<Company, Guid> ReadOnlyCompanyRepository => LazyServiceProvider.LazyGetRequiredService<IReadOnlyRepository<Company, Guid>>();
    private IRepository<Invitation, Guid> Repository => LazyServiceProvider.LazyGetRequiredService<IRepository<Invitation, Guid>>();
    private IIdentity Identity => LazyServiceProvider.LazyGetRequiredService<IIdentity>();
    private IOptions<ApplicationConfiguration> ApplicationConfiguration => LazyServiceProvider.LazyGetRequiredService<IOptions<ApplicationConfiguration>>();
    private IStringLocalizer<AdministrationResource> Localizer => LazyServiceProvider.LazyGetRequiredService<IStringLocalizer<AdministrationResource>>();
    private MailSender MailSender => LazyServiceProvider.LazyGetRequiredService<MailSender>();

    public virtual async Task<InvitationCreatedDto> Handle(CreateInvitationCommand command, CancellationToken cancellationToken)
    {
        List<InvitationRole> invitationRoles = command.Roles.Select(role => new InvitationRole(role)).ToList();

        Company company = await ReadOnlyCompanyRepository.GetAsync(command.CompanyId, cancellationToken: cancellationToken);
        if (company.Type == CompanyType.External && invitationRoles.Exists(role => !IdentityRoles.ExternRoles.Contains(role.Name)))
        {
            throw new AbpValidationException("Cannot create invitation for an external company with the specified roles");
        }

        Guid invitationId = GuidGenerator.Create();
        Generated.Identity.InvitationDto identityInvitationDto = await Identity.CreateInvitationAsync(CurrentTenant.Name!, CommonConsts.ProductLineName,
            new InvitationCreationDto
            {
                RoleNames = command.Roles,
                SkipOnboardingPage = true,
                CallBackUri = $"{ApplicationConfiguration.Value.WebAppUrl}{CurrentTenant.Name!}/onboarding/complete/{invitationId}?skipLoginPage=true"
            }, cancellationToken);
        Logger.LogInformation("Invitation created in identity with code {Code}", identityInvitationDto.Code);

        Invitation invitation = await InvitationDomainService.CreateAsync(invitationId, identityInvitationDto.Code, command.Email, invitationRoles, command.Language, command.CompanyId, identityInvitationDto.ExpirationDate.UtcDateTime);
        invitation.Name = command.Name;
        invitation.FirstName = command.FirstName;
        invitation.OnboardingUri = identityInvitationDto.OnboardingUri;
        invitation = await Repository.InsertAsync(invitation, cancellationToken: cancellationToken);
        Logger.LogInformation("Invitation inserted in our database with ID {Id}", invitation.Id);

        await SendOrQueueEmailAsync(command, invitation.Id, cancellationToken);
        Logger.LogInformation("Invitation email sent to {Email} or queued to be sent later", command.Email);

        return ObjectMapper.Map<Invitation, InvitationCreatedDto>(invitation);
    }

    private async Task SendOrQueueEmailAsync(CreateInvitationCommand command, Guid invitationId, CancellationToken cancellationToken)
    {
        Tenant tenant = await TenantRepository.GetAsync(CurrentTenant.Id!.Value, cancellationToken: cancellationToken);
        string tenantDisplayName = tenant.DisplayName()!;


        string subject;
        using (CultureHelper.Use(command.Language))
        {
            subject = Localizer.GetString("Administration.Invitation.Mail.Title", tenantDisplayName);
        }

        var model = new
        {
            TenantDisplayName = tenantDisplayName,
            OnboardingUrl = $"{ApplicationConfiguration.Value.WebAppUrl}{CurrentTenant.Name!}/onboarding/start/{invitationId}"
        };

        SendMailArgs sendMailArgs = new(tenant.Name, command.Email, subject, MailingTemplateConsts.Invitation, model, command.Language);
        await MailSender.SendOrQueueAsync(sendMailArgs, cancellationToken);
    }
}
