using SpareParts.AbpMediatR.Handlers;
using SpareParts.Administration.Generated.Identity;
using SpareParts.Administration.Invitations.Dtos;
using SpareParts.Common;
using System;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace SpareParts.Administration.Invitations.Commands;

public class CancelInvitationCommandHandler : AdministrationRequestBase, ICommandHandler<CancelInvitationCommand, CancelledInvitationDto>
{
    private IIdentity Identity => LazyServiceProvider.LazyGetRequiredService<IIdentity>();
    private IRepository<Invitation, Guid> Repository => LazyServiceProvider.LazyGetRequiredService<IRepository<Invitation, Guid>>();

    private InvitationDomainService InvitationDomainService =>
        LazyServiceProvider.LazyGetRequiredService<InvitationDomainService>();

    public virtual async Task<CancelledInvitationDto> Handle(CancelInvitationCommand request, CancellationToken cancellationToken)
    {
        Invitation invitation = await Repository.GetAsync(request.Id, cancellationToken: cancellationToken);
        InvitationDomainService.Cancel(invitation);
        await Identity.CancelInvitationAsync(CurrentTenant.Name, CommonConsts.ProductLineName,
            invitation.Code, cancellationToken);
        invitation = await Repository.UpdateAsync(invitation, true, cancellationToken: cancellationToken);
        return ObjectMapper.Map<Invitation, CancelledInvitationDto>(invitation);
    }
}