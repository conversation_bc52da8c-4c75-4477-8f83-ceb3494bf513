using SpareParts.Administration.Permissions;
using SpareParts.Common;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.PermissionManagement;

namespace SpareParts.Administration.SeedContributors;

public class AdministrationPermissionDataSeedContributor : IDataSeedContributor, ITransientDependency
{
    protected IPermissionDataSeeder PermissionDataSeeder { get; }

    public AdministrationPermissionDataSeedContributor(IPermissionDataSeeder permissionDataSeeder)
    {
        PermissionDataSeeder = permissionDataSeeder;
    }

    public virtual async Task SeedAsync(DataSeedContext context)
    {
        if (!context.TenantId.HasValue)
        {
            return;
        }

        List<string> accountManagerPermissions = AdministrationPermissions.GetAccountManagerPermissions();
        await PermissionDataSeeder.SeedAsync(RolePermissionValueProvider.ProviderName,
            IdentityRoles.AccountManager, accountManagerPermissions, context.TenantId);

        List<string> configurationAdministratorPermissions = AdministrationPermissions.GetConfigurationAdministratorPermissions();
        await PermissionDataSeeder.SeedAsync(RolePermissionValueProvider.ProviderName,
            IdentityRoles.ConfigurationAdministrator, configurationAdministratorPermissions, context.TenantId);

        List<string> contentManagerPermissions = AdministrationPermissions.GetContentManagerPermissions();
        await PermissionDataSeeder.SeedAsync(RolePermissionValueProvider.ProviderName,
            IdentityRoles.ContentManager, contentManagerPermissions, context.TenantId);

        List<string> contentViewerPermissions = AdministrationPermissions.GetContentViewerPermissions();
        await PermissionDataSeeder.SeedAsync(RolePermissionValueProvider.ProviderName,
            IdentityRoles.ContentViewer, contentViewerPermissions, context.TenantId);
    }
}