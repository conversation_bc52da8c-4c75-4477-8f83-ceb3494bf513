using System;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Identity;
using Volo.Abp.Uow;

namespace SpareParts.Administration.SeedContributors;

[Dependency(ReplaceServices = true)]
public class IdentityDataSeeder : ITransientDependency, IIdentityDataSeeder
{
    [UnitOfWork]
    public virtual Task<IdentityDataSeedResult> SeedAsync(string adminEmail, string adminPassword, Guid? tenantId = null, string? adminUserName = null)
    {
        return Task.FromResult(new IdentityDataSeedResult());
    }
}