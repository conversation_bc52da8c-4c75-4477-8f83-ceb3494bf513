using SpareParts.Common;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Guids;
using Volo.Abp.Identity;
using Volo.Abp.MultiTenancy;
using IIdentityRoleRepository = SpareParts.Administration.Repositories.IIdentityRoleRepository;

namespace SpareParts.Administration.SeedContributors;

public class IdentityRolesDataSeedContributor : IDataSeedContributor, ITransientDependency
{
    private readonly IGuidGenerator _guidGenerator;
    private readonly IIdentityRoleRepository _identityRoleRepository;
    private readonly ICurrentTenant _currentTenant;

    public IdentityRolesDataSeedContributor(IGuidGenerator guidGenerator, IIdentityRoleRepository identityRoleRepository, ICurrentTenant currentTenant)
    {
        _guidGenerator = guidGenerator;
        _identityRoleRepository = identityRoleRepository;
        _currentTenant = currentTenant;
    }
    public virtual async Task SeedAsync(DataSeedContext context)
    {
        if (context.TenantId.HasValue)
        {
            using (_currentTenant.Change(context.TenantId.Value))
            {
                List<string> existentNames = await _identityRoleRepository.GetNamesAsync();
                IdentityRole[] identityRolesToAdd = IdentityRoles.AllRoles.Except(existentNames)
                    .Select(name => new IdentityRole(_guidGenerator.Create(), name, context.TenantId.Value)).ToArray();
                if (identityRolesToAdd.Length > 0)
                {
                    await _identityRoleRepository.InsertManyAsync(identityRolesToAdd);
                }
            }
        }
        else
        {
            using (_currentTenant.Change(null))
            {
                IdentityRole adminRole = await _identityRoleRepository.FindByNormalizedNameAsync(IdentityRoles.Admin.ToUpperInvariant());
                if (adminRole == null)
                {
                    await _identityRoleRepository.InsertAsync(new IdentityRole(_guidGenerator.Create(),
                        IdentityRoles.Admin));
                }
            }
        }
    }
}