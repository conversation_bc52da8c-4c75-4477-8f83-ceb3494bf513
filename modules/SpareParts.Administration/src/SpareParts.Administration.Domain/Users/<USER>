using System;
using SpareParts.Common;
using Volo.Abp.Data;
using Volo.Abp.Identity;

namespace SpareParts.Administration.Users;

public static class UserExtension
{
    internal static void SetCompanyId(this IdentityUser user, Guid companyId)
    {
        user.SetProperty(AdministrationModuleExtensionConfigurator.CompanyIdPropertyName, companyId);
    }
    
    public static Guid GetCompanyId(this IdentityUser user)
    {
        return Check.NotDefault(user.GetProperty<Guid>(AdministrationModuleExtensionConfigurator.CompanyIdPropertyName),
            AdministrationModuleExtensionConfigurator.CompanyIdPropertyName);
    }

    internal static void SetExternalId(this IdentityUser user, Guid externalId)
    {
        user.SetProperty(AdministrationModuleExtensionConfigurator.ExternalIdPropertyName, externalId);
    }
    
    public static Guid GetExternalId(this IdentityUser user)
    {
        return Check.NotDefault(user.GetProperty<Guid>(AdministrationModuleExtensionConfigurator.ExternalIdPropertyName),
            AdministrationModuleExtensionConfigurator.ExternalIdPropertyName);
    }

    public static void SetGdprConsentStatus(this IdentityUser user, bool isAccepted)
    {
        user.SetProperty(AdministrationModuleExtensionConfigurator.HasAcceptedGdprPropertyName, isAccepted);
    }
    
    public static bool GetGdprConsentStatus(this IdentityUser user)
    {
        return Volo.Abp.Check.NotNull(user.GetProperty<bool?>(AdministrationModuleExtensionConfigurator.HasAcceptedGdprPropertyName),
            AdministrationModuleExtensionConfigurator.HasAcceptedGdprPropertyName)!.Value;
    }
}