using JetBrains.Annotations;
using System;
using System.Collections.Generic;
using System.Linq;
using Volo.Abp;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.MultiTenancy;

namespace SpareParts.Administration.Invitations;
public sealed class Invitation : AuditedEntity<Guid>, IMultiTenant
{
    public string Code { get; private set; } = default!;

    public string Email { get; private set; } = default!;

    public string? Name { get; set; }

    public string? FirstName { get; set; }

    //TODO : readonly list
    public List<InvitationRole> Roles { get; private set; } = default!;

    public string Language { get; private set; } = default!;

    public Guid CompanyId { get; private set; }
    
    //TODO : why store that
    public bool IsInternal { get; private set; }

    public DateTime ExpirationDate { get; private set; }

    public DateTime? CompletionDate { get; internal set; }

    public DateTime? CancellationDate { get; internal set; }

    public Guid? TenantId { get; private set; }

    public string OnboardingUri { get; set; }= default!;

    [UsedImplicitly]
    private Invitation()
    {
    }

    internal Invitation(Guid id, string code, string email, List<InvitationRole> roles, string language, Guid companyId, bool isInternal, DateTime expirationDate) : base(id)
    {
        Common.Check.NotDefault(Id, nameof(Id));
        Check.NotDefaultOrNull(TenantId, nameof(TenantId));
        Code = code;
        Email = email;
        Roles = roles;
        Language = language;
        CompanyId = companyId;
        IsInternal = isInternal;
        ExpirationDate = expirationDate;
        OnboardingUri = string.Empty;
    }

    public List<string> GetRoles()
    {
        return Roles.Select(x => x.Name).ToList();
    }
}