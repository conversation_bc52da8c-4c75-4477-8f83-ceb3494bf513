using SpareParts.Common;
using Volo.Abp.Domain;
using Volo.Abp.Domain.Entities.Events.Distributed;
using Volo.Abp.Identity;
using Volo.Abp.Modularity;

namespace SpareParts.Administration;

[DependsOn(
    typeof(AbpDddDomainModule),
    typeof(AdministrationDomainSharedModule),
    typeof(AbpIdentityDomainModule),
    typeof(CommonDomainModule)
)]
public class AdministrationDomainModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        Configure<AbpDistributedEntityEventOptions>(options =>
        {
            options.AutoEventSelectors.Remove<IdentityUser>();
            options.AutoEventSelectors.Remove<IdentityRole>();
        });
    }
}