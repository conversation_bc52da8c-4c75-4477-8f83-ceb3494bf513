using JetBrains.Annotations;
using SpareParts.Common.Localization;
using Volo.Abp.Localization;
using Volo.Abp.Settings;

namespace SpareParts.Administration.Settings;
[UsedImplicitly]
public class AdministrationSettingDefinitionProvider : SettingDefinitionProvider
{
    public override void Define(ISettingDefinitionContext context)
    {
        context.Add(
    new SettingDefinition(AdministrationSettings.InvitationAvailableStatuses, "Accepted,Canceled,Expired,Pending",
              L("DisplayName:" + AdministrationSettings.InvitationAvailableStatuses), isVisibleToClients: true)
              );
    }

    private static LocalizableString L(string name)
    {
        return LocalizableString.Create<CommonResource>(name);
    }
}
