using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Administration.Users.Dtos;
using SpareParts.Administration.Users.Queries;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Administration.MyProfile;

public partial class MyProfileController
{
    [HttpGet]
    [Route("gdpr")]
    [SwaggerOperation(Summary = "Retrieves the current user Gdpr",
        Description = "Get the Gdpr acceptation of the current user",
        OperationId = "Get_CurrentUserGdpr",
        Tags = ["MyProfile"])]
    public async Task<GdprStatusResponseDto> GetGdprStatus()
    {
        return await QuerySender.Send(new GetCurrentUserGdprQuery());
    }
}