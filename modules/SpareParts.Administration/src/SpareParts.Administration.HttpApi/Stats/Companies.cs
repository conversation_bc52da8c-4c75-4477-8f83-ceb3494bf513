using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Administration.Companies.Dtos;
using SpareParts.Administration.Companies.Queries;
using SpareParts.Administration.Permissions;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Administration.Stats;
public partial class StatsController
{
    [HttpGet]
    [Route("companies")]
    [Authorize(AdministrationPermissions.Companies.Stats)]
    [SwaggerOperation(Summary = "Get stats for companies",
        Description = "Get statistics for companies",
        OperationId = "Get_Companies_Stats",
        Tags = ["Stats"])]
    [ProducesResponseType(typeof(CompaniesStatsDto), StatusCodes.Status200OK)]
    public async Task<CompaniesStatsDto> GetStatsForCompanies()
    {
        _currentCompany.ThrowIfExternal();
        return await QuerySender.Send(new GetCompaniesStatsQuery());
    }
}
