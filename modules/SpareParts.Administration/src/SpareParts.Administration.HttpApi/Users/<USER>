using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Administration.Users.Dtos;
using SpareParts.Administration.Users.Queries;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Administration.Users;
public partial class UsersController
{
    [HttpGet]
    [Route("{id:guid}")]
    [SwaggerOperation(Summary = "Retrieves a user based on the specified id.",
        Description = "Get an existing user identified by its unique identifier.",
        OperationId = "Get_User",
        Tags = ["Users"])]
    public async Task<UserDto> Get(Guid id)
    {
        return await QuerySender.Send(new GetUserByIdQuery(id));
    }
}
