using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Administration.Permissions;
using SpareParts.Administration.Users.Commands;
using SpareParts.Administration.Users.Dtos;
using SpareParts.Administration.Users.Dtos.Inputs;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Administration.Users;

public partial class UsersController
{
    [HttpPut]
    [Authorize(AdministrationPermissions.Users.Edit)]
    [Route("{id:guid}")]
    [SwaggerOperation(Summary = "Update user",
        Description = "Update the company and roles of a user identified by its unique identifier",
        OperationId = "Update_User",
        Tags = ["Users"])]
    public async Task<UserUpdatedDto> UpdateUser(Guid id, [FromBody] UpdateUserDto updateUserDto)
    {
        return await CommandSender.Send(new UpdateUserCommand(id, updateUserDto.Roles, updateUserDto.CompanyId));
    }
}