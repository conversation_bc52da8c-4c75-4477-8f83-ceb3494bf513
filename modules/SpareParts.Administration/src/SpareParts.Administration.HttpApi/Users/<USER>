using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Administration.Permissions;
using SpareParts.Administration.Users.Commands;
using SpareParts.Administration.Users.Dtos.Inputs;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Administration.Users;

public partial class UsersController
{
    [HttpPut]
    [Authorize(AdministrationPermissions.Users.Edit)]
    [Route("{id:guid}/roles")]
    [SwaggerOperation(Summary = "Update user roles",
        Description = "Update the roles of a user identified by its unique identifier",
        OperationId = "Update_Roles",
        Tags = ["Users"])]
    public async Task UpdateRoles(Guid id, [FromBody] UpdateUserRolesDto updateUserRolesDto)
    {
        await CommandSender.Send(new UpdateUserRolesCommand(id, updateUserRolesDto.Roles));
    }
}