using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Administration.Invitations.Commands;
using SpareParts.Administration.Invitations.Dtos;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Administration.Invitations;
public partial class InvitationsController
{
    [Authorize]
    [HttpPost]
    [Route("{id:guid}/complete")]
    [SwaggerOperation(Summary = "Complete an invitation",
        Description = "Complete an invitation and create a new user",
        OperationId = "Complete_Invitation",
        Tags = ["Invitations"])]
    [ProducesResponseType(typeof(InvitationCompletedDto), StatusCodes.Status201Created)]
    public async Task<InvitationCompletedDto> Complete(Guid id)
    {
        CompleteInvitationCommand completeInvitationCommand = new(id);
        return await CommandSender.Send(completeInvitationCommand);
    }
}
