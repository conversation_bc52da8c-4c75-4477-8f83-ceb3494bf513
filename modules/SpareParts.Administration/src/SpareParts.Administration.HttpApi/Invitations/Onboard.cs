using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Administration.Invitations.Dtos;
using SpareParts.Administration.Invitations.Queries;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Administration.Invitations;

public partial class InvitationsController
{
    [HttpGet]
    [Route("{id:guid}/onboard")]
    [SwaggerOperation(Summary = "Onboard invitation",
        Description = "Retrieve onboarding uri for invitation if it's valid",
        OperationId = "Onboard_Invitation",
        Tags = ["Invitations"])]
    public async Task<OnboardInvitationDto> OnboardInvitation(Guid id)
    {
        return await QuerySender.Send(new OnboardInvitationQuery(id));
    }
}
