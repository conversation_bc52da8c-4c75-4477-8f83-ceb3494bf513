using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Administration.Invitations.Commands;
using SpareParts.Administration.Invitations.Dtos;
using SpareParts.Administration.Permissions;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Administration.Invitations;

public partial class InvitationsController
{
    [HttpPatch]
    [Authorize(AdministrationPermissions.Invitations.Default)]
    [Authorize(AdministrationPermissions.Invitations.Cancel)]
    [Route("{id:guid}/cancel")]
    [SwaggerOperation(Summary = "Cancel invitation",
        Description = "Cancel An Invitation",
        OperationId = "Cancel_Invitation",
        Tags = ["Invitations"])]
    public async Task<CancelledInvitationDto> Cancel(Guid id)
    {
        return await CommandSender.Send(new CancelInvitationCommand(id));
    }
}