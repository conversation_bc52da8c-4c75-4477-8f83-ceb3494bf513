using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Administration.Invitations.Dtos;
using SpareParts.Administration.Invitations.Queries;
using SpareParts.Administration.Invitations.Queries.QueryFilters;
using SpareParts.Administration.Permissions;
using Swashbuckle.AspNetCore.Annotations;
using Volo.Abp.Application.Dtos;

namespace SpareParts.Administration.Invitations;
public partial class InvitationsController
{
    [HttpGet]
    [Authorize(AdministrationPermissions.Invitations.Default)]
    [SwaggerOperation(Summary = "Retrieve invitations",
        Description = "Retrieve a list of invitations with optional pagination and sorting parameters",
        OperationId = "Get_Invitations",
        Tags = ["Invitations"])]
    public async Task<PagedResultDto<InvitationDto>> GetInvitations([FromQuery] InvitationPaginationFilter filter)
    {
        return await QuerySender.Send(new GetInvitationsQuery(filter));
    }


}
