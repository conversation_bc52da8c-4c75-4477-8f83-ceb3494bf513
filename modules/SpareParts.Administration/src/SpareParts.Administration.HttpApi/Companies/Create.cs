using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Administration.Companies.Commands;
using SpareParts.Administration.Companies.Dtos;
using SpareParts.Administration.Companies.Dtos.Inputs;
using SpareParts.Administration.Permissions;
using SpareParts.Common.Companies;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Administration.Companies;

public partial class CompaniesController
{
    [HttpPost]
    [Authorize(AdministrationPermissions.Companies.Create)]
    [SwaggerOperation(Summary = "Create a new company",
        Description = "Create a new company",
        OperationId = "Create_Company",
        Tags = ["Companies"])]
    [ProducesResponseType(typeof(CompanyDto), StatusCodes.Status201Created)]
    public async Task<CreatedResult> Create([FromBody] CreateCompanyDto companyDto)
    {
        CreateCompanyCommand createCompanyCommand =
            new(companyDto.Code, companyDto.Name, companyDto.LegalName, CompanyType.External);
        CompanyDto newCompanyDto = await CommandSender.Send(createCompanyCommand);
        return Created(string.Empty, newCompanyDto);
    }
}