using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Administration.Companies.Dtos;
using SpareParts.Administration.Companies.Queries;
using SpareParts.Administration.Companies.QueryFilters;
using Swashbuckle.AspNetCore.Annotations;
using Volo.Abp.Application.Dtos;

namespace SpareParts.Administration.Companies;
public partial class CompaniesController
{

    [HttpGet]
    [SwaggerOperation(Summary = "Retrieves a paged list of companies based on the specified filter criteria.",
        Description = "Use this endpoint to get a paginated list of companies based on the provided filter.",
        OperationId = "Get_Companies",
        Tags = ["Companies"])]
    public async Task<PagedResultDto<CompanyDto>> GetAll([FromQuery] CompanyPaginationFilter filter)
    {
        return await QuerySender.Send(new GetCompaniesQuery(filter));
    }
}
