using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SpareParts.Administration.Companies.Commands;
using SpareParts.Administration.Companies.Dtos;
using SpareParts.Administration.Permissions;
using Swashbuckle.AspNetCore.Annotations;

namespace SpareParts.Administration.Companies;
public partial class CompaniesController
{
    [HttpDelete]
    [Authorize(AdministrationPermissions.Companies.Delete)]
    [Route("{id:guid}")]
    [SwaggerOperation(Summary = "Delete a company",
        Description = "Delete an existing company identified by its unique identifier",
        OperationId = "Delete_Company",
        Tags = ["Companies"])]
    [ProducesResponseType(typeof(CompanyDto), StatusCodes.Status202Accepted)]
    public async Task<string> Delete(Guid id)
    {
        DeleteCompanyCommand deleteCompanyCommand = new(id);
        return await CommandSender.Send(deleteCompanyCommand);
    }
}
