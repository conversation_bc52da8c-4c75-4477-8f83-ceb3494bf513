using SpareParts.Common.Company;
using SpareParts.Common.Dtos;
using System.Threading.Tasks;
using Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations;
using Volo.Abp.Data;

namespace SpareParts.Administration.Contributors;
public class ConfigurationContributor : IApplicationConfigurationContributor
{
    public Task ContributeAsync(ApplicationConfigurationContributorContext context)
    {
        ICurrentCompany currentCompany = (ICurrentCompany)context.ServiceProvider.GetService(typeof(ICurrentCompany))!;
        CurrentCompanyDto companyDto = currentCompany.GetCurrentCompanyDto();

        context.ApplicationConfiguration.SetProperty(ConfigurationContributorConsts.CurrentCompany, companyDto);
        return Task.CompletedTask;
    }
}