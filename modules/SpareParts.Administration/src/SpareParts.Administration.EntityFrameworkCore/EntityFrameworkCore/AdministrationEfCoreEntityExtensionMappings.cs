using System;
using SpareParts.Common.Companies;
using Volo.Abp.Identity;
using Volo.Abp.ObjectExtending;
using Volo.Abp.Threading;

namespace SpareParts.Administration.EntityFrameworkCore;

public static class AdministrationEfCoreEntityExtensionMappings
{
    private static readonly OneTimeRunner OneTimeRunner = new();

    public static void Configure()
    { 
        OneTimeRunner.Run(() =>
        {
            ObjectExtensionManager.Instance.MapEfCoreProperty<IdentityUser, Guid>(AdministrationModuleExtensionConfigurator.CompanyIdPropertyName,
                    (builder, _) =>
                    {
                        builder.HasOne(typeof(Company)).WithMany()
                            .HasForeignKey(AdministrationModuleExtensionConfigurator.CompanyIdPropertyName);
                    })
                .MapEfCoreProperty<IdentityUser, Guid>(AdministrationModuleExtensionConfigurator.ExternalIdPropertyName)
                .MapEfCoreProperty<IdentityUser, bool>(AdministrationModuleExtensionConfigurator.HasAcceptedGdprPropertyName);
        });
    }
}