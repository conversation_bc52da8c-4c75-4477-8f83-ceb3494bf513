using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.Identity.EntityFrameworkCore;

namespace SpareParts.Administration.Repositories;
public class IdentityRoleRepository : EfCoreIdentityRoleRepository, IIdentityRoleRepository
{
    public IdentityRoleRepository(IDbContextProvider<IIdentityDbContext> dbContextProvider) : base(dbContextProvider)
    {
    }

    public async Task<List<Guid>> GetIdsByNamesAsync(HashSet<string> names, CancellationToken cancellationToken = default)
    {
        cancellationToken = GetCancellationToken(cancellationToken);
        return await (await GetQueryableAsync()).Where(ir => names.Contains(ir.Name)).Select(c => c.Id).ToListAsync(cancellationToken);
    }

    public async Task<List<string>> GetNamesAsync(CancellationToken cancellationToken = default)
    {
        cancellationToken = GetCancellationToken(cancellationToken);
        return await (await GetQueryableAsync()).Select(c => c.Name).ToListAsync(cancellationToken);
    }
}
