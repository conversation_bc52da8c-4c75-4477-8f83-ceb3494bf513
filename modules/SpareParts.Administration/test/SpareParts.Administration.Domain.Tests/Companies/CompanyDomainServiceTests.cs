using Microsoft.Extensions.DependencyInjection;
using Shouldly;
using SpareParts.Common.Companies;
using System;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;
using Volo.Abp.MultiTenancy;
using Xunit;

namespace SpareParts.Administration.Companies;

public abstract class CompanyDomainServiceTests<TStartupModule> : AdministrationDomainTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    private CompanyDomainService CompanyDomainService => ServiceProvider.GetRequiredService<CompanyDomainService>();
    private ICurrentTenant CurrentTenant => ServiceProvider.GetRequiredService<ICurrentTenant>();
    private IRepository<Company> CompanyRepository => ServiceProvider.GetRequiredService<IRepository<Company>>();

    [Fact]
    public async Task CreateAsync_Should_Throw_Exception_If_Code_Already_Exist()
    {
        using (CurrentTenant.Change(Guid.NewGuid()))
        {
            string code = "code";
            string name = "name";
            string legalName = "legalName";
            const CompanyType type = CompanyType.External;
            await WithUnitOfWorkAsync(async () =>
                      {
                          // Arrange
                          Company company = await CompanyDomainService.CreateAsync(Guid.NewGuid(), code, name, legalName, type);
                          await CompanyRepository.InsertAsync(company, true);
                      });

            await WithUnitOfWorkAsync(async () =>
            {
                // Act & Assert
                await CompanyDomainService.CreateAsync(Guid.NewGuid(), code, name, legalName, type).ShouldThrowAsync<BusinessException>();
            });
        }
    }
}