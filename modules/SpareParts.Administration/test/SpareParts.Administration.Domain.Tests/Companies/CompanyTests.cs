using Microsoft.Extensions.DependencyInjection;
using Shouldly;
using SpareParts.Common;
using SpareParts.Common.Companies;
using System;
using System.Threading.Tasks;
using Volo.Abp.Modularity;
using Volo.Abp.MultiTenancy;
using Xunit;

namespace SpareParts.Administration.Companies;

public abstract class CompanyTests<TStartupModule> : AdministrationDomainTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    private CompanyDomainService CompanyDomainService => ServiceProvider.GetRequiredService<CompanyDomainService>();
    private ICurrentTenant CurrentTenant => ServiceProvider.GetRequiredService<ICurrentTenant>();

    [Fact]
    public async Task CreateAsync_Should_Throw_Exception_With_Incorrect_Value_Arguments()
    {
        using (CurrentTenant.Change(Guid.NewGuid()))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                string code = "code";
                string name = "name";
                string legalName = "legalName";
                const CompanyType type = CompanyType.External;

                // Act & Assert
                await CompanyDomainService.CreateAsync(Guid.Empty, code, name, legalName, type).ShouldThrowAsync<ArgumentException>();
                await CompanyDomainService.CreateAsync(Guid.NewGuid(), string.Empty, name, legalName, type).ShouldThrowAsync<ArgumentException>();
                await CompanyDomainService.CreateAsync(Guid.NewGuid(), code, string.Empty, legalName, type).ShouldThrowAsync<ArgumentException>();
                await CompanyDomainService.CreateAsync(Guid.NewGuid(), code, name, string.Empty, type).ShouldThrowAsync<ArgumentException>();
            });
        }
    }

    [Fact]
    public async Task CreateAsync_Should_Throw_Exception_With_Wrong_Length_Arguments()
    {
        using (CurrentTenant.Change(Guid.NewGuid()))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                string code = "code";
                string name = "name";
                string legalName = "legalName";
                const CompanyType type = CompanyType.External;

                // Act & Assert
                await CompanyDomainService.CreateAsync(Guid.NewGuid(), new string('x', CommonDbProperties.CompanyTableProperties.CodeMaxLength + 1), name, legalName, type).ShouldThrowAsync<ArgumentException>();
                await CompanyDomainService.CreateAsync(Guid.NewGuid(), code, new string('x', CommonDbProperties.CompanyTableProperties.NameMaxLength + 1), legalName, type).ShouldThrowAsync<ArgumentException>();
                await CompanyDomainService.CreateAsync(Guid.NewGuid(), code, name, new string('x', CommonDbProperties.CompanyTableProperties.LegalNameMaxLength + 1), type).ShouldThrowAsync<ArgumentException>();
            });
        }
    }
}