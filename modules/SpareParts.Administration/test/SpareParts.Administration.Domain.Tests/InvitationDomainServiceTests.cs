using SpareParts.Administration.Invitations;
using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Shouldly;
using SpareParts.Common.Companies;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;
using Volo.Abp.Validation;
using Xunit;
using Volo.Abp.MultiTenancy;

namespace SpareParts.Administration;

public abstract class InvitationDomainServiceTests<TStartupModule> : AdministrationDomainTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    private InvitationDomainService InvitationDomainService =>
        ServiceProvider.GetRequiredService<InvitationDomainService>();
    private CompanyDomainService CompanyDomainService =>
        ServiceProvider.GetRequiredService<CompanyDomainService>();
    private IRepository<Company> CompanyRepository => ServiceProvider.GetRequiredService<IRepository<Company>>();
    private ICurrentTenant CurrentTenant => ServiceProvider.GetRequiredService<ICurrentTenant>();

    [Fact]
    public async Task Cancel_ShouldThrowException_WhenInvitationIsCompleted()
    {
        Invitation invitation = null!;
        Guid companyId = Guid.Empty;
        using (CurrentTenant.Change(Guid.NewGuid()))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                Company company = await CompanyDomainService.CreateAsync(Guid.NewGuid(), "code", "name", "legalName", CompanyType.External);
                companyId = company.Id;
                await CompanyRepository.InsertAsync(company);
            });
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                invitation = await InvitationDomainService.CreateAsync(Guid.NewGuid(), "code", "<EMAIL>", [new InvitationRole("configuration-administrator")], "en", companyId, DateTime.UtcNow.AddDays(1));
                InvitationDomainService.Complete(invitation);
            });
        }

        // Act & Assert
        Should.Throw<AbpValidationException>(() => InvitationDomainService.Cancel(invitation));
    }

    [Fact]
    public async Task Cancel_ShouldThrowException_WhenInvitationIsAlreadyCanceled()
    {
        Invitation invitation = null!;
        Guid companyId = Guid.Empty;
        using (CurrentTenant.Change(Guid.NewGuid()))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                Company company = await CompanyDomainService.CreateAsync(Guid.NewGuid(), "code", "name", "legalName", CompanyType.External);
                companyId = company.Id;
                await CompanyRepository.InsertAsync(company);
            });
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                invitation = await InvitationDomainService.CreateAsync(Guid.NewGuid(), "code", "<EMAIL>", [new InvitationRole("configuration-administrator")], "en", companyId, DateTime.UtcNow.AddDays(1));
                InvitationDomainService.Cancel(invitation);
            });
        }

        // Act & Assert
        Should.Throw<AbpValidationException>(() => InvitationDomainService.Cancel(invitation));
    }

    [Fact]
    public async Task Cancel_ShouldThrowException_WhenInvitationIsExpired()
    {
        // Arrange
        Invitation invitation = null!;
        Guid companyId = Guid.Empty;
        using (CurrentTenant.Change(Guid.NewGuid()))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                Company company = await CompanyDomainService.CreateAsync(Guid.NewGuid(), "code", "name", "legalName", CompanyType.External);
                companyId = company.Id;
                await CompanyRepository.InsertAsync(company);
            });
            await WithUnitOfWorkAsync(async () =>
            {
                invitation = await InvitationDomainService.CreateAsync(Guid.NewGuid(), "code", "<EMAIL>", [new InvitationRole("configuration-administrator")], "en", companyId, DateTime.UtcNow.AddDays(-1));
            });
        }

        // Act & Assert
        Should.Throw<AbpValidationException>(() => InvitationDomainService.Cancel(invitation));
    }

    [Fact]
    public async Task Cancel_ShouldCancelInvitation_WhenInvitationIsValid()
    {
        // Arrange
        Invitation invitation = null!;
        Guid companyId = Guid.Empty;
        using (CurrentTenant.Change(Guid.NewGuid()))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                Company company = await CompanyDomainService.CreateAsync(Guid.NewGuid(), "code", "name", "legalName", CompanyType.External);
                companyId = company.Id;
                await CompanyRepository.InsertAsync(company);
            });
            await WithUnitOfWorkAsync(async () =>
            {
                invitation = await InvitationDomainService.CreateAsync(Guid.NewGuid(), "code", "<EMAIL>", [new InvitationRole("configuration-administrator")], "en", companyId, DateTime.UtcNow.AddDays(1));

            });
        }
        
        // Act
        InvitationDomainService.Cancel(invitation);

        // Assert
        invitation.CancellationDate.ShouldNotBeNull();
    }

    [Fact]
    public async Task Complete_ShouldThrowException_WhenInvitationIsCanceled()
    {
        // Arrange
        Invitation invitation = null!;
        Guid companyId = Guid.Empty;
        using (CurrentTenant.Change(Guid.NewGuid()))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                Company company = await CompanyDomainService.CreateAsync(Guid.NewGuid(), "code", "name", "legalName", CompanyType.External);
                companyId = company.Id;
                await CompanyRepository.InsertAsync(company);
            });
            await WithUnitOfWorkAsync(async () =>
            {
                invitation = await InvitationDomainService.CreateAsync(Guid.NewGuid(), "code", "<EMAIL>", [new InvitationRole("configuration-administrator")], "en", companyId, DateTime.UtcNow.AddDays(1));

            });
        }
        InvitationDomainService.Cancel(invitation);

        // Act & Assert
        Should.Throw<AbpValidationException>(() => InvitationDomainService.Complete(invitation));
    }

    [Fact]
    public async Task Complete_ShouldThrowException_WhenInvitationIsAlreadyCompleted()
    {
        // Arrange
        Invitation invitation = null!;
        Guid companyId = Guid.Empty;
        using (CurrentTenant.Change(Guid.NewGuid()))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                Company company = await CompanyDomainService.CreateAsync(Guid.NewGuid(), "code", "name", "legalName", CompanyType.External);
                companyId = company.Id;
                await CompanyRepository.InsertAsync(company);
            });
            await WithUnitOfWorkAsync(async () =>
            {
                invitation = await InvitationDomainService.CreateAsync(Guid.NewGuid(), "code", "<EMAIL>", [new InvitationRole("configuration-administrator")], "en", companyId, DateTime.UtcNow.AddDays(1));
            });
        }
        InvitationDomainService.Complete(invitation);

        // Act & Assert
        Should.Throw<AbpValidationException>(() => InvitationDomainService.Complete(invitation));
    }

    [Fact]
    public async Task Complete_ShouldThrowException_WhenInvitationIsExpired()
    {
        // Arrange
        Invitation invitation = null!;
        Guid companyId = Guid.Empty;
        using (CurrentTenant.Change(Guid.NewGuid()))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                Company company = await CompanyDomainService.CreateAsync(Guid.NewGuid(), "code", "name", "legalName", CompanyType.External);
                companyId = company.Id;
                await CompanyRepository.InsertAsync(company);
            });
            await WithUnitOfWorkAsync(async () =>
            {
                invitation = await InvitationDomainService.CreateAsync(Guid.NewGuid(), "code", "<EMAIL>", [new InvitationRole("configuration-administrator")], "en", companyId, DateTime.UtcNow.AddDays(-1));
            });
        }

        // Act & Assert
        Should.Throw<AbpValidationException>(() => InvitationDomainService.Complete(invitation));
    }

    [Fact]
    public async Task Complete_ShouldCompleteInvitation_WhenInvitationIsValid()
    {
        // Arrange
        Invitation invitation = null!;
        Guid companyId = Guid.Empty;
        using (CurrentTenant.Change(Guid.NewGuid()))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                Company company = await CompanyDomainService.CreateAsync(Guid.NewGuid(), "code", "name", "legalName", CompanyType.External);
                companyId = company.Id;
                await CompanyRepository.InsertAsync(company);
            });
            await WithUnitOfWorkAsync(async () =>
            {
                invitation = await InvitationDomainService.CreateAsync(Guid.NewGuid(), "code", "<EMAIL>", [new InvitationRole("configuration-administrator")], "en", companyId, DateTime.UtcNow.AddDays(1));
            });
        }

        // Act
        InvitationDomainService.Complete(invitation);

        // Assert
        invitation.CompletionDate.ShouldNotBeNull();
    }
}