using System;
using System.Reflection;
using System.Threading.Tasks;
using Shouldly;
using SpareParts.Administration.Invitations.Dtos;
using SpareParts.Administration.Invitations.Queries;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Modularity;
using Volo.Abp.Validation;
using Xunit;

namespace SpareParts.Administration.Invitations.QueryHandlers;

public abstract class OnboardInvitationQueryHandlerTests<TStartupModule> : InvitationTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    [Fact]
    public async Task? OnboardInvitationQuery_Should_Throw_EntityNotFoundException_If_Invitation_Does_Not_Exist()
    {
        using (CurrentTenant.Change(Guid.NewGuid()))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                await QuerySender.Send(new OnboardInvitationQuery(Guid.NewGuid())).ShouldThrowAsync<EntityNotFoundException>();
            });
        }
    }

    [Fact]
    public async Task? OnboardInvitationQuery_Should_Throw_AbpValidationException_If_Invitation_Cancelled()
    {
        Guid tenantId = GuidGenerator.Create();
        TestDataContext testDataContext = new(tenantId) { GenerateInvitations = true };
        AdministrationTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(testDataContext);
        Guid invitationId = storeContext.InvitationIds[0];
        using (CurrentTenant.Change(tenantId))
        {
            // Arrange
            await WithUnitOfWorkAsync(async () =>
            {
                Invitation invitation = await InvitationRepository.GetAsync(invitationId);
                SetPropertyByReflection(invitation, nameof(Invitation.CompletionDate), null);
                SetPropertyByReflection(invitation, nameof(Invitation.CancellationDate), DateTime.Now.AddDays(-1));
                SetPropertyByReflection(invitation, nameof(Invitation.ExpirationDate), DateTime.Now.AddDays(1));

                await InvitationRepository.UpdateAsync(invitation, true);
            });

            // Act & Assert
            await WithUnitOfWorkAsync(async () =>
            {
                await QuerySender.Send(new OnboardInvitationQuery(invitationId))
                    .ShouldThrowAsync<AbpValidationException>();
            });
        }
    }

    [Fact]
    public async Task? OnboardInvitationQuery_Should_Throw_AbpValidationException_If_Invitation_Completed()
    {
        Guid tenantId = GuidGenerator.Create();
        TestDataContext testDataContext = new(tenantId) { GenerateInvitations = true };
        AdministrationTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(testDataContext);
        Guid invitationId = storeContext.InvitationIds[0];
        using (CurrentTenant.Change(tenantId))
        {
            // Arrange
            await WithUnitOfWorkAsync(async () =>
            {
                Invitation invitation = await InvitationRepository.GetAsync(invitationId);
                SetPropertyByReflection(invitation, nameof(Invitation.CompletionDate), DateTime.Now.AddDays(-1));
                SetPropertyByReflection(invitation, nameof(Invitation.CancellationDate), null);
                SetPropertyByReflection(invitation, nameof(Invitation.ExpirationDate), DateTime.Now.AddDays(1));

                await InvitationRepository.UpdateAsync(invitation, true);
            });

            // Act & Assert
            await WithUnitOfWorkAsync(async () =>
            {
                await QuerySender.Send(new OnboardInvitationQuery(invitationId))
                    .ShouldThrowAsync<AbpValidationException>();
            });
        }
    }

    [Fact]
    public async Task? OnboardInvitationQuery_Should_Throw_AbpValidationException_If_Invitation_Expired()
    {
        Guid tenantId = GuidGenerator.Create();
        TestDataContext testDataContext = new(tenantId) { GenerateInvitations = true };
        AdministrationTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(testDataContext);
        Guid invitationId = storeContext.InvitationIds[0];
        using (CurrentTenant.Change(tenantId))
        {
            // Arrange
            await WithUnitOfWorkAsync(async () =>
            {
                Invitation invitation = await InvitationRepository.GetAsync(invitationId);
                SetPropertyByReflection(invitation, nameof(Invitation.CompletionDate), null);
                SetPropertyByReflection(invitation, nameof(Invitation.CancellationDate), null);
                SetPropertyByReflection(invitation, nameof(Invitation.ExpirationDate), DateTime.Now.AddDays(-1));

                await InvitationRepository.UpdateAsync(invitation, true);
            });


            // Act & Assert
            await WithUnitOfWorkAsync(async () =>
            {
                await QuerySender.Send(new OnboardInvitationQuery(invitationId))
                    .ShouldThrowAsync<AbpValidationException>();
            });
        }
    }

    [Fact]
    public async Task? OnboardInvitationQuery_Should_Return_OnboardingUri_If_Invitation_Valid()
    {
        const string expectedUri = "https://onboardinguri/";

        Guid tenantId = GuidGenerator.Create();
        TestDataContext testDataContext = new(tenantId) { GenerateInvitations = true };
        AdministrationTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(testDataContext);
        Guid invitationId = storeContext.InvitationIds[0];
        using (CurrentTenant.Change(tenantId))
        {
            // Arrange
            await WithUnitOfWorkAsync(async () =>
            {
                Invitation invitation = await InvitationRepository.GetAsync(invitationId);
                SetPropertyByReflection(invitation, nameof(Invitation.CompletionDate), null);
                SetPropertyByReflection(invitation, nameof(Invitation.CancellationDate), null);
                SetPropertyByReflection(invitation, nameof(Invitation.ExpirationDate), DateTime.Now.AddDays(1));
                invitation.OnboardingUri = expectedUri;

                await InvitationRepository.UpdateAsync(invitation, true);
            });

            // Act & Assert
            await WithUnitOfWorkAsync(async () =>
            {
                OnboardInvitationDto dto = await QuerySender.Send(new OnboardInvitationQuery(invitationId));
                dto.OnboardingUri.ShouldBe(expectedUri);
            });
        }
    }

    private static void SetPropertyByReflection<T>(T instance, string fieldName, object? value)
    {
        PropertyInfo? property = typeof(Invitation).GetProperty(fieldName, BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
        property!.SetValue(instance, value);
    }
}