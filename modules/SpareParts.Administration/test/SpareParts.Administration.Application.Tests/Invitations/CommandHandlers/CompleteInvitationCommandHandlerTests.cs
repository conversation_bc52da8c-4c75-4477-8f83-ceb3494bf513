using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using NSubstitute;
using Shouldly;
using SpareParts.Administration.Companies.Commands;
using SpareParts.Administration.Companies.Dtos;
using SpareParts.Administration.Generated.Identity;
using SpareParts.Administration.Invitations.Commands;
using SpareParts.Administration.Invitations.Dtos;
using SpareParts.Common.Companies;
using System;
using System.Threading.Tasks;
using Volo.Abp.Modularity;
using Volo.Abp.Users;
using Volo.Abp.Validation;
using Xunit;

namespace SpareParts.Administration.Invitations.CommandHandlers;

public abstract class CompleteInvitationCommandHandlerTests<TStartupModule> : InvitationTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    private ICurrentUser CurrentUser => ServiceProvider.GetRequiredService<ICurrentUser>();
    private DateTimeOffset? _completionDate;

    protected override void AfterAddApplication(IServiceCollection services)
    {
        IIdentity identityApiClient = Substitute.For<IIdentity>();
        identityApiClient.CreateInvitationAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<InvitationCreationDto>())
            .Returns(_ => new Generated.Identity.InvitationDto
            {
                Code = Guid.NewGuid().ToString(),
                OnboardingUri = "https://identity.com/onboarding/complete/1234",
                ExpirationDate = DateTimeOffset.UtcNow.AddDays(1)
            });
        identityApiClient.GetAnInvitationAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>()).Returns(_ =>
            new Generated.Identity.InvitationDto
            {
                CompleteDate = _completionDate
            });
        services.Replace(ServiceDescriptor.Singleton(identityApiClient));
    }

    [Fact]
    public async Task Handle_WhenInvitationIsCompleted_ShouldCreateUser()
    {
        Guid tenantId = await CreateTenantAsync(Guid.NewGuid().ToString("N"), Guid.NewGuid().ToString("N"));
        TestDataContext testDataContext = new(tenantId) { GenerateInvitations = true, GenerateBranding = true };
        await SeedDataForTenantAsync(testDataContext);
        using (CurrentTenant.Change(tenantId))
        {
            // Arrange
            CompanyDto companyDto = null!;
            InvitationCreatedDto invitationCreatedDto = null!;
            InvitationCompletedDto invitationCompletedDto = null!;

            await WithUnitOfWorkAsync(async () =>
            {
                CreateCompanyCommand createCompanyCommand = new("code", "name", "legalName", CompanyType.Internal);
                companyDto = await CommandSender.Send(createCompanyCommand);
            });
            await WithUnitOfWorkAsync(async () =>
            {
                CreateInvitationCommand createInvitationCommand = new("<EMAIL>", ["configuration-administrator"], "en", companyDto.Id);
                invitationCreatedDto = await CommandSender.Send(createInvitationCommand);
            });

            // Act
            await WithUnitOfWorkAsync(async () =>
            {
                CompleteInvitationCommand command = new(invitationCreatedDto.Id);
                _completionDate = DateTimeOffset.UtcNow;
                invitationCompletedDto = await CommandSender.Send(command);
            });

            // Assert
            await WithUnitOfWorkAsync(async () =>
            {
                invitationCompletedDto.CompanyId.ShouldBe(companyDto.Id);
                invitationCompletedDto.User.ExternalId.ShouldBe(CurrentUser.Id!.Value);
                DateTime? completionDate = (await InvitationRepository.GetAsync(invitationCreatedDto.Id)).CompletionDate;
                completionDate.ShouldNotBeNull();
            });
        }
    }

    [Fact]
    public async Task Handle_WhenInvitationIsNotCompleted_ShouldThrowValidationException()
    {
        Guid tenantId = await CreateTenantAsync(Guid.NewGuid().ToString("N"), Guid.NewGuid().ToString("N"));
        TestDataContext testDataContext = new(tenantId) { GenerateInvitations = true, GenerateBranding = true };
        await SeedDataForTenantAsync(testDataContext);

        using (CurrentTenant.Change(tenantId))
        {
            // Arrange
            CompanyDto companyDto = null!;
            InvitationCreatedDto invitationCreatedDto = null!;

            await WithUnitOfWorkAsync(async () =>
            {
                CreateCompanyCommand createCompanyCommand = new("code", "name", "legalName", CompanyType.Internal);
                companyDto = await CommandSender.Send(createCompanyCommand);
            });
            await WithUnitOfWorkAsync(async () =>
            {
                CreateInvitationCommand createInvitationCommand = new("<EMAIL>", ["configuration-administrator"], "en", companyDto.Id);
                invitationCreatedDto = await CommandSender.Send(createInvitationCommand);
            });

            // Act & Assert
            await WithUnitOfWorkAsync(async () =>
            {
                _completionDate = null;
                await CommandSender.Send(new CompleteInvitationCommand(invitationCreatedDto.Id)).ShouldThrowAsync<AbpValidationException>();
            });
        }
    }
}