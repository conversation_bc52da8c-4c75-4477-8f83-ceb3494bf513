using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using NSubstitute;
using Shouldly;
using SpareParts.Administration.Generated.Identity;
using SpareParts.Administration.Invitations.Commands;
using SpareParts.Administration.Invitations.Dtos;
using SpareParts.Common;
using SpareParts.Common.Companies;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;
using Volo.Abp.Validation;
using Xunit;

namespace SpareParts.Administration.Invitations.CommandHandlers;

public abstract class CreateInvitationCommandHandlerTests<TStartupModule> : InvitationTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    private readonly string _code = Guid.NewGuid().ToString();
    private readonly DateTimeOffset _expirationDate = DateTimeOffset.UtcNow.AddDays(1);

    protected override void AfterAddApplication(IServiceCollection services)
    {
        IIdentity identityApiClient = Substitute.For<IIdentity>();
        identityApiClient.CreateInvitationAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<InvitationCreationDto>())
            .Returns(_ => new Generated.Identity.InvitationDto
            {
                Code = _code,
                OnboardingUri = "https://identity.com/onboarding/complete/1234",
                ExpirationDate = _expirationDate
            });
        services.Replace(ServiceDescriptor.Singleton(identityApiClient));
    }

    [Fact]
    public async Task CreateInvitationCommand_Should_Create_Invitation()
    {
        Guid tenantId = await CreateTenantAsync(Guid.NewGuid().ToString("N"), Guid.NewGuid().ToString("N"));
        TestDataContext testDataContext = new(tenantId) { GenerateInvitations = true, GenerateBranding = true };
        AdministrationTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(testDataContext);
        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid companyId = storeContext.InternalCompanyId;
                Company company = await CompanyRepository.GetAsync(companyId);

                string name = Faker.Name.LastName();
                string firstName = Faker.Name.FirstName();
                string email = Faker.Internet.Email();
                List<string> roles = [IdentityRoles.ContentViewer];
                string language = Faker.Locale;

                CreateInvitationCommand createInvitationCommand = new(email, roles, language, companyId)
                {
                    Name = name,
                    FirstName = firstName
                };

                // Act
                InvitationCreatedDto invitationCreatedDto = await CommandSender.Send(createInvitationCommand);

                // Assert
                invitationCreatedDto.Code.ShouldBe(_code);
                invitationCreatedDto.FirstName.ShouldBe(firstName);
                invitationCreatedDto.Name.ShouldBe(name);
                invitationCreatedDto.Email.ShouldBe(email);
                invitationCreatedDto.Roles.ShouldBe(roles);
                invitationCreatedDto.IsInternal.ShouldBe(company.Type == CompanyType.Internal);
                invitationCreatedDto.CompanyId.ShouldBe(companyId);
                invitationCreatedDto.ExpirationDate.ShouldBe(_expirationDate.UtcDateTime);
            });
        }
    }

    [Fact]
    public async Task CreateInvitationCommand_Should_Throw_Exception_With_Wrong_Arguments()
    {
        Guid tenantId = GuidGenerator.Create();
        TestDataContext testDataContext = new(tenantId) { GenerateInvitations = true, GenerateBranding = true };
        AdministrationTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(testDataContext);
        using (CurrentTenant.Change(tenantId))
        {
            Guid companyId = Guid.Empty;
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                companyId = storeContext.InternalCompanyId;

                string existingCode = Faker.Commerce.ProductName();

                List<InvitationRole> invitationRoles = [new InvitationRole(IdentityRoles.ConfigurationAdministrator)];
                Invitation invitation = await InvitationDomainService.CreateAsync(Guid.NewGuid(), existingCode, Faker.Internet.Email(), invitationRoles, Faker.Locale, companyId, DateTime.Now);
                await InvitationRepository.InsertAsync(invitation, true);
            });

            await WithUnitOfWorkAsync(async () =>
            {
                string name = Faker.Name.LastName();
                string firstName = Faker.Name.FirstName();
                string email = Faker.Internet.Email();
                List<string> roles = [IdentityRoles.ContentManager];
                string language = Faker.Locale;

                // Act & Assert
                await CommandSender.Send(new CreateInvitationCommand(string.Empty, roles, language, companyId)
                {
                    Name = name,
                    FirstName = firstName
                }).ShouldThrowAsync<AbpValidationException>();

                await CommandSender.Send(new CreateInvitationCommand(email, [], language, companyId)
                {
                    Name = name,
                    FirstName = firstName
                }).ShouldThrowAsync<AbpValidationException>();

                await CommandSender.Send(new CreateInvitationCommand(email, roles, string.Empty, companyId)
                {
                    Name = name,
                    FirstName = firstName
                }).ShouldThrowAsync<AbpValidationException>();

                await CommandSender.Send(new CreateInvitationCommand(email, roles, language, Guid.Empty)
                {
                    Name = name,
                    FirstName = firstName
                }).ShouldThrowAsync<AbpValidationException>();

                await CommandSender.Send(new CreateInvitationCommand(email, roles, language, GuidGenerator.Create())
                {
                    Name = name,
                    FirstName = firstName
                }).ShouldThrowAsync<EntityNotFoundException>();
            });
        }
    }

    [Fact]
    public async Task CreateInvitationCommand_Should_Throw_Exception_If_Max_Length_Exceed()
    {
        Guid tenantId = GuidGenerator.Create();
        TestDataContext testDataContext = new(tenantId) { GenerateInvitations = true, GenerateBranding = true };
        AdministrationTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(testDataContext);
        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid companyId = storeContext.InternalCompanyId;

                string name = Faker.Name.LastName();
                string firstName = Faker.Name.FirstName();
                string email = Faker.Internet.Email();
                List<string> roles = [IdentityRoles.ContentViewer];
                string language = Faker.Locale;

                // Act & Assert
                await CommandSender.Send(new CreateInvitationCommand(new string('x', AdministrationConsts.Invitation.EmailMaxLength + 1), roles, language, companyId)
                {
                    Name = name,
                    FirstName = firstName
                }).ShouldThrowAsync<AbpValidationException>();

                await CommandSender.Send(new CreateInvitationCommand(email, roles, new string('x', AdministrationConsts.Invitation.LanguageMaxLength + 1), companyId)
                {
                    Name = name,
                    FirstName = firstName
                }).ShouldThrowAsync<AbpValidationException>();

                await CommandSender.Send(new CreateInvitationCommand(email, roles, language, companyId)
                {
                    Name = new string('x', AdministrationConsts.Invitation.NameMaxLength + 1),
                    FirstName = firstName
                }).ShouldThrowAsync<AbpValidationException>();

                await CommandSender.Send(new CreateInvitationCommand(email, roles, language, companyId)
                {
                    Name = name,
                    FirstName = new string('x', AdministrationConsts.Invitation.FirstNameMaxLength + 1)
                }).ShouldThrowAsync<AbpValidationException>();
            });
        }
    }

    [Fact]
    public async Task CreateInvitationCommand_Should_Throw_Exception_If_Roles_Are_Not_Conform_For_External_Company()
    {
        Guid tenantId = GuidGenerator.Create();
        TestDataContext testDataContext = new(tenantId) { GenerateInvitations = true, GenerateBranding = true };
        await SeedDataForTenantAsync(testDataContext);
        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Company company = await CompanyRepository.FirstAsync(c => c.Type == CompanyType.External);
                Guid companyId = company.Id;

                string name = Faker.Name.LastName();
                string firstName = Faker.Name.FirstName();
                string email = Faker.Internet.Email();
                List<string> roles = [IdentityRoles.ConfigurationAdministrator];
                string language = Faker.Locale;

                CreateInvitationCommand createInvitationCommand = new(email, roles, language, companyId)
                {
                    Name = name,
                    FirstName = firstName
                };

                // Act & Assert
                await CommandSender.Send(createInvitationCommand).ShouldThrowAsync<AbpValidationException>();
            });
        }
    }
}