using System;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;

namespace SpareParts.Administration.Invitations;
public abstract class InvitationTestBase<TStartupModule> : AdministrationApplicationTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    protected readonly IRepository<Invitation, Guid> InvitationRepository;
    protected readonly InvitationDomainService InvitationDomainService;

    protected InvitationTestBase()
    {
        InvitationRepository = ServiceProvider.GetRequiredService<IRepository<Invitation, Guid>>();
        InvitationDomainService = ServiceProvider.GetRequiredService<InvitationDomainService>();
    }

}
