using System.Threading.Tasks;
using Shouldly;
using SpareParts.Administration.Tenants.Commands;
using SpareParts.Administration.Tenants.Dtos;
using SpareParts.Common.Companies;
using SpareParts.Common.Tenants;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;
using Volo.Abp.TenantManagement;
using Xunit;

namespace SpareParts.Administration.Tenants.CommandHandlers;

public abstract class CreateOrEnableTenantCommandHandlerTests<TStartupModule> : AdministrationApplicationTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    private ITenantRepository TenantRepository => GetRequiredService<ITenantRepository>();

    [Fact]
    public async Task Should_Create_New_Tenant_If_Not_Exists()
    {
        // Arrange
        const string tenantName = "NewTenant";
        const string tenantDisplayName = "NewTenantDisplayName";
        CreateOrEnableTenantCommand command = new(tenantName, tenantDisplayName);

        // Act
        TenantDto tenantDto = await CommandSender.Send(command);

        // Assert
        await WithUnitOfWorkAsync(async () =>
        {
            Tenant? tenant = await TenantRepository.FindAsync(tenantDto.TenantId);
            tenant.ShouldNotBeNull();
            tenant.Id.ShouldBe(tenantDto.TenantId);
            tenant.IsEnabled().ShouldBeTrue();
            tenant.Name.ShouldBe(tenantName);
            tenant.DisplayName().ShouldBe(tenantDisplayName);
            using (CurrentTenant.Change(tenant.Id, tenant.Name))
            {
                Company company = await CompanyRepository.FirstAsync(c => c.Code.Equals(tenantName));
                company.Id.ShouldBe(tenantDto.CompanyId);
                company.Code.ShouldBe(tenantName);
                company.Name.ShouldBe(tenantDisplayName);
                company.LegalName.ShouldBe(tenantDisplayName);
                company.Type.ShouldBe(CompanyType.Internal);
                company.TenantId.ShouldBe(tenant.Id);
            }
        });
    }

    [Fact]
    public async Task Should_Enable_Tenant_If_Exists_But_Disabled()
    {
        // Arrange
        const string tenantName = "ExistingDisabledTenant";
        const string tenantDisplayName = "NewTenantDisplayName";
        CreateOrEnableTenantCommand command = new(tenantName, tenantDisplayName);
        DisableTenantCommand disableTenantCommand = new(tenantName);

        // Act
        await CommandSender.Send(command);
        await CommandSender.Send(disableTenantCommand);
        TenantDto tenantDto = await CommandSender.Send(command);

        // Assert
        await WithUnitOfWorkAsync(async () =>
        {
            Tenant? tenant = await TenantRepository.FindAsync(tenantDto.TenantId);
            tenant.ShouldNotBeNull();
            tenant.Id.ShouldBe(tenantDto.TenantId);
            tenant.IsEnabled().ShouldBeTrue();
            tenant.Name.ShouldBe(tenantName);
            tenant.DisplayName().ShouldBe(tenantDisplayName);
        });
    }

    [Fact]
    public async Task Should_Not_Change_Tenant_If_Exists_And_Enabled()
    {
        // Arrange
        const string tenantName = "ExistingEnabledTenant";
        const string tenantDisplayName = "ExistingTenantDisplayName";
        CreateOrEnableTenantCommand command = new(tenantName, tenantDisplayName);

        // Act
        await CommandSender.Send(command);
        TenantDto tenantDto = await CommandSender.Send(command);

        // Assert
        await WithUnitOfWorkAsync(async () =>
        {
            Tenant? tenant = await TenantRepository.FindAsync(tenantDto.TenantId);
            tenant.ShouldNotBeNull();
            tenant.Id.ShouldBe(tenantDto.TenantId);
            tenant.IsEnabled().ShouldBeTrue();
            tenant.Name.ShouldBe(tenantName);
            tenant.DisplayName().ShouldBe(tenantDisplayName);
            using (CurrentTenant.Change(tenant.Id, tenant.Name))
            {
                Company company = await CompanyRepository.FirstAsync(c => c.Code.Equals(tenantName));
                company.Id.ShouldBe(tenantDto.CompanyId);
                company.Code.ShouldBe(tenantName);
                company.Name.ShouldBe(tenantDisplayName);
                company.LegalName.ShouldBe(tenantDisplayName);
                company.Type.ShouldBe(CompanyType.Internal);
                company.TenantId.ShouldBe(tenant.Id);
            }
        });
    }

}