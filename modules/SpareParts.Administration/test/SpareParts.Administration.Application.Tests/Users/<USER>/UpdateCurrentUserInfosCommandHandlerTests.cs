using System;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Shouldly;
using SpareParts.Administration.Users.Commands;
using SpareParts.Administration.Users.Dtos;
using Volo.Abp.Identity;
using Volo.Abp.Modularity;
using Volo.Abp.Security.Claims;
using Xunit;

namespace SpareParts.Administration.Users.CommandHandlers;

public abstract class UpdateCurrentUserInfosCommandHandlerTests<TStartupModule> : UserTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    private readonly ICurrentPrincipalAccessor _currentPrincipalAccessor;

    protected UpdateCurrentUserInfosCommandHandlerTests()
    {
        _currentPrincipalAccessor = ServiceProvider.GetRequiredService<ICurrentPrincipalAccessor>();
    }

    [Fact]
    public async Task UpdateCurrentUserInfosCommandHandler_Should_Update_User_Infos()
    {
        Guid tenantId = GuidGenerator.Create();
        TestDataContext testDataContext = new(tenantId) { GenerateUsers = true };
        AdministrationTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(testDataContext);

        string newFirstName = Guid.NewGuid().ToString();
        string newLastName = Guid.NewGuid().ToString();

        Guid userId = storeContext.UserIds[0];

        using (CurrentTenant.Change(tenantId))
        {
            IdentityUser user = default!;
            await WithUnitOfWorkAsync(async () =>
            {
                user = await UserRepository.GetAsync(userId);
                user.Name.ShouldNotBe(newFirstName);
                user.Surname.ShouldNotBe(newLastName);
            });

            UpdatedUserInfosResponseDto dto = null!;
            using (_currentPrincipalAccessor.Change([
                       new Claim(AbpClaimTypes.UserId, user.GetExternalId().ToString())
                   ]))
            {
                await WithUnitOfWorkAsync(async () =>
                {
                    dto = await CommandSender.Send(new UpdateCurrentUserInfosCommand(newFirstName, newLastName));
                });
            }
            dto.LastName.ShouldBe(newLastName);
            dto.FirstName.ShouldBe(newFirstName);
            user = await UserRepository.GetAsync(userId);
            user.Name.ShouldBe(newFirstName);
            user.Surname.ShouldBe(newLastName);
        }
    }
}