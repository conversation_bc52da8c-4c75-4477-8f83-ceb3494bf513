using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using NSubstitute;
using Shouldly;
using SpareParts.Administration.Users.Commands;
using SpareParts.Administration.Users.Dtos.Inputs;
using SpareParts.Common;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Identity;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Administration.Users.CommandHandlers;
public abstract class UpdateUserRolesCommandHandlerTests<TStartupModule> : UserTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    protected override void AfterAddApplication(IServiceCollection services)
    {
        Generated.Identity.IIdentity identityApiClient = Substitute.For<Generated.Identity.IIdentity>();
        identityApiClient.UpdateRolesAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>(),
            Arg.Any<IEnumerable<string>>()).Returns(_ => []);
        services.Replace(ServiceDescriptor.Singleton(identityApiClient));
    }

    [Fact]
    public async Task UpdateUserRoles_Should_Update_User_Roles()
    {
        Guid tenantId = GuidGenerator.Create();
        TestDataContext testDataContext = new(tenantId) { GenerateUsers = true };
        AdministrationTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(testDataContext);
        using (CurrentTenant.Change(tenantId))
        {
            Guid userId = Guid.Empty;
            IdentityUser user = null!;
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                userId = storeContext.UserIds[0];
                user = await UserRepository.GetAsync(userId);
                (await UserManager.SetRolesAsync(user, [IdentityRoles.AccountManager])).CheckErrors();
                await UserRepository.UpdateAsync(user);
            });

            await WithUnitOfWorkAsync(async () =>
            {
                List<string> newRoles = [IdentityRoles.ContentViewer, IdentityRoles.ContentManager];

                // Act 
                await CommandSender.Send(new UpdateUserRolesCommand(userId, newRoles));

                // Assert
                IList<string> results = await UserManager.GetRolesAsync(user);
                results.ShouldContain(newRoles[0]);
                results.ShouldContain(newRoles[1]);
            });
        }
    }

    [Fact]
    public async Task UpdateUserRoles_Should_Throw_Exception_If_User_Not_Exist()
    {
        Guid tenantId = GuidGenerator.Create();
        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid userId = GuidGenerator.Create();

                List<string> newRoles = [IdentityRoles.ContentViewer, IdentityRoles.ContentManager];

                // Act & Assert
                await CommandSender.Send(new UpdateUserRolesCommand(userId, newRoles)).ShouldThrowAsync<EntityNotFoundException>();
            });
        }
    }

    [Fact]
    public async Task UpdateUserRoles_Should_Throw_Exception_If_Roles_Is_Empty()
    {
        Guid tenantId = GuidGenerator.Create();
        TestDataContext testDataContext = new(tenantId) { GenerateUsers = true };
        AdministrationTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(testDataContext);
        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid userId = storeContext.UserIds[0];
                await UserRepository.GetAsync(userId);

                List<string> newRoles = [];

                // Act & Assert
                await CommandSender.Send(new UpdateUserRolesCommand(userId, newRoles)).ShouldThrowAsync<ArgumentException>();
            });
        }
    }

    [Fact]
    public async Task UpdateUserRoles_Should_Throw_Exception_If_Role_Not_Exist()
    {
        Guid tenantId = GuidGenerator.Create();
        TestDataContext testDataContext = new(tenantId) { GenerateUsers = true };
        AdministrationTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(testDataContext);
        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid userId = storeContext.UserIds[0];
                await UserRepository.GetAsync(userId);

                List<string> newRoles = ["Incorrect"];

                // Act & Assert
                await CommandSender.Send(new UpdateUserRolesCommand(userId, newRoles)).ShouldThrowAsync<InvalidOperationException>();
            });
        }
    }

    [Fact]
    public void UpdateUserRolesDto_Validation_Should_Throw_Exception_If_Roles_Is_Empty()
    {
        // Arrange
        UpdateUserRolesDto updateUserRolesDto = new()
        {
            Roles = []
        };

        // Act
        IList<ValidationResult> errors = ValidateModel(updateUserRolesDto);

        // Assert
        errors.Count.ShouldBe(1);
        errors[0].ErrorMessage!.ShouldContain("Roles");
    }
}
