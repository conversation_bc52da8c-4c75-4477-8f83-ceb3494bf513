using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Shouldly;
using SpareParts.Administration.Users.Dtos;
using SpareParts.Administration.Users.Queries;
using Volo.Abp.Identity;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Administration.Users.QueryHandlers;
public abstract class GetUserByIdQueryHandlerTests<TStartupModule> : UserTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    [Fact]
    public async Task GetUserById_Should_Return_User()
    {
        Guid tenantId = GuidGenerator.Create();
        TestDataContext testDataContext = new(tenantId) { GenerateUsers = true };
        AdministrationTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(testDataContext);

        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                Guid userId = storeContext.UserIds[0];
                IdentityUser user = await UserRepository.GetAsync(userId);
                IList<string> roles = await UserManager.GetRolesAsync(user);

                // Act
                UserDto result = await QuerySender.Send(new GetUserByIdQuery(userId));

                // Assert
                result.ShouldNotBeNull();
                result.Id.ShouldBe(userId);
                result.FirstName.ShouldBe(user.Name);
                result.LastName.ShouldBe(user.Surname);
                result.Email.ShouldBe(user.Email);
                result.Company.Id.ShouldBe(user.GetCompanyId());
                result.Roles.Count.ShouldBe(roles.Count);
            });
        }
    }

    [Fact]
    public async Task GetUserById_Should_Throw_Exception_If_Not_Exist()
    {
        Guid tenantId = GuidGenerator.Create();
        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Act
                await QuerySender.Send(new GetUserByIdQuery(GuidGenerator.Create())).ShouldThrowAsync<InvalidOperationException>();
            });
        }
    }
}
