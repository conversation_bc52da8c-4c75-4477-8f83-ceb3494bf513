using Microsoft.Extensions.DependencyInjection;
using System;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using Volo.Abp.Modularity;

namespace SpareParts.Administration.Users;
public abstract class UserTestBase<TStartupModule> : AdministrationApplicationTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    protected readonly IRepository<IdentityUser, Guid> UserRepository;
    protected readonly IdentityUserManager UserManager;

    protected UserTestBase()
    {
        UserRepository = ServiceProvider.GetRequiredService<IRepository<IdentityUser, Guid>>();
        UserManager = ServiceProvider.GetRequiredService<IdentityUserManager>();
    }
}
