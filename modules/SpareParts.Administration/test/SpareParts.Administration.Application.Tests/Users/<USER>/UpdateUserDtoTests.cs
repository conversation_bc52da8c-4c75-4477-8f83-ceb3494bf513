using Shouldly;
using SpareParts.Administration.Users.Dtos.Inputs;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.Administration.Users.Dtos;
public abstract class UpdateUserDtoTests<TStartupModule> : UserTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    [Fact]
    public void UpdateUserDto_Should_Be_Successfully_Instantiated()
    {
        // Arrange
        UpdateUserDto updateUserDto = new()
        {
            CompanyId = GuidGenerator.Create(),
            Roles = ["content-viewer"]
        };

        // Act
        IList<ValidationResult> errors = ValidateModel(updateUserDto);

        // Assert
        errors.Count.ShouldBe(0);
    }

    [Fact]
    public void UpdateUserDto_Should_Throw_Errors_If_Roles_Is_Null()
    {
        // Arrange
        UpdateUserDto updateUserDto = new()
        {
            CompanyId = GuidGenerator.Create()
        };

        // Act
        IList<ValidationResult> errors = ValidateModel(updateUserDto);

        // Assert
        errors.Count.ShouldBe(1);
        errors[0].ErrorMessage!.ShouldContain("The Roles field is required.");
    }

    [Fact]
    public void UpdateUserDto_Should_Throw_Errors_If_Roles_Is_Empty()
    {
        // Arrange
        UpdateUserDto updateUserDto = new()
        {
            CompanyId = GuidGenerator.Create(),
            Roles = []
        };

        // Act
        IList<ValidationResult> errors = ValidateModel(updateUserDto);

        // Assert
        errors.Count.ShouldBe(1);
        errors[0].ErrorMessage!.ShouldContain("minimum length");
    }
}
