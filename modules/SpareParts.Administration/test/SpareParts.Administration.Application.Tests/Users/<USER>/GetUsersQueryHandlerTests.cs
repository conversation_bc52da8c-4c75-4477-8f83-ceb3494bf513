using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Shouldly;
using SpareParts.Administration.Users.Dtos;
using SpareParts.Administration.Users.Queries;
using SpareParts.Administration.Users.QueryFilters;
using SpareParts.Common;
using SpareParts.Common.Companies;
using SpareParts.Common.Helpers;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Identity;
using Volo.Abp.Modularity;
using Volo.Abp.Validation;
using Xunit;

namespace SpareParts.Administration.Users.QueryHandlers;
public abstract class GetUsersQueryHandlerTests<TStartupModule> : UserTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    private readonly Repositories.IIdentityRoleRepository _roleRepository;
    private readonly IExpressionCreator _expressionCreator;

    protected GetUsersQueryHandlerTests()
    {
        _roleRepository = ServiceProvider.GetRequiredService<Repositories.IIdentityRoleRepository>();
        _expressionCreator = ServiceProvider.GetRequiredService<IExpressionCreator>();
    }

    [Fact]
    public async Task GetUsersQuery_Should_Return_List_Filtered_By_Mail_Contains()
    {
        Guid tenantId = GuidGenerator.Create();
        TestDataContext testDataContext = new(tenantId) { GenerateUsers = true };
        AdministrationTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(testDataContext);

        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                Guid userId = storeContext.UserIds[0];
                IdentityUser user = await UserRepository.GetAsync(userId);
                string truncatedEmail = user.Email.Substring(1, 2).ToUpper();

                List<IdentityUser> users = await UserRepository.GetListAsync(x => x.Email.Contains(truncatedEmail) || x.Name.Contains(truncatedEmail) || x.Surname.Contains(truncatedEmail));

                // Act
                PagedResultDto<UserDto> results = await QuerySender.Send(
                    new GetUsersQuery(new UserPaginationFilter
                    {
                        Email = truncatedEmail,
                        PerPage = users.Count
                    }));

                // Assert
                results.TotalCount.ShouldBe(users.Count);
                results.Items.Count.ShouldBe(users.Count);
                IEnumerable<Guid> ids = users.Select(x => x.Id).Intersect(results.Items.Select(x => x.Id));
                ids.Count().ShouldBe(users.Count);
            });
        }
    }

    [Fact]
    public async Task GetUsersQuery_Should_Return_List_Filtered_By_FirstName_Contains()
    {
        Guid tenantId = GuidGenerator.Create();
        TestDataContext testDataContext = new(tenantId) { GenerateUsers = true };
        AdministrationTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(testDataContext);

        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                Guid userId = storeContext.UserIds[0];
                IdentityUser user = await UserRepository.GetAsync(userId);
                string truncatedEmail = user.Name.Substring(2).ToUpper();

                List<IdentityUser> users = await UserRepository.GetListAsync(x => x.Email.Contains(truncatedEmail) || x.Name.Contains(truncatedEmail) || x.Surname.Contains(truncatedEmail));

                // Act
                PagedResultDto<UserDto> results = await QuerySender.Send(
                    new GetUsersQuery(new UserPaginationFilter
                    {
                        Email = truncatedEmail,
                        PerPage = users.Count
                    }));

                // Assert
                results.TotalCount.ShouldBe(users.Count);
                results.Items.Count.ShouldBe(users.Count);
                IEnumerable<Guid> ids = users.Select(x => x.Id).Intersect(results.Items.Select(x => x.Id));
                ids.Count().ShouldBe(users.Count);
            });
        }
    }

    [Fact]
    public async Task GetUsersQuery_Should_Return_List_Filtered_By_LastName_Contains()
    {
        Guid tenantId = GuidGenerator.Create();
        TestDataContext testDataContext = new(tenantId) { GenerateUsers = true };
        AdministrationTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(testDataContext);

        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                Guid userId = storeContext.UserIds[0];
                IdentityUser user = await UserRepository.GetAsync(userId);
                string truncatedEmail = user.Surname.Substring(2).ToUpper();

                List<IdentityUser> users = await UserRepository.GetListAsync(x => x.Email.Contains(truncatedEmail) || x.Name.Contains(truncatedEmail) || x.Surname.Contains(truncatedEmail));

                // Act
                PagedResultDto<UserDto> results = await QuerySender.Send(
                    new GetUsersQuery(new UserPaginationFilter
                    {
                        Email = truncatedEmail,
                        PerPage = users.Count
                    }));

                // Assert
                results.TotalCount.ShouldBe(users.Count);
                results.Items.Count.ShouldBe(users.Count);
                IEnumerable<Guid> ids = users.Select(x => x.Id).Intersect(results.Items.Select(x => x.Id));
                ids.Count().ShouldBe(users.Count);
            });
        }
    }

    [Fact]
    public async Task GetUsersQuery_Should_Return_List_Filtered_By_CompanyId()
    {
        Guid tenantId = GuidGenerator.Create();
        TestDataContext testDataContext = new(tenantId) { GenerateUsers = true };
        AdministrationTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(testDataContext);

        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                Guid userId = storeContext.UserIds[0];
                IdentityUser user = await UserRepository.GetAsync(userId);

                List<IdentityUser> users = await UserRepository.GetListAsync(_expressionCreator.GetEqualsExpression<IdentityUser, Guid>(AdministrationModuleExtensionConfigurator.CompanyIdPropertyName, user.GetCompanyId()));

                // Act
                PagedResultDto<UserDto> results = await QuerySender.Send(
                    new GetUsersQuery(new UserPaginationFilter
                    {
                        CompanyId = user.GetCompanyId(),
                        PerPage = users.Count
                    }));

                // Assert
                results.TotalCount.ShouldBe(users.Count);
                results.Items.Count.ShouldBe(users.Count);
                IEnumerable<Guid> ids = users.Select(x => x.Id).Intersect(results.Items.Select(x => x.Id));
                ids.Count().ShouldBe(users.Count);
            });
        }
    }

    [Fact]
    public async Task GetUsersQuery_Should_Return_List_Filtered_By_Roles()
    {
        Guid tenantId = GuidGenerator.Create();
        TestDataContext testDataContext = new(tenantId) { GenerateUsers = true };
        await SeedDataForTenantAsync(testDataContext);

        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                List<string> roles = [IdentityRoles.AccountManager];
                List<Guid> idRoles = await _roleRepository.GetIdsByNamesAsync(roles.ToHashSet());
                List<IdentityUser> users = await UserRepository.GetListAsync(x => x.Roles.Any(y => idRoles.Contains(y.RoleId)));

                // Act
                PagedResultDto<UserDto> results = await QuerySender.Send(new GetUsersQuery(new UserPaginationFilter { Roles = roles, PerPage = users.Count }));

                // Assert
                results.TotalCount.ShouldBe(users.Count);
                results.Items.Count.ShouldBe(users.Count);
                IEnumerable<Guid> ids = users.Select(x => x.Id).Intersect(results.Items.Select(x => x.Id));
                ids.Count().ShouldBe(users.Count);
            });
        }
    }

    [Fact]
    public async Task GetUsersQuery_Should_Return_List_Filtered_By_Type()
    {
        Guid tenantId = GuidGenerator.Create();
        TestDataContext testDataContext = new(tenantId) { GenerateUsers = true };
        await SeedDataForTenantAsync(testDataContext);

        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                CompanyType type = CompanyType.External;
                List<Company> companies = await CompanyRepository.GetListAsync(x => x.Type == type);
                IEnumerable<Guid> companyIds = companies.Select(x => x.Id);

                List<IdentityUser> users = await UserRepository.GetListAsync(_expressionCreator.GetContainsExpression<IdentityUser, Guid>(AdministrationModuleExtensionConfigurator.CompanyIdPropertyName, companyIds));

                // Act
                PagedResultDto<UserDto> results = await QuerySender.Send(new GetUsersQuery(new UserPaginationFilter { Type = type, PerPage = users.Count }));

                // Assert
                results.TotalCount.ShouldBe(users.Count);
                results.Items.Count.ShouldBe(users.Count);
                IEnumerable<Guid> ids = users.Select(x => x.Id).Intersect(results.Items.Select(x => x.Id));
                ids.Count().ShouldBe(users.Count);
            });
        }
    }

    [Fact]
    public async Task GetUsersQuery_Should_Return_List_Filtered_By_All_Parameters()
    {
        Guid tenantId = GuidGenerator.Create();
        TestDataContext testDataContext = new(tenantId) { GenerateUsers = true };
        AdministrationTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(testDataContext);

        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                Guid userId = storeContext.UserIds[0];
                IdentityUser user = await UserRepository.GetAsync(userId);
                string truncatedEmail = user.Email.Substring(1).ToUpper();

                List<string> roles = [.. IdentityRoles.AllRoles];
                List<Guid> idRoles = await _roleRepository.GetIdsByNamesAsync([.. roles]);

                List<IdentityUser> users = await UserRepository.GetListAsync(_expressionCreator.GetEqualsExpression<IdentityUser, Guid>(AdministrationModuleExtensionConfigurator.CompanyIdPropertyName, user.GetCompanyId()).AndAlso(CreateRoleAndEmailPredicate(idRoles, truncatedEmail)));

                // Act
                PagedResultDto<UserDto> results = await QuerySender.Send(new GetUsersQuery(
                        new UserPaginationFilter
                        {
                            Email = truncatedEmail,
                            CompanyId = user.GetCompanyId(),
                            Roles = roles,
                            PerPage = storeContext.UsersCount
                        }));

                // Assert
                results.TotalCount.ShouldBe(users.Count);
                results.Items.Count.ShouldBe(users.Count);
                IEnumerable<Guid> ids = users.Select(x => x.Id).Intersect(results.Items.Select(x => x.Id));
                ids.Count().ShouldBe(users.Count);
            });
        }
    }

    private static Expression<Func<IdentityUser, bool>> CreateRoleAndEmailPredicate(List<Guid> idRoles, string truncatedEmail)
    {
        return x =>
            x.Roles.Any(y => idRoles.Contains(y.RoleId)) &&
            x.Email.Contains(truncatedEmail);
    }

    [Fact]
    public async Task GetUsersQuery_Should_Throw_Exception_If_Sort_Is_Not_In_Possible_Sorting_List()
    {
        Guid tenantId = GuidGenerator.Create();
        TestDataContext testDataContext = new(tenantId) { GenerateUsers = true };
        await SeedDataForTenantAsync(testDataContext);

        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Act & Assert
                await Should.ThrowAsync<AbpValidationException>(async () => await QuerySender.Send(
                         new GetUsersQuery(new UserPaginationFilter
                         {
                             Sort = "WrongField"
                         })));
            });
        }
    }
}

public static class PredicateExtensions
{
    public static Expression<Func<T, bool>> AndAlso<T>(this Expression<Func<T, bool>> expr1, Expression<Func<T, bool>> expr2)
    {
        InvocationExpression invokedExpr = Expression.Invoke(expr2, expr1.Parameters);
        return Expression.Lambda<Func<T, bool>>(
            Expression.AndAlso(expr1.Body, invokedExpr),
            expr1.Parameters);
    }
}