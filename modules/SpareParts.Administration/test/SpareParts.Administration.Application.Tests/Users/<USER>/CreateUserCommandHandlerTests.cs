using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Shouldly;
using SpareParts.Administration.Users.Commands;
using SpareParts.Common;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Identity;
using Volo.Abp.Modularity;
using Volo.Abp.Validation;
using Xunit;

namespace SpareParts.Administration.Users.CommandHandlers;
public abstract class CreateUserCommandHandlerTests<TStartupModule> : UserTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    [Fact]
    public async Task CreateUserCommandHandler_Should_Create_User()
    {
        Guid tenantId = GuidGenerator.Create();
        TestDataContext testDataContext = new(tenantId) { GenerateUsers = true };
        AdministrationTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(testDataContext);

        // Arrange
        Guid companyId = storeContext.InternalCompanyId;
        Guid externalId = GuidGenerator.Create();
        string userName = Faker.Internet.UserName();
        string email = Faker.Internet.Email();
        string firstName = Faker.Name.FirstName();
        string lastName = Faker.Name.LastName();
        HashSet<string> roles = [IdentityRoles.ContentViewer];

        using (CurrentTenant.Change(tenantId))
        {
            Guid id = await WithUnitOfWorkAsync(async () => (await CommandSender.Send(new CreateUserCommand(externalId, userName, firstName, lastName, email, roles, companyId))).Id);

            await WithUnitOfWorkAsync(async () =>
            {
                // Assert
                IdentityUser user = await UserRepository.GetAsync(id);
                user.UserName.ShouldBe(userName);
                user.Name.ShouldBe(firstName);
                user.Email.ShouldBe(email);
                user.GetCompanyId().ShouldBe(companyId);
                user.ConcurrencyStamp.ShouldNotBeNull();
                user.IsExternal.ShouldBeTrue();
                IList<string> rolesResult = await UserManager.GetRolesAsync(user);
                rolesResult.ShouldContain(IdentityRoles.ContentViewer);
            });
        }
    }

    [Fact]
    public async Task CreateUserCommandHandler_Should_Throw_Exception_With_Wrong_Arguments()
    {
        Guid tenantId = GuidGenerator.Create();
        TestDataContext testDataContext = new(tenantId) { GenerateUsers = true };
        AdministrationTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(testDataContext);
        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid companyId = storeContext.InternalCompanyId;

                Guid userId = GuidGenerator.Create();
                string userName = Faker.Internet.UserName();
                string email = Faker.Internet.Email();
                string firstName = Faker.Name.FirstName();
                string lastName = Faker.Name.LastName();
                HashSet<string> roles = [IdentityRoles.ContentManager];

                // Act
                await CommandSender.Send(new CreateUserCommand(Guid.Empty, userName, firstName, lastName, email, roles, companyId)).ShouldThrowAsync<AbpValidationException>();
                await CommandSender.Send(new CreateUserCommand(userId, string.Empty, firstName, lastName, email, roles, companyId)).ShouldThrowAsync<AbpValidationException>();
                await CommandSender.Send(new CreateUserCommand(userId, userName, firstName, lastName, string.Empty, roles, companyId)).ShouldThrowAsync<AbpValidationException>();
                await CommandSender.Send(new CreateUserCommand(userId, userName, firstName, lastName, email, roles, Guid.Empty)).ShouldThrowAsync<AbpValidationException>();
                await CommandSender.Send(new CreateUserCommand(userId, userName, firstName, lastName, email, roles, Guid.NewGuid())).ShouldThrowAsync<EntityNotFoundException>();
            });
        }
    }
}
