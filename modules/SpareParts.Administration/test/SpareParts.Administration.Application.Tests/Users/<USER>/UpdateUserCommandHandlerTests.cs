using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using NSubstitute;
using Shouldly;
using SpareParts.Administration.Users.Commands;
using SpareParts.Administration.Users.Dtos;
using SpareParts.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Identity;
using Volo.Abp.Modularity;
using Volo.Abp.Validation;
using Xunit;

namespace SpareParts.Administration.Users.CommandHandlers;
public abstract class UpdateUserCommandHandlerTests<TStartupModule> : UserTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    protected override void AfterAddApplication(IServiceCollection services)
    {
        Generated.Identity.IIdentity identityApiClient = Substitute.For<Generated.Identity.IIdentity>();
        identityApiClient.UpdateRolesAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>(),
            Arg.Any<IEnumerable<string>>()).Returns(_ => []);
        services.Replace(ServiceDescriptor.Singleton(identityApiClient));
    }

    [Fact]
    public async Task UpdateUserCommandHandler_Should_Update_User()
    {
        Guid tenantId = GuidGenerator.Create();
        TestDataContext testDataContext = new(tenantId) { GenerateUsers = true };
        AdministrationTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(testDataContext);
        using (CurrentTenant.Change(tenantId))
        {
            HashSet<string> roles = [IdentityRoles.ConfigurationAdministrator, IdentityRoles.ContentManager];
            Guid companyId = storeContext.InternalCompanyId;
            Guid userId = Guid.Empty;
            IdentityUser user = default!;

            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                userId = storeContext.UserIds[0];
                user = await UserRepository.GetAsync(userId);

                if (storeContext.InternalCompanyId == user.GetCompanyId())
                {
                    companyId = storeContext.ExternalCompanyId;
                }

                (await UserManager.SetRolesAsync(user, [IdentityRoles.AccountManager])).CheckErrors();
            });

            await WithUnitOfWorkAsync(async () =>
            {
                // Act
                UserUpdatedDto userUpdatedDto = await CommandSender.Send(new UpdateUserCommand(userId, roles, companyId));

                // Assert
                userUpdatedDto.Id.ShouldBe(userId);
                userUpdatedDto.CompanyDto.Id.ShouldBe(companyId);
                userUpdatedDto.Roles.ShouldContain(roles.First());
                userUpdatedDto.Roles.ShouldContain(roles.Last());
            });

            await WithUnitOfWorkAsync(async () =>
            {
                IdentityUser userUpdated = await UserRepository.GetAsync(userId);
                userUpdated.UserName.ShouldBe(user.UserName);
                userUpdated.Name.ShouldBe(user.Name);
                userUpdated.Email.ShouldBe(user.Email);
                userUpdated.IsExternal.ShouldBeTrue();
                userUpdated.GetCompanyId().ShouldBe(companyId);
                userUpdated.ConcurrencyStamp.ShouldNotBe(user.ConcurrencyStamp);
                IList<string> results = await UserManager.GetRolesAsync(user);
                results.ShouldContain(roles.First());
                results.ShouldContain(roles.Last());
            });
        }
    }

    [Fact]
    public async Task UpdateUserCommandHandler_WhenUpdatingOnlyCompany_Should_UpdateConcurrencyStamp()
    {
        Guid tenantId = GuidGenerator.Create();
        TestDataContext testDataContext = new(tenantId) { GenerateUsers = true };
        AdministrationTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(testDataContext);
        using (CurrentTenant.Change(tenantId))
        {
            Guid companyId = storeContext.InternalCompanyId;
            Guid userId = Guid.Empty;
            IdentityUser user = default!;

            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                userId = storeContext.UserIds[0];
                user = await UserRepository.GetAsync(userId);

                if (storeContext.InternalCompanyId == user.GetCompanyId())
                {
                    companyId = storeContext.ExternalCompanyId;
                }

                (await UserManager.SetRolesAsync(user, [IdentityRoles.AccountManager])).CheckErrors();
            });

            await WithUnitOfWorkAsync(async () =>
            {
                // Act
                UserUpdatedDto userUpdatedDto = await CommandSender.Send(new UpdateUserCommand(userId, [IdentityRoles.AccountManager], companyId));

                // Assert
                userUpdatedDto.Id.ShouldBe(userId);
                userUpdatedDto.CompanyDto.Id.ShouldBe(companyId);
            });

            await WithUnitOfWorkAsync(async () =>
            {
                IdentityUser userUpdated = await UserRepository.GetAsync(userId);
                userUpdated.GetCompanyId().ShouldBe(companyId);
                userUpdated.ConcurrencyStamp.ShouldNotBe(user.ConcurrencyStamp);
            });
        }
    }

    [Fact]
    public async Task UpdateUserCommandHandler_Should_Throw_Exception_With_Wrong_Arguments()
    {
        Guid tenantId = GuidGenerator.Create();
        TestDataContext testDataContext = new(tenantId) { GenerateUsers = true };
        AdministrationTestsSeedingDataStore storeContext = await SeedDataForTenantAsync(testDataContext);
        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid userId = storeContext.UserIds[0];
                IdentityUser user = await UserRepository.GetAsync(userId);

                Guid companyId = storeContext.InternalCompanyId;
                HashSet<string> roles = [IdentityRoles.ContentManager];
                HashSet<string> incorrectRoles = ["Incorrect"];

                // Act
                await CommandSender.Send(new UpdateUserCommand(Guid.Empty, roles, companyId)).ShouldThrowAsync<AbpValidationException>();
                await CommandSender.Send(new UpdateUserCommand(user.Id, roles, Guid.Empty)).ShouldThrowAsync<AbpValidationException>();
                await CommandSender.Send(new UpdateUserCommand(user.Id, incorrectRoles, companyId)).ShouldThrowAsync<InvalidOperationException>();
                await CommandSender.Send(new UpdateUserCommand(user.Id, [], companyId)).ShouldThrowAsync<ArgumentException>();
            });
        }
    }
}
