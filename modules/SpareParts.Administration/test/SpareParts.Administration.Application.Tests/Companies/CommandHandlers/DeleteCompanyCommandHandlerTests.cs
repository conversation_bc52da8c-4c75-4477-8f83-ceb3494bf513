using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using NSubstitute;
using Shouldly;
using SpareParts.AbpMediatR.Notifications;
using SpareParts.Administration.Companies.Commands;
using SpareParts.Administration.Companies.Notifications;
using SpareParts.Administration.Generated.Identity;
using SpareParts.Administration.Invitations;
using SpareParts.Administration.Tenants.Commands;
using SpareParts.Administration.Tenants.Dtos;
using SpareParts.Common.Companies;
using SpareParts.Common.Helpers;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using Volo.Abp.Modularity;
using Volo.Abp.TenantManagement;
using Xunit;
using Invitation = SpareParts.Administration.Invitations.Invitation;

namespace SpareParts.Administration.Companies.CommandHandlers;
public abstract class DeleteCompanyCommandHandlerTests<TStartupModule> : AdministrationApplicationTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    private readonly InvitationDomainService _invitationDomainService;
    private readonly IRepository<Invitation> _invitationRepository;
    private readonly IRepository<IdentityUser> _userRepository;
    private readonly IBackgroundJobRepository _backgroundJobStore;
    private readonly ITenantRepository _tenantRepository;
    private readonly INotificationPublisher _publisher;
    private readonly IExpressionCreator _expressionCreator;
    private IIdentity _identityApiClient = null!;

    protected DeleteCompanyCommandHandlerTests()
    {
        _invitationDomainService = ServiceProvider.GetRequiredService<InvitationDomainService>();
        ServiceProvider.GetRequiredService<ITenantManager>();
        _tenantRepository = ServiceProvider.GetRequiredService<ITenantRepository>();
        _invitationRepository = ServiceProvider.GetRequiredService<IRepository<Invitation>>();
        _userRepository = ServiceProvider.GetRequiredService<IRepository<IdentityUser>>();
        _backgroundJobStore = ServiceProvider.GetRequiredService<IBackgroundJobRepository>();
        _publisher = ServiceProvider.GetRequiredService<INotificationPublisher>();
        _expressionCreator = ServiceProvider.GetRequiredService<IExpressionCreator>();
    }

    protected override void AfterAddApplication(IServiceCollection services)
    {
        _identityApiClient = Substitute.For<IIdentity>();
        _identityApiClient.CancelInvitationAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>())
            .Returns(_ => new InvitationDto());
        _identityApiClient.UpdateRolesAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>(), Arg.Any<IEnumerable<string>>())
            .Returns(_ => []);
        services.Replace(ServiceDescriptor.Singleton(_identityApiClient));
    }

    [Fact]
    public async Task DeleteCompanyCommandHandler_Should_Delete_Company_And_Create_BackgroundJob()
    {
        AdministrationTestsSeedingDataStore store = null!;
        string jobId = string.Empty;

        // Arrange
        const string tenantName = "NewTenant";
        const string tenantDisplayName = "NewTenantDisplayName";
        CreateOrEnableTenantCommand command = new(tenantName, tenantDisplayName);
        TenantDto tenantDto = null!;
        await WithUnitOfWorkAsync(async () =>
        {
            tenantDto = await CommandSender.Send(command);
            TestDataContext testDataContext = new(tenantDto.TenantId) { GenerateCompanies = true };
            store = await SeedDataForTenantAsync(testDataContext);
        });
        Guid companyId = store.InternalCompanyId;

        using (CurrentTenant.Change(tenantDto.TenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Act
                jobId = await CommandSender.Send(new DeleteCompanyCommand(companyId));
            });

            await WithUnitOfWorkAsync(async () =>
            {
                Company? result = await CompanyRepository.FindAsync(companyId);

                // Assert
                result.ShouldBeNull();

                Guid.TryParse(jobId, out Guid id).ShouldBeTrue();
                BackgroundJobRecord? job = await _backgroundJobStore.FindAsync(id);
                job.ShouldNotBeNull();
            });
        }
    }

    public static Expression<Func<T, bool>> CreatePredicate<T>(string propertyName, Guid value)
    {
        ParameterExpression parameter = Expression.Parameter(typeof(T), "u");
        MemberExpression property = Expression.Property(parameter, propertyName);
        ConstantExpression constant = Expression.Constant(value);
        BinaryExpression equals = Expression.Equal(property, constant);

        return Expression.Lambda<Func<T, bool>>(equals, parameter);
    }

    [Fact]
    public async Task DeleteCompanyCommandHandler_Publish_Notification_Should_Delete_Associated_Entities()
    {
        AdministrationTestsSeedingDataStore store = null!;

        // Arrange
        const string tenantName = "NewTenant";
        const string tenantDisplayName = "NewTenantDisplayName";
        CreateOrEnableTenantCommand command = new(tenantName, tenantDisplayName);
        TenantDto tenantDto = null!;
        await WithUnitOfWorkAsync(async () =>
        {
            tenantDto = await CommandSender.Send(command);
            TestDataContext testDataContext = new(tenantDto.TenantId) { GenerateCompanies = true };
            store = await SeedDataForTenantAsync(testDataContext);
        });
        Guid companyId = store.InternalCompanyId;


        using (CurrentTenant.Change(tenantDto.TenantId))
        {
            List<IdentityUser> users;
            List<Invitation> invitations;
            int userCount = 0;
            int cancellableInvitationCount = 0;
            await WithUnitOfWorkAsync(async () =>
            {
                users = await _userRepository.GetListAsync(_expressionCreator.GetEqualsExpression<IdentityUser, Guid>(AdministrationModuleExtensionConfigurator.CompanyIdPropertyName, companyId));
                invitations = await _invitationRepository.GetListAsync(i => i.CompanyId == companyId);

                userCount = users.Count;
                cancellableInvitationCount = invitations.Count(_invitationDomainService.CanBeCancelled);

                Tenant tenant = await _tenantRepository.GetAsync(tenantDto.TenantId);

                // Act
                await _publisher.Publish(new CompanyDeletedNotification(tenant.Id, tenant.Name, companyId));
            });
            await WithUnitOfWorkAsync(async () =>
            {
                // Assert
                users = await _userRepository.GetListAsync(_expressionCreator.GetEqualsExpression<IdentityUser, Guid>(AdministrationModuleExtensionConfigurator.CompanyIdPropertyName, companyId));
                users.ShouldBeEmpty();
                await _identityApiClient.ReceivedWithAnyArgs(userCount).UpdateRolesAsync(default, default, default, default);
                invitations = await _invitationRepository.GetListAsync(i => i.CompanyId == companyId);
                invitations.ShouldBeEmpty();
                await _identityApiClient.ReceivedWithAnyArgs(cancellableInvitationCount).CancelInvitationAsync(default, default, default);
            });
        }
    }

    [Fact]
    public async Task DeleteCompanyCommandHandler_Should_Return_Empty_If_Company_Not_Exist()
    {
        using (CurrentTenant.Change(GuidGenerator.Create()))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Act & Assert
                string jobId = await CommandSender.Send(new DeleteCompanyCommand(Guid.Empty));
                jobId.ShouldBeEmpty();
            });
        }
    }
}
