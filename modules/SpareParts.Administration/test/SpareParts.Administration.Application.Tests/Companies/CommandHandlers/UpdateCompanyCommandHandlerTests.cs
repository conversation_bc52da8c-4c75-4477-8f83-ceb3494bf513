using System;
using System.Threading.Tasks;
using Bogus.Extensions.UnitedStates;
using Shouldly;
using SpareParts.Administration.Companies.Commands;
using SpareParts.Administration.Companies.Dtos;
using SpareParts.Common.Companies;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Modularity;
using Volo.Abp.Validation;
using Xunit;

namespace SpareParts.Administration.Companies.CommandHandlers;
public abstract class UpdateCompanyCommandHandlerTests<TStartupModule> : AdministrationApplicationTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    [Fact]
    public async Task UpdateCompanyCommandHandler_Should_Update_Company()
    {
        Guid tenantId = GuidGenerator.Create();
        TestDataContext testDataContext = new(tenantId) { GenerateCompanies = true };
        AdministrationTestsSeedingDataStore store = await SeedDataForTenantAsync(testDataContext);

        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid companyId = store.InternalCompanyId;
                Company company = await CompanyRepository.GetAsync(companyId);
                string type = company.Type.ToString();

                string code = Faker.Company.Ein();
                string name = Faker.Company.CompanyName();
                string legalName = Faker.Company.CompanyName();

                // Act
                CompanyDto companyDto = await CommandSender.Send(new UpdateCompanyCommand(companyId, code, name, legalName));

                // Assert
                companyDto.Code.ShouldBe(code);
                companyDto.Name.ShouldBe(name);
                companyDto.LegalName.ShouldBe(legalName);
                companyDto.Type.ShouldBe(type);
            });
        }
    }

    [Fact]
    public async Task UpdateCompanyCommandHandler_Should_Throw_Exception_With_Wrong_Arguments()
    {
        Guid tenantId = GuidGenerator.Create();
        TestDataContext testDataContext = new(tenantId) { GenerateCompanies = true };
        AdministrationTestsSeedingDataStore store = await SeedDataForTenantAsync(testDataContext);

        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                Guid companyId = store.InternalCompanyId;
                await CompanyRepository.GetAsync(companyId);

                Guid id = GuidGenerator.Create();
                string code = Faker.Company.Ein();
                string name = Faker.Company.CompanyName();
                string legalName = Faker.Company.CompanyName();

                // Act
                await CommandSender.Send(new UpdateCompanyCommand(id, code, name, legalName)).ShouldThrowAsync<EntityNotFoundException>();
                await CommandSender.Send(new UpdateCompanyCommand(Guid.Empty, code, name, legalName)).ShouldThrowAsync<AbpValidationException>();
                await CommandSender.Send(new UpdateCompanyCommand(id, string.Empty, name, legalName)).ShouldThrowAsync<AbpValidationException>();
                await CommandSender.Send(new UpdateCompanyCommand(id, code, name, string.Empty)).ShouldThrowAsync<AbpValidationException>();
            });
        }
    }
}