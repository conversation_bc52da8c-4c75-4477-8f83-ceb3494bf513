using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Shouldly;
using SpareParts.Administration.Companies.Dtos;
using SpareParts.Administration.Companies.Queries;
using SpareParts.Common;
using SpareParts.Common.Companies;
using Volo.Abp.Authorization;
using Volo.Abp.Modularity;
using Volo.Abp.Security.Claims;
using Xunit;

namespace SpareParts.Administration.Companies.QueryHandlers;
public abstract class GetCompaniesStatsQueryHandlerTests<TStartupModule> : AdministrationApplicationTestBase<TStartupModule> where TStartupModule : IAbpModule
{
    private const int CompaniesCount = 20;
    private const int ExternalCompaniesCount = 12;
    private readonly ICurrentPrincipalAccessor _currentPrincipalAccessor;

    protected GetCompaniesStatsQueryHandlerTests()
    {
        _currentPrincipalAccessor = ServiceProvider.GetRequiredService<ICurrentPrincipalAccessor>();
    }

    [Fact]
    public async Task GetCompaniesStatsQuery_Should_Return_Stats()
    {
        using (CurrentTenant.Change(GuidGenerator.Create()))
        {
            await base.WithUnitOfWorkAsync(async () =>
            {
                // Arrange
                await SeedCompanies();
            });

            await WithUnitOfWorkAsync(async () =>
            {
                using (_currentPrincipalAccessor.Change([new Claim(CommonClaimTypes.CompanyType, CompanyType.Internal.ToString())]))
                {
                    // Act
                    CompaniesStatsDto result = await QuerySender.Send(new GetCompaniesStatsQuery());

                    // Assert
                    result.Count.ShouldBe(ExternalCompaniesCount);
                }
            });
        }
    }

    [Fact]
    public async Task GetCompaniesStatsQuery_With_External_User_Should_Throw_Authorization_Exception()
    {
        using (CurrentTenant.Change(GuidGenerator.Create()))
        {
            await base.WithUnitOfWorkAsync(async () =>
            {
                using (_currentPrincipalAccessor.Change([new Claim(CommonClaimTypes.CompanyType, CompanyType.External.ToString())]))
                {
                    // Act
                    await QuerySender.Send(new GetCompaniesStatsQuery()).ShouldThrowAsync<AbpAuthorizationException>();
                }
            });
        }
    }

    private async Task SeedCompanies()
    {
        List<Company> companies = [];
        for (int i = 1; i <= CompaniesCount; i++)
        {
            string code = "CompanyCode" + i;
            string name = "CompanyName" + i;
            string legalName = "CompanyLegalName" + i;
            Company company = await CompanyDomainService.CreateAsync(GuidGenerator.Create(), code, name, legalName,
                i <= ExternalCompaniesCount ? CompanyType.External : CompanyType.Internal);
            companies.Add(company);
        }
        await CompanyRepository.InsertManyAsync(companies, true);
    }
}
