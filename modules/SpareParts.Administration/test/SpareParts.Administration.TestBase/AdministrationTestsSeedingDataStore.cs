using System;
using System.Collections.Generic;

namespace SpareParts.Administration;
public class AdministrationTestsSeedingDataStore
{
    public Guid TenantId { get; }
    public int UsersCount { get; set; } = AdministrationTestConsts.UsersCount;
    public Guid InternalCompanyId { get; set; } = Guid.Empty;
    public Guid ExternalCompanyId { get; set; } = Guid.Empty;
    public List<Guid> UserIds { get; set; } = [];
    public List<Guid> InvitationIds { get; set; } = [];

    public AdministrationTestsSeedingDataStore(Guid tenantId)
    {
        TenantId = tenantId;
    }
}
