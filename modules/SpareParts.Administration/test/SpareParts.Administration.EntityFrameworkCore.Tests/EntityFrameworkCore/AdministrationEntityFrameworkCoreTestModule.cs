using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.Extensions.DependencyInjection;
using SpareParts.Common;
using SpareParts.Common.Configurations;
using System;
using System.Runtime.CompilerServices;
using Volo.Abp.Auditing;
using Volo.Abp.BlobStoring;
using Volo.Abp.BlobStoring.Azure;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore.SqlServer;
using Volo.Abp.Modularity;
using Volo.Abp.PermissionManagement;
using Volo.Abp.SettingManagement;
using Volo.Abp.VirtualFileSystem;
using static SpareParts.Common.SqlScriptProvider;

namespace SpareParts.Administration.EntityFrameworkCore;

[DependsOn(
    typeof(AdministrationApplicationTestModule),
    typeof(AdministrationEntityFrameworkCoreModule),
    typeof(AbpEntityFrameworkCoreSqlServerModule),
    typeof(AbpBlobStoringAzureModule)
)]
public class AdministrationEntityFrameworkCoreTestModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        context.Services.AddTransient<IExplicitTestDataSeeder, EfAdministrationTestDataSeeder>();

        Configure<SettingManagementOptions>(options =>
        {
            options.SaveStaticSettingsToDatabase = false;
            options.IsDynamicSettingStoreEnabled = false;
        });
        Configure<PermissionManagementOptions>(options =>
        {
            options.SaveStaticPermissionsToDatabase = false;
            options.IsDynamicPermissionStoreEnabled = false;
        });
        Configure<AbpAuditingOptions>(opts =>
        {
            opts.IsEnabled = false;
        });

        Configure<AbpVirtualFileSystemOptions>(options =>
        {
            options.FileSets.AddEmbedded<AdministrationEntityFrameworkCoreTestModule>("SpareParts.Administration");
        });

        Configure<AbpBlobStoringOptions>(options =>
        {
            options.Containers.ConfigureDefault(container =>
            {
                container.UseAzure(azure =>
                {
                    azure.ConnectionString = "DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://127.0.0.1:10000/devstoreaccount1;";
                    azure.CreateContainerIfNotExists = true;
                });
            });
        });

        ConfigureMsSqlDatabase(context.Services);

        Configure<ApplicationConfiguration>(options =>
        {
            options.WebAppUrl = "https://localhost".EnsureEndsWith('/');
        });
    }

    private static void ConfigureMsSqlDatabase(IServiceCollection services)
    {
        string connectionString = DataBaseFixture.ConnectionString;
        using (AdministrationDbContext context = new(new DbContextOptionsBuilder<AdministrationDbContext>()
                   .UseSqlServer(connectionString)
                   .Options))
        {
            IRelationalDatabaseCreator relationalDatabaseCreator = context.GetService<IRelationalDatabaseCreator>();
            if (!relationalDatabaseCreator.HasTables())
            {
                relationalDatabaseCreator.CreateTables();
                ExecuteSqlScript(context, ScriptName.EntityChangesSqlView);
            }
        }

        services.Configure<AbpDbContextOptions>(options =>
        {
            options.Configure(context =>
            {
                context.DbContextOptions.UseSqlServer(connectionString);
            });
        });
    }

    private static void ExecuteSqlScript(AdministrationDbContext context, ScriptName scriptName)
    {
        context.Database.ExecuteSql(FormattableStringFactory.Create(GetSqlQuery(scriptName.ToString())));
    }
}
