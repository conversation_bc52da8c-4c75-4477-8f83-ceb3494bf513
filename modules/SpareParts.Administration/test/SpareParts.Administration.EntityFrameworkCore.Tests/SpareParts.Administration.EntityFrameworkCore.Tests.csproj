<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>SpareParts.Administration</RootNamespace>
		<GenerateEmbeddedFilesManifest>true</GenerateEmbeddedFilesManifest>
  </PropertyGroup>

  <ItemGroup>
		<EmbeddedResource Include="Files\*.*" />
		<None Remove="Files\*.*" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.FileProviders.Embedded" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Proxies" />
    <PackageReference Include="Testcontainers.MsSql" />
    <PackageReference Include="Volo.Abp.BlobStoring.Azure" />
    <ProjectReference Include="..\..\src\SpareParts.Administration.EntityFrameworkCore\SpareParts.Administration.EntityFrameworkCore.csproj" />
    <ProjectReference Include="..\SpareParts.Administration.Application.Tests\SpareParts.Administration.Application.Tests.csproj" />
    <PackageReference Include="Volo.Abp.EntityFrameworkCore.SqlServer" />

  </ItemGroup>

  <ItemGroup>
    <PackageReference Update="coverlet.collector">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
    </PackageReference>
    <PackageReference Update="Fody">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
    </PackageReference>
  </ItemGroup>

</Project>
