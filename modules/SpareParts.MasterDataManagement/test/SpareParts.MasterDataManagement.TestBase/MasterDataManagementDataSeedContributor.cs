using System.Threading.Tasks;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.MultiTenancy;

namespace SpareParts.MasterDataManagement;

public class MasterDataManagementDataSeedContributor(ICurrentTenant currentTenant)
    : IDataSeedContributor, ITransientDependency
{
    public Task SeedAsync(DataSeedContext context)
    {
        /* Instead of returning the Task.CompletedTask, you can insert your test data
         * at this point!
         */

        using (currentTenant.Change(context.TenantId))
        {
            return Task.CompletedTask;
        }
    }
}
