<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>SpareParts.MasterDataManagement</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Proxies" />
    <PackageReference Include="Testcontainers.MsSql" />
    <ProjectReference Include="..\..\src\SpareParts.MasterDataManagement.EntityFrameworkCore\SpareParts.MasterDataManagement.EntityFrameworkCore.csproj" />
    <ProjectReference Include="..\SpareParts.MasterDataManagement.Application.Tests\SpareParts.MasterDataManagement.Application.Tests.csproj" />
    <PackageReference Include="Volo.Abp.EntityFrameworkCore.SqlServer" />
    <PackageReference Include="Volo.Abp.BlobStoring.Azure" />

  </ItemGroup>

  <ItemGroup>
    <PackageReference Update="coverlet.collector">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
    </PackageReference>
    <PackageReference Update="Fody">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
    </PackageReference>
  </ItemGroup>

</Project>
