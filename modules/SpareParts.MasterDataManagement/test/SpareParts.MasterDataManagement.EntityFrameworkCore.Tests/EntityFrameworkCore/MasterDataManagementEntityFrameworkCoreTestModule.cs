using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.Extensions.DependencyInjection;
using System.Runtime.CompilerServices;
using SpareParts.Common;
using Volo.Abp.Auditing;
using Volo.Abp.BlobStoring;
using Volo.Abp.BlobStoring.Azure;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore.SqlServer;
using Volo.Abp.Modularity;
using Volo.Abp.PermissionManagement;
using Volo.Abp.SettingManagement;
using ScriptName = SpareParts.Core.ScriptName;

namespace SpareParts.MasterDataManagement.EntityFrameworkCore;

[DependsOn(
    typeof(MasterDataManagementApplicationTestModule),
    typeof(MasterDataManagementEntityFrameworkCoreModule),
    typeof(AbpEntityFrameworkCoreSqlServerModule),
    typeof(AbpBlobStoringAzureModule)
)]
public class MasterDataManagementEntityFrameworkCoreTestModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        base.ConfigureServices(context);
        Configure<SettingManagementOptions>(options =>
        {
            options.SaveStaticSettingsToDatabase = false;
            options.IsDynamicSettingStoreEnabled = false;
        });
        Configure<PermissionManagementOptions>(options =>
        {
            options.SaveStaticPermissionsToDatabase = false;
            options.IsDynamicPermissionStoreEnabled = false;
        });
        Configure<AbpAuditingOptions>(opts =>
        {
            opts.IsEnabled = false;
        });

        Configure<AbpBlobStoringOptions>(options =>
        {
            options.Containers.ConfigureDefault(container =>
            {
                container.UseAzure(azure =>
                {
                    azure.ConnectionString = "DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://127.0.0.1:10000/devstoreaccount1;";
                    azure.CreateContainerIfNotExists = true;
                });
            });
        });

        ConfigureMsSqlDatabase(context.Services);
    }

    private static void ConfigureMsSqlDatabase(IServiceCollection services)
    {
        string connectionString = DataBaseFixture.ConnectionString;
        using (MasterDataManagementDbContext context = new(new DbContextOptionsBuilder<MasterDataManagementDbContext>()
                   .UseSqlServer(connectionString)
                   .Options))
        {
            IRelationalDatabaseCreator relationalDatabaseCreator = context.GetService<IRelationalDatabaseCreator>();
            if (!relationalDatabaseCreator.HasTables())
            {
                relationalDatabaseCreator.CreateTables();
                ExecuteSqlScript(context, ScriptName.ProductFlattenHierarchySqlView);
                ExecuteSqlScript(context, ScriptName.ResourceVisibilitySqlView);
                ExecuteSqlScript(context, ScriptName.ProductFamilyFlattenHierarchySqlView);
                ExecuteSqlScript(context, ScriptName.BomPathsSqlView);
                ExecuteSqlScript(context, ScriptName.BeforeDeleteFromComponentsTrigger);
                ExecuteSqlScript(context, ScriptName.BeforeDeleteFromDrawingsTrigger);
                ExecuteSqlScript(context, ScriptName.InsertIntoBomHierarchyHistoryProcedure);
                ExecuteSqlScript(context, ScriptName.BeforeDeleteFromProductFamiliesTrigger);
            }
        }

        services.Configure<AbpDbContextOptions>(options =>
        {
            options.Configure(context =>
            {
                context.DbContextOptions.UseSqlServer(connectionString);
            });
        });
    }

    private static void ExecuteSqlScript(MasterDataManagementDbContext context, ScriptName scriptName)
    {
        context.Database.ExecuteSql(FormattableStringFactory.Create(SqlScriptProvider.GetSqlQuery(scriptName.ToString())));
    }
}