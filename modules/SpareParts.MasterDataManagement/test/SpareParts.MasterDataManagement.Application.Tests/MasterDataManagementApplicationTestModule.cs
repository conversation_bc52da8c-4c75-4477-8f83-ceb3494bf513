using Volo.Abp.Modularity;
using Volo.Abp.VirtualFileSystem;

namespace SpareParts.MasterDataManagement;

[DependsOn(
    typeof(MasterDataManagementApplicationModule),
    typeof(MasterDataManagementDomainTestModule)
)]
public class MasterDataManagementApplicationTestModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        base.ConfigureServices(context);
        Configure<AbpVirtualFileSystemOptions>(options =>
        {
            options.FileSets.AddEmbedded<MasterDataManagementApplicationTestModule>("SpareParts.MasterDataManagement");
        });
    }
}