using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.FileProviders;
using NSubstitute;
using Shouldly;
using SpareParts.Core.DomainServices.Components;
using SpareParts.Core.DomainServices.Resources;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Resources;
using SpareParts.MasterDataManagement.Etos;
using SpareParts.MasterDataManagement.Etos.ComponentImage;
using System;
using System.IO;
using System.Threading.Tasks;
using SpareParts.Core.Enums;
using Volo.Abp.Content;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.Modularity;
using Volo.Abp.MultiTenancy;
using Xunit;

namespace SpareParts.MasterDataManagement.EventHandlers;

public abstract class ComponentImageEventHandlerTests<TStartupModule> : MasterDataManagementApplicationTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    private readonly IDistributedEventBus _distributedEventBus;
    private readonly ICurrentTenant _currentTenant;
    private readonly ImageManager _imageManager;
    private bool _throwExceptionForDatahubApi;
    private ComponentDomainService ComponentDomainService => ServiceProvider.GetRequiredService<ComponentDomainService>();
    private IRepository<Component> ComponentRepository => ServiceProvider.GetRequiredService<IRepository<Component>>();

    protected ComponentImageEventHandlerTests()
    {
        _distributedEventBus = ServiceProvider.GetRequiredService<IDistributedEventBus>();
        _currentTenant = ServiceProvider.GetRequiredService<ICurrentTenant>();
        _imageManager = ServiceProvider.GetRequiredService<ImageManager>();
    }

    protected override void AfterAddApplication(IServiceCollection services)
    {
        DatahubFileShareApiClient datahubFileShareApiClient = Substitute.For<DatahubFileShareApiClient>();
        datahubFileShareApiClient.GetStreamAsync(Arg.Any<string>()).Returns(_ => GetImageFromDatahubApi());
        services.Replace(ServiceDescriptor.Singleton(datahubFileShareApiClient));
    }

    private MemoryStream GetImageFromDatahubApi()
    {
        if (_throwExceptionForDatahubApi)
        {
            throw new Exception("DatahubFileShareApiClient.GetFileStreamAsync failed");
        }
        IFileInfo imageFile = VirtualFileProvider.GetFileInfo("/Files/fff.png");
        using Stream readStream = imageFile.CreateReadStream();
        return readStream.CreateMemoryStream();
    }

    [Fact]
    public async Task ComponentImageCreatedOrUpdated_With_Unknown_Tenant_Should_Reject_Dcp()
    {
        string proposalId = Guid.NewGuid().ToString();
        string batchId = Guid.NewGuid().ToString();
        const int count = 10;
        const int step = 1;
        const string tenantName = "unknownTenant";
        ComponentImageCreatedOrUpdatedEto eto = new()
        {
            Payload = new ComponentImagePayload
            {
                ComponentCode = "",
                ImagePath = ""
            },
            ProposalId = proposalId,
            TenantName = tenantName,
            BatchInfo = new BatchInfoEto
            {
                BatchId = batchId,
                Count = count,
                Step = step
            }
        };
        _distributedEventBus.Subscribe<RejectedComponentImageDcpEvent>(r =>
        {
            r.ProposalId.ShouldBe(proposalId);
            r.ErrorMessages[0].Code.ShouldBe(ErrorCodes.TenantNotFound);
            r.ErrorMessages[0].Message.ShouldBe($"Unknown/disabled Tenant : {tenantName}");
            return Task.CompletedTask;
        });
        _distributedEventBus.Subscribe<AcceptedComponentImageDcpEvent>(r =>
        {
            r.AcceptedProposalIds.ShouldNotBe([proposalId]);
            return Task.CompletedTask;
        });
        await _distributedEventBus.PublishAsync(eto);
    }

    [Fact]
    public async Task ComponentImageCreatedOrUpdatedEvent_Should_Be_Accepted_If_Image_Is_Downloaded_From_Datahub_And_Component_Is_Found()
    {
        string tenantName = Guid.NewGuid().ToString("N");
        string tenantDisplayName = Guid.NewGuid().ToString("N");
        Guid tenantId = await CreateTenantAsync(tenantName, tenantDisplayName);
        Component product;
        Guid imageId = Guid.Empty;
        const string oldFileName = "product.png";
        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                IFileInfo imageFile = VirtualFileProvider.GetFileInfo("/Files/default-image.png");
                await using Stream imageStream = imageFile.CreateReadStream();
                Resource res = await _imageManager.CreateAsync(new RemoteStreamContent(imageStream, oldFileName));
                imageId = res.Id;
                product = await ComponentDomainService.CreateAsync("code",
                    [new ComponentTranslation("en", "label")], ComponentType.Assembly, res);
                product = await ComponentRepository.InsertAsync(product);
            });
        }
        string proposalId = Guid.NewGuid().ToString();
        string batchId = Guid.NewGuid().ToString();
        const int count = 10;
        const int step = 1;
        const string newFileName = "imagePath.png";
        ComponentImageCreatedOrUpdatedEto eto = new()
        {
            Payload = new ComponentImagePayload
            {
                ComponentCode = "code",
                ImagePath = newFileName
            },
            ProposalId = proposalId,
            TenantName = tenantName,
            BatchInfo = new BatchInfoEto
            {
                BatchId = batchId,
                Count = count,
                Step = step
            }
        };
        _distributedEventBus.Subscribe<AcceptedComponentImageDcpEvent>(r =>
        {
            r.AcceptedProposalIds.ShouldBe([proposalId]);
            return Task.CompletedTask;
        });
        _distributedEventBus.Subscribe<RejectedComponentImageDcpEvent>(r =>
        {
            r.ProposalId.ShouldNotBe(proposalId);
            return Task.CompletedTask;
        });
        await _distributedEventBus.PublishAsync(eto);
        await WithUnitOfWorkAsync(async () =>
        {
            using (_currentTenant.Change(tenantId))
            {
                (await _imageManager.BlobExistsAsync(imageId)).ShouldBeTrue();
                (await (await _imageManager.GetBlobAsync(imageId)).GetAllBytesAsync()).ShouldBe(await GetImageFromDatahubApi().GetAllBytesAsync());
            }
        });
    }

    [Fact]
    public async Task ComponentImageCreatedOrUpdatedEvent_Should_Be_Rejected_If_Component_Is_Not_Found()
    {
        string tenantName = Guid.NewGuid().ToString("N");
        string tenantDisplayName = Guid.NewGuid().ToString("N");
        await CreateTenantAsync(tenantName, tenantDisplayName);
        string proposalId = Guid.NewGuid().ToString();
        string batchId = Guid.NewGuid().ToString();
        const int count = 10;
        const int step = 1;
        const string newFileName = "imagePath.jpg";
        ComponentImageCreatedOrUpdatedEto eto = new()
        {
            Payload = new ComponentImagePayload
            {
                ComponentCode = "code",
                ImagePath = newFileName
            },
            ProposalId = proposalId,
            TenantName = tenantName,
            BatchInfo = new BatchInfoEto
            {
                BatchId = batchId,
                Count = count,
                Step = step
            }
        };
        _distributedEventBus.Subscribe<AcceptedComponentImageDcpEvent>(r =>
        {
            r.AcceptedProposalIds.ShouldNotBe([proposalId]);
            return Task.CompletedTask;
        });
        _distributedEventBus.Subscribe<RejectedComponentImageDcpEvent>(r =>
        {
            r.ProposalId.ShouldBe(proposalId);
            r.ErrorMessages[0].Code.ShouldBe(ErrorCodes.ComponentUnknown);
            r.ErrorMessages[0].Message.ShouldBe($"Component with code {eto.Payload.ComponentCode} is unknown");
            return Task.CompletedTask;
        });
        await _distributedEventBus.PublishAsync(eto);
    }

    [Fact]
    public async Task ComponentImageCreatedOrUpdatedEvent_Should_Be_Rejected_If_Image_Is_Not_Downloaded_From_Datahub()
    {
        _throwExceptionForDatahubApi = true;
        string tenantName = Guid.NewGuid().ToString("N");
        string tenantDisplayName = Guid.NewGuid().ToString("N");
        Guid tenantId = await CreateTenantAsync(tenantName, tenantDisplayName);
        Component product;
        const string oldFileName = "product.png";
        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                IFileInfo imageFile = VirtualFileProvider.GetFileInfo("/Files/default-image.png");
                await using Stream imageStream = imageFile.CreateReadStream();
                Resource res = await _imageManager.CreateAsync(new RemoteStreamContent(imageStream, oldFileName));
                _ = res.Id;
                product = await ComponentDomainService.CreateAsync("code",
                    [new ComponentTranslation("en", "label")], ComponentType.Assembly, res);
                product = await ComponentRepository.InsertAsync(product);
            });
        }
        string proposalId = Guid.NewGuid().ToString();
        string batchId = Guid.NewGuid().ToString();
        const int count = 10;
        const int step = 1;
        const string newFileName = "imagePath.jpg";
        ComponentImageCreatedOrUpdatedEto eto = new()
        {
            Payload = new ComponentImagePayload
            {
                ComponentCode = "code",
                ImagePath = newFileName
            },
            ProposalId = proposalId,
            TenantName = tenantName,
            BatchInfo = new BatchInfoEto
            {
                BatchId = batchId,
                Count = count,
                Step = step
            }
        };
        _distributedEventBus.Subscribe<AcceptedComponentImageDcpEvent>(r =>
        {
            r.AcceptedProposalIds.ShouldNotBe([proposalId]);
            return Task.CompletedTask;
        });
        _distributedEventBus.Subscribe<RejectedComponentImageDcpEvent>(r =>
        {
            r.ProposalId.ShouldBe(proposalId);
            r.ErrorMessages[0].Code.ShouldBe(ErrorCodes.ThumbnailInternalError);
            r.ErrorMessages[0].Message.ShouldBe($"Image {newFileName} cannot be downloaded from datahub");
            return Task.CompletedTask;
        });
        await _distributedEventBus.PublishAsync(eto);
    }
}