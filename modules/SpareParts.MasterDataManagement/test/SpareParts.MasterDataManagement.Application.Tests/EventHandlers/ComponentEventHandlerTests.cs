using Microsoft.Extensions.DependencyInjection;
using Shouldly;
using SpareParts.Core;
using SpareParts.Core.DomainServices.Components;
using SpareParts.Core.Entities.Components;
using SpareParts.MasterDataManagement.Etos;
using SpareParts.MasterDataManagement.Etos.Component;
using System;
using System.Linq;
using System.Threading.Tasks;
using SpareParts.Core.Enums;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.Modularity;
using Volo.Abp.MultiTenancy;
using Xunit;

namespace SpareParts.MasterDataManagement.EventHandlers;

public abstract class ComponentEventHandlerTests<TStartupModule> : MasterDataManagementApplicationTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    private readonly IDistributedEventBus _distributedEventBus;
    private readonly ICurrentTenant _currentTenant;
    private ComponentDomainService ComponentDomainService => ServiceProvider.GetRequiredService<ComponentDomainService>();
    private IRepository<Component, Guid> ComponentRepository => ServiceProvider.GetRequiredService<IRepository<Component, Guid>>();

    protected ComponentEventHandlerTests()
    {
        _distributedEventBus = ServiceProvider.GetRequiredService<IDistributedEventBus>();
        _currentTenant = ServiceProvider.GetRequiredService<ICurrentTenant>();
    }

    [Fact]
    public async Task ComponentCreatedOrUpdated_With_Unknown_Tenant_Should_Reject_Dcp()
    {
        string proposalId = Guid.NewGuid().ToString();
        string batchId = Guid.NewGuid().ToString();
        const int count = 10;
        const int step = 1;
        const string tenantName = "unknownTenant";
        ComponentCreatedOrUpdatedEto eto = new()
        {
            Payload = new ComponentPayload
            {
                Code = "",
                Type = "",
                Translations = [],
                Key = ""
            },
            ProposalId = proposalId,
            TenantName = tenantName,
            BatchInfo = new BatchInfoEto
            {
                BatchId = batchId,
                Count = count,
                Step = step
            }
        };
        _distributedEventBus.Subscribe<RejectedComponentDcpEvent>(r =>
        {
            r.ProposalId.ShouldBe(proposalId);
            r.ErrorMessages[0].Code.ShouldBe(ErrorCodes.TenantNotFound);
            r.ErrorMessages[0].Message.ShouldBe($"Unknown/disabled Tenant : {tenantName}");
            return Task.CompletedTask;
        });
        _distributedEventBus.Subscribe<AcceptedComponentDcpEvent>(r =>
        {
            r.AcceptedProposalIds.ShouldNotBe([proposalId]);
            return Task.CompletedTask;
        });
        await _distributedEventBus.PublishAsync(eto);
    }

    [Fact]
    public async Task ComponentDeleted_With_Unknown_Tenant_Should_Reject_Dcp()
    {
        string proposalId = Guid.NewGuid().ToString();
        string batchId = Guid.NewGuid().ToString();
        const int count = 10;
        const int step = 1;
        const string tenantName = "unknownTenant";
        ComponentCreatedOrUpdatedEto eto = new()
        {
            Payload = new ComponentPayload
            {
                Code = "",
                Type = "",
                Translations = [],
                Key = ""
            },
            ProposalId = proposalId,
            TenantName = tenantName,
            BatchInfo = new BatchInfoEto
            {
                BatchId = batchId,
                Count = count,
                Step = step
            }
        };
        _distributedEventBus.Subscribe<RejectedComponentDcpEvent>(r =>
        {
            r.ProposalId.ShouldBe(proposalId);
            r.ErrorMessages[0].Code.ShouldBe(ErrorCodes.TenantNotFound);
            r.ErrorMessages[0].Message.ShouldBe($"Unknown/disabled Tenant : {tenantName}");
            return Task.CompletedTask;
        });
        _distributedEventBus.Subscribe<AcceptedComponentDcpEvent>(r =>
        {
            r.AcceptedProposalIds.ShouldNotBe([proposalId]);
            return Task.CompletedTask;
        });
        await _distributedEventBus.PublishAsync(eto);
    }

    [Fact]
    public async Task Unknown_Type_From_Datahub_Should_Reject_Dcp()
    {
        string tenantName = Guid.NewGuid().ToString("N");
        string tenantDisplayName = Guid.NewGuid().ToString("N");
        await CreateTenantAsync(tenantName, tenantDisplayName);
        string proposalId = Guid.NewGuid().ToString();
        string batchId = Guid.NewGuid().ToString();
        const int count = 10;
        const int step = 1;
        const string unknown = "unknown";
        ComponentCreatedOrUpdatedEto eto = new()
        {
            Payload = new ComponentPayload
            {
                Code = "21",
                Type = unknown,
                Translations = [],
                Key = "21"
            },
            ProposalId = proposalId,
            TenantName = tenantName,
            BatchInfo = new BatchInfoEto
            {
                BatchId = batchId,
                Count = count,
                Step = step
            }
        };
        _distributedEventBus.Subscribe<RejectedComponentDcpEvent>(r =>
        {
            r.ProposalId.ShouldBe(proposalId);
            r.ErrorMessages[0].Code.ShouldBe(ErrorCodes.ComponentTypeError);
            r.ErrorMessages[0].Message.ShouldBe($"Error while creating/updating component with code {eto.Payload.Key} because type {eto.Payload.Type} is unknown");
            return Task.CompletedTask;
        });
        _distributedEventBus.Subscribe<AcceptedComponentDcpEvent>(r =>
        {
            r.AcceptedProposalIds.ShouldNotBe([proposalId]);
            return Task.CompletedTask;
        });
        await _distributedEventBus.PublishAsync(eto);
    }

    [Fact]
    public async Task From_Datahub_Create_Assembly_If_Inexistent()
    {
        string tenantName = Guid.NewGuid().ToString("N");
        string tenantDisplayName = Guid.NewGuid().ToString("N");
        Guid tenantId = await CreateTenantAsync(tenantName, tenantDisplayName);
        string proposalId = Guid.NewGuid().ToString();
        string batchId = Guid.NewGuid().ToString();
        const int count = 10;
        const int step = 10;
        ComponentCreatedOrUpdatedEto eto = new()
        {
            Payload = new ComponentPayload
            {
                Code = "code",
                Type = MasterDataManagementConsts.RawComponentTypesConsts.Assembly,
                Translations = [new TranslationEto
                {
                    Language = "en",
                    Label = "label"
                }],
                Key = "code"
            },
            ProposalId = proposalId,
            TenantName = tenantName,
            BatchInfo = new BatchInfoEto
            {
                BatchId = batchId,
                Count = count,
                Step = step
            }
        };
        _distributedEventBus.Subscribe<AcceptedComponentDcpEvent>(r =>
        {
            r.AcceptedProposalIds.ShouldBe([proposalId]);
            return Task.CompletedTask;
        });
        _distributedEventBus.Subscribe<RejectedComponentDcpEvent>(r =>
        {
            r.ProposalId.ShouldNotBe(proposalId);
            return Task.CompletedTask;
        });
        _distributedEventBus.Subscribe<ComponentPublishedSyncDoneEto>(r =>
        {
            r.EntityType.ShouldBe(DataHubEventsConstants.ComponentDcp);
            r.BatchInfo.BatchId.ShouldBe(batchId);
            r.BatchInfo.Count.ShouldBe(count);
            r.BatchInfo.Step.ShouldBe(step);
            return Task.CompletedTask;
        });
        await _distributedEventBus.PublishAsync(eto);
        await WithUnitOfWorkAsync(async () =>
        {
            using (_currentTenant.Change(tenantId))
            {
                Component? assembly =
                    await ComponentRepository.FirstOrDefaultAsync(c => c.Code.Equals(eto.Payload.Code) && c.Type == ComponentType.Assembly);
                assembly.ShouldNotBeNull();
            }
        });
    }

    [Fact]
    public async Task From_Datahub_Create_Assembly_With_Incorrect_Data_Should_Reject()
    {
        string tenantName = Guid.NewGuid().ToString("N");
        string tenantDisplayName = Guid.NewGuid().ToString("N");
        await CreateTenantAsync(tenantName, tenantDisplayName);
        string proposalId = Guid.NewGuid().ToString();
        string batchId = Guid.NewGuid().ToString();
        const int count = 10;
        const int step = 10;
        ComponentCreatedOrUpdatedEto eto = new()
        {
            Payload = new ComponentPayload
            {
                Code = null!,
                Type = MasterDataManagementConsts.RawComponentTypesConsts.Assembly,
                Translations = [new TranslationEto
                {
                    Language = "en",
                    Label = "label"
                }],
                Key = "code"
            },
            ProposalId = proposalId,
            TenantName = tenantName,
            BatchInfo = new BatchInfoEto
            {
                BatchId = batchId,
                Count = count,
                Step = step
            }
        };
        _distributedEventBus.Subscribe<RejectedComponentDcpEvent>(r =>
        {
            r.ProposalId.ShouldBe(proposalId);
            r.ErrorMessages[0].Code.ShouldBe(ErrorCodes.UnknownError);
            return Task.CompletedTask;
        });
        _distributedEventBus.Subscribe<AcceptedComponentDcpEvent>(r =>
        {
            r.AcceptedProposalIds.ShouldNotBe([proposalId]);
            return Task.CompletedTask;
        });
        _distributedEventBus.Subscribe<ComponentPublishedSyncDoneEto>(r =>
        {
            r.EntityType.ShouldBe(DataHubEventsConstants.ComponentDcp);
            r.BatchInfo.BatchId.ShouldBe(batchId);
            r.BatchInfo.Count.ShouldBe(count);
            r.BatchInfo.Step.ShouldBe(step);
            return Task.CompletedTask;
        });
        await _distributedEventBus.PublishAsync(eto);
    }

    [Fact]
    public async Task From_Datahub_Create_Assembly_With_Invalid_Code_Reject_Dcp()
    {
        string tenantName = Guid.NewGuid().ToString("N");
        string tenantDisplayName = Guid.NewGuid().ToString("N");
        Guid tenantId = await CreateTenantAsync(tenantName, tenantDisplayName);
        string proposalId = Guid.NewGuid().ToString();
        string batchId = Guid.NewGuid().ToString();
        const int count = 10;
        const int step = 1;
        ComponentCreatedOrUpdatedEto eto = new()
        {
            Payload = new ComponentPayload
            {
                Code = "#",
                Type = MasterDataManagementConsts.RawComponentTypesConsts.Assembly,
                Translations = [new TranslationEto
                {
                    Language = "en",
                    Label = "label"
                }],
                Key = "code"
            },
            ProposalId = proposalId,
            TenantName = tenantName,
            BatchInfo = new BatchInfoEto
            {
                BatchId = batchId,
                Count = count,
                Step = step
            }
        };
        _distributedEventBus.Subscribe<AcceptedComponentDcpEvent>(r =>
        {
            r.AcceptedProposalIds.ShouldNotBe([proposalId]);
            return Task.CompletedTask;
        });
        _distributedEventBus.Subscribe<RejectedComponentDcpEvent>(r =>
        {
            r.ProposalId.ShouldBe(proposalId);
            r.ErrorMessages[0].Code.ShouldBe(CoreErrorCodes.InvalidComponentCode);
            r.ErrorMessages[0].Message.ShouldBe("Invalid code error: Single-character codes among these characters [\",-,_,=,%,#] are not allowed.");
            return Task.CompletedTask;
        });
        await _distributedEventBus.PublishAsync(eto);
        await WithUnitOfWorkAsync(async () =>
        {
            using (_currentTenant.Change(tenantId))
            {
                Component? component =
                    await ComponentRepository.FirstOrDefaultAsync(c => c.Code.Equals(eto.Payload.Code));
                component.ShouldBeNull();
            }
        });
    }

    [Fact]
    public async Task From_Datahub_Create_Assembly_With_Invalid_Label_Reject_Dcp()
    {
        string tenantName = Guid.NewGuid().ToString("N");
        string tenantDisplayName = Guid.NewGuid().ToString("N");
        Guid tenantId = await CreateTenantAsync(tenantName, tenantDisplayName);
        string proposalId = Guid.NewGuid().ToString();
        string batchId = Guid.NewGuid().ToString();
        const int count = 10;
        const int step = 1;
        ComponentCreatedOrUpdatedEto eto = new()
        {
            Payload = new ComponentPayload
            {
                Code = "code",
                Type = MasterDataManagementConsts.RawComponentTypesConsts.Assembly,
                Translations = [new TranslationEto
                {
                    Language = "en",
                    Label = ""
                }],
                Key = "code"
            },
            ProposalId = proposalId,
            TenantName = tenantName,
            BatchInfo = new BatchInfoEto
            {
                BatchId = batchId,
                Count = count,
                Step = step
            }
        };
        _distributedEventBus.Subscribe<AcceptedComponentDcpEvent>(r =>
        {
            r.AcceptedProposalIds.ShouldNotBe([proposalId]);
            return Task.CompletedTask;
        });
        _distributedEventBus.Subscribe<RejectedComponentDcpEvent>(r =>
        {
            r.ProposalId.ShouldBe(proposalId);
            r.ErrorMessages[0].Code.ShouldBe(ErrorCodes.ComponentError);
            r.ErrorMessages[0].Message.ShouldBe("Error while creating component with code code. There are 1 validation errors:-The field Label must be a string or array type with a minimum length of '2'. (Label)");
            return Task.CompletedTask;
        });
        await _distributedEventBus.PublishAsync(eto);
        await WithUnitOfWorkAsync(async () =>
        {
            using (_currentTenant.Change(tenantId))
            {
                Component? component =
                    await ComponentRepository.FirstOrDefaultAsync(c => c.Code.Equals(eto.Payload.Code));
                component.ShouldBeNull();
            }
        });
    }

    [Fact]
    public async Task From_Datahub_Update_Assembly_If_Existent()
    {
        string tenantName = Guid.NewGuid().ToString("N");
        string tenantDisplayName = Guid.NewGuid().ToString("N");
        Guid tenantId = await CreateTenantAsync(tenantName, tenantDisplayName);
        Component assembly = null!;
        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                assembly = await ComponentDomainService.CreateAsync("code",
                    [new ComponentTranslation("en", "label")], ComponentType.Assembly);
                assembly = await ComponentRepository.InsertAsync(assembly);
            });
        }

        string proposalId = Guid.NewGuid().ToString();
        string batchId = Guid.NewGuid().ToString();
        const int count = 10;
        const int step = 1;
        ComponentCreatedOrUpdatedEto eto = new()
        {
            Payload = new ComponentPayload
            {
                Code = "code",
                Type = MasterDataManagementConsts.RawComponentTypesConsts.Assembly,
                Translations = [new TranslationEto
                {
                    Language = "en",
                    Label = "label"
                }],
                Key = "code"
            },
            ProposalId = proposalId,
            TenantName = tenantName,
            BatchInfo = new BatchInfoEto
            {
                BatchId = batchId,
                Count = count,
                Step = step
            }
        };
        _distributedEventBus.Subscribe<AcceptedComponentDcpEvent>(r =>
        {
            r.AcceptedProposalIds.ShouldBe([proposalId]);
            return Task.CompletedTask;
        });
        _distributedEventBus.Subscribe<RejectedComponentDcpEvent>(r =>
        {
            r.ProposalId.ShouldNotBe(proposalId);
            return Task.CompletedTask;
        });
        await _distributedEventBus.PublishAsync(eto);
        await WithUnitOfWorkAsync(async () =>
        {
            using (_currentTenant.Change(tenantId))
            {
                Component? updatedAssembly =
                    await ComponentRepository.FindAsync(assembly.Id);
                updatedAssembly.ShouldNotBeNull();
                updatedAssembly.Translations.First().Description.ShouldBeNull();
            }
        });
    }

    [Fact]
    public async Task From_Datahub_Change_Assembly_To_Part()
    {
        string tenantName = Guid.NewGuid().ToString("N");
        string tenantDisplayName = Guid.NewGuid().ToString("N");
        Guid tenantId = await CreateTenantAsync(tenantName, tenantDisplayName);
        Component assembly = default!;
        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                assembly = await ComponentDomainService.CreateAsync("45454",
                    [new ComponentTranslation("en", "label")], ComponentType.Assembly);
                assembly = await ComponentRepository.InsertAsync(assembly);
            });
        }

        string proposalId = Guid.NewGuid().ToString();
        string batchId = Guid.NewGuid().ToString();
        const int count = 10;
        const int step = 1;
        ComponentCreatedOrUpdatedEto eto = new()
        {
            Payload = new ComponentPayload
            {
                Code = "45454",
                Type = MasterDataManagementConsts.RawComponentTypesConsts.Part,
                Translations = [new TranslationEto
                {
                    Language = "en",
                    Label = "label"
                }],
                Key = "45454"
            },
            ProposalId = proposalId,
            TenantName = tenantName,
            BatchInfo = new BatchInfoEto
            {
                BatchId = batchId,
                Count = count,
                Step = step
            }
        };
        _distributedEventBus.Subscribe<AcceptedComponentDcpEvent>(r =>
        {
            r.AcceptedProposalIds.ShouldBe([proposalId]);
            return Task.CompletedTask;
        });
        _distributedEventBus.Subscribe<RejectedComponentDcpEvent>(r =>
        {
            r.ProposalId.ShouldNotBe(proposalId);
            return Task.CompletedTask;
        });
        await _distributedEventBus.PublishAsync(eto);
        await WithUnitOfWorkAsync(async () =>
        {
            using (_currentTenant.Change(tenantId))
            {
                Component? part =
                    await ComponentRepository.FindAsync(assembly.Id);
                part.ShouldNotBeNull();
            }
        });
    }

    [Fact]
    public async Task From_Datahub_Change_Part_To_Assembly()
    {
        string tenantName = Guid.NewGuid().ToString("N");
        string tenantDisplayName = Guid.NewGuid().ToString("N");
        Guid tenantId = await CreateTenantAsync(tenantName, tenantDisplayName);
        Component part = default!;
        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                part = await ComponentDomainService.CreateAsync("88754",
                    [new ComponentTranslation("en", "label")], ComponentType.Part);
                part = await ComponentRepository.InsertAsync(part);
            });
        }

        string proposalId = Guid.NewGuid().ToString();
        string batchId = Guid.NewGuid().ToString();
        const int count = 10;
        const int step = 1;
        ComponentCreatedOrUpdatedEto eto = new()
        {
            Payload = new ComponentPayload
            {
                Code = "88754",
                Type = MasterDataManagementConsts.RawComponentTypesConsts.Assembly,
                Translations = [new TranslationEto
                {
                    Language = "en",
                    Label = "label"
                }],
                Key = "88754"
            },
            ProposalId = proposalId,
            TenantName = tenantName,
            BatchInfo = new BatchInfoEto
            {
                BatchId = batchId,
                Count = count,
                Step = step
            }
        };
        _distributedEventBus.Subscribe<AcceptedComponentDcpEvent>(r =>
        {
            r.AcceptedProposalIds.ShouldBe([proposalId]);
            return Task.CompletedTask;
        });
        _distributedEventBus.Subscribe<RejectedComponentDcpEvent>(r =>
        {
            r.ProposalId.ShouldNotBe(proposalId);
            return Task.CompletedTask;
        });
        await _distributedEventBus.PublishAsync(eto);
        await WithUnitOfWorkAsync(async () =>
        {
            using (_currentTenant.Change(tenantId))
            {
                Component? assembly =
                    await ComponentRepository.FindAsync(part.Id);
                assembly.ShouldNotBeNull();
            }
        });
    }

    [Fact]
    public async Task From_Datahub_Create_Part_If_Inexistent()
    {
        string tenantName = Guid.NewGuid().ToString("N");
        string tenantDisplayName = Guid.NewGuid().ToString("N");
        Guid tenantId = await CreateTenantAsync(tenantName, tenantDisplayName);
        string proposalId = Guid.NewGuid().ToString();
        string batchId = Guid.NewGuid().ToString();
        const int count = 10;
        const int step = 1;
        ComponentCreatedOrUpdatedEto eto = new()
        {
            Payload = new ComponentPayload
            {
                Code = "code",
                Type = MasterDataManagementConsts.RawComponentTypesConsts.Part,
                Translations = [new TranslationEto
                {
                    Language = "en",
                    Label = "label"
                }],
                Key = "code"
            },
            ProposalId = proposalId,
            TenantName = tenantName,
            BatchInfo = new BatchInfoEto
            {
                BatchId = batchId,
                Count = count,
                Step = step
            }
        };
        _distributedEventBus.Subscribe<AcceptedComponentDcpEvent>(r =>
        {
            r.AcceptedProposalIds.ShouldBe([proposalId]);
            return Task.CompletedTask;
        });
        _distributedEventBus.Subscribe<RejectedComponentDcpEvent>(r =>
        {
            r.ProposalId.ShouldNotBe(proposalId);
            return Task.CompletedTask;
        });
        await _distributedEventBus.PublishAsync(eto);
        await WithUnitOfWorkAsync(async () =>
        {
            using (_currentTenant.Change(tenantId))
            {
                Component? component =
                    await ComponentRepository.FirstOrDefaultAsync(c => c.Code.Equals(eto.Payload.Code));
                component.ShouldNotBeNull();
                Component? part =
                    await ComponentRepository.FindAsync(component.Id);
                part.ShouldNotBeNull();
            }
        });
    }

    [Fact]
    public async Task From_Datahub_Create_Part_Without_Translations()
    {
        string tenantName = Guid.NewGuid().ToString("N");
        string tenantDisplayName = Guid.NewGuid().ToString("N");
        Guid tenantId = await CreateTenantAsync(tenantName, tenantDisplayName);
        string proposalId = Guid.NewGuid().ToString();
        string batchId = Guid.NewGuid().ToString();
        const int count = 10;
        const int step = 1;
        ComponentCreatedOrUpdatedEto eto = new()
        {
            Payload = new ComponentPayload
            {
                Code = "code",
                Type = MasterDataManagementConsts.RawComponentTypesConsts.Part,
                Translations = [],
                Key = "code"
            },
            ProposalId = proposalId,
            TenantName = tenantName,
            BatchInfo = new BatchInfoEto
            {
                BatchId = batchId,
                Count = count,
                Step = step
            }
        };
        _distributedEventBus.Subscribe<AcceptedComponentDcpEvent>(r =>
        {
            r.AcceptedProposalIds.ShouldNotBe([proposalId]);
            return Task.CompletedTask;
        });
        _distributedEventBus.Subscribe<RejectedComponentDcpEvent>(r =>
        {
            r.ProposalId.ShouldBe(proposalId);
            r.ErrorMessages[0].Code.ShouldBe(ErrorCodes.ComponentTranslationError);
            r.ErrorMessages[0].Message.ShouldBe($"Error while creating component with code {eto.Payload.Key} because there are no translations");
            return Task.CompletedTask;
        });
        await _distributedEventBus.PublishAsync(eto);
        await WithUnitOfWorkAsync(async () =>
        {
            using (_currentTenant.Change(tenantId))
            {
                Component? component =
                    await ComponentRepository.FirstOrDefaultAsync(c => c.Code.Equals(eto.Payload.Code));
                component.ShouldBeNull();
            }
        });
    }

    [Fact]
    public async Task From_Datahub_Update_Part_If_Existent()
    {
        string tenantName = Guid.NewGuid().ToString("N");
        string tenantDisplayName = Guid.NewGuid().ToString("N");
        Guid tenantId = await CreateTenantAsync(tenantName, tenantDisplayName);
        Component part = default!;
        using (CurrentTenant.Change(tenantId))
        {
            await WithUnitOfWorkAsync(async () =>
            {
                part = await ComponentDomainService.CreateAsync("code",
                    [new ComponentTranslation("en", "label")], ComponentType.Part);
                part = await ComponentRepository.InsertAsync(part);
            });
        }

        string proposalId = Guid.NewGuid().ToString();
        string batchId = Guid.NewGuid().ToString();
        const int count = 10;
        const int step = 1;
        ComponentCreatedOrUpdatedEto eto = new()
        {
            Payload = new ComponentPayload
            {
                Code = "code",
                Type = MasterDataManagementConsts.RawComponentTypesConsts.Part,
                Translations = [new TranslationEto
                {
                    Language = "en",
                    Label = "label"
                }],
                Key = "code"
            },
            ProposalId = proposalId,
            TenantName = tenantName,
            BatchInfo = new BatchInfoEto
            {
                BatchId = batchId,
                Count = count,
                Step = step
            }
        };
        _distributedEventBus.Subscribe<AcceptedComponentDcpEvent>(r =>
        {
            r.AcceptedProposalIds.ShouldBe([proposalId]);
            return Task.CompletedTask;
        });
        _distributedEventBus.Subscribe<RejectedComponentDcpEvent>(r =>
        {
            r.ProposalId.ShouldNotBe(proposalId);
            return Task.CompletedTask;
        });
        await _distributedEventBus.PublishAsync(eto);
        await WithUnitOfWorkAsync(async () =>
        {
            using (_currentTenant.Change(tenantId))
            {
                Component? updatedPart =
                    await ComponentRepository.FindAsync(part.Id);
                updatedPart.ShouldNotBeNull();
            }
        });
    }
}