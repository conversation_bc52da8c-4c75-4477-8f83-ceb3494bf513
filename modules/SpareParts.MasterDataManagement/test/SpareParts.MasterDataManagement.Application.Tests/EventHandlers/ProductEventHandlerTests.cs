using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Shouldly;
using SpareParts.AbpMediatR.Commands;
using SpareParts.Common.Dtos;
using SpareParts.Core.Components.Commands;
using SpareParts.Core.Enums;
using SpareParts.MasterDataManagement.Etos;
using SpareParts.MasterDataManagement.Etos.Component;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.Modularity;
using Xunit;

namespace SpareParts.MasterDataManagement.EventHandlers;

public abstract class ProductEventHandlerTests<TStartupModule> : MasterDataManagementApplicationTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    private readonly IDistributedEventBus _distributedEventBus;
    private readonly ICommandSender _sender;

    protected ProductEventHandlerTests()
    {
        _distributedEventBus = ServiceProvider.GetRequiredService<IDistributedEventBus>();
        _sender = ServiceProvider.GetRequiredService<ICommandSender>();
    }

    [Fact]
    public async Task ProductCreatedEto_ShouldTerminateEarly_IfTenantIsNull()
    {
        string proposalId = Guid.NewGuid().ToString();
        string batchId = Guid.NewGuid().ToString();
        const int count = 10;
        const int step = 1;
        const string tenantName = "unknownTenant";
        ProductCreatedEto eto = new()
        {
            Payload = new PayloadWithKey
            {
                Key = ""
            },
            ProposalId = proposalId,
            TenantName = tenantName,
            BatchInfo = new BatchInfoEto
            {
                BatchId = batchId,
                Count = count,
                Step = step
            }
        };
        _distributedEventBus.Subscribe<RejectedProductDcpEvent>(r =>
        {
            r.ProposalId.ShouldBe(proposalId);
            r.ErrorMessages[0].Code.ShouldBe(ErrorCodes.TenantNotFound);
            r.ErrorMessages[0].Message.ShouldBe($"Unknown/disabled Tenant : {tenantName}");
            return Task.CompletedTask;
        });
        _distributedEventBus.Subscribe<AcceptedProductDcpEvent>(r =>
        {
            r.AcceptedProposalIds.ShouldNotBe([proposalId]);
            return Task.CompletedTask;
        });
        await _distributedEventBus.PublishAsync(eto);
    }

    [Fact]
    public async Task ProductCreatedEto_ShouldPublishRejectedEvent_IfAssemblyIsUnknown()
    {
        string tenantName = Guid.NewGuid().ToString("N");
        string tenantDisplayName = Guid.NewGuid().ToString("N");
        await CreateTenantAsync(tenantName, tenantDisplayName);

        string proposalId = Guid.NewGuid().ToString();
        string batchId = Guid.NewGuid().ToString();
        const int count = 10;
        const int step = 1;
        ProductCreatedEto eto = new()
        {
            Payload = new PayloadWithKey
            {
                Key = "unknownAssembly"
            },
            ProposalId = proposalId,
            TenantName = tenantName,
            BatchInfo = new BatchInfoEto
            {
                BatchId = batchId,
                Count = count,
                Step = step
            }
        };
        _distributedEventBus.Subscribe<RejectedProductDcpEvent>(r =>
        {
            r.ProposalId.ShouldBe(proposalId);
            r.ErrorMessages[0].Code.ShouldBe(ErrorCodes.ProductMissingAssembly);
            r.ErrorMessages[0].Message.ShouldBe($"Cannot process a product based on an unknown assembly : {eto.Payload.Key}");
            return Task.CompletedTask;
        });
        _distributedEventBus.Subscribe<AcceptedProductDcpEvent>(r =>
        {
            r.AcceptedProposalIds.ShouldNotBe([proposalId]);
            return Task.CompletedTask;
        });
        await _distributedEventBus.PublishAsync(eto);
    }

    [Fact]
    public async Task ProductCreatedEto_ShouldCreateProduct_WhenAssemblyIsKnown()
    {
        string tenantName = Guid.NewGuid().ToString("N");
        string tenantDisplayName = Guid.NewGuid().ToString("N");
        Guid tenantId = await CreateTenantAsync(tenantName, tenantDisplayName);
        const string assemblyCode = "assemblyCode";
        await WithUnitOfWorkAsync(async () =>
        {
            using (CurrentTenant.Change(tenantId))
            {
                await _sender.Send(new CreateComponentCommand(assemblyCode, [new CommonTranslationDto("en", "label")], ComponentType.Assembly));
            }
        });
        string proposalId = Guid.NewGuid().ToString();
        string batchId = Guid.NewGuid().ToString();
        const int count = 10;
        const int step = 1;
        ProductCreatedEto eto = new()
        {
            Payload = new PayloadWithKey
            {
                Key = assemblyCode
            },
            ProposalId = proposalId,
            TenantName = tenantName,
            BatchInfo = new BatchInfoEto
            {
                BatchId = batchId,
                Count = count,
                Step = step
            }
        };
        _distributedEventBus.Subscribe<AcceptedProductDcpEvent>(r =>
        {
            r.AcceptedProposalIds.ShouldBe([proposalId]);
            return Task.CompletedTask;
        });
        _distributedEventBus.Subscribe<RejectedProductDcpEvent>(r =>
        {
            r.ProposalId.ShouldNotBe(proposalId);
            return Task.CompletedTask;
        });
        await _distributedEventBus.PublishAsync(eto);
    }
}