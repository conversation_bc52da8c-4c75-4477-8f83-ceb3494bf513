using Microsoft.Extensions.DependencyInjection;
using Shouldly;
using SpareParts.Core.DomainServices.Components;
using SpareParts.Core.Entities.Boms;
using SpareParts.Core.Entities.Components;
using SpareParts.MasterDataManagement.Etos;
using SpareParts.MasterDataManagement.Etos.Bom;
using System;
using System.Threading.Tasks;
using SpareParts.Core.Enums;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.Modularity;
using Volo.Abp.MultiTenancy;
using Xunit;

namespace SpareParts.MasterDataManagement.EventHandlers;

public abstract class CreateOrUpdateBomEventHandlerV1Tests<TStartupModule> : MasterDataManagementApplicationTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    private readonly IDistributedEventBus _distributedEventBus;
    private readonly ICurrentTenant _currentTenant;
    private readonly IRepository<BomLine> _bomLineRepository;
    private readonly BomLineDomainService _bomLineDomainService;
    private ComponentDomainService ComponentDomainService => ServiceProvider.GetRequiredService<ComponentDomainService>();
    private IRepository<Component> ComponentRepository => ServiceProvider.GetRequiredService<IRepository<Component>>();

    protected CreateOrUpdateBomEventHandlerV1Tests()
    {
        _distributedEventBus = ServiceProvider.GetRequiredService<IDistributedEventBus>();
        _currentTenant = ServiceProvider.GetRequiredService<ICurrentTenant>();
        _bomLineRepository = ServiceProvider.GetRequiredService<IRepository<BomLine>>();
        _bomLineDomainService = ServiceProvider.GetRequiredService<BomLineDomainService>();
    }

    [Fact]
    public async Task BomCreatedOrUpdated_With_Unknown_Tenant_Should_Reject_Dcp()
    {
        string proposalId = Guid.NewGuid().ToString();
        string batchId = Guid.NewGuid().ToString();
        const int count = 10;
        const int step = 1;
        const string tenantName = "unknownTenant";
        BomCreatedOrUpdatedEto eto = new()
        {
            Payload = new BomPayload
            {
                Children = [],
                ParentAssemblyCode = ""
            },
            ProposalId = proposalId,
            TenantName = tenantName,
            BatchInfo = new BatchInfoEto
            {
                BatchId = batchId,
                Count = count,
                Step = step
            }
        };
        _distributedEventBus.Subscribe<RejectedBomDcpEvent>(r =>
        {
            r.ProposalId.ShouldBe(proposalId);
            r.ErrorMessages[0].Code.ShouldBe(ErrorCodes.TenantNotFound);
            r.ErrorMessages[0].Message.ShouldBe($"Unknown/disabled Tenant : {tenantName}");
            return Task.CompletedTask;
        });
        _distributedEventBus.Subscribe<AcceptedBomDcpEvent>(r =>
        {
            r.AcceptedProposalIds.ShouldNotBe([proposalId]);
            return Task.CompletedTask;
        });
        await _distributedEventBus.PublishAsync(eto);
    }

    [Fact]
    public async Task From_Datahub_Create_BomLine_If_Inexistent()
    {
        string tenantName = Guid.NewGuid().ToString("N");
        string tenantDisplayName = Guid.NewGuid().ToString("N");
        Guid tenantId = await CreateTenantAsync(tenantName, tenantDisplayName);
        const string prodcode = "prodCode";
        const string partcode = "partCode";
        Guid prodId = Guid.Empty;
        Guid partId = Guid.Empty;
        await WithUnitOfWorkAsync(async () =>
        {
            using (CurrentTenant.Change(tenantId))
            {
                Component assembly = await ComponentDomainService.CreateAsync(prodcode, [new ComponentTranslation("en", "prodLabel")], ComponentType.Assembly);
                prodId = assembly.Id;
                await ComponentRepository.InsertAsync(assembly);
                Component part = await ComponentDomainService.CreateAsync(partcode, [new ComponentTranslation("en", "partLabel")], ComponentType.Part);
                partId = part.Id;
                await ComponentRepository.InsertAsync(part);
            }
        });
        string proposalId = Guid.NewGuid().ToString();
        string batchId = Guid.NewGuid().ToString();
        const int count = 10;
        const int step = 1;
        BomCreatedOrUpdatedEto eto = new()
        {
            Payload = new BomPayload
            {
                Children = [new BomLinePayload { ChildComponentCode = partcode, Quantity = 1 }],
                ParentAssemblyCode = prodcode
            },
            ProposalId = proposalId,
            TenantName = tenantName,
            BatchInfo = new BatchInfoEto
            {
                BatchId = batchId,
                Count = count,
                Step = step
            }
        };
        _distributedEventBus.Subscribe<AcceptedBomDcpEvent>(r =>
        {
            r.AcceptedProposalIds.ShouldBe([proposalId]);
            return Task.CompletedTask;
        });
        _distributedEventBus.Subscribe<RejectedBomDcpEvent>(r =>
        {
            r.ProposalId.ShouldNotBe(proposalId);
            return Task.CompletedTask;
        });
        await _distributedEventBus.PublishAsync(eto);
        await WithUnitOfWorkAsync(async () =>
        {
            using (_currentTenant.Change(tenantId))
            {
                BomLine? bomLine = await _bomLineRepository.FindAsync(bl => bl.ChildComponentId.Equals(partId) && bl.ParentAssemblyId.Equals(prodId));
                bomLine.ShouldNotBeNull();
                bomLine.Quantity.ShouldBe(1);
            }
        });
    }

    [Fact]
    public async Task From_Datahub_Reject_If_Parent_Is_Unknown()
    {
        string tenantName = Guid.NewGuid().ToString("N");
        string tenantDisplayName = Guid.NewGuid().ToString("N");
        const string prodcode = "prodCode";
        const string partcode = "partCode";
        await CreateTenantAsync(tenantName, tenantDisplayName);
        string proposalId = Guid.NewGuid().ToString();
        string batchId = Guid.NewGuid().ToString();
        const int count = 10;
        const int step = 1;
        BomCreatedOrUpdatedEto eto = new()
        {
            Payload = new BomPayload
            {
                Children = [new BomLinePayload { ChildComponentCode = partcode, Quantity = 1 }],
                ParentAssemblyCode = prodcode
            },
            ProposalId = proposalId,
            TenantName = tenantName,
            BatchInfo = new BatchInfoEto
            {
                BatchId = batchId,
                Count = count,
                Step = step
            }
        };
        _distributedEventBus.Subscribe<AcceptedBomDcpEvent>(r =>
        {
            r.AcceptedProposalIds.ShouldNotBe([proposalId]);
            return Task.CompletedTask;
        });
        _distributedEventBus.Subscribe<RejectedBomDcpEvent>(r =>
        {
            r.ProposalId.ShouldBe(proposalId);
            r.ErrorMessages[0].Code.ShouldBe(ErrorCodes.ComponentUnknown);
            r.ErrorMessages[0].Message.ShouldBe($"Component with code {prodcode} is unknown");
            return Task.CompletedTask;
        });
        await _distributedEventBus.PublishAsync(eto);
    }

    [Fact]
    public async Task From_Datahub_Reject_If_Child_Is_Unknown()
    {
        string tenantName = Guid.NewGuid().ToString("N");
        string tenantDisplayName = Guid.NewGuid().ToString("N");
        const string prodcode = "prodCode";
        const string partcode = "partCode";
        Guid tenantId = await CreateTenantAsync(tenantName, tenantDisplayName);
        await WithUnitOfWorkAsync(async () =>
        {
            using (CurrentTenant.Change(tenantId))
            {
                Component assembly = await ComponentDomainService.CreateAsync(prodcode, [new ComponentTranslation("en", "prodLabel")], ComponentType.Assembly);
                await ComponentRepository.InsertAsync(assembly);
            }
        });
        string proposalId = Guid.NewGuid().ToString();
        string batchId = Guid.NewGuid().ToString();
        const int count = 10;
        const int step = 1;
        BomCreatedOrUpdatedEto eto = new()
        {
            Payload = new BomPayload
            {
                Children = [new BomLinePayload { ChildComponentCode = partcode, Quantity = 1 }],
                ParentAssemblyCode = prodcode
            },
            ProposalId = proposalId,
            TenantName = tenantName,
            BatchInfo = new BatchInfoEto
            {
                BatchId = batchId,
                Count = count,
                Step = step
            }
        };
        _distributedEventBus.Subscribe<AcceptedBomDcpEvent>(r =>
        {
            r.AcceptedProposalIds.ShouldNotBe([proposalId]);
            return Task.CompletedTask;
        });
        _distributedEventBus.Subscribe<RejectedBomDcpEvent>(r =>
        {
            r.ProposalId.ShouldBe(proposalId);
            r.ErrorMessages[0].Code.ShouldBe(ErrorCodes.ComponentUnknown);
            r.ErrorMessages[0].Message.ShouldBe($"Component with code {partcode} is unknown");
            return Task.CompletedTask;
        });
        await _distributedEventBus.PublishAsync(eto);
    }

    [Fact]
    public async Task From_Datahub_Create_BomLine_With_Negative_Quantity()
    {
        string tenantName = Guid.NewGuid().ToString("N");
        string tenantDisplayName = Guid.NewGuid().ToString("N");
        Guid tenantId = await CreateTenantAsync(tenantName, tenantDisplayName);
        const string prodcode = "prodCode";
        const string partcode = "partCode";
        Guid prodId = Guid.Empty;
        Guid partId = Guid.Empty;
        await WithUnitOfWorkAsync(async () =>
        {
            using (CurrentTenant.Change(tenantId))
            {
                Component assembly = await ComponentDomainService.CreateAsync(prodcode, [new ComponentTranslation("en", "prodLabel")], ComponentType.Assembly);
                prodId = assembly.Id;
                await ComponentRepository.InsertAsync(assembly);
                Component part = await ComponentDomainService.CreateAsync(partcode, [new ComponentTranslation("en", "partLabel")], ComponentType.Part);
                partId = part.Id;
                await ComponentRepository.InsertAsync(part);
            }
        });
        string proposalId = Guid.NewGuid().ToString();
        string batchId = Guid.NewGuid().ToString();
        const int count = 10;
        const int step = 10;
        BomCreatedOrUpdatedEto eto = new()
        {
            Payload = new BomPayload
            {
                Children = [new BomLinePayload { ChildComponentCode = partcode, Quantity = -1 }],
                ParentAssemblyCode = prodcode
            },
            ProposalId = proposalId,
            TenantName = tenantName,
            BatchInfo = new BatchInfoEto
            {
                BatchId = batchId,
                Count = count,
                Step = step
            }
        };
        _distributedEventBus.Subscribe<AcceptedBomDcpEvent>(r =>
        {
            r.AcceptedProposalIds.ShouldNotBe([proposalId]);
            return Task.CompletedTask;
        });
        _distributedEventBus.Subscribe<RejectedBomDcpEvent>(r =>
        {
            r.ProposalId.ShouldBe(proposalId);
            r.ErrorMessages[0].Message.ShouldBe($"Error while creating BOM line with parent component code {eto.Payload.ParentAssemblyCode} and child component code {eto.Payload.Children[0].ChildComponentCode}");
            return Task.CompletedTask;
        });
        _distributedEventBus.Subscribe<BomPublishedSyncDoneEto>(r =>
        {
            r.EntityType.ShouldBe(DataHubEventsConstants.BomDcp);
            r.BatchInfo.BatchId.ShouldBe(batchId);
            r.BatchInfo.Count.ShouldBe(count);
            r.BatchInfo.Step.ShouldBe(step);
            return Task.CompletedTask;
        });
        await _distributedEventBus.PublishAsync(eto);
        await WithUnitOfWorkAsync(async () =>
        {
            using (_currentTenant.Change(tenantId))
            {
                BomLine? bomLine = await _bomLineRepository.FindAsync(bl => bl.ChildComponentId.Equals(partId) && bl.ParentAssemblyId.Equals(prodId));
                bomLine.ShouldBeNull();
            }
        });
    }

    [Fact]
    public async Task From_Datahub_Update_BomLine_If_Existent()
    {
        string tenantName = Guid.NewGuid().ToString("N");
        string tenantDisplayName = Guid.NewGuid().ToString("N");
        Guid tenantId = await CreateTenantAsync(tenantName, tenantDisplayName);
        const string prodcode = "prodCode";
        const string partcode = "partCode";
        Guid prodId = Guid.Empty;
        Guid partId = Guid.Empty;
        await WithUnitOfWorkAsync(async () =>
        {
            using (CurrentTenant.Change(tenantId))
            {
                Component assembly = await ComponentDomainService.CreateAsync(prodcode, [new ComponentTranslation("en", "prodLabel")], ComponentType.Assembly);
                prodId = assembly.Id;
                await ComponentRepository.InsertAsync(assembly);
                Component part = await ComponentDomainService.CreateAsync(partcode, [new ComponentTranslation("en", "partLabel")], ComponentType.Part);
                partId = part.Id;
                await ComponentRepository.InsertAsync(part);
                BomLine bomline = await _bomLineDomainService.CreateAsync(prodId, partId, 1, 1);
                await _bomLineRepository.InsertAsync(bomline);
            }
        });
        string proposalId = Guid.NewGuid().ToString();
        string batchId = Guid.NewGuid().ToString();
        const int count = 10;
        const int step = 1;
        BomCreatedOrUpdatedEto eto = new()
        {
            Payload = new BomPayload
            {
                Children = [new BomLinePayload { ChildComponentCode = partcode, Quantity = 2 }],
                ParentAssemblyCode = prodcode
            },
            ProposalId = proposalId,
            TenantName = tenantName,
            BatchInfo = new BatchInfoEto
            {
                BatchId = batchId,
                Count = count,
                Step = step
            }
        };
        _distributedEventBus.Subscribe<AcceptedBomDcpEvent>(r =>
        {
            r.AcceptedProposalIds.ShouldBe([proposalId]);
            return Task.CompletedTask;
        });
        _distributedEventBus.Subscribe<RejectedBomDcpEvent>(r =>
        {
            r.ProposalId.ShouldNotBe(proposalId);
            return Task.CompletedTask;
        });
        await _distributedEventBus.PublishAsync(eto);
        await WithUnitOfWorkAsync(async () =>
        {
            using (_currentTenant.Change(tenantId))
            {
                BomLine? bomLine = await _bomLineRepository.FindAsync(bl => bl.ChildComponentId.Equals(partId) && bl.ParentAssemblyId.Equals(prodId));
                bomLine.ShouldNotBeNull();
                bomLine.Quantity.ShouldBe(2);
            }
        });
    }

    [Fact]
    public async Task From_Datahub_Update_BomLine_With_Negative_Quantity()
    {
        string tenantName = Guid.NewGuid().ToString("N");
        string tenantDisplayName = Guid.NewGuid().ToString("N");
        Guid tenantId = await CreateTenantAsync(tenantName, tenantDisplayName);
        const string prodcode = "prodCode";
        const string partcode = "partCode";
        Guid prodId = Guid.Empty;
        Guid partId = Guid.Empty;
        await WithUnitOfWorkAsync(async () =>
        {
            using (CurrentTenant.Change(tenantId))
            {
                Component assembly = await ComponentDomainService.CreateAsync(prodcode, [new ComponentTranslation("en", "prodLabel")], ComponentType.Assembly);
                prodId = assembly.Id;
                await ComponentRepository.InsertAsync(assembly);
                Component part = await ComponentDomainService.CreateAsync(partcode, [new ComponentTranslation("en", "partLabel")], ComponentType.Part);
                partId = part.Id;
                await ComponentRepository.InsertAsync(part);
                BomLine bomline = await _bomLineDomainService.CreateAsync(prodId, partId, 1, 1);
                await _bomLineRepository.InsertAsync(bomline);
            }
        });
        string proposalId = Guid.NewGuid().ToString();
        string batchId = Guid.NewGuid().ToString();
        const int count = 10;
        const int step = 1;
        BomCreatedOrUpdatedEto eto = new()
        {
            Payload = new BomPayload
            {
                Children = [new BomLinePayload { ChildComponentCode = partcode, Quantity = -1 }],
                ParentAssemblyCode = prodcode
            },
            ProposalId = proposalId,
            TenantName = tenantName,
            BatchInfo = new BatchInfoEto
            {
                BatchId = batchId,
                Count = count,
                Step = step
            }
        };
        _distributedEventBus.Subscribe<AcceptedBomDcpEvent>(r =>
        {
            r.AcceptedProposalIds.ShouldNotBe([proposalId]);
            return Task.CompletedTask;
        });
        _distributedEventBus.Subscribe<RejectedBomDcpEvent>(r =>
        {
            r.ProposalId.ShouldBe(proposalId);
            r.ErrorMessages[0].Message.ShouldBe($"Error while updating BOM line with parent component code {eto.Payload.ParentAssemblyCode} and child component code {eto.Payload.Children[0].ChildComponentCode}");
            return Task.CompletedTask;
        });
        await _distributedEventBus.PublishAsync(eto);
        await WithUnitOfWorkAsync(async () =>
        {
            using (_currentTenant.Change(tenantId))
            {
                BomLine? bomLine = await _bomLineRepository.FindAsync(bl => bl.ChildComponentId.Equals(partId) && bl.ParentAssemblyId.Equals(prodId));
                bomLine.ShouldNotBeNull();
                bomLine.Quantity.ShouldBe(1);
            }
        });
    }

    [Fact]
    public async Task From_Datahub_Delete_BomLine()
    {
        string tenantName = Guid.NewGuid().ToString("N");
        string tenantDisplayName = Guid.NewGuid().ToString("N");
        Guid tenantId = await CreateTenantAsync(tenantName, tenantDisplayName);
        const string prodcode = "prodCode";
        const string partcode = "partCode";
        Guid prodId = Guid.Empty;
        Guid partId = Guid.Empty;
        await WithUnitOfWorkAsync(async () =>
        {
            using (CurrentTenant.Change(tenantId))
            {
                Component assembly = await ComponentDomainService.CreateAsync(prodcode, [new ComponentTranslation("en", "prodLabel")], ComponentType.Assembly);
                prodId = assembly.Id;
                await ComponentRepository.InsertAsync(assembly);
                Component part = await ComponentDomainService.CreateAsync(partcode, [new ComponentTranslation("en", "partLabel")], ComponentType.Part);
                partId = part.Id;
                await ComponentRepository.InsertAsync(part);
                BomLine bomline = await _bomLineDomainService.CreateAsync(prodId, partId, 1, 1);
                await _bomLineRepository.InsertAsync(bomline);
            }
        });
        string proposalId = Guid.NewGuid().ToString();
        string batchId = Guid.NewGuid().ToString();
        const int count = 10;
        const int step = 1;
        BomCreatedOrUpdatedEto eto = new()
        {
            Payload = new BomPayload
            {
                Children = [],
                ParentAssemblyCode = prodcode
            },
            ProposalId = proposalId,
            TenantName = tenantName,
            BatchInfo = new BatchInfoEto
            {
                BatchId = batchId,
                Count = count,
                Step = step
            }
        };
        _distributedEventBus.Subscribe<AcceptedBomDcpEvent>(r =>
        {
            r.AcceptedProposalIds.ShouldBe([proposalId]);
            return Task.CompletedTask;
        });
        _distributedEventBus.Subscribe<RejectedBomDcpEvent>(r =>
        {
            r.ProposalId.ShouldNotBe(proposalId);
            return Task.CompletedTask;
        });
        await _distributedEventBus.PublishAsync(eto);
        await WithUnitOfWorkAsync(async () =>
        {
            using (_currentTenant.Change(tenantId))
            {
                BomLine? bomLine = await _bomLineRepository.FindAsync(bl => bl.ChildComponentId.Equals(partId) && bl.ParentAssemblyId.Equals(prodId));
                bomLine.ShouldBeNull();
            }
        });
    }
}