<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>SpareParts.MasterDataManagement</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Files\*.*" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Files\*.*" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\SpareParts.MasterDataManagement.Application\SpareParts.MasterDataManagement.Application.csproj" />
    <ProjectReference Include="..\SpareParts.MasterDataManagement.Domain.Tests\SpareParts.MasterDataManagement.Domain.Tests.csproj" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Update="coverlet.collector">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
    </PackageReference>
    <PackageReference Update="Fody">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
    </PackageReference>
  </ItemGroup>

</Project>
