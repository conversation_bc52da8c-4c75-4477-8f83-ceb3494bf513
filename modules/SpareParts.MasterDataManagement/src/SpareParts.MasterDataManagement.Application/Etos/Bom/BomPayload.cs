using System.Collections.Generic;
using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace SpareParts.MasterDataManagement.Etos.Bom;

public class BomPayload
{
    [DataMember(Name = DataHubEventsConstants.BomConstants.Children)]
    [JsonPropertyName(DataHubEventsConstants.BomConstants.Children)]
    public List<BomLinePayload> Children { get; set; } = [];

    [DataMember(Name = DataHubEventsConstants.BomConstants.ParentAssemblyCode)]
    [JsonPropertyName(DataHubEventsConstants.BomConstants.ParentAssemblyCode)]
    public string ParentAssemblyCode { get; set; } = null!;
}