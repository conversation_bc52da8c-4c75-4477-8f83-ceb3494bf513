using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace SpareParts.MasterDataManagement.Etos.Bases;

public abstract class PublishedSyncDoneEto
{
    [DataMember(Name = DataHubEventsConstants.DataChangeProposalEventConstants.BatchInfo)]
    [JsonPropertyName(DataHubEventsConstants.DataChangeProposalEventConstants.BatchInfo)]
    public BatchInfoEto BatchInfo { get; set; } = null!;

    [DataMember(Name = DataHubEventsConstants.DataChangeProposalEventConstants.EntityType)]
    [JsonPropertyName(DataHubEventsConstants.DataChangeProposalEventConstants.EntityType)]
    public abstract string EntityType { get; }
}