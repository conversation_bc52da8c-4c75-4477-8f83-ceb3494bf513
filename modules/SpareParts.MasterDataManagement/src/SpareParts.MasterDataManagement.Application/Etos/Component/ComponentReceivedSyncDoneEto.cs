using System.Diagnostics.CodeAnalysis;
using System.Runtime.Serialization;
using System.Text.Json.Serialization;
using Volo.Abp.EventBus;

namespace SpareParts.MasterDataManagement.Etos.Component;

[EventName(DataHubEventsConstants.DcpEvents.ComponentReceivedSyncDone)]
[ExcludeFromCodeCoverage]
public class ComponentReceivedSyncDoneEto
{
    [DataMember(Name = DataHubEventsConstants.DataChangeProposalEventConstants.BatchInfo)]
    [JsonPropertyName(DataHubEventsConstants.DataChangeProposalEventConstants.BatchInfo)]
    public BatchInfoEto BatchInfo { get; set; } = null!;
    [DataMember(Name = DataHubEventsConstants.DataChangeProposalEventConstants.TenantName)]
    [JsonPropertyName(DataHubEventsConstants.DataChangeProposalEventConstants.TenantName)]
    public string? TenantName { get; set; }
    [DataMember(Name = DataHubEventsConstants.DataChangeProposalEventConstants.UserId)]
    [JsonPropertyName(DataHubEventsConstants.DataChangeProposalEventConstants.UserId)]
    public string? ExternalUserId { get; set; }
}