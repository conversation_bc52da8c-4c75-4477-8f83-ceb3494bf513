using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace SpareParts.MasterDataManagement.Etos.ComponentImage;

public class ComponentImagePayload
{
    [DataMember(Name = DataHubEventsConstants.PayloadConstants.Key)]
    [JsonPropertyName(DataHubEventsConstants.PayloadConstants.Key)]
    public string ComponentCode { get; set; } = null!;

    [DataMember(Name = DataHubEventsConstants.PayloadConstants.Path)]
    [JsonPropertyName(DataHubEventsConstants.PayloadConstants.Path)]
    public string ImagePath { get; set; } = null!;
}