using System.Diagnostics.CodeAnalysis;
using System.Runtime.Serialization;
using System.Text.Json.Serialization;
using Volo.Abp.EventBus;

namespace SpareParts.MasterDataManagement.Etos;

[EventName(DataHubEventsConstants.DcpEvents.ImportDone)]
[ExcludeFromCodeCoverage]
public class ImportDoneEto
{
    [DataMember(Name = DataHubEventsConstants.DataChangeProposalEventConstants.ImportId)]
    [JsonPropertyName(DataHubEventsConstants.DataChangeProposalEventConstants.ImportId)]
    public string ImportId { get; set; } = null!;
    [DataMember(Name = DataHubEventsConstants.DataChangeProposalEventConstants.TenantName)]
    [JsonPropertyName(DataHubEventsConstants.DataChangeProposalEventConstants.TenantName)]
    public string? TenantName { get; set; } = null!;
}