using System.Collections.Specialized;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
using Volo.Abp.MultiTenancy;

namespace SpareParts.MasterDataManagement;

[ExcludeFromCodeCoverage]
public class DatahubFileShareApiClient
{
    private readonly ICurrentTenant _currentTenant = null!;
    private readonly HttpClient _httpClient = null!;

    public DatahubFileShareApiClient()
    {
    }

    public DatahubFileShareApiClient(HttpClient httpClient, ICurrentTenant currentTenant)
    {
        _httpClient = httpClient;
        _currentTenant = currentTenant;
    }

    public virtual async Task<MemoryStream> GetStreamAsync(string path)
    {
        return await (await _httpClient.GetStreamAsync(GetUrl(path))).CreateMemoryStreamAsync();
    }

    private string GetUrl(string path)
    {
        NameValueCollection queryString = HttpUtility.ParseQueryString("");
        queryString.Add("path", path);
        return $"{_currentTenant.Name}/storage/file?{queryString}";
    }
}