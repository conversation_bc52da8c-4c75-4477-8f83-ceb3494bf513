namespace SpareParts.MasterDataManagement;

public static class ErrorCodes
{
    public const string TenantNotFound = "Spareparts.Tenant.NotFound";
    public const string ComponentUnknown = "Spareparts.Component.Unknown";
    public const string ComponentError = "Spareparts.Component.Error";
    public const string ComponentTranslationError = "Spareparts.Component.TranslationError";
    public const string ComponentTypeError = "Spareparts.Component.TypeError";
    public const string BomError = "Spareparts.BOM.Error";
    public const string DrawingInternalError = "Spareparts.Drawing.InternalError";
    public const string DrawingUnknownComponents = "Spareparts.Drawing.UnknownComponents";
    public const string DrawingError = "Spareparts.Drawing.Error";
    public const string DrawingDeleteError = "Spareparts.Drawing.DeleteError";
    public const string ThumbnailInternalError = "Spareparts.Thumbnail.InternalError";
    public const string ThumbnailError = "Spareparts.Thumbnail.Error";
    public const string ProductMissingAssembly = "Spareparts.Product.MissingAssembly";
    public const string ProductError = "Spareparts.Product.Error";
    public const string UnknownError = "Spareparts.Unknown";
}