using Microsoft.Extensions.Logging;
using SpareParts.Common.Dtos;
using SpareParts.Core.Components.Commands;
using SpareParts.Core.Components.Commands.Products;
using SpareParts.Core.DataImportMonitoring;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Enums;
using SpareParts.MasterDataManagement.BackgroundJobs.Args;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Domain.Repositories;

namespace SpareParts.MasterDataManagement.BackgroundJobs;

[ExcludeFromCodeCoverage]
public class ImportProductJob : JobBase<ImportProductJobArgs>
{
    private IRepository<ComponentProposal> ComponentProposalRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<ComponentProposal>>();
    private IRepository<BomProposal> BomProposalRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<BomProposal>>();
    private IRepository<Product> ProductRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Product>>();
    private IBackgroundJobManager BackgroundJobManager => LazyServiceProvider.LazyGetRequiredService<IBackgroundJobManager>();

    public override async Task ExecuteAsync(ImportProductJobArgs args)
    {
        CurrentTenant.Change(args.TenantId, args.TenantName);
        MasterProposal masterProposal = await MasterProposalRepository.FirstAsync(mp => mp.ExternalImportId == args.Product.BatchInfo.ImportId);
        
        SetCurrentUser(args.TenantId, masterProposal);
        
        await StartImportAsync(args, masterProposal);
        
        List<ComponentProposal> componentProposals = await ComponentProposalRepository.GetListAsync(cp => cp.MasterProposalId == masterProposal.Id);
        foreach (ComponentProposal componentProposal in componentProposals)
        {
            try
            {
                await StartProposalAsync(componentProposal, ComponentProposalRepository);
                if (!Enum.TryParse(componentProposal.Type, true, out ComponentType type))
                {
                    ((IProposal)componentProposal).SetError($"Type {componentProposal.Type} is unknown");
                    continue;
                }
                if (componentProposal.Translations.Count == 0)
                {
                    ((IProposal)componentProposal).SetError("Translations are empty");
                    continue;
                }
                List<CommonTranslationDto> translations = [];
                foreach (KeyValuePair<string, string> componentProposalTranslation in componentProposal.Translations)
                {
                    try
                    {
                        translations.Add(new CommonTranslationDto(componentProposalTranslation.Key,
                            componentProposalTranslation.Value));
                    }
                    catch (Exception ex)
                    {
                        ((IProposal)componentProposal).AddWarning($"Error while parsing translation: {ex.Message}");
                    }

                }

                Guid? componentId = await GetComponentIdAsync(componentProposal.Code);
                if (componentId.HasValue)
                {
                    try
                    {
                        await CommandSender.Send(new SetComponentTranslationsCommand(componentId.Value, translations));
                        await CommandSender.Send(new ChangeComponentTypeCommand(componentId.Value, type));
                        await UnitOfWork.SaveChangesAsync();
                    }
                    catch (Exception ex)
                    {
                        ((IProposal)componentProposal).SetError($"Error while updating component: {ex.Message}");
                    }
                }
                else
                {
                    try
                    {
                        await CommandSender.Send(new CreateComponentCommand(componentProposal.Code, translations, type));
                        await UnitOfWork.SaveChangesAsync();
                    }
                    catch (Exception ex)
                    {
                        ((IProposal)componentProposal).SetError($"Error while creating component: {ex.Message}");
                    }
                }
            }
            finally
            {
                await CompleteProposalAsync(componentProposal, ComponentProposalRepository, masterProposal);
            }
        }

        List<BomProposal> bomProposals = await BomProposalRepository.GetListAsync(bp => bp.MasterProposalId == masterProposal.Id);
        foreach (BomProposal bomProposal in bomProposals)
        {
            try
            {
                await StartProposalAsync(bomProposal, BomProposalRepository);
                if (string.IsNullOrWhiteSpace(bomProposal.ParentAssemblyCode))
                {
                    ((IProposal)bomProposal).SetError("Parent code is empty");
                    continue;
                }

                if (bomProposal.Lines.Count == 0)
                {
                    ((IProposal)bomProposal).SetError("Children are empty");
                    continue;
                }
                Component? parentAssembly = await ComponentRepository.FindAsync(c => c.Code == bomProposal.ParentAssemblyCode && c.Type == ComponentType.Assembly);
                if (parentAssembly == null)
                {
                    ((IProposal)bomProposal).SetError($"Parent assembly with code {bomProposal.ParentAssemblyCode} does not exist");
                    continue;
                }

                Dictionary<string, Guid> dictionary = (await ComponentRepository.GetQueryableAsync()).Where(c =>
                    bomProposal.Lines.Select(bl => bl.Key).Contains(c.Code)).ToDictionary(c => c.Code.Trim().ToLower(), c => c.Id);
                if (dictionary.Count != bomProposal.Lines.Count)
                {
                    ((IProposal)bomProposal).AddWarning("There are unknown children");
                }

                try
                {
                    await CommandSender.Send(new SetBomCommand(parentAssembly.Id,
                        bomProposal.Lines.Select(bl =>
                            new ChildBomLine(dictionary[bl.Key.Trim().ToLower()], bl.Value)).ToArray()));
                    await UnitOfWork.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    ((IProposal)bomProposal).SetError($"Error while setting bom: {ex.Message}");
                }
            }
            finally
            {
                await CompleteProposalAsync(bomProposal, BomProposalRepository, masterProposal);
            }
        }

        try
        {
            await StartProposalAsync(masterProposal, MasterProposalRepository);
            Guid? assemblyId = await GetAssembyIdAsync(masterProposal.ProductCode!);
            try
            {
                await CommandSender.Send(new CreateProductCommand(assemblyId.Value));
                await UnitOfWork.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                ((IProposal)masterProposal).SetError($"Error while creating product: {ex.Message}");
            }
        }
        finally
        {
            await CompleteProposalAsync(masterProposal, MasterProposalRepository, masterProposal);
        }
        await BackgroundJobManager.EnqueueAsync(new ImpactedProductsJobArgs(args.TenantId, args.TenantName, masterProposal.Id));
    }

    private async Task StartImportAsync(ImportProductJobArgs args, MasterProposal masterProposal)
    {
        string code = args.Product.Payload.Key;
        ImportAction importAction = await ProductRepository.AsyncExecuter.AnyAsync(await ProductRepository.GetQueryableAsync(),
            p => p.Component.Code == code)
            ? ImportAction.Modify
            : ImportAction.Create;
        masterProposal.SetAction(importAction);
        masterProposal.SetProductCode(code);
        masterProposal.SetExternalProductProposalId(args.Product.ProposalId);
        masterProposal.ImportStartedAt = Clock.Now;
        Logger.LogInformation("Import begins: {@MasterProposal}", masterProposal);
        await MasterProposalRepository.UpdateAsync(masterProposal, true);
    }
}