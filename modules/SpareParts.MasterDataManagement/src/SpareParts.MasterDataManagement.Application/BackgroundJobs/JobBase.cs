using System;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using SpareParts.AbpMediatR.Commands;
using SpareParts.Core.DataImportMonitoring;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Enums;
using Volo.Abp.Auditing;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.ChangeTracking;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.MultiTenancy;
using Volo.Abp.Security.Claims;
using Volo.Abp.Timing;
using Volo.Abp.Uow;

namespace SpareParts.MasterDataManagement.BackgroundJobs;

[ExcludeFromCodeCoverage]
public abstract class JobBase<TJobArgs> : AsyncBackgroundJob<TJobArgs>, ITransientDependency, IUnitOfWorkEnabled, IAuditingEnabled
{
    public IAbpLazyServiceProvider LazyServiceProvider { get; set; } = null!;
    protected ICommandSender CommandSender => LazyServiceProvider.LazyGetRequiredService<ICommandSender>();
    protected IUnitOfWork UnitOfWork => LazyServiceProvider.LazyGetRequiredService<IUnitOfWork>();
    protected IClock Clock => LazyServiceProvider.LazyGetRequiredService<IClock>();
    protected ICurrentTenant CurrentTenant => LazyServiceProvider.LazyGetRequiredService<ICurrentTenant>();
    protected IRepository<Component> ComponentRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Component>>();
    protected IRepository<MasterProposal> MasterProposalRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<MasterProposal>>();
    private ICurrentPrincipalAccessor CurrentPrincipalAccessor => LazyServiceProvider.LazyGetRequiredService<ICurrentPrincipalAccessor>();
    
    [DisableEntityChangeTracking]
    protected async Task<Guid?> GetComponentIdAsync(string code)
    {
        IQueryable<Guid?> idQueryable = (await ComponentRepository.GetQueryableAsync()).Where(c =>
                string.Equals(c.Code, code))
            .Select(c => (Guid?)c.Id);
        return await ComponentRepository.AsyncExecuter.FirstOrDefaultAsync(idQueryable);
    }
    
    [DisableEntityChangeTracking]
    protected async Task<Guid?> GetAssembyIdAsync(string code)
    {
        IQueryable<Guid?> idQueryable = (await ComponentRepository.GetQueryableAsync()).Where(c =>
                c.Code == code && c.Type == ComponentType.Assembly)
            .Select(c => (Guid?)c.Id);
        return await ComponentRepository.AsyncExecuter.FirstOrDefaultAsync(idQueryable);
    }
    
    protected async Task StartProposalAsync<TProposal>(TProposal proposal, IRepository<TProposal> repository) where TProposal : class, IEntity, IProposal
    {
        proposal.SetStatus(ProposalStatus.InProgress);
        Logger.LogInformation("{Type} {Status}: {@Proposal}", typeof(TProposal), proposal.Status.ToString(), proposal);
        await repository.UpdateAsync(proposal, true);
    }
    
    protected async Task CompleteProposalAsync<TProposal>(TProposal proposal, IRepository<TProposal> repository, MasterProposal masterProposal) where TProposal : class, IEntity, IProposal
    {
        proposal.SetStatus(ProposalStatus.Completed);
        Logger.LogInformation("{Type} {Status}: {@Proposal}", typeof(TProposal), proposal.Status.ToString(), proposal);
        await repository.UpdateAsync(proposal, true);
        if (proposal.Error != null && !masterProposal.ImportHasErrors)
        {
            masterProposal.ImportHasErrors = true;
        }

        if (proposal.Warnings.Count > 0 && !masterProposal.ImportHasWarnings)
        {
            masterProposal.ImportHasWarnings = true;
        }
        await MasterProposalRepository.UpdateAsync(masterProposal, true);
    }
    
    protected void SetCurrentUser(Guid tenantId, MasterProposal masterProposal)
    {
        string? userId = masterProposal.ExternalUserId.ToString();
        if (userId != null)
        {
            CurrentPrincipalAccessor.Change([
                new Claim(AbpClaimTypes.UserId,
                    userId),
                new Claim(AbpClaimTypes.TenantId, tenantId.ToString())
            ]);
        }
    }
}