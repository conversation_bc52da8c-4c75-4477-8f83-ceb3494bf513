using Microsoft.Extensions.Logging;
using SpareParts.Core.Components.Commands.Images;
using SpareParts.Core.DataImportMonitoring;
using SpareParts.Core.Drawings.Commands;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Resources;
using SpareParts.Core.Enums;
using SpareParts.MasterDataManagement.BackgroundJobs.Args;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Content;
using Volo.Abp.Domain.ChangeTracking;
using Volo.Abp.Domain.Repositories;

namespace SpareParts.MasterDataManagement.BackgroundJobs;

[ExcludeFromCodeCoverage]
public class ImportDoneJob : JobBase<ImportDoneJobArgs>
{
    private IRepository<ComponentImageProposal> ComponentImageProposalRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<ComponentImageProposal>>();
    private DatahubFileShareApiClient DatahubFileShareApiClient => LazyServiceProvider.LazyGetRequiredService<DatahubFileShareApiClient>();
    private IRepository<DrawingProposal> DrawingProposalRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<DrawingProposal>>();
    private IRepository<Drawing> DrawingRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Drawing>>();
    private IRepository<Resource> ResourceRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Resource>>();

    public override async Task ExecuteAsync(ImportDoneJobArgs args)
    {
        CurrentTenant.Change(args.TenantId, args.TenantName);
        MasterProposal masterProposal = await MasterProposalRepository.FirstAsync(mp => mp.ExternalImportId == args.ExternalImportId);
        while (masterProposal.Status != ProposalStatus.Completed)
        {
            masterProposal = await MasterProposalRepository
                .FirstAsync(mp => mp.ExternalImportId == args.ExternalImportId);
        }

        SetCurrentUser(args.TenantId, masterProposal);
        
        List<ComponentImageProposal> componentImageProposals = await ComponentImageProposalRepository.GetListAsync(cp => cp.MasterProposalId == masterProposal.Id);
        foreach (ComponentImageProposal imageProposal in componentImageProposals)
        {
            try
            {
                await StartProposalAsync(imageProposal, ComponentImageProposalRepository);
                Guid? componentId = await GetComponentIdAsync(imageProposal.ComponentCode);
                if (!componentId.HasValue)
                {
                    ((IProposal)imageProposal).SetError("Component is unknown");
                    continue;
                }
                try
                {
                    await using MemoryStream image = await DatahubFileShareApiClient.GetStreamAsync(imageProposal.ImagePath);
                    await CommandSender.Send(
                        new ChangeComponentImageCommand(componentId.Value,
                            new RemoteStreamContent(image, Path.GetFileName(imageProposal.ImagePath))));
                    await UnitOfWork.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    ((IProposal)imageProposal).SetError(ex.Message);
                }
            }
            finally
            {
                await CompleteProposalAsync(imageProposal, ComponentImageProposalRepository, masterProposal);
            }
        }
        List<DrawingProposal> drawingProposals = await DrawingProposalRepository.GetListAsync(cp => cp.MasterProposalId == masterProposal.Id);
        foreach (DrawingProposal drawingProposal in drawingProposals)
        {
            try
            {
                await StartProposalAsync(drawingProposal, DrawingProposalRepository);
                Guid? assemblyId = await GetAssembyIdAsync(drawingProposal.AssemblyCode);
                await using MemoryStream drawingFile = await DatahubFileShareApiClient.GetStreamAsync(drawingProposal.Path);
                string drawingFileName = Path.GetFileName(drawingProposal.Path);
                //TODO : these ids should be as bom children
                Dictionary<string, Guid> componentIdsByCode = (await ComponentRepository.GetQueryableAsync()).Where(c => drawingProposal.ComponentCodesByIndex.Select(x => x.Value).Contains(c.Code))
                    .Select(c => new { c.Id, c.Code }).ToDictionary(c => c.Code, c => c.Id);
                Dictionary<string, Guid> componentIdsByIndex = [];
                foreach ((string index, string code) in drawingProposal.ComponentCodesByIndex.Where(x => !string.IsNullOrWhiteSpace(x.Value) && !string.IsNullOrWhiteSpace(x.Key)))
                {
                    if (componentIdsByCode.ContainsKey(code.Trim()))
                    {
                        componentIdsByIndex[index] = componentIdsByCode[code.Trim()];
                    }
                }

                try
                {
                    Guid? drawingId = await GetDrawingId(drawingFileName, assemblyId.Value);
                    if (drawingId.HasValue)
                    {
                        await CommandSender.Send(new UpdateDrawingCommand(drawingId.Value, new RemoteStreamContent(drawingFile, drawingFileName), componentIdsByIndex));
                    }
                    else
                    {
                        await CommandSender.Send(new CreateDrawingCommand(assemblyId.Value, new RemoteStreamContent(drawingFile, drawingFileName), componentIdsByIndex, DrawingOrigin.SolidWorks));
                    }
                    await UnitOfWork.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    ((IProposal)drawingProposal).SetError(ex.Message);
                }
            }
            finally
            {
                await CompleteProposalAsync(drawingProposal, DrawingProposalRepository, masterProposal);
            }
        }

        await FinishImportAsync(masterProposal);
    }

    private async Task FinishImportAsync(MasterProposal masterProposal)
    {
        masterProposal.ImportFinishedAt = Clock.Now;
        Logger.LogInformation("Import finished: {@MasterProposal}", masterProposal);
        await MasterProposalRepository.UpdateAsync(masterProposal, true);
    }

    [DisableEntityChangeTracking]
    private async Task<Guid?> GetDrawingId(string drawingFileName, Guid assemblyId)
    {
        IQueryable<Guid?> drawingIdQueryable = from drawing in await DrawingRepository.GetQueryableAsync()
            join resource in await ResourceRepository.GetQueryableAsync()
                on new { fileId = drawing.SourceFileId, assemblyId = drawing.AssemblyId, fileName = drawingFileName }
                equals new { fileId = resource.Id, assemblyId, fileName = resource.FileName }
            select drawing.Id as Guid?;
        return await DrawingRepository.AsyncExecuter.FirstOrDefaultAsync(drawingIdQueryable);
    }
}