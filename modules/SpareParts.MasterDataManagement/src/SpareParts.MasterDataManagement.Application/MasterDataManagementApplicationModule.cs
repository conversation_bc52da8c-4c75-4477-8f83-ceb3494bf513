using System;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using SpareParts.Core;
using Volo.Abp.AutoMapper;
using Volo.Abp.Modularity;
using Volo.Abp.Application;
using SpareParts.Common;
using System.Diagnostics.CodeAnalysis;

namespace SpareParts.MasterDataManagement;

[DependsOn(
    typeof(MasterDataManagementDomainModule),
    typeof(MasterDataManagementApplicationContractsModule),
    typeof(AbpDddApplicationModule),
    typeof(AbpAutoMapperModule),
    typeof(CoreApplicationModule)
    )]
[ExcludeFromCodeCoverage]
public class MasterDataManagementApplicationModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        base.ConfigureServices(context);

        context.Services.AddAlwaysAllowAuthorization();

        context.Services.AddAutoMapperObjectMapper<MasterDataManagementApplicationModule>();
        Configure<AbpAutoMapperOptions>(options =>
        {
            options.AddMaps<MasterDataManagementApplicationModule>(validate: true);
        });

        IConfigurationSection datahubFileShareApiSection = context.Services.GetConfiguration().GetSection("Datahub:FileShareApi");
        CommonApplicationModule.AddPolicyHandlers(context.Services
            .AddHttpClient<DatahubFileShareApiClient>()
            .ConfigureHttpClient(httpClient =>
            {
                httpClient.BaseAddress = new Uri(datahubFileShareApiSection["BaseUrl"]!);
            }).AddClientCredentialsTokenHandler(CommonApplicationModule.TokenClientName));
    }
}