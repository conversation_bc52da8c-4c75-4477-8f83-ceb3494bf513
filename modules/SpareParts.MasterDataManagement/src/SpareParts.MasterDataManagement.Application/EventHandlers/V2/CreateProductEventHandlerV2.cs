using SpareParts.MasterDataManagement.BackgroundJobs.Args;
using SpareParts.MasterDataManagement.Etos.Component;
using System.Diagnostics.CodeAnalysis;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.DependencyInjection;
using Volo.Abp.TenantManagement;

namespace SpareParts.MasterDataManagement.EventHandlers.V2;

[ExcludeFromCodeCoverage]
[ExposeServices(typeof(IMdmEventHandlerV2<ProductCreatedEto, PayloadWithKey>))]
public class CreateProductEventHandlerV2 : EventHandlerBaseV2<ProductCreatedEto, PayloadWithKey, ProductPublishedSyncDoneEto, RejectedProductDcpEvent>
{
    private IBackgroundJobManager BackgroundJobManager => LazyServiceProvider.LazyGetRequiredService<IBackgroundJobManager>();

    private async Task PublishAcceptedAsync(ProductCreatedEto eventData, CancellationToken cancellationToken)
    {
        await DistributedEventBus.PublishAsync(new AcceptedProductDcpEvent()
        {
            AcceptedProposalIds = [eventData.ProposalId]
        }, false);
        await PublishProgressAsync(eventData, cancellationToken);
    }

    public override async Task ProcessEventAsync(ProductCreatedEto eventData, CancellationToken cancellationToken)
    {
        Tenant? tenant = await GetTenantOrRejectAsync(eventData, cancellationToken);
        if (tenant == null)
        {
            return;
        }
        await PublishAcceptedAsync(eventData, cancellationToken);
        await BackgroundJobManager.EnqueueAsync(new ImportProductJobArgs(tenant.Id, tenant.Name, eventData));
    }
}