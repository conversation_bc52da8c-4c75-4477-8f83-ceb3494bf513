using SpareParts.Core.Features;
using SpareParts.MasterDataManagement.BackgroundJobs.Args;
using SpareParts.MasterDataManagement.Etos;
using System.Diagnostics.CodeAnalysis;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Domain.ChangeTracking;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.GlobalFeatures;
using Volo.Abp.TenantManagement;

namespace SpareParts.MasterDataManagement.EventHandlers.V2;

[ExcludeFromCodeCoverage]
public class ImportDoneEventHandler : ApplicationService, IDistributedEventHandler<ImportDoneEto>
{
    private ITenantRepository TenantRepository => LazyServiceProvider.LazyGetRequiredService<ITenantRepository>();
    private IBackgroundJobManager BackgroundJobManager => LazyServiceProvider.LazyGetRequiredService<IBackgroundJobManager>();

    public virtual async Task HandleEventAsync(ImportDoneEto eventData)
    {
        if (GlobalFeatureManager.Instance.Modules.CoreFeatures().DataImportMonitoring.IsEnabled)
        {
            Tenant? tenant = await GetTenantAsync(eventData);
            if (tenant == null)
            {
                return;
            }

            await BackgroundJobManager.EnqueueAsync(new ImportDoneJobArgs(tenant.Id, tenant.Name, eventData.ImportId));
        }
    }

    [DisableEntityChangeTracking]
    private async Task<Tenant?> GetTenantAsync(ImportDoneEto eventData, CancellationToken cancellationToken = default)
    {
        return await TenantRepository.FindByNameAsync(eventData.TenantName, cancellationToken: cancellationToken);
    }
}