using SpareParts.Core.DataImportMonitoring;
using SpareParts.MasterDataManagement.Etos.Bom;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.TenantManagement;

namespace SpareParts.MasterDataManagement.EventHandlers.V2;

[ExcludeFromCodeCoverage]
[ExposeServices(typeof(IMdmEventHandlerV2<BomCreatedOrUpdatedEto, BomPayload>))]
public class CreateOrUpdateBomEventHandlerV2 : ChildEventHandlerBaseV2<BomCreatedOrUpdatedEto, BomPayload, BomPublishedSyncDoneEto, AcceptedBomDcpEvent, RejectedBomDcpEvent, BomProposal>
{
    public override async Task ProcessEventAsync(BomCreatedOrUpdatedEto eventData, CancellationToken cancellationToken)
    {
        Tenant? tenant = await GetTenantOrRejectAsync(eventData, cancellationToken);
        if (tenant == null)
        {
            return;
        }
        using (CurrentTenant.Change(tenant.Id, tenant.Name))
        {
            MasterProposal masterProposal = await MasterProposalRepository
                .FirstAsync(mp => mp.ExternalImportId == eventData.BatchInfo.ImportId, cancellationToken);
            if (eventData.BatchInfo.Step == 1)
            {
                masterProposal.SetBomProposalsCount(eventData.BatchInfo.Count);
                await MasterProposalRepository.UpdateAsync(masterProposal, true, cancellationToken);
            }

            BomProposal bomProposal = new(eventData.ProposalId, masterProposal.Id
                , eventData.Payload.ParentAssemblyCode,
                eventData.Payload.Children.ToDictionary(c => c.ChildComponentCode, c => c.Quantity));
            await ProposalRepository.InsertAsync(bomProposal, true, cancellationToken);

            if (eventData.BatchInfo.Count == eventData.BatchInfo.Step)
            {
                await PublishAcceptedAsync(eventData, masterProposal.Id, cancellationToken);
            }
        }
    }
}