using SpareParts.Core.Drawings.Commands;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Resources;
using SpareParts.Core.Enums;
using SpareParts.MasterDataManagement.Etos.Drawing;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Content;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.ChangeTracking;
using Volo.Abp.Domain.Repositories;

namespace SpareParts.MasterDataManagement.EventHandlers;

[ExposeServices(typeof(IMdmEventHandlerV1<DrawingCreatedOrUpdatedEto, DrawingPayload>))]
public class CreateOrUpdateDrawingEventHandler : EventHandlerBase<DrawingCreatedOrUpdatedEto, DrawingPayload, DrawingPublishedSyncDoneEto, AcceptedDrawingDcpEvent, RejectedDrawingDcpEvent>
{
    private IRepository<Drawing> DrawingRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Drawing>>();
    private IRepository<Resource> ResourceRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Resource>>();
    private DatahubFileShareApiClient DatahubFileShareApiClient => LazyServiceProvider.LazyGetRequiredService<DatahubFileShareApiClient>();

    [ExcludeFromCodeCoverage]
    protected override async Task CustomHandleEventAsync(DrawingCreatedOrUpdatedEto eventData)
    {
        Guid? componentId = await GetComponentIdOrReject(eventData, eventData.Payload.AssemblyCode);
        if (!componentId.HasValue)
        {
            return;
        }

        MemoryStream drawingFile;
        try
        {
            drawingFile = await DatahubFileShareApiClient.GetStreamAsync(eventData.Payload.DrawingPath);
        }
        catch (Exception ex)
        {
            string message = $"Drawing {eventData.Payload.DrawingPath} cannot be downloaded from datahub";
            await RollbackUowAndPublishRejectedAsync(eventData, message, ErrorCodes.DrawingInternalError, ex);
            return;
        }

        await using (drawingFile)
        {
            Dictionary<string, Guid> componentIdsByIndex = [];
            string eventDataDrawingFileName = Path.GetFileName(eventData.DrawingFileName);
            if (eventData.Payload.ComponentCodesByIndex.Count > 0)
            {
                Dictionary<string, Guid> componentIdsByCode = (await ComponentRepository.GetQueryableAsync()).Where(c => eventData.Payload.ComponentCodesByIndex.Select(x => x.Value).Contains(c.Code))
                    .Select(c => new { c.Id, c.Code }).ToDictionary(c => c.Code, c => c.Id);
                if (componentIdsByCode.Count == 0)
                {
                    string unknownComponentCodes = string.Join(',', eventData.Payload.ComponentCodesByIndex.Select(x => x.Value).Distinct());
                    string message = $"All components [{unknownComponentCodes}] inside drawing {eventDataDrawingFileName} are unknown";
                    await RollbackUowAndPublishRejectedAsync(eventData, message, ErrorCodes.DrawingUnknownComponents);
                    return;
                }

                foreach ((string index, string code) in eventData.Payload.ComponentCodesByIndex.Where(x => !string.IsNullOrWhiteSpace(x.Value) && !string.IsNullOrWhiteSpace(x.Key)))
                {
                    if (componentIdsByCode.ContainsKey(code.Trim()))
                    {
                        componentIdsByIndex[index] = componentIdsByCode[code.Trim()];
                    }
                }
            }

            Guid? drawingId = await GetDrawingId(eventDataDrawingFileName, componentId);
            if (drawingId.HasValue)
            {
                try
                {
                    await CommandSender.Send(new UpdateDrawingCommand(drawingId.Value, new RemoteStreamContent(drawingFile, eventDataDrawingFileName), componentIdsByIndex));
                    await CompleteUowAndPublishAcceptedAsync(eventData);
                }
                catch (Exception ex)
                {
                    await ErrorWhileUpdatingAsync(eventData, ex);
                }
            }
            else
            {
                try
                {
                    await CommandSender.Send(new CreateDrawingCommand(componentId.Value, new RemoteStreamContent(drawingFile, eventDataDrawingFileName), componentIdsByIndex, DrawingOrigin.SolidWorks));
                    await CompleteUowAndPublishAcceptedAsync(eventData);
                }
                catch (Exception ex)
                {
                    await ErrorWhileCreatingAsync(eventData, ex);
                }
            }
        }
    }

    [ExcludeFromCodeCoverage]
    private async Task ErrorWhileCreatingAsync(DrawingCreatedOrUpdatedEto eventData, Exception ex)
    {
        string eventDataDrawingFileName = Path.GetFileName(eventData.DrawingFileName);
        string message = $"Error while creating drawing {eventDataDrawingFileName} for component with code {eventData.Payload.AssemblyCode}";
        await RollbackUowAndPublishRejectedAsync(eventData, message, ErrorCodes.DrawingError, ex);
    }

    [ExcludeFromCodeCoverage]
    private async Task ErrorWhileUpdatingAsync(DrawingCreatedOrUpdatedEto eventData, Exception ex)
    {
        string eventDataDrawingFileName = Path.GetFileName(eventData.DrawingFileName);
        string message = $"Error while updating drawing {eventDataDrawingFileName} for component with code {eventData.Payload.AssemblyCode}";
        await RollbackUowAndPublishRejectedAsync(eventData, message, ErrorCodes.DrawingError, ex);
    }

    [DisableEntityChangeTracking]
    [ExcludeFromCodeCoverage]
    private async Task<Guid?> GetDrawingId(string eventDataDrawingFileName, [DisallowNull] Guid? componentId)
    {
        IQueryable<Guid?> drawingIdQueryable = from drawing in await DrawingRepository.GetQueryableAsync()
                                               join resource in await ResourceRepository.GetQueryableAsync()
                                                   on new { fileId = drawing.SourceFileId, componentId = drawing.AssemblyId, fileName = eventDataDrawingFileName }
                                                   equals new { fileId = resource.Id, componentId = componentId.Value, fileName = resource.FileName }
                                               select drawing.Id as Guid?;
        return await DrawingRepository.AsyncExecuter.FirstOrDefaultAsync(drawingIdQueryable);
    }
}