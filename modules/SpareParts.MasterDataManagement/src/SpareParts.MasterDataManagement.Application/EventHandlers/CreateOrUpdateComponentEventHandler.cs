using SpareParts.Common.Dtos;
using SpareParts.Core.Components.Commands;
using SpareParts.Core.Enums;
using SpareParts.MasterDataManagement.Etos;
using SpareParts.MasterDataManagement.Etos.Component;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.ChangeTracking;

namespace SpareParts.MasterDataManagement.EventHandlers;

[ExposeServices(typeof(IMdmEventHandlerV1<ComponentCreatedOrUpdatedEto, ComponentPayload>))]
public class CreateOrUpdateComponentEventHandler : EventHandlerBase<ComponentCreatedOrUpdatedEto, ComponentPayload, ComponentPublishedSyncDoneEto, AcceptedComponentDcpEvent, RejectedComponentDcpEvent>
{
    private sealed record SimpleComponent(Guid Id, ComponentType Type);

    [ExcludeFromCodeCoverage]
    protected override async Task CustomHandleEventAsync(ComponentCreatedOrUpdatedEto eventData)
    {
        SimpleComponent? simpleComponent = await GetSimpleComponent(eventData);

        ComponentType newType;
        try
        {
            newType = FromDatahubTypeToSparePartsType(eventData.Payload.Type);
        }
        catch (Exception ex)
        {
            string message = $"Error while creating/updating component with code {eventData.Payload.Code} because type {eventData.Payload.Type} is unknown";
                await RollbackUowAndPublishRejectedAsync(eventData, message, ErrorCodes.ComponentTypeError, ex);
                return;
        }
        List<TranslationEto> translations = eventData.Payload.Translations;
        if (simpleComponent is not null)
        {
            try
            {
                await SendUpdateComponentAsync(simpleComponent.Id, translations);
                ComponentType oldType = simpleComponent.Type;
                if (!newType.Equals(oldType))
                {
                    await CommandSender.Send(new ChangeComponentTypeCommand(simpleComponent.Id, newType));
                }

                await CompleteUowAndPublishAcceptedAsync(eventData);
            }
            catch (Exception ex)
            {
                await ErrorWhileUpdatingAsync(eventData, ex);
            }
        }
        else
        {
            if (translations.Count == 0)
            {
                string message =
                    $"Error while creating component with code {eventData.Payload.Code} because there are no translations";
                await RollbackUowAndPublishRejectedAsync(eventData, message, ErrorCodes.ComponentTranslationError);
                return;
            }
            try
            {
                await SendCreateConcreteComponentAsync(newType, eventData.Payload.Code, translations);
                await CompleteUowAndPublishAcceptedAsync(eventData);
            }
            catch (Exception ex)
            {
                await ErrorWhileCreatingAsync(eventData, ex);
            }
        }
    }

    [DisableEntityChangeTracking]
    [ExcludeFromCodeCoverage]
    private async Task<SimpleComponent?> GetSimpleComponent(ComponentCreatedOrUpdatedEto eventData)
    {
        IQueryable<SimpleComponent?> simpleComponentQueryable = (await ComponentRepository.GetQueryableAsync()).Where(c =>
                string.Equals(c.Code,
                    eventData.Payload.Code))
            .Select(c => new SimpleComponent(c.Id, c.Type));
        return await ComponentRepository.AsyncExecuter.FirstOrDefaultAsync(simpleComponentQueryable);
    }

    [ExcludeFromCodeCoverage]
    private async Task ErrorWhileCreatingAsync(ComponentCreatedOrUpdatedEto eventData, Exception ex)
    {
        string message = $"Error while creating component with code {eventData.Payload.Code}";
        await RollbackUowAndPublishRejectedAsync(eventData, message, ErrorCodes.ComponentError, ex);
    }

    [ExcludeFromCodeCoverage]
    private async Task ErrorWhileUpdatingAsync(ComponentCreatedOrUpdatedEto eventData, Exception ex)
    {
        string message = $"Error while updating component with code {eventData.Payload.Code}";
        await RollbackUowAndPublishRejectedAsync(eventData, message, ErrorCodes.ComponentError, ex);
    }

    private static ComponentType FromDatahubTypeToSparePartsType(string type)
    {
        if (type.Equals(MasterDataManagementConsts.RawComponentTypesConsts.Assembly, StringComparison.OrdinalIgnoreCase))
        {
            return ComponentType.Assembly;
        }
        if (type.Equals(MasterDataManagementConsts.RawComponentTypesConsts.Part, StringComparison.OrdinalIgnoreCase))
        {
            return ComponentType.Part;
        }
        throw new ArgumentOutOfRangeException(nameof(type), type, null);
    }

    private async Task SendUpdateComponentAsync(Guid id, List<TranslationEto> translationEtos)
    {
        await CommandSender.Send(new SetComponentTranslationsCommand(id, translationEtos.Select(t => new CommonTranslationDto(t.Language, t.Label))));
    }

    private async Task SendCreateConcreteComponentAsync(ComponentType type, string code, List<TranslationEto> translationEtos)
    {
        IEnumerable<CommonTranslationDto> commonTranslationDtos = translationEtos.Select(t => new CommonTranslationDto(t.Language, t.Label));
        await CommandSender.Send(new CreateComponentCommand(code, commonTranslationDtos, type));
    }
}