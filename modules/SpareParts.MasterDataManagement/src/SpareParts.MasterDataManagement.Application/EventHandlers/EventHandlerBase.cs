using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using SpareParts.AbpMediatR.Commands;
using SpareParts.Core.Entities.Components;
using SpareParts.MasterDataManagement.Etos;
using SpareParts.MasterDataManagement.Etos.Bases;
using SpareParts.MasterDataManagement.Etos.InnerEtos;
using Volo.Abp;
using Volo.Abp.Auditing;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.ChangeTracking;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.MultiTenancy;
using Volo.Abp.TenantManagement;
using Volo.Abp.Uow;
using Volo.Abp.Validation;

namespace SpareParts.MasterDataManagement.EventHandlers;

public abstract class EventHandlerBase<TEvent, TPayload, TDoneEto, TAcceptedEvent, TRejectedEvent> : IMdmEventHandlerV1<TEvent, TPayload>, IAuditingEnabled
    where TEvent : EtoBase<TPayload>
    where TPayload : class
    where TDoneEto : PublishedSyncDoneEto, new()
    where TAcceptedEvent : AcceptedDcpEvent, new()
    where TRejectedEvent : RejectedDcpEvent, new()
{
    public IAbpLazyServiceProvider LazyServiceProvider { get; set; } = null!;
    private ITenantRepository TenantRepository => LazyServiceProvider.LazyGetRequiredService<ITenantRepository>();
    private ILoggerFactory LoggerFactory => LazyServiceProvider.LazyGetRequiredService<ILoggerFactory>();
    protected ILogger Logger => LazyServiceProvider.LazyGetService<ILogger>(_ => LoggerFactory.CreateLogger(GetType().FullName!));
    protected ICommandSender CommandSender => LazyServiceProvider.LazyGetRequiredService<ICommandSender>();
    private IUnitOfWorkManager UnitOfWorkManager => LazyServiceProvider.LazyGetRequiredService<IUnitOfWorkManager>();
    private IUnitOfWork? CurrentUnitOfWork => UnitOfWorkManager.Current;
    private IDistributedEventBus DistributedEventBus => LazyServiceProvider.LazyGetRequiredService<IDistributedEventBus>();
    protected IRepository<Component> ComponentRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Component>>();
    private ICurrentTenant CurrentTenant => LazyServiceProvider.LazyGetRequiredService<ICurrentTenant>();

    [DisableEntityChangeTracking]
    protected async Task<Guid?> GetComponentIdOrReject(TEvent eventData, string componentCode)
    {
        IQueryable<Guid?> idQueryable = (await ComponentRepository.GetQueryableAsync()).Where(c =>
                string.Equals(c.Code,
                    componentCode))
            .Select(c => (Guid?)c.Id);
        Guid? componentId = await ComponentRepository.AsyncExecuter.FirstOrDefaultAsync(idQueryable);
        if (componentId is null)
        {
            await RollbackUowAndPublishRejectedForUnknownComponent(eventData, componentCode);
        }

        return componentId;
    }

    private async Task RollbackUowAndPublishRejectedForUnknownComponent(TEvent eventData, string componentCode)
    {
        string message = $"Component with code {componentCode} is unknown";
        await RollbackUowAndPublishRejectedAsync(eventData, message, ErrorCodes.ComponentUnknown);
    }

    private async Task PublishProgressAsync(TEvent eventData)
    {
        BatchInfoEto batchInfoEto = eventData.BatchInfo;
        if (batchInfoEto.Count == batchInfoEto.Step)
        {
            await DistributedEventBus.PublishAsync(new TDoneEto
            {
                BatchInfo = batchInfoEto
            }, false);
        }
        Logger.LogInformation("Progress:Step={Step};Total={Count};Batch={BatchId};Tenant={TenantName}", batchInfoEto.Step, batchInfoEto.Count, batchInfoEto.BatchId, eventData.TenantName);
    }

    private void LogRejection(TEvent eventData, string message, Exception? exception = null)
    {
        Logger.LogError(exception, "Rejected:Proposal={ProposalId};Batch={BatchId};Tenant={TenantName};EntityType={EntityType};Message={Message}", eventData.ProposalId, eventData.BatchInfo.BatchId, eventData.TenantName, eventData.EntityType, message);
    }

    [DisableEntityChangeTracking]
    private async Task<Tenant?> GetTenantOrRejectAsync(TEvent eventData, CancellationToken cancellationToken = default)
    {
        Tenant? tenant = await TenantRepository.FindByNameAsync(eventData.TenantName, cancellationToken: cancellationToken);
        if (tenant != null)
        {
            return tenant;
        }

        string message = $"Unknown/disabled Tenant : {eventData.TenantName}";
        await RollbackUowAndPublishRejectedAsync(eventData, message, ErrorCodes.TenantNotFound);
        return null;
    }

    protected async Task CompleteUowAndPublishAcceptedAsync(TEvent eventData, CancellationToken cancellationToken = default)
    {
        await CurrentUnitOfWork!.CompleteAsync(cancellationToken);
        await DistributedEventBus.PublishAsync(new TAcceptedEvent
        {
            AcceptedProposalIds = [eventData.ProposalId]
        }, false);
        Logger.LogInformation("Accepted:Proposal={ProposalId};Batch={BatchId};Tenant={TenantName};EntityType={EntityType}", eventData.ProposalId, eventData.BatchInfo.BatchId, eventData.TenantName, eventData.EntityType);
        await PublishProgressAsync(eventData);
    }

    protected async Task RollbackUowAndPublishRejectedAsync(TEvent eventData, string message, string code, Exception? exception = null)
    {
        switch (exception)
        {
            case BusinessException businessException:
            {
                message = businessException.Message;
                if (businessException.Code is not null)
                {
                    code = businessException.Code;
                }

                break;
            }
            case AbpValidationException validationException:
            {
                if (!validationException.ValidationErrors.IsNullOrEmpty())
                {
                    StringBuilder validationErrors = new();
                    validationErrors.Append(". There are " + validationException.ValidationErrors.Count + " validation errors:");
                    foreach (ValidationResult validationResult in validationException.ValidationErrors)
                    {
                        string memberNames = "";
                        if (validationResult.MemberNames.Any())
                        {
                            memberNames = " (" + string.Join(", ", validationResult.MemberNames) + ")";
                        }

                        validationErrors.Append('-' + validationResult.ErrorMessage + memberNames);
                    }
                    message += validationErrors.ToString();
                }

                break;
            }
        }
        LogRejection(eventData, message, exception);
        await CurrentUnitOfWork!.RollbackAsync();
        TRejectedEvent rejectedDcp = new()
        {
            ProposalId = eventData.ProposalId
        };
        rejectedDcp.ErrorMessages.Add(new ErrorMessage
        {
            Code = code,
            Message = message
        });
        await DistributedEventBus.PublishAsync(rejectedDcp, false);
        await PublishProgressAsync(eventData);
    }

    public virtual async Task ProcessEventAsync(TEvent eventData, CancellationToken cancellationToken)
    {
        Stopwatch stopwatch = new();
        stopwatch.Start();
        Logger.LogInformation("Begin:Proposal={ProposalId};Batch={BatchId};Tenant={TenantName};EntityType={EntityType}", eventData.ProposalId, eventData.BatchInfo.BatchId, eventData.TenantName, eventData.EntityType);

        using IUnitOfWork uow = UnitOfWorkManager.Begin();
        Tenant? tenant = await GetTenantOrRejectAsync(eventData, cancellationToken);
        if (tenant == null)
        {
            return;
        }

        using (CurrentTenant.Change(tenant.Id, tenant.Name))
        {
            try
            {
                await CustomHandleEventAsync(eventData);
            }
            catch (Exception e)
            {
                await RollbackUowAndPublishRejectedAsync(eventData, e.Message, ErrorCodes.UnknownError, e);
            }
        }

        stopwatch.Stop();
        Logger.LogInformation("End:Proposal={ProposalId};Batch={BatchId};Tenant={TenantName};EntityType={EntityType};ElapsedMilliseconds={ElapsedMilliseconds}", eventData.ProposalId, eventData.BatchInfo.BatchId, eventData.TenantName, eventData.EntityType, stopwatch.ElapsedMilliseconds);
    }

    protected abstract Task CustomHandleEventAsync(TEvent eventData);
}