<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
    <CentralPackageTransitivePinningEnabled>false</CentralPackageTransitivePinningEnabled>
  </PropertyGroup>
  <ItemGroup>
    <PackageVersion Include="AspNetCore.HealthChecks.Prometheus.Metrics" Version="8.0.1" />
    <PackageVersion Include="AutoFilterer" Version="3.2.0" />
    <PackageVersion Include="Azure.AI.Translation.Text" Version="1.0.0" />
    <PackageVersion Include="Bogus" Version="35.6.3" />
    <PackageVersion Include="ConfigureAwait.Fody" Version="3.3.2" />
    <PackageVersion Include="coverlet.collector" Version="6.0.4" />
    <PackageVersion Include="CsvHelper" Version="33.1.0" />
    <PackageVersion Include="DistributedLock.SqlServer" Version="1.0.6" />
    <PackageVersion Include="Duende.AccessTokenManagement" Version="3.2.0" />
    <PackageVersion Include="Fody" Version="6.9.2" />
    <PackageVersion Include="Hangfire.SqlServer" Version="1.8.20" />
    <PackageVersion Include="HarfBuzzSharp.NativeAssets.Linux" Version="8.3.1.1" />
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.8" />
    <PackageVersion Include="Microsoft.AspNetCore.DataProtection.EntityFrameworkCore" Version="9.0.8" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.8" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Proxies" Version="9.0.8" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Extensions.FileProviders.Embedded" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Extensions.Hosting" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Extensions.Http.Polly" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Extensions.Identity.Core" Version="9.0.8" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
    <PackageVersion Include="Mime-Detective" Version="25.8.1" />
    <PackageVersion Include="MimeTypes" Version="2.5.2" />
    <PackageVersion Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageVersion Include="NSubstitute" Version="5.3.0" />
    <PackageVersion Include="NSubstitute.Analyzers.CSharp" Version="1.0.17" />
    <PackageVersion Include="OpenTelemetry.Contrib.Preview" Version="1.0.0-beta2" />
    <PackageVersion Include="OpenTelemetry.Exporter.OneCollector" Version="1.9.1" />
    <PackageVersion Include="OpenTelemetry.Exporter.Prometheus.AspNetCore" Version="1.12.0-beta.1" />
    <PackageVersion Include="OpenTelemetry.Extensions.Hosting" Version="1.12.0" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.12.0" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.EventCounters" Version="1.5.1-alpha.1" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.Http" Version="1.12.0" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.Process" Version="1.12.0-beta.1" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.Runtime" Version="1.12.0" />
    <PackageVersion Include="PDFtoImage" Version="5.1.1" />
    <PackageVersion Include="PdfToSvg.NET" Version="1.6.1" />
    <PackageVersion Include="QuestPDF" Version="2022.12.15" />
    <PackageVersion Include="Refit" Version="8.0.0" />
    <PackageVersion Include="Refit.HttpClientFactory" Version="8.0.0" />
    <PackageVersion Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageVersion Include="Serilog.Expressions" Version="5.0.0" />
    <PackageVersion Include="Serilog.Extensions.Hosting" Version="9.0.0" />
    <PackageVersion Include="Serilog.Extensions.Logging" Version="9.0.2" />
    <PackageVersion Include="Serilog.Sinks.Async" Version="2.1.0" />
    <PackageVersion Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageVersion Include="Serilog.Sinks.File" Version="7.0.0" />
    <PackageVersion Include="Shouldly" Version="4.3.0" />
    <PackageVersion Include="SixLabors.ImageSharp" Version="3.1.11" />
    <PackageVersion Include="SkiaSharp.NativeAssets.Linux.NoDependencies" Version="2.88.8" />
    <PackageVersion Include="Swashbuckle.AspNetCore.Annotations" Version="9.0.3" />
    <PackageVersion Include="Testcontainers.MsSql" Version="4.6.0" />
    <PackageVersion Include="Volo.Abp.AspNetCore" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.AspNetCore.Mvc" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.AspNetCore.Mvc.UI.MultiTenancy" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.AspNetCore.Serilog" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.AuditLogging.Domain" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.AuditLogging.Domain.Shared" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.AuditLogging.EntityFrameworkCore" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.Authorization" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.Autofac" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.AutoMapper" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.BackgroundJobs" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.BackgroundJobs.Abstractions" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.BackgroundJobs.Domain" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.BackgroundJobs.Domain.Shared" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.BackgroundJobs.EntityFrameworkCore" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.BackgroundWorkers.Hangfire" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.BlobStoring" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.BlobStoring.Azure" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.Caching.StackExchangeRedis" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.Core" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.Ddd.Application" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.Ddd.Application.Contracts" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.Ddd.Domain" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.Ddd.Domain.Shared" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.DistributedLocking" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.Emailing" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.EntityFrameworkCore" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.EntityFrameworkCore.SqlServer" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.EventBus.Kafka" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.FeatureManagement.Domain" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.FeatureManagement.Domain.Shared" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.FeatureManagement.EntityFrameworkCore" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.FluentValidation" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.GlobalFeatures" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.Guids" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.Identity.Domain" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.Identity.Domain.Shared" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.Identity.EntityFrameworkCore" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.Imaging.Abstractions" Version="9.1.3" />
    <PackageVersion Include="Volo.Abp.Imaging.ImageSharp" Version="9.1.3" />
    <PackageVersion Include="Volo.Abp.MailKit" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.PermissionManagement.Domain" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.PermissionManagement.Domain.Identity" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.PermissionManagement.Domain.Shared" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.PermissionManagement.EntityFrameworkCore" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.SettingManagement.Application" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.SettingManagement.Application.Contracts" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.SettingManagement.Domain" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.SettingManagement.Domain.Shared" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.SettingManagement.EntityFrameworkCore" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.Swashbuckle" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.TenantManagement.Domain" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.TenantManagement.Domain.Shared" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.TenantManagement.EntityFrameworkCore" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.TestBase" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.TextTemplating.Scriban" Version="9.2.3" />
    <PackageVersion Include="Volo.Abp.Validation" Version="9.2.3" />
    <PackageVersion Include="xunit" Version="2.9.3" />
    <PackageVersion Include="xunit.extensibility.execution" Version="2.9.3" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="3.1.3" />
  </ItemGroup>
</Project>