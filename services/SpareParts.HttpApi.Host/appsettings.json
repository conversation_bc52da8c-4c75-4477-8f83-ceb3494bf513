{"App": {"CorsOrigins": "https://localhost:4200,https://idp.core.dev.my.visiativ.com", "WebAppUrl": "https://localhost:4200"}, "ConnectionStrings": {"Default": ""}, "AuthServer": {"Authority": "https://agoraidpint.b2clogin.com/agoraidpint.onmicrosoft.com/B2C_1A_AGORACUSTOMPOLICY_SIGNIN/v2.0", "SwaggerClientId": ""}, "Kafka": {"Connections": {"Default": {"BootstrapServers": "", "SaslUsername": "$ConnectionString", "SaslPassword": ""}}, "EventBus": {"GroupId": "local-spareparts-consumer", "TopicName": "identity-events-topic"}}, "Azure": {"BlobStorage": {"ConnectionString": "", "CreateContainerIfNotExists": "true"}, "AiTranslator": {"Endpoint": "", "Key": "", "Region": "francecentral"}}, "Kestrel": {"Endpoints": {"Api": {"Url": "https://localhost:44378", "Protocols": "Http1AndHttp2"}}}, "Settings": {"Abp.Mailing.Smtp.Host": "127.0.0.1", "Abp.Mailing.Smtp.Port": "25", "Abp.Mailing.Smtp.UserName": "", "Abp.Mailing.Smtp.Password": "", "Abp.Mailing.Smtp.Domain": "", "Abp.Mailing.Smtp.EnableSsl": "false", "Abp.Mailing.Smtp.UseDefaultCredentials": "true", "Abp.Mailing.DefaultFromAddress": "<EMAIL>", "Abp.Mailing.DefaultFromDisplayName": "Spare Parts Catalog"}, "Identity": {"Api": {"BaseAddress": ""}, "M2m": {"ClientId": "", "ClientSecret": "", "TokenEndpoint": "", "Scope": ""}}, "GlobalFeatures": {"Core.Equipment": "false", "Core.DocumentCenter": "false", "Core.BomDocument": "false", "Common.AutoTranslation": "false", "Core.MultiDrawing": "false", "Core.DataImportMonitoring": "false"}, "BomHierarchyHistoryWorker": {"CronExpression": "0 18 * * 1-5"}, "BomDocumentWorker": {"CronExpression": "0 18 * * 1-5"}}