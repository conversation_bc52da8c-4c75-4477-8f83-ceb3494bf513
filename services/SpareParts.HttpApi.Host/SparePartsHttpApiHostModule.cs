using Asp.Versioning;
using Asp.Versioning.ApiExplorer;
using Azure;
using Azure.AI.Translation.Text;
using Hangfire;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using OpenTelemetry;
using OpenTelemetry.Metrics;
using SpareParts.Common.Configurations;
using SpareParts.Core.BackgroundJobs;
using SpareParts.EntityFrameworkCore;
using SpareParts.EventBus.Kafka;
using SpareParts.Metrics;
using SpareParts.MultiTenancy;
using SpareParts.Swagger;
using Swashbuckle.AspNetCore.SwaggerGen;
using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.AspNetCore.MultiTenancy;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc.AntiForgery;
using Volo.Abp.AspNetCore.Mvc.UI.MultiTenancy;
using Volo.Abp.AspNetCore.Serilog;
using Volo.Abp.Autofac;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.BackgroundWorkers;
using Volo.Abp.BackgroundWorkers.Hangfire;
using Volo.Abp.Caching;
using Volo.Abp.Modularity;
using Volo.Abp.Swashbuckle;
using Volo.Abp.VirtualFileSystem;

namespace SpareParts;

[DependsOn(
    typeof(SparePartsHttpApiModule),
    typeof(AbpAutofacModule),
    typeof(AbpAspNetCoreMvcUiMultiTenancyModule),
    typeof(SparePartsApplicationModule),
    typeof(SparePartsEntityFrameworkCoreModule),
    typeof(AbpAspNetCoreSerilogModule),
    typeof(AbpSwashbuckleModule),
    typeof(SparePartsHostSharedModule),
    typeof(AgoraEventBusKafkaModule),
    typeof(AbpBackgroundWorkersHangfireModule)
)]
public class SparePartsHttpApiHostModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        IConfiguration configuration = context.Services.GetConfiguration();

        Configure<AbpBackgroundJobWorkerOptions>(options =>
        {
            options.ApplicationName = "spareparts_api";
        });

        Configure<AbpAspNetCoreMultiTenancyOptions>(options =>
        {
            options.TenantKey = MultiTenancyConsts.TenantKey;
        });

        Configure<ApiBehaviorOptions>(options
            => options.SuppressModelStateInvalidFilter = true);

        ConfigureConventionalControllers();
        ConfigureCache();
        ConfigureVirtualFileSystem(context);
        ConfigureDataProtection(context);
        ConfigureOpenTelemetry(context);

        ConfigureMvcCore(context);
        ConfigureAuthentication(context, configuration);
        ConfigureApplication(context, configuration);
        ConfigureApiVersioning(context);
        ConfigureSwaggerServices(context, configuration);
        ConfigureHealthChecks(context);

        context.Services.AddResponseCaching();

        Configure<AbpAntiForgeryOptions>(options =>
        {
            options.AutoValidate = false;
        });
        context.Services.TransformAbpClaims();

        context.Services.AddHangfire(config =>
        {
            config.UseSqlServerStorage(configuration.GetConnectionString("Default"));
        });

        context.Services.AddSingleton<TextTranslationClient>(_ => new TextTranslationClient(new AzureKeyCredential(configuration["Azure:AiTranslator:Key"]!), new Uri(configuration["Azure:AiTranslator:Endpoint"]!), configuration["Azure:AiTranslator:Region"]!));
    }

    private static void ConfigureOpenTelemetry(ServiceConfigurationContext context)
    {
        context.Services.AddOpenTelemetry()
            .WithMetrics(metrics =>
            {
                metrics.AddPrometheusExporter()
                    .AddSparePartsInstrumentation();
            });

        context.Services.AddKeyedSingleton("technicalMeterProvider", Sdk.CreateMeterProviderBuilder()
                .AddPrometheusExporter()
                .AddRuntimeInstrumentation()
                .AddHttpClientInstrumentation()
                .AddAspNetCoreInstrumentation()
                .AddEventCountersInstrumentation()
                .AddProcessInstrumentation().Build());
    }

    private void ConfigureApiVersioning(ServiceConfigurationContext context)
    {
        context.Services.AddApiVersioning(options =>
            {
                options.DefaultApiVersion = new ApiVersion(1, 0);
                options.ReportApiVersions = true;
                options.AssumeDefaultVersionWhenUnspecified = true;
            })
            .AddApiExplorer(options =>
            {
                options.GroupNameFormat = "'v'VVV";
                options.AssumeDefaultVersionWhenUnspecified = true;
                options.DefaultApiVersion = new ApiVersion(1, 0);
            });

        Configure<AbpAspNetCoreMvcOptions>(options =>
        {
            options.ChangeControllerModelApiExplorerGroupName = false;
        });
    }

    private void ConfigureCache()
    {
        Configure<AbpDistributedCacheOptions>(options =>
        {
            options.KeyPrefix = "SpareParts:";
            options.GlobalCacheEntryOptions = new DistributedCacheEntryOptions
            {
                AbsoluteExpiration = DateTimeOffset.UnixEpoch
            };
        });
    }

    private void ConfigureVirtualFileSystem(ServiceConfigurationContext context)
    {
        IWebHostEnvironment hostingEnvironment = context.Services.GetHostingEnvironment();

        if (hostingEnvironment.IsDevelopment())
        {
            Configure<AbpVirtualFileSystemOptions>(options =>
            {
                options.FileSets.ReplaceEmbeddedByPhysical<SparePartsDomainSharedModule>(
                    Path.Combine(hostingEnvironment.ContentRootPath,
                        $"..{Path.DirectorySeparatorChar}..{Path.DirectorySeparatorChar}src{Path.DirectorySeparatorChar}SpareParts.Domain.Shared"));
                options.FileSets.ReplaceEmbeddedByPhysical<SparePartsDomainModule>(
                    Path.Combine(hostingEnvironment.ContentRootPath,
                        $"..{Path.DirectorySeparatorChar}..{Path.DirectorySeparatorChar}src{Path.DirectorySeparatorChar}SpareParts.Domain"));
                options.FileSets.ReplaceEmbeddedByPhysical<SparePartsApplicationContractsModule>(
                    Path.Combine(hostingEnvironment.ContentRootPath,
                        $"..{Path.DirectorySeparatorChar}..{Path.DirectorySeparatorChar}src{Path.DirectorySeparatorChar}SpareParts.Application.Contracts"));
                options.FileSets.ReplaceEmbeddedByPhysical<SparePartsApplicationModule>(
                    Path.Combine(hostingEnvironment.ContentRootPath,
                        $"..{Path.DirectorySeparatorChar}..{Path.DirectorySeparatorChar}src{Path.DirectorySeparatorChar}SpareParts.Application"));
            });
        }
    }

    private void ConfigureConventionalControllers()
    {
        Configure<AbpAspNetCoreMvcOptions>(options =>
        {
            options.ConventionalControllers.Create(typeof(SparePartsApplicationModule).Assembly);
        });
    }

    private static void ConfigureAuthentication(ServiceConfigurationContext context, IConfiguration configuration)
    {
        context.Services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                string authority = configuration["AuthServer:Authority"]!;
                options.Authority = authority;
                options.Audience = configuration["AuthServer:SwaggerClientId"];
            });
    }

    private static void ConfigureSwaggerServices(ServiceConfigurationContext context, IConfiguration configuration)
    {
        context.Services.AddTransient<IConfigureOptions<SwaggerGenOptions>, ConfigureSwaggerOptions>();
        string clientId = configuration["AuthServer:SwaggerClientId"]!;
        string[] scopes =
        [
            "openid",
            "profile",
            "email",
            "offline_access",
            clientId
        ];
        context.Services.AddAbpSwaggerGenWithOidc(
            configuration["AuthServer:Authority"]!,
            scopes: scopes,
            setupAction: options =>
            {
                options.OperationFilter<SwaggerDefaultValues>();
                options.DocumentFilter<HideFromSwaggerDocumentFilter>();
                options.CustomSchemaIds(type => type.FullName);
                options.OperationFilter<TenantFilter>();
                options.DescribeAllParametersInCamelCase();
                options.EnableAnnotations();
                options.UserFriendlyEnums();
            });
    }

    private static void ConfigureMvcCore(ServiceConfigurationContext context)
    {
        context.Services.AddMvcCore(
            options => options.Conventions.Add
            (
                new RouteTokenTransformerConvention(new SlugifyParameterTransformer())
            )
        );
    }

    private static void ConfigureDataProtection(
        ServiceConfigurationContext context)
    {
        IDataProtectionBuilder dataProtectionBuilder = context.Services.AddDataProtection().SetApplicationName("SpareParts");
        dataProtectionBuilder.PersistKeysToDbContext<SparePartsDbContext>();
    }

    private static void ConfigureApplication(ServiceConfigurationContext context, IConfiguration configuration)
    {
        context.Services.Configure<ApplicationConfiguration>(options =>
        {
            options.WebAppUrl = configuration["App:WebAppUrl"]!.EnsureEndsWith('/');
        });
    }

    private static void ConfigureHealthChecks(ServiceConfigurationContext context)
    {
        context.Services.AddSparePartsHealthChecks();
    }

    public override async Task OnApplicationInitializationAsync(ApplicationInitializationContext context)
    {
        IApplicationBuilder app = context.GetApplicationBuilder();
        IWebHostEnvironment env = context.GetEnvironment();

        context
            .ServiceProvider
            .GetRequiredService<AgoraKafkaDistributedEventBus>()
            .Initialize();

        await context.AddBackgroundWorkerAsync<BomHierarchyHistoryBackgroundWorker>();

        if (env.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
        }

        app.UseAbpRequestLocalization();
        app.UseCorrelationId();
        app.MapAbpStaticAssets();
        app.UseRouting();
        app.UseResponseCaching();
        app.UseAuthentication();

        if (MultiTenancyConsts.IsEnabled)
        {
            app.UseMultiTenancy();
        }

        app.UseUnitOfWork();
        app.UseMiddleware<SparePartsClaimsMiddleware>();
        app.UseMiddleware<SparePartsVisibilityMiddleware>();
        app.UseAuthorization();

        app.UseSwagger();
        app.UseAbpSwaggerUI(options =>
        {
            IApiVersionDescriptionProvider provider = app.ApplicationServices.GetRequiredService<IApiVersionDescriptionProvider>();
            // build a swagger endpoint for each discovered API version
            foreach (string groupName in provider.ApiVersionDescriptions.Select(desc => desc.GroupName))
            {
                string url = $"/swagger/{groupName}/swagger.json";
                string name = groupName.ToUpperInvariant();
                options.SwaggerEndpoint(url, name);
            }
            IConfiguration configuration = context.GetConfiguration();
            options.OAuthClientId(configuration["AuthServer:SwaggerClientId"]);
            options.OAuthUsePkce();
            options.DisplayRequestDuration();
        });

        app.UseAuditing();
        app.UseAbpSerilogEnrichers();

        app.UseConfiguredEndpoints(
            static builder =>
            {
                builder.MapPrometheusScrapingEndpoint(null, builder.ServiceProvider.GetRequiredKeyedService<MeterProvider>("technicalMeterProvider"), null, null);
                builder.MapPrometheusScrapingEndpoint("/usage-metrics");
            });
    }
}