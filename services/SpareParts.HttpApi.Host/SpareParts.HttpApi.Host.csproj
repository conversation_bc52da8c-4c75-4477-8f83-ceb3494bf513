<Project Sdk="Microsoft.NET.Sdk.Web">

	<Import Project="..\..\common.props" />

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<RootNamespace>SpareParts</RootNamespace>
		<PreserveCompilationReferences>true</PreserveCompilationReferences>
		<UserSecretsId>SpareParts-4681b4fd-151f-4221-84a4-929d86723e4c</UserSecretsId>
		<Version>1.14.0</Version>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Hangfire.SqlServer" />
		<PackageReference Include="OpenTelemetry.Exporter.Prometheus.AspNetCore" />
		<PackageReference Include="OpenTelemetry.Extensions.Hosting" />
		<PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" />
		<PackageReference Include="OpenTelemetry.Instrumentation.EventCounters" />
		<PackageReference Include="OpenTelemetry.Instrumentation.Http" />
		<PackageReference Include="OpenTelemetry.Instrumentation.Process" />
		<PackageReference Include="OpenTelemetry.Instrumentation.Runtime" />
		<PackageReference Include="Serilog.AspNetCore" />
		<PackageReference Include="Serilog.Sinks.Async">
			<TreatAsUsed>true</TreatAsUsed>
		</PackageReference>
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
		<PackageReference Include="DistributedLock.SqlServer">
			<TreatAsUsed>true</TreatAsUsed>
		</PackageReference>
		<PackageReference Include="Volo.Abp.AspNetCore.Mvc.UI.MultiTenancy" />
		<PackageReference Include="Volo.Abp.Autofac">
			<TreatAsUsed>true</TreatAsUsed>
		</PackageReference>
		<PackageReference Include="Volo.Abp.Caching.StackExchangeRedis">
			<TreatAsUsed>true</TreatAsUsed>
		</PackageReference>
		<PackageReference Include="Volo.Abp.DistributedLocking">
			<TreatAsUsed>true</TreatAsUsed>
		</PackageReference>
		<PackageReference Include="Volo.Abp.AspNetCore.Serilog" />
		<PackageReference Include="Volo.Abp.Swashbuckle" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<TreatAsUsed>true</TreatAsUsed>
		</PackageReference>
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\shared\SpareParts.Host.Shared\SpareParts.Host.Shared.csproj" />
		<ProjectReference Include="..\..\src\SpareParts.Application\SpareParts.Application.csproj" />
		<ProjectReference Include="..\..\src\SpareParts.EntityFrameworkCore\SpareParts.EntityFrameworkCore.csproj" />
		<ProjectReference Include="..\..\src\SpareParts.HttpApi\SpareParts.HttpApi.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Compile Remove="Logs\**" />
		<Content Remove="Logs\**" />
		<EmbeddedResource Remove="Logs\**" />
		<None Remove="Logs\**" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Update="Fody">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
		</PackageReference>
	</ItemGroup>

	<Target Name="CopyDemoDataAfterBuild" AfterTargets="Build">
		<RemoveDir Directories="$(OutputPath)demo-data" />
		<ItemGroup>
			<DemoDataFiles Include=".\demo-data\**\*" />
		</ItemGroup>
		<Copy SourceFiles="@(DemoDataFiles)" DestinationFolder="$(OutputPath)demo-data\%(RecursiveDir)" />
	</Target>

	<Target Name="CopyDemoDataAfterPublish" AfterTargets="Publish">
		<RemoveDir Directories="$(PublishDir)demo-data" />
		<ItemGroup>
			<DemoDataFiles Include=".\demo-data\**\*" />
		</ItemGroup>
		<Copy SourceFiles="@(DemoDataFiles)" DestinationFolder="$(PublishDir)demo-data\%(RecursiveDir)" />
	</Target>

</Project>
