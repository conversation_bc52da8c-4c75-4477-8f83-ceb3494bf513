using System;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Options;
using Volo.Abp.AspNetCore.ExceptionHandling;
using Volo.Abp.DependencyInjection;
using Volo.Abp.ExceptionHandling.Localization;
using Volo.Abp.Http;
using Volo.Abp.Localization.ExceptionHandling;
using Volo.Abp.Validation;

namespace SpareParts;

[ExposeServices(typeof(DefaultExceptionToErrorInfoConverter), typeof(IExceptionToErrorInfoConverter))]
[Dependency(ReplaceServices = true)]
public partial class AgoraExceptionToErrorInfoConverter(
    IOptions<AbpExceptionLocalizationOptions> localizationOptions,
    IStringLocalizerFactory stringLocalizerFactory,
    IStringLocalizer<AbpExceptionHandlingResource> stringLocalizer,
    IServiceProvider serviceProvider)
    : DefaultExceptionToErrorInfoConverter(localizationOptions, stringLocalizerFactory, stringLocalizer,
        serviceProvider)
{
    protected override RemoteServiceErrorInfo CreateErrorInfoWithoutCode(Exception exception, AbpExceptionHandlingOptions options)
    {
        RemoteServiceErrorInfo remoteServiceErrorInfo = base.CreateErrorInfoWithoutCode(exception, options);
        remoteServiceErrorInfo.Data = null;
        return remoteServiceErrorInfo;
    }

    protected override string GetValidationErrorNarrative(IHasValidationErrors validationException)
    {
        return RemoveCriticalInfos(base.GetValidationErrorNarrative(validationException));
    }

    protected override RemoteServiceValidationErrorInfo[] GetValidationErrorInfos(IHasValidationErrors validationException)
    {
        RemoteServiceValidationErrorInfo[] validationErrorInfos = base.GetValidationErrorInfos(validationException);
        foreach (RemoteServiceValidationErrorInfo remoteServiceValidationErrorInfo in validationErrorInfos)
        {
            remoteServiceValidationErrorInfo.Message = RemoveCriticalInfos(remoteServiceValidationErrorInfo.Message);
        }
        return validationErrorInfos;
    }

    private static string RemoveCriticalInfos(string message)
    {
        if (!message.Contains(". Path: $."))
        {
            return message;
        }

        message = MyRegex().Replace(message, "$2");

        // Optionally, remove the part after "Path"
        int pathIndex = message.IndexOf('|');
        if (pathIndex != -1)
        {
            message = message[..pathIndex].Trim();
        }

        return message;
    }

    [GeneratedRegex(@"(\w+\.)+(\w+)")]
    private static partial Regex MyRegex();
}