using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;

namespace SpareParts;

public static class Program
{
    public static async Task<int> Main(string[] args)
    {
        string assemblyName = typeof(Program).Assembly.GetName().Name!;
        Log.Logger = SerilogConfigurationHelper.CreateLogger(assemblyName);
        try
        {
            Log.Information("Starting SpareParts.HttpApi.Host.");
            WebApplicationBuilder builder = WebApplication.CreateBuilder(args);
            builder.Host.AddAppSettingsSecretsJson()
                .UseAutofac()
                .UseSerilog();
            builder.WebHost.UseKestrel(options => options.AddServerHeader = false);
            await builder.AddApplicationAsync<SparePartsHttpApiHostModule>();
            WebApplication app = builder.Build();
            await app.InitializeApplicationAsync();
            await app.RunAsync();
            return 0;
        }
        catch (Exception ex)
        {
            if (ex is HostAbortedException)
            {
                throw;
            }

            Log.Fatal(ex, "Host terminated unexpectedly!");
            return 1;
        }
        finally
        {
            await Log.CloseAndFlushAsync();
        }
    }
}
