using Microsoft.AspNetCore.Antiforgery;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using Volo.Abp.AspNetCore.Mvc.AntiForgery;
using Volo.Abp.DependencyInjection;

namespace SpareParts;

[Dependency(ReplaceServices = true)]
[ExposeServices(typeof(AspNetCoreAbpAntiForgeryManager), typeof(IAbpAntiForgeryManager))]
public class AntiForgeryManager(
    IAntiforgery antiforgery,
    IHttpContextAccessor httpContextAccessor,
    IOptions<AbpAntiForgeryOptions> options)
    : AspNetCoreAbpAntiForgeryManager(antiforgery, httpContextAccessor, options)
{
    public override void SetCookie()
    {
    }
}