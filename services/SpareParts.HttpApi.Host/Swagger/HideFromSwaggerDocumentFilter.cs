using System;
using System.Linq;
using Microsoft.AspNetCore.Mvc.ApiExplorer;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using Volo.Abp.GlobalFeatures;

namespace SpareParts.Swagger;

public class HideFromSwaggerDocumentFilter : IDocumentFilter
{
    public void Apply(OpenApiDocument swaggerDoc, DocumentFilterContext context)
    {
        foreach (ApiDescription? apiDescription in context.ApiDescriptions)
        {
            if (apiDescription.ActionDescriptor is not ControllerActionDescriptor controllerActionDescriptor)
            {
                continue;
            }
            RequiresGlobalFeatureAttribute? requiresGlobalFeatureAttribute = controllerActionDescriptor.ControllerTypeInfo.GetCustomAttributes(true)
                .OfType<RequiresGlobalFeatureAttribute>().FirstOrDefault();
            if (requiresGlobalFeatureAttribute == null)
            {
                continue;
            }

            Type? featureType = requiresGlobalFeatureAttribute.Type;
            if (featureType != null && GlobalFeatureManager.Instance.IsEnabled(featureType))
            {
                continue;
            }

            string? featureName = requiresGlobalFeatureAttribute.Name;
            if (featureName != null && GlobalFeatureManager.Instance.IsEnabled(featureName))
            {
                continue;
            }

            string pathToRemove = $"/{apiDescription.RelativePath!}";
            swaggerDoc.Paths.Remove(pathToRemove);
        }
    }
}