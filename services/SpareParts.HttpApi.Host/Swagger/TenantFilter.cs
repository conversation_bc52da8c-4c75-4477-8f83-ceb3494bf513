using Microsoft.OpenApi.Models;
using SpareParts.MultiTenancy;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace SpareParts.Swagger;

public class TenantFilter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        operation.Parameters.Add(new OpenApiParameter
        {
            Name = MultiTenancyConsts.TenantKey,
            In = ParameterLocation.Header
        });
    }
}