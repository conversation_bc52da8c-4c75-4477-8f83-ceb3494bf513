using Microsoft.EntityFrameworkCore;
using SpareParts.Administration;
using SpareParts.Administration.Enums;
using SpareParts.Administration.Invitations;
using SpareParts.Common;
using SpareParts.Common.Companies;
using SpareParts.Common.DataFilter;
using SpareParts.Common.Helpers;
using SpareParts.Core.DataVisibility;
using SpareParts.Core.Entities.Components;
using SpareParts.Core.Entities.Equipments;
using SpareParts.Core.Entities.Resources;
using System;
using System.Collections.Generic;
using System.Diagnostics.Metrics;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.ChangeTracking;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using Volo.Abp.MultiTenancy;
using Volo.Abp.TenantManagement;
using Volo.Abp.Threading;
using Volo.Abp.Timing;
using SpareParts.Core.Entities.Boms;

namespace SpareParts.Metrics;

[DisableEntityChangeTracking]
public class SparePartsMetrics
{
    public const string Name = "SpareParts.Instrumentation.Stats";
    private const string Tenant = "tenant";
    private const string Host = "host";
    public IAbpLazyServiceProvider LazyServiceProvider { get; set; } = null!;
    private ITenantRepository TenantRepository => LazyServiceProvider.LazyGetRequiredService<ITenantRepository>();
    private IDataFilter DataFilter => LazyServiceProvider.LazyGetRequiredService<IDataFilter>();
    private ICurrentTenant CurrentTenant => LazyServiceProvider.LazyGetRequiredService<ICurrentTenant>();
    private IRepository<Company> CompanyRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Company>>();
    private IRepository<IdentityUser> UserRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<IdentityUser>>();
    private IRepository<Product> ProductRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Product>>();
    private IRepository<Document> DocumentRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Document>>();
    private IRepository<IdentityRole> RoleRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<IdentityRole>>();
    private IRepository<Invitation> InvitationRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Invitation>>();
    private IRepository<Equipment> EquipmentRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<Equipment>>();
    private IRepository<BomDocument> BomDocumentRepository => LazyServiceProvider.LazyGetRequiredService<IRepository<BomDocument>>();
    private List<Tenant>? _tenants;
    protected IExpressionCreator ExpressionCreator => LazyServiceProvider.LazyGetRequiredService<IExpressionCreator>();
    protected IClock Clock => LazyServiceProvider.LazyGetRequiredService<IClock>();

    public SparePartsMetrics()
    {
        Meter meterInstance = new(Name);

        meterInstance.CreateObservableUpDownCounter(
            "spareparts.tenants.count",
            () => AsyncHelper.RunSync(async () => await GetTenantsCountAsync()),
            description: "Number of tenants.");

        meterInstance.CreateObservableUpDownCounter(
            "spareparts.companies.count",
         () => AsyncHelper.RunSync(async () => await GetCompaniesCountAsync()),
            description: "Number of companies.");

        meterInstance.CreateObservableUpDownCounter(
            "spareparts.users.count",
                  () => AsyncHelper.RunSync(async () => await GetUsersCountAsync()),
            description: "Number of users.");

        meterInstance.CreateObservableUpDownCounter(
            "spareparts.users.internal.count",
            () => AsyncHelper.RunSync(async () => await GetUsersCountAsync(true)),
            description: "Number of internal users.");

        meterInstance.CreateObservableUpDownCounter(
            "spareparts.users.external.count",
            () => AsyncHelper.RunSync(async () => await GetUsersCountAsync(false)),
            description: "Number of external users.");

        foreach (string role in IdentityRoles.AllRoles)
        {
            string roleName = role.Replace("-", ".");
            meterInstance.CreateObservableUpDownCounter(
                $"spareparts.users.internal.{roleName}.role.count",
                () => AsyncHelper.RunSync(async () => await GetInternalUserInRoleCountAsync(role)),
                description: $"Number of internal {role} users.");
        }

        foreach (InvitationStatus status in Enum.GetValues(typeof(InvitationStatus)))
        {
            string statusName = status.ToString().ToLower();
            meterInstance.CreateObservableUpDownCounter(
                $"spareparts.invitations.internal.{statusName}.count",
                () => AsyncHelper.RunSync(async () => await GetInvitationsCountAsync(true, status)),
                description: $"Number of {statusName} internal invitations.");

            meterInstance.CreateObservableUpDownCounter(
                $"spareparts.invitations.external.{status}.count",
                () => AsyncHelper.RunSync(async () => await GetInvitationsCountAsync(false, status)),
                description: $"Number of {statusName} external invitations.");
        }

        meterInstance.CreateObservableUpDownCounter(
            "spareparts.products.count",
            () => AsyncHelper.RunSync(async () => await GetProductsCountAsync()),
            description: "Number of products.");

        meterInstance.CreateObservableUpDownCounter(
            "spareparts.products.published.count",
            () => AsyncHelper.RunSync(async () => await GetProductsCountAsync(x => x.IsVisible)),
            description: "Number of published products.");

        meterInstance.CreateObservableUpDownCounter(
            "spareparts.products.unpublished.count",
            () => AsyncHelper.RunSync(async () => await GetProductsCountAsync(x => !x.IsVisible)),
            description: "Number of unpublished products.");

        meterInstance.CreateObservableUpDownCounter(
            "spareparts.products.public.count",
            () => AsyncHelper.RunSync(async () => await GetProductsCountAsync(x => x.IsPublic)),
            description: "Number of public products.");

        meterInstance.CreateObservableUpDownCounter(
            "spareparts.products.notPublic.count",
            () => AsyncHelper.RunSync(async () => await GetProductsCountAsync(x => !x.IsPublic)),
            description: "Number of not public products.");

        meterInstance.CreateObservableUpDownCounter(
            "spareparts.documents.count",
            () => AsyncHelper.RunSync(async () => await GetDocumentsCountAsync()),
            description: "Number of documents.");

        meterInstance.CreateObservableUpDownCounter(
            "spareparts.equipments.count",
            () => AsyncHelper.RunSync(async () => await GetEquipmentsCountAsync()),
            description: "Number of equipments.");

        meterInstance.CreateObservableUpDownCounter(
            "spareparts.bomDocuments.count",
            () => AsyncHelper.RunSync(async () => await GetBomDocumentsCountAsync()),
            description: "Number of BOM documents.");

        meterInstance.CreateObservableUpDownCounter(
            "spareparts.bomDocuments.minSize.bytes",
            () => AsyncHelper.RunSync(async () => await GetBomDocumentsMinSizeAsync()),
            description: "Min size of BOM documents.");

        meterInstance.CreateObservableUpDownCounter(
            "spareparts.bomDocuments.maxSize.bytes",
            () => AsyncHelper.RunSync(async () => await GetBomDocumentsMaxSizeAsync()),
            description: "Max size of BOM documents.");

        meterInstance.CreateObservableUpDownCounter(
            "spareparts.bomDocuments.minProcessingTime.ms",
            () => AsyncHelper.RunSync(async () => await GetBomDocumentsMinProcessingTimeAsync()),
            description: "Min processing time of BOM documents.");

        meterInstance.CreateObservableUpDownCounter(
            "spareparts.bomDocuments.maxProcessingTime.ms",
            () => AsyncHelper.RunSync(async () => await GetBomDocumentsMaxProcessingTimeAsync()),
            description: "Max processing time of BOM documents.");
    }

    private async Task<List<Tenant>> GetTenantsAsync()
    {
        return _tenants ??= await TenantRepository.GetListAsync();
    }

    private async Task<IEnumerable<Measurement<long>>> GetCompaniesCountAsync()
    {
        List<Measurement<long>> measurements = [];

        using (DataFilter.Disable<IMultiTenant>())
        {
            long companies = await CompanyRepository.GetCountAsync();
            measurements.Add(new Measurement<long>(companies, new KeyValuePair<string, object?>(Tenant, Host)));
        }

        foreach (Tenant tenant in await GetTenantsAsync())
        {
            using (CurrentTenant.Change(tenant.Id))
            {
                long companies = await CompanyRepository.GetCountAsync();
                measurements.Add(new Measurement<long>(companies, new KeyValuePair<string, object?>(Tenant, tenant.Name)));
            }
        }
        return measurements;
    }

    private async Task<IEnumerable<Measurement<long>>> GetUsersCountAsync()
    {
        List<Measurement<long>> measurements = [];

        using (DataFilter.Disable<IMultiTenant>())
        {
            long users = await UserRepository.GetCountAsync();
            measurements.Add(new Measurement<long>(users, new KeyValuePair<string, object?>(Tenant, Host)));
        }

        foreach (Tenant tenant in await GetTenantsAsync())
        {
            using (CurrentTenant.Change(tenant.Id))
            {
                long users = await UserRepository.GetCountAsync();
                measurements.Add(new Measurement<long>(users, new KeyValuePair<string, object?>(Tenant, tenant.Name)));
            }
        }
        return measurements;
    }

    private async Task<IEnumerable<Measurement<long>>> GetUsersCountAsync(bool isInternal)
    {
        List<Measurement<long>> measurements = [];

        using (DataFilter.Disable<IMultiTenant>())
        {
            IQueryable<Guid> query = await GetUserQueryableAsync(isInternal);
            long users = await query.CountAsync();
            measurements.Add(new Measurement<long>(users, new KeyValuePair<string, object?>(Tenant, Host)));
        }

        foreach (Tenant tenant in await GetTenantsAsync())
        {
            using (CurrentTenant.Change(tenant.Id))
            {
                IQueryable<Guid> query = await GetUserQueryableAsync(isInternal);
                long users = await query.CountAsync();
                measurements.Add(new Measurement<long>(users, new KeyValuePair<string, object?>(Tenant, tenant.Name)));
            }
        }
        return measurements;
    }

    private async Task<IQueryable<Guid>> GetUserQueryableAsync(bool isInternal)
    {
        IQueryable<IdentityUser> userQueryable = await UserRepository.GetQueryableAsync();

        IQueryable<Company> companyQueryable = await CompanyRepository.GetQueryableAsync();
        companyQueryable = companyQueryable.Where(x => isInternal ? x.Type == CompanyType.Internal : x.Type == CompanyType.External);


        IQueryable<Guid> query = userQueryable
            .Join(
                companyQueryable,
                ExpressionCreator.GetPropertyExpression<IdentityUser, Guid>(AdministrationModuleExtensionConfigurator.CompanyIdPropertyName),
                company => company.Id,
                (user, company) => new { user, company })
            .Select(x => x.user.Id);
        return query;
    }

    private async Task<IEnumerable<Measurement<long>>> GetInternalUserInRoleCountAsync(string role)
    {
        List<Measurement<long>> measurements = [];

        using (DataFilter.Disable<IMultiTenant>())
        {
            IQueryable<Guid> query = await GetInternalUserInRoleQueryableAsync(role);
            long users = await query.CountAsync();
            measurements.Add(new Measurement<long>(users, new KeyValuePair<string, object?>(Tenant, Host)));
        }

        foreach (Tenant tenant in await GetTenantsAsync())
        {
            using (CurrentTenant.Change(tenant.Id))
            {
                IQueryable<Guid> query = await GetInternalUserInRoleQueryableAsync(role);
                long users = await query.CountAsync();
                measurements.Add(new Measurement<long>(users, new KeyValuePair<string, object?>(Tenant, tenant.Name)));
            }
        }
        return measurements;
    }

    private async Task<IQueryable<Guid>> GetInternalUserInRoleQueryableAsync(string role)
    {
        IQueryable<IdentityRole> roleQueryable = await RoleRepository.GetQueryableAsync();
        roleQueryable = roleQueryable.Where(x => x.Name == role);

        IQueryable<Company> companyQueryable = await CompanyRepository.GetQueryableAsync();
        companyQueryable = companyQueryable.Where(x => x.Type == CompanyType.Internal);

        IQueryable<IdentityUser> userQueryable = await UserRepository.WithDetailsAsync(x => x.Roles);

        userQueryable = userQueryable.Where(user => user.Roles.Any(userRole =>
                roleQueryable.Any(x => x.Id == userRole.RoleId)));

        IQueryable<Guid> query = userQueryable
            .Join(
                companyQueryable,
                ExpressionCreator.GetPropertyExpression<IdentityUser, Guid>(AdministrationModuleExtensionConfigurator.CompanyIdPropertyName),
                company => company.Id,
                (user, company) => new { user, company })
            .Select(x => x.user.Id);

        return query;
    }

    private async Task<IEnumerable<Measurement<long>>> GetInvitationsCountAsync(bool isInternal, InvitationStatus invitationStatus)
    {
        List<Measurement<long>> measurements = [];

        using (DataFilter.Disable<IMultiTenant>())
        {
            IQueryable<Invitation> query = await GetInvitationQueryableAsync(isInternal, invitationStatus);
            long invitations = await query.CountAsync();
            measurements.Add(new Measurement<long>(invitations, new KeyValuePair<string, object?>(Tenant, Host)));
        }

        foreach (Tenant tenant in await GetTenantsAsync())
        {
            using (CurrentTenant.Change(tenant.Id))
            {
                IQueryable<Invitation> query = await GetInvitationQueryableAsync(isInternal, invitationStatus);
                long invitations = await query.CountAsync();
                measurements.Add(new Measurement<long>(invitations, new KeyValuePair<string, object?>(Tenant, tenant.Name)));
            }
        }
        return measurements;
    }

    private async Task<IQueryable<Invitation>> GetInvitationQueryableAsync(bool isInternal, InvitationStatus invitationStatus)
    {
        IQueryable<Invitation> invitationQueryable = await InvitationRepository.GetQueryableAsync();
        invitationQueryable = invitationQueryable.Where(x => x.IsInternal == isInternal);

        switch (invitationStatus)
        {
            case InvitationStatus.Accepted:
                invitationQueryable = invitationQueryable.Where(x => x.CompletionDate != null);
                break;
            case InvitationStatus.Canceled:
                invitationQueryable = invitationQueryable.Where(x => x.CancellationDate != null);
                break;
            case InvitationStatus.Expired:
                invitationQueryable = invitationQueryable.Where(x => x.CompletionDate == null && x.CancellationDate == null && x.ExpirationDate < Clock.Now);
                break;
            case InvitationStatus.Pending:
                invitationQueryable = invitationQueryable.Where(x => x.CompletionDate == null && x.CancellationDate == null && x.ExpirationDate >= Clock.Now);
                break;
        }
        return invitationQueryable;
    }

    private async Task<IEnumerable<Measurement<long>>> GetProductsCountAsync(Expression<Func<Product, bool>>? predicate = null)
    {
        List<Measurement<long>> measurements = [];

        using (DataFilter.Disable<IHasPublic>())
        {
            using (DataFilter.Disable<IHasVisibility>())
            {
                using (DataFilter.Disable<IMultiTenant>())
                {
                    IQueryable<Product> query = await GetProductQueryable(predicate);
                    long products = await query.CountAsync();
                    measurements.Add(new Measurement<long>(products, new KeyValuePair<string, object?>(Tenant, Host)));
                }

                foreach (Tenant tenant in await GetTenantsAsync())
                {
                    using (CurrentTenant.Change(tenant.Id))
                    {
                        IQueryable<Product> query = await GetProductQueryable(predicate);
                        long products = await query.CountAsync();
                        measurements.Add(new Measurement<long>(products, new KeyValuePair<string, object?>(Tenant, tenant.Name)));
                    }
                }
            }
        }
        return measurements;
    }

    private async Task<IQueryable<Product>> GetProductQueryable(Expression<Func<Product, bool>>? predicate)
    {
        IQueryable<Product> queryable = await ProductRepository.GetQueryableAsync();
        queryable = queryable.WhereIf(predicate != null, predicate!);
        return queryable;
    }

    private async Task<Measurement<long>> GetTenantsCountAsync()
    {
        return new Measurement<long>((await GetTenantsAsync()).Count);
    }

    private async Task<IEnumerable<Measurement<long>>> GetDocumentsCountAsync()
    {
        List<Measurement<long>> measurements = [];

        using (DataFilter.Disable<IMultiTenant>())
        {
            long count = await DocumentRepository.GetCountAsync();
            measurements.Add(new Measurement<long>(count, new KeyValuePair<string, object?>(Tenant, Host)));
        }

        foreach (Tenant tenant in await GetTenantsAsync())
        {
            using (CurrentTenant.Change(tenant.Id))
            {
                long count = await DocumentRepository.GetCountAsync();
                measurements.Add(new Measurement<long>(count, new KeyValuePair<string, object?>(Tenant, tenant.Name)));
            }
        }
        return measurements;
    }

    private async Task<IEnumerable<Measurement<long>>> GetEquipmentsCountAsync()
    {
        List<Measurement<long>> measurements = [];

        using (DataFilter.Disable<IMultiTenant>())
        {
            long count = await EquipmentRepository.GetCountAsync();
            measurements.Add(new Measurement<long>(count, new KeyValuePair<string, object?>(Tenant, Host)));
        }

        foreach (Tenant tenant in await GetTenantsAsync())
        {
            using (CurrentTenant.Change(tenant.Id))
            {
                long count = await EquipmentRepository.GetCountAsync();
                measurements.Add(new Measurement<long>(count, new KeyValuePair<string, object?>(Tenant, tenant.Name)));
            }
        }
        return measurements;
    }

    private async Task<IEnumerable<Measurement<long>>> GetBomDocumentsCountAsync()
    {
        List<Measurement<long>> measurements = [];

        using (DataFilter.Disable<IMultiTenant>())
        {
            long count = await BomDocumentRepository.GetCountAsync();
            measurements.Add(new Measurement<long>(count, new KeyValuePair<string, object?>(Tenant, Host)));
        }

        foreach (Tenant tenant in await GetTenantsAsync())
        {
            using (CurrentTenant.Change(tenant.Id))
            {
                long count = await BomDocumentRepository.GetCountAsync();
                measurements.Add(new Measurement<long>(count, new KeyValuePair<string, object?>(Tenant, tenant.Name)));
            }
        }
        return measurements;
    }

    private async Task<IEnumerable<Measurement<long>>> GetBomDocumentsMinSizeAsync()
    {
        List<Measurement<long>> measurements = [];

        using (DataFilter.Disable<IMultiTenant>())
        {
            long? size = await (await BomDocumentRepository.GetQueryableAsync()).Where(bd => bd.Size != null).MinAsync(bd => bd.Size);
            if (size.HasValue)
            {
                measurements.Add(new Measurement<long>(size.Value, new KeyValuePair<string, object?>(Tenant, Host)));
            }
        }

        foreach (Tenant tenant in await GetTenantsAsync())
        {
            using (CurrentTenant.Change(tenant.Id))
            {
                long? size = await (await BomDocumentRepository.GetQueryableAsync()).Where(bd => bd.Size != null).MinAsync(bd => bd.Size);
                if (size.HasValue)
                {
                    measurements.Add(new Measurement<long>(size.Value, new KeyValuePair<string, object?>(Tenant, tenant.Name)));
                }
            }
        }
        return measurements;
    }

    private async Task<IEnumerable<Measurement<long>>> GetBomDocumentsMaxSizeAsync()
    {
        List<Measurement<long>> measurements = [];

        using (DataFilter.Disable<IMultiTenant>())
        {
            long? size = await (await BomDocumentRepository.GetQueryableAsync()).Where(bd => bd.Size != null).MaxAsync(bd => bd.Size);
            if (size is not null)
            {
                measurements.Add(new Measurement<long>(size.Value, new KeyValuePair<string, object?>(Tenant, Host)));
            }
        }

        foreach (Tenant tenant in await GetTenantsAsync())
        {
            using (CurrentTenant.Change(tenant.Id))
            {
                long? size = await (await BomDocumentRepository.GetQueryableAsync()).Where(bd => bd.Size != null).MaxAsync(bd => bd.Size);
                if (size != null)
                {
                    measurements.Add(new Measurement<long>(size.Value,
                        new KeyValuePair<string, object?>(Tenant, tenant.Name)));
                }
            }
        }
        return measurements;
    }

    private async Task<IEnumerable<Measurement<double>>> GetBomDocumentsMinProcessingTimeAsync()
    {
        List<Measurement<double>> measurements = [];

        using (DataFilter.Disable<IMultiTenant>())
        {
            double? processingTime = await (await BomDocumentRepository.GetQueryableAsync()).Where(bd => bd.Size != null).MinAsync(bd => bd.ProcessingTime);
            if (processingTime != null)
            {
                measurements.Add(new Measurement<double>(processingTime.Value,
                    new KeyValuePair<string, object?>(Tenant, Host)));
            }
        }

        foreach (Tenant tenant in await GetTenantsAsync())
        {
            using (CurrentTenant.Change(tenant.Id))
            {
                double? processingTime = await (await BomDocumentRepository.GetQueryableAsync()).Where(bd => bd.Size != null).MinAsync(bd => bd.ProcessingTime);
                if (processingTime != null)
                {
                    measurements.Add(new Measurement<double>(processingTime.Value,
                        new KeyValuePair<string, object?>(Tenant, tenant.Name)));
                }
            }
        }
        return measurements;
    }

    private async Task<IEnumerable<Measurement<double>>> GetBomDocumentsMaxProcessingTimeAsync()
    {
        List<Measurement<double>> measurements = [];

        using (DataFilter.Disable<IMultiTenant>())
        {
            double? processingTime = await (await BomDocumentRepository.GetQueryableAsync()).Where(bd => bd.Size != null).MaxAsync(bd => bd.ProcessingTime);
            if (processingTime != null)
            {
                measurements.Add(new Measurement<double>(processingTime.Value,
                    new KeyValuePair<string, object?>(Tenant, Host)));
            }
        }

        foreach (Tenant tenant in await GetTenantsAsync())
        {
            using (CurrentTenant.Change(tenant.Id))
            {
                double? processingTime = await (await BomDocumentRepository.GetQueryableAsync()).Where(bd => bd.Size != null).MaxAsync(bd => bd.ProcessingTime);
                if (processingTime != null)
                {
                    measurements.Add(new Measurement<double>(processingTime.Value,
                        new KeyValuePair<string, object?>(Tenant, tenant.Name)));
                }
            }
        }
        return measurements;
    }
}