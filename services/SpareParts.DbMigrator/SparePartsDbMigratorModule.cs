using SpareParts.Common;
using SpareParts.EntityFrameworkCore;
using Volo.Abp.Autofac;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Caching;
using Volo.Abp.Modularity;

namespace SpareParts;

[DependsOn(
    typeof(AbpAutofacModule),
    typeof(SparePartsEntityFrameworkCoreModule),
    typeof(SparePartsApplicationContractsModule),
    typeof(SparePartsHostSharedModule),
    typeof(CommonApplicationModule)
    )]
public class SparePartsDbMigratorModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        Configure<AbpDistributedCacheOptions>(options => { options.KeyPrefix = "SpareParts:"; });

        Configure<AbpBackgroundJobOptions>(options =>
        {
            options.IsJobExecutionEnabled = false;
        });
    }
}