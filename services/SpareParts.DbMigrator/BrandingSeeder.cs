using Microsoft.Extensions.Logging;
using SpareParts.Common.Branding;
using SpareParts.Common.Settings;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.MultiTenancy;
using Volo.Abp.SettingManagement;
using Volo.Abp.TenantManagement;
using Volo.Abp.Uow;

namespace SpareParts;

[ExcludeFromCodeCoverage]
public class BrandingSeeder(
    IRepository<TenantBranding, Guid> brandingRepository,
    TenantBrandingDomainService brandingDomainService,
    IBrandingProvider brandingProvider,
    ISettingManager settingManager,
    ICurrentTenant currentTenant,
    ILoggerFactory loggerFactory) : IBrandingSeeder, ITransientDependency
{
    private readonly ILogger _logger = loggerFactory.CreateLogger(typeof(BrandingSeeder).Assembly.GetName().Name!);
    private readonly JsonSerializerOptions _options = new() { PropertyNameCaseInsensitive = true };

    [UnitOfWork]
    public virtual async Task SeedAsync(Tenant tenant)
    {
        using (currentTenant.Change(tenant.Id, tenant.Name))
        {
            // Seed only if there is no branding for the current tenant or if there is an old branding
            TenantBranding? existingBranding = await brandingRepository.SingleOrDefaultAsync();
            bool isOldBranding = existingBranding != null && existingBranding.Logo.Default == Guid.Empty;
            if (existingBranding != null && !isOldBranding)
            {
                return;
            }

            try
            {
                if (existingBranding == null)
                {
                    TenantBranding branding = await brandingProvider.CreateBrandingFromIdentityAsync(tenant);

                    branding.LoginPageHideTenantName = await GetBrandingHideTenantName(CommonSettings.LoginPageHideTenantName);
                    branding.WelcomePageHideTenantName = await GetBrandingHideTenantName(CommonSettings.WelcomePageHideTenantName);
                    branding.LoginPageTranslations = await GetBrandingTranslations(CommonSettings.LoginPageTranslations);
                    branding.WelcomePageTranslations = await GetBrandingTranslations(CommonSettings.WelcomePageTranslations);
                    await brandingDomainService.ChangeLoginPageImageAsync(branding, await GetBrandingImageId(CommonSettings.LoginPageImageId));
                    await brandingDomainService.ChangeWelcomePageImageAsync(branding, await GetBrandingImageId(CommonSettings.WelcomePageImageId));

                    await brandingRepository.InsertAsync(branding);
                }
                else
                {
                    existingBranding = await brandingProvider.UpdateBrandingFromIdentityAsync(tenant, existingBranding);
                    await brandingRepository.UpdateAsync(existingBranding);
                }
            }
            catch (Exception ex)
            {
                string message = $"The branding for the tenant {tenant.Name} was not {(isOldBranding ? "updated" : "created")}.";
                _logger.LogError(ex, message);
            }
        }
    }

    private async Task<Guid?> GetBrandingImageId(string settingName)
    {
        string imageIdSetting = await settingManager.GetOrNullForCurrentTenantAsync(settingName);
        return Guid.TryParse(imageIdSetting, out Guid imageId) ? imageId : null;
    }

    private async Task<bool> GetBrandingHideTenantName(string settingName)
    {
        string hide = await settingManager.GetOrNullForCurrentTenantAsync(settingName);
        return bool.TryParse(hide, out bool value) && value;
    }

    private async Task<List<TenantBrandingTranslation>> GetBrandingTranslations(string settingName)
    {
        string translations = await settingManager.GetOrNullForCurrentTenantAsync(settingName);
        return string.IsNullOrWhiteSpace(translations) ? [] : JsonSerializer.Deserialize<List<TenantBrandingTranslation>>(translations, _options) ?? [];
    }
}
