<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <RootNamespace>SpareParts</RootNamespace>
    <Nullable>enable</Nullable>
    <UserSecretsId>2404fb49-c575-4e83-9be8-d6004254a786</UserSecretsId>
    <Version>1.14.0</Version>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="appsettings.json" />
    <Content Include="appsettings.json">
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>    
    <PackageReference Include="Serilog.Extensions.Logging" />
    <PackageReference Include="Microsoft.Extensions.Hosting" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design">
	    <PrivateAssets>all</PrivateAssets>
	    <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Autofac" />
    <ProjectReference Include="..\..\modules\SpareParts.Common\src\SpareParts.Common.Application\SpareParts.Common.Application.csproj" />
    <ProjectReference Include="..\..\shared\SpareParts.Host.Shared\SpareParts.Host.Shared.csproj" />
    <ProjectReference Include="..\..\src\SpareParts.Application.Contracts\SpareParts.Application.Contracts.csproj" />
    <ProjectReference Include="..\..\src\SpareParts.EntityFrameworkCore\SpareParts.EntityFrameworkCore.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Logs\**" />
    <Content Remove="Logs\**" />
    <EmbeddedResource Remove="Logs\**" />
    <None Remove="Logs\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Update="Fody">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
    </PackageReference>
  </ItemGroup>

</Project>
