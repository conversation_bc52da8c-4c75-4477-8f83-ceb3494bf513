using System.Diagnostics.CodeAnalysis;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Volo.Abp;

namespace SpareParts;

[ExcludeFromCodeCoverage]
public class MasterDataManagementServiceHostedService(
    MainService mainService,
    IAbpApplicationWithExternalServiceProvider abpApplication)
    : IHostedService
{
    public async Task StartAsync(CancellationToken cancellationToken)
    {
        await mainService.StartAsync();
    }

    public async Task StopAsync(CancellationToken cancellationToken)
    {
        await abpApplication.ShutdownAsync();
    }
}
