using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Text;
using System.Threading.Tasks;
using Confluent.Kafka;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using SpareParts.EventBus.Kafka;
using SpareParts.Infra.Extensions;
using SpareParts.MasterDataManagement.Etos;
using SpareParts.MasterDataManagement.Etos.Component;
using Volo.Abp.DependencyInjection;
using Volo.Abp.EventBus;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.EventBus.Local;
using Volo.Abp.Guids;
using Volo.Abp.Kafka;
using Volo.Abp.MultiTenancy;
using Volo.Abp.Timing;
using Volo.Abp.Tracing;
using Volo.Abp.Uow;

namespace SpareParts.Infra.Kafka;

[ExposeServices(typeof(IDistributedEventBus), typeof(AgoraKafkaDistributedEventBus), typeof(DataHubKafkaDistributedEventBus))]
public class DataHubKafkaDistributedEventBus(
    IServiceScopeFactory serviceScopeFactory,
    ICurrentTenant currentTenant,
    IUnitOfWorkManager unitOfWorkManager,
    IOptions<AgoraKafkaEventBusOptions> agoraKafkaEventBusOptions,
    IKafkaMessageConsumerFactory messageConsumerFactory,
    IOptions<AbpDistributedEventBusOptions> abpDistributedEventBusOptions,
    IKafkaSerializer serializer,
    IProducerPool producerPool,
    IGuidGenerator guidGenerator,
    IClock clock,
    IEventHandlerInvoker eventHandlerInvoker,
    ILocalEventBus localEventBus,
    ICorrelationIdProvider correlationIdProvider)
    : AgoraKafkaDistributedEventBus(serviceScopeFactory, currentTenant, unitOfWorkManager, agoraKafkaEventBusOptions,
        messageConsumerFactory, abpDistributedEventBusOptions, serializer, producerPool, guidGenerator, clock,
        eventHandlerInvoker, localEventBus, correlationIdProvider)
{
    public ILogger<DataHubKafkaDistributedEventBus> Logger { get; set; } = default!;

    protected override async Task ProcessEventAsync(Message<string, byte[]> message)
    {
        string eventName = message.GetEventName();
        Type? eventType = EventTypes.GetOrDefault(eventName);
        if (eventType == null)
        {
            Logger.LogWarning("Skipped message, reason: Unknown message type {EventName}", eventName);
            return;
        }
        string? messageId = message.GetEventId();
        object eventData;
        try
        {
            eventData = Serializer.Deserialize(message.Value, eventType);
            switch (eventData)
            {
                case ComponentReceivedSyncDoneEto sync:
                    sync.TenantName ??= message.GetTenantName();
                    sync.ExternalUserId ??= message.GetUserId();
                    break;
                case ImportDoneEto import:
                    import.TenantName ??= message.GetTenantName();
                    break;
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Contract violation during message deserialization");
            return;
        }
        if (await AddToInboxAsync(messageId, eventName, eventType, eventData, messageId))
        {
            return;
        }
        await TriggerHandlersAsync(eventType, eventData);
    }

    protected override Headers GetDefaultHeaders(Guid messageId, string eventType)
    {
        ReadOnlyDictionary<string, DataHubEventsConstants.PublishedEventHeaders> eventInfos = DataHubEventsConstants.DcpEvents.GetEventInfos();
        return new Headers
        {
            {
                MessageId, Encoding.UTF8.GetBytes(messageId.ToString("N"))
            },
            {
                EventType, Encoding.UTF8.GetBytes(eventInfos[eventType].EventType)
            },
            {
                DataHubEventsConstants.DataChangeProposalEventConstants.EntityType, Encoding.UTF8.GetBytes(eventInfos[eventType].EntityType)
            }
        };
    }
}