using Microsoft.Extensions.DependencyInjection;
using SpareParts.EventBus.Kafka;
using SpareParts.Infra.Kafka;
using SpareParts.MasterDataManagement;
using SpareParts.MasterDataManagement.EntityFrameworkCore;
using System.Diagnostics.CodeAnalysis;
using SpareParts.Core.Features;
using Volo.Abp;
using Volo.Abp.Autofac;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.GlobalFeatures;
using Volo.Abp.Modularity;
using Volo.Abp.Threading;

namespace SpareParts;

[DependsOn(
    typeof(AbpAutofacModule),
    typeof(MasterDataManagementApplicationModule),
    typeof(MasterDataManagementEntityFrameworkCoreModule),
    typeof(AgoraEventBusKafkaModule),
    typeof(SparePartsHostSharedModule)
)]
[ExcludeFromCodeCoverage]
public class MasterDataManagementServiceModule : AbpModule
{
    private static readonly OneTimeRunner OneTimeRunner = new();
    
    public override void OnApplicationInitialization(ApplicationInitializationContext context)
    {
        context
            .ServiceProvider
            .GetRequiredService<DataHubKafkaDistributedEventBus>()
            .Initialize();
    }

    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        Configure<AbpBackgroundJobWorkerOptions>(options =>
        {
            options.ApplicationName = "spareparts_mdm";
        });
    }
    
    public override void PreConfigureServices(ServiceConfigurationContext context)
    {
        base.PreConfigureServices(context);

        OneTimeRunner.Run(() =>
        {
            if (bool.TryParse(context.Services.GetConfiguration()["GlobalFeatures:Core.DataImportMonitoring"], out bool dataImportMonitoringEnabled) && dataImportMonitoringEnabled)
            {
                GlobalFeatureManager.Instance.Modules.CoreFeatures().DataImportMonitoring.Enable();
            }
        });
    }
}