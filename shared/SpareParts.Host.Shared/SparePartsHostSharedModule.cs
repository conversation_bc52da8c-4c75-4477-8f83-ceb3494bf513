using System;
using System.Diagnostics.CodeAnalysis;
using Medallion.Threading;
using Medallion.Threading.SqlServer;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using SpareParts.MultiTenancy;
using Volo.Abp.Autofac;
using Volo.Abp.BlobStoring;
using Volo.Abp.BlobStoring.Azure;
using Volo.Abp.DistributedLocking;
using Volo.Abp.Modularity;
using Volo.Abp.MultiTenancy;

namespace SpareParts;

[ExcludeFromCodeCoverage]
[DependsOn(
    typeof(AbpAutofacModule),
    typeof(AbpDistributedLockingModule),
    typeof(AbpBlobStoringAzureModule)
)]
public class SparePartsHostSharedModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        IConfiguration configuration = context.Services.GetConfiguration();

        IConfigurationSection section = configuration.GetSection("Azure:BlobStorage");
        Configure<AbpBlobStoringOptions>(options =>
        {
            options.Containers.ConfigureDefault(container =>
            {
                container.UseAzure(azure =>
                {
                    azure.ConnectionString = section["ConnectionString"]!;
                    azure.CreateContainerIfNotExists = Convert.ToBoolean(section["CreateContainerIfNotExists"]);
                });
            });
        });

        context.Services.AddSingleton<IDistributedLockProvider>(_ =>
            new SqlDistributedSynchronizationProvider(configuration.GetConnectionString("Default")!));

        Configure<AbpMultiTenancyOptions>(options =>
        {
            options.IsEnabled = MultiTenancyConsts.IsEnabled;
        });
    }
}