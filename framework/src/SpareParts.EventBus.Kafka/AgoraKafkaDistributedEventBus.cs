using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Threading.Tasks;
using Confluent.Kafka;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Volo.Abp.DependencyInjection;
using Volo.Abp.EventBus;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.EventBus.Kafka;
using Volo.Abp.EventBus.Local;
using Volo.Abp.Guids;
using Volo.Abp.Kafka;
using Volo.Abp.MultiTenancy;
using Volo.Abp.Timing;
using Volo.Abp.Tracing;
using Volo.Abp.Uow;

namespace SpareParts.EventBus.Kafka;

[ExposeServices(typeof(IDistributedEventBus), typeof(KafkaDistributedEventBus), typeof(AgoraKafkaDistributedEventBus))]
[ExcludeFromCodeCoverage]
public class AgoraKafkaDistributedEventBus(
    IServiceScopeFactory serviceScopeFactory,
    ICurrentTenant currentTenant,
    IUnitOfWorkManager unitOfWorkManager,
    IOptions<AgoraKafkaEventBusOptions> agoraKafkaEventBusOptions,
    IKafkaMessageConsumerFactory messageConsumerFactory,
    IOptions<AbpDistributedEventBusOptions> abpDistributedEventBusOptions,
    IKafkaSerializer serializer,
    IProducerPool producerPool,
    IGuidGenerator guidGenerator,
    IClock clock,
    IEventHandlerInvoker eventHandlerInvoker,
    ILocalEventBus localEventBus,
    ICorrelationIdProvider correlationIdProvider)
    : KafkaDistributedEventBus(serviceScopeFactory,
        currentTenant,
        unitOfWorkManager,
        agoraKafkaEventBusOptions,
        messageConsumerFactory,
        abpDistributedEventBusOptions,
        serializer,
        producerPool,
        guidGenerator,
        clock,
        eventHandlerInvoker,
        localEventBus,
        correlationIdProvider)
{
    protected const string MessageId = "messageId";
    protected const string EventType = "eventType";
    
    protected AgoraKafkaEventBusOptions AgoraKafkaEventBusOptions { get; } = agoraKafkaEventBusOptions.Value;
    protected string? ProducerTopicName => AgoraKafkaEventBusOptions.ProducerTopicName;

    public new void Initialize()
    {
        IKafkaMessageConsumer consumer = MessageConsumerFactory.Create(
            AgoraKafkaEventBusOptions.TopicName,
            AgoraKafkaEventBusOptions.GroupId,
            AgoraKafkaEventBusOptions.ConnectionName);
        consumer.OnMessageReceived(ProcessEventAsync);

        SubscribeHandlers(AbpDistributedEventBusOptions.Handlers);
    }

    protected virtual async Task ProcessEventAsync(Message<string, byte[]> message)
    {
        string? eventName = Serializer.Deserialize<AgoraEvent>(message.Value).Type;
        if (eventName == null)
        {
            //TODO : log unknown type
            return;
        }
        Type? eventType = EventTypes.GetOrDefault(eventName);
        if (eventType == null)
        {
            return;
        }

        string? messageId = message.GetMessageId();
        object eventData = Serializer.Deserialize(message.Value, eventType);
        //TODO : pass correlation id, for now we pass messageId instead (to be defined with identity team)
        if (await AddToInboxAsync(messageId, eventName, eventType, eventData, messageId))
        {
            return;
        }
        await TriggerHandlersAsync(eventType, eventData);
    }

    protected override async Task PublishToEventBusAsync(Type eventType, object eventData)
    {
        if (string.IsNullOrWhiteSpace(ProducerTopicName))
        {
            return;
        }

        Guid messageId = Guid.NewGuid();
        string eventName = EventNameAttribute.GetNameOrDefault(eventType);
        await PublishAsync(
            ProducerTopicName,
            eventType,
            eventData,
            GetDefaultHeaders(messageId,eventName)
        );
    }

    protected virtual Task PublishAsync(string topicName, Type eventType, object eventData, Headers headers)
    {
        string eventName = EventNameAttribute.GetNameOrDefault(eventType);
        byte[] body = Serializer.Serialize(eventData);

        return PublishAsync(topicName, eventName, body, headers);
    }

    protected virtual Task<DeliveryResult<string, byte[]>> PublishAsync(
        string topicName,
        string eventName,
        byte[] body,
        Headers headers)
    {
        IProducer<string, byte[]> producer = ProducerPool.Get(AgoraKafkaEventBusOptions.ConnectionName);

        return producer.ProduceAsync(
            topicName,
            new Message<string, byte[]>
            {
                Key = eventName,
                Value = body,
                Headers = headers
            });
    }

    public override Task PublishFromOutboxAsync(
        OutgoingEventInfo outgoingEvent,
        OutboxConfig outboxConfig)
    {
        if (string.IsNullOrWhiteSpace(ProducerTopicName))
        {
            return Task.CompletedTask;
        }
        Headers headers = GetDefaultHeaders(outgoingEvent.Id,outgoingEvent.EventName);
        return Task.FromResult(PublishAsync(
            ProducerTopicName,
            outgoingEvent.EventName,
            outgoingEvent.EventData,
            headers
        ));
    }

    public override Task PublishManyFromOutboxAsync(IEnumerable<OutgoingEventInfo> outgoingEvents, OutboxConfig outboxConfig)
    {
        if (string.IsNullOrWhiteSpace(ProducerTopicName))
        {
            return Task.CompletedTask;
        }
        IProducer<string, byte[]> producer = ProducerPool.Get(AgoraKafkaEventBusOptions.ConnectionName);
        OutgoingEventInfo[] outgoingEventArray = outgoingEvents.ToArray();
        
        foreach (OutgoingEventInfo outgoingEvent in outgoingEventArray)
        {
            Headers headers = GetDefaultHeaders(outgoingEvent.Id,outgoingEvent.EventName);
            producer.Produce(
                ProducerTopicName,
                new Message<string, byte[]>
                {
                    Key = outgoingEvent.EventName,
                    Value = outgoingEvent.EventData,
                    Headers = headers
                });
        }

        return Task.CompletedTask;
    }

    protected virtual Headers GetDefaultHeaders(Guid messageId, string eventType)
    {
        return new Headers
        {
            {
                MessageId, System.Text.Encoding.UTF8.GetBytes(messageId.ToString("N"))
            },
            {
                EventType, System.Text.Encoding.UTF8.GetBytes(eventType)
            }
        };
    }
}