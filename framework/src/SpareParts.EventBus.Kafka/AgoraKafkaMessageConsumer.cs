using System;
using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;
using Confluent.Kafka;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Volo.Abp.DependencyInjection;
using Volo.Abp.ExceptionHandling;
using Volo.Abp.Kafka;
using Volo.Abp.Threading;

namespace SpareParts.EventBus.Kafka;


[Dependency(ReplaceServices = true)]
[ExposeServices(typeof(KafkaMessageConsumer))]
[ExcludeFromCodeCoverage]
public class AgoraKafkaMessageConsumer(
    IConsumerPool consumerPool,
    IExceptionNotifier exceptionNotifier,
    IOptions<AbpKafkaOptions> options,
    IProducerPool producerPool,
    AbpAsyncTimer timer)
    : KafkaMessageConsumer(consumerPool, exceptionNotifier, options, producerPool, timer)
{
    protected override Task Timer_Elapsed(AbpAsyncTimer timer)
    {
        if (Consumer == null)
        {
            Consume();
        }
        return Task.CompletedTask;
    }

    protected override async Task HandleIncomingMessage(ConsumeResult<string, byte[]> consumeResult)
    {
        try
        {
            foreach (Func<Message<string, byte[]>, Task> callback in Callbacks)
            {
                await callback(consumeResult.Message);
            }

            Consumer?.Commit(consumeResult);
        }
        catch (Exception ex)
        {
            Logger.LogException(ex);
            await ExceptionNotifier.NotifyAsync(ex);
        }
    }
}