using System;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using SpareParts.AbpMediatR.Commands;
using SpareParts.AbpMediatR.Handlers;
using Volo.Abp.DependencyInjection;

namespace SpareParts.AbpMediatR;

public class CommandSender(IServiceProvider serviceProvider) : ICommandSender, IScopedDependency
{
    public Task<TResponse> Send<TResponse>(ICommand<TResponse> command, CancellationToken cancellationToken = default)
    {
        MethodInfo methodInfo = typeof(CommandSender)
            .GetMethod(nameof(SendCommand), BindingFlags.NonPublic | BindingFlags.Instance)!;

        MethodInfo send = methodInfo.MakeGenericMethod(command.GetType(), typeof(TResponse));

        return (Task<TResponse>)send.Invoke(this, [command, cancellationToken])!;
    }

    public Task Send<TCommand>(TCommand command, CancellationToken cancellationToken = default)
        where TCommand : ICommand
    {
        ICommandHandler<TCommand> handler = serviceProvider.GetRequiredService<ICommandHandler<TCommand>>();
        return handler.Handle(command, cancellationToken);
    }

    private Task<TResponse> SendCommand<TCommand, TResponse>(TCommand command,
        CancellationToken cancellationToken = default)
        where TCommand : ICommand<TResponse>
    {
        ICommandHandler<TCommand, TResponse> handler = serviceProvider.GetRequiredService<ICommandHandler<TCommand, TResponse>>();
        return handler.Handle(command, cancellationToken);
    }
}