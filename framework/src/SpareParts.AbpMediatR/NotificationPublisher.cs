using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using SpareParts.AbpMediatR.Notifications;

namespace SpareParts.AbpMediatR;

public class NotificationPublisher(IServiceProvider serviceProvider)
    : INotificationPublisher
{
    public async Task Publish<TNotification>(TNotification notification, CancellationToken cancellationToken = default)
        where TNotification : INotification
    {
        IEnumerable<INotificationHandler<TNotification>> notificationHandlers = serviceProvider.GetServices<INotificationHandler<TNotification>>();

        foreach (INotificationHandler<TNotification> notificationHandler in notificationHandlers)
        {
            await notificationHandler.Handle(notification, cancellationToken);
        }
    }
}