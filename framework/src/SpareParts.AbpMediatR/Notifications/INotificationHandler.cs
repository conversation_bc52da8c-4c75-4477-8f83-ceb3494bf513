using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace SpareParts.AbpMediatR.Notifications;

public interface INotificationHandler<in TNotification> : ITransientDependency
    where TNotification : INotification
{
    /// <summary>
    /// Handles a notification
    /// </summary>
    /// <param name="notification">The notification</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task Handle(TNotification notification, CancellationToken cancellationToken);
}