using Microsoft.Extensions.DependencyInjection;
using SpareParts.AbpMediatR.Commands;
using SpareParts.AbpMediatR.Notifications;
using SpareParts.AbpMediatR.Queries;
using Volo.Abp;
using Volo.Abp.Testing;

namespace SpareParts.AbpMediatR;
public abstract class CqrsTestBase : AbpIntegratedTest<CqrsTestModule>
{
    protected readonly ICommandSender CommandSender;
    protected readonly IQuerySender QuerySender;
    protected readonly INotificationPublisher NotificationPublisher;

    protected CqrsTestBase()
    {
        CommandSender = ServiceProvider.GetRequiredService<ICommandSender>();
        QuerySender = ServiceProvider.GetRequiredService<IQuerySender>();
        NotificationPublisher = ServiceProvider.GetRequiredService<INotificationPublisher>();
    }
    protected override void SetAbpApplicationCreationOptions(AbpApplicationCreationOptions options)
    {
        options.UseAutofac();
    }
}
