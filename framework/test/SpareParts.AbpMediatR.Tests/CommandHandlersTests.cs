using System;
using System.Threading.Tasks;
using Shouldly;
using SpareParts.AbpMediatR.Commands;
using SpareParts.AbpMediatR.Handlers;
using SpareParts.AbpMediatR.Notifications;

namespace SpareParts.AbpMediatR;

public sealed class CommandHandlersTests : CqrsTestBase
{    
    [Fact]
    public async Task CreateCommand_Should_Return_Non_Empty_Guid()
    {
        Guid guid = Guid.NewGuid();
        Guid result = await CommandSender.Send(new CreateObjectCommand(guid));
        result.ShouldBe(guid);
    }
}

public sealed class NotificationHandlerTests : CqrsTestBase
{
    [Fact]
    public async Task ObjectCreatedNotificationHandler_Should_Return_Non_Empty_Guid()
    {
        Guid guid = Guid.NewGuid();
        await NotificationPublisher.Publish(new ObjectCreatedNotification(guid));
        FakeNotificationStore.Result.Id.ShouldBe(guid);
    }
}